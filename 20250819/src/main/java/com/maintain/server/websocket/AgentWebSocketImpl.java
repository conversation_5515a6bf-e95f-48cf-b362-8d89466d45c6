package com.maintain.server.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.common.log.Log;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.maintain.server.Constants;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.*;
import com.maintain.server.utils.*;
import com.maintain.server.vo.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.java_websocket.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-02-28
 */
@Service("agentWebSocketService")
public class AgentWebSocketImpl extends BaseWebSocket implements IceFlag, Constants {

    @Autowired
    private AgentService agentService;

    @Autowired
    private SoftwareService softwareService;

    @Value("${server.localHost}")
    protected String maintainServerIp;

    @Value("${ntpServer}")
    protected String ntpServer;

    private String iceServerPort;


    private static final ReentrantLock lock = new ReentrantLock(true);

    @Override
    @OperateRecordAnnotation(name = "开启%s上的Agent", module = "agent")
    public String openSoftware(String param, WebSocket session) throws ServiceException {
        AgentVo agentVo = getAgentVo(param);
        try {
            openAgent(agentVo, session);
            if (agentVo.getPid() != -1) {
                return SUCCESS;
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
        return CLOSE;
    }

    @Override
    @OperateRecordAnnotation(name = "关闭%s上的Agent", module = "agent")
    public String closeSoftware(String param, WebSocket session) throws ServiceException {
        AgentVo agentVo = getAgentVo(param);
        try {
            closeAgent(agentVo, session);
            return SUCCESS;
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            throw new ServiceException(e);
        }
    }

    @Override
    @OperateRecordAnnotation(name = "重启%s上的Agent", module = "agent")
    public String reloadSoftware(String param, WebSocket session) throws ServiceException {
        throw new ServiceException("未知请求");
    }

    @Override
    @OperateRecordAnnotation(name = "更新%s上的Agent", module = "agent")
    public void updateSoftware(String param, WebSocket webSocket) throws ServiceException {
        AgentCriteria criteria = new AgentCriteria();
        if (param != null) {
            HashMap hashMap = JsonUtil.parseObject(param, HashMap.class);
            Object ip = hashMap.get("ip");
            if (ip != null) {
                criteria.setIp(ip.toString());
            }
        }
        sentText(webSocket, "开始更新Agent");
        List<AgentVo> agentList = agentService.getAgentList(criteria);
        if (ListUtil.isEmpty(agentList)) {
            throw new ServiceException(ResponseType.OPERATE_ERROR.getMsg() + ",找不到Agent");
        }
        try {
            List<UpdateResult> updateResults = new LinkedList<>();
            for (AgentVo agentVo : agentList) {
                UpdateResult updateResult = new UpdateResult();
                updateResult.setServerIP(agentVo.getIp());
                try {
                    addAndUpdateAgent(JsonUtil.toJsonString(agentVo), webSocket, "update");
                    updateResult.setUpdateResult(true);
                } catch (ServiceException e) {
                    updateResult.setUpdateResult(false);
                    Log.high.error(e.getMessage(), e);
                    sentText(webSocket, e.getMessage().concat(" ").concat(agentVo.getIp()));
                }
                updateResults.add(updateResult);
            }

            Map<Boolean, List<UpdateResult>> resultCollect = updateResults.stream().collect(Collectors.groupingBy(UpdateResult::getUpdateResult));
            int updateSuccCount = 0;
            int failure = 0;
            List<String> failureIps = null;
            if (resultCollect.get(true) != null) {
                updateSuccCount = resultCollect.get(true).size();
            }
            if (resultCollect.get(false) != null) {
                failure = resultCollect.get(false).size();
                failureIps = resultCollect.get(false).stream().map(m -> m.getServerIP()).collect(Collectors.toList());
            }

            sentText(webSocket, "成功更新: " + updateSuccCount + " 个agent, 失败: " + failure + " 个agent");
            if (failureIps != null) {
                sentText(webSocket, "更新失败的有: " + failureIps);
            }
            sentText(webSocket, "Agent更新完成!");
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    @OperateRecordAnnotation(name = "向%s上添加Agent", module = "agent")
    public Boolean autoDeploy(String param, WebSocket webSocket) throws ServiceException {
        addAndUpdateAgent(param, webSocket, "add");
        return true;
    }

    @Override
    @OperateRecordAnnotation(name = "一键同步%s的时间", module = "agent")
    public void setSyschroTime(String param, WebSocket webSocket) throws ServiceException {
        AgentCriteria criteria = new AgentCriteria();
        if (param != null) {
            HashMap hashMap = JsonUtil.parseObject(param, HashMap.class);
            Object ip = hashMap.get("ip");
            if (ip != null) {
                criteria.setIp(ip.toString());
            }
        }
        sentText(webSocket, "开始同步ntp服务器时间");
        List<Map<String, Object>> allHosts = agentService.getAllHosts(criteria);
        if (ListUtil.isEmpty(allHosts)) {
            throw new ServiceException(ResponseType.OPERATE_ERROR.getMsg() + ",找不到Hosts");
        }
        try {
            int succ = 0;
            int failure = 0;
            List<String> failureIps = new LinkedList<>();
            for (Map<String, Object> host : allHosts) {
                String ip = host.get("ip").toString();
                if (!ip.equals(ntpServer)) {
                    sentText(webSocket, "开始同步:[" + ip + "]主机时间");
                    try {
                        final String result = softwareService.iceRequest(ip.toString(), IceFlag.SET_SYSCHRONISED_TIME, null);
                        if (!StringUtils.isEmpty(result)) {
                            succ++;
                            sentText(webSocket, "[" + ip.toString() + "]主机同步时间结果： " + result);
                        } else {
                            failure++;
                            failureIps.add(ip.toString());
                            sentText(webSocket, "[" + ip.toString() + "]主机同步时间失败, 请检查Agent连接情况!");
                        }
                        sentText(webSocket, "---------------------------------------");
                    } catch (ServiceException e) {
                        Log.high.error(e.getMessage(), e);
                        sentText(webSocket, e.getMessage().concat(" ").concat(ip.toString()));
                    }
                }
            }
            sentText(webSocket, "所有主机时间同步操作完成; 成功：" + succ + "个, 失败：" + failure + "个;");
            if (!failureIps.isEmpty()) {
                sentText(webSocket, "同步失败的主机为：" + failureIps);
                sentText(webSocket, "请检查同步失败主机的Agent连接情况");
            }
            sentText(webSocket, "提示:平滑同步过渡需要一定时间,时差越大过渡时间越长,请稍后再查看系统时间是否同步成功!");
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }


    public void addAndUpdateAgent(String param, WebSocket webSocket, String operate) {
        AgentVo agentVo = JsonUtil.parseObject(param, AgentVo.class);
        try {
            Session session = BaseWebSocket.getSession(agentVo);
            ProcessUtil.initChannelShell(session, agentVo, 150000L);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            throw new ServiceException("连接远程服务器异常", e);
        }
        sentText(webSocket, "开始检查 " + agentVo.getIp() + " 基础环境");
        boolean[] checkBaseEnvResult = null;
        try {
            checkBaseEnvResult = checkBaseEnv(agentVo, webSocket);
            if (checkBaseEnvResult == null) {
                sentText(webSocket, agentVo.getIp() + " 基础环境检查结果为null");
                return;
            }
            if (!checkBaseEnvResult[0]) {
                sentText(webSocket, agentVo.getIp() + " 没有安装yml，请先手动安装再更新！");
                return;
            }
            if (!checkBaseEnvResult[1]) {
                sentText(webSocket, agentVo.getIp() + " 没有安装expect，请先手动安装再更新！");
                return;
            }
        } catch (Exception e) {
            Log.high.error(agentVo.getIp() + " 基础环境检查失败", e);
            sentText(webSocket, agentVo.getIp() + " 的基础环境检查异常");
        }
        if (Objects.isNull(agentVo.getCreateTime())) {
            try {
                installBaseEvn(checkBaseEnvResult, agentVo, webSocket);
            } catch (Exception e) {
                Log.high.error(agentVo.getIp() + " 安装基础环境异常", e);
                sentText(webSocket, agentVo.getIp() + " 安装基础环境失败，请手动安装！");
            }
        }
        try {
            boolean[] checkResult = checkBaseEnv(agentVo, webSocket);
            if (checkResult[2]) {
                sentText(webSocket, agentVo.getIp() + " 成功安装java环境");
            } else {
                sentText(webSocket, agentVo.getIp() + " 安装java环境失败，请手动安装！");
            }
            if (checkResult[3]) {
                sentText(webSocket, agentVo.getIp() + " 成功安装python环境");
            } else {
                sentText(webSocket, agentVo.getIp() + " 安装python环境失败，请手动安装！");
            }
            if (checkResult[4]) {
                sentText(webSocket, agentVo.getIp() + " 成功安装7z环境");
            } else {
                sentText(webSocket, agentVo.getIp() + " 安装7z环境失败，请手动安装！");
            }
        } catch (Exception e) {
            Log.high.error(agentVo.getIp() + " 基础环境检查失败", e);
            sentText(webSocket, agentVo.getIp() + " 的基础环境检查异常");
        } finally {
            sentText(webSocket, "基础环境检查完毕，关闭SSH连接");
            try {
                ProcessUtil.destroyChannelShell(agentVo);
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
        }

        final AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(agentVo.getIp());
        Map<String, Object> map = prepareAddAgent(agentVo);

        if (StringUtils.isEmpty(maintainServerIp)) {
            throw new ServiceException("系统错误，系统配置中运维服务器IP有误");
        }
        if (map == null) {
            throw new ServiceException("map参数不能为空");
        }
        if (map.get("name") == null) {
            throw new ServiceException("未知程序名");
        }
        if (agentVo.getIp() == null) {
            throw new ServiceException("未知服务器IP");
        }
        String name = map.get("name").toString();

        map.put("ip", agentVo.getIp());
        if (agentVo.getPid() != null && agentVo.getPid() > 0) {
            map.put("pid", agentVo.getPid());
        }
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        if (osId == null || agentVo.getOs() == null) {
            throw new ServiceException("未知操作系统类型: " + osName);
        }
        // 1、查找本地目录是否有待上传的程序
        String remotePath = "";
        String localPath = "";
        String remoteUploadPath = "";
        if (osId.equals(LINUX)) {
            localPath = MAINTAIN_LINUX_TEMP + name;
        } else if (osId.equals(WINDOWS)) {
            localPath = MAINTAIN_WINDOWS_TEMP + name;
        }
        File file = new File(localPath);
        if (!file.exists()) {
            throw new ServiceException("找不到路径" + localPath + ",请确保待上传程序已经存放于此。");
        }
        if (agentVo.getOs().equals(LINUX)) {
            remotePath = MAINTAIN_LINUX_REMOTE;
            if ("root".equals(agentVo.getName())) {
                remotePath = remotePath + name;
                remoteUploadPath = remotePath;
            } else {
                remoteUploadPath = "/home/" + agentVo.getName() + "/" + name;
            }

        } else if (agentVo.getOs().equals(WINDOWS)) {
            remotePath = MAINTAIN_WINDOWS_REMOTE + name;
        }
        // 2、将本地程序拷贝到临时目录tempFile
        String tempPath = localPath + "-" + agentVo.getIp() + "-TEMP";
        map.put("remotePath", remotePath);
        map.put("tempPath", tempPath);
        map.put("remoteUploadPath", remoteUploadPath);
        File tempFile = new File(tempPath);
        try {
            if (tempFile.exists()) {
                Log.low.info("deleteDir:" + tempFile.getAbsolutePath());
                FileUtil.deleteDir(tempFile);
            }
            FileUtils.copyDirectory(file, tempFile);
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
        try {
            sentText(webSocket, "修改" + name + "配置文件");
            this.updateConfigFile(tempFile, map);
            sentText(webSocket, name + "配置文件修改完毕");
            if (agentVo.getIp() != null && agentVo.getIp().equals(this.maintainServerIp)) {
                // 本地部署
                sentText(webSocket, "准备在本地服务器中部署" + name);
                localDeploy(map, osId, webSocket);
                sentText(webSocket, "部署完毕");
            } else {
                sentText(webSocket, "准备在 " + agentVo.getIp() + " 服务器中部署" + name);
                //远程部署前，判断用户名是否为root，非root则先执行切换root并移动文件夹的脚本
                remoteDeploy(agentVo, map, tempFile, webSocket);
                sentText(webSocket, "部署完毕");
            }
            try {
                agentVo.setStatus(AlarmStatusType.GREEN);
                agentVo.setProgramStatus(ProgramStatusType.OPEN);
                agentVo.setOperateType(SoftwareOperateType.CLOSE);
                if (operate.equals("add")) {
                    AddResult addResult = agentService.addAgent(agentVo);
                    if (addResult.getAddSucc() != 0) {
                        sentText(webSocket, "agent添加成功");
                    } else if (addResult.getRepeat() != 0) {
                        sentText(webSocket, "不允许重复添加");
                    } else {
                        sentText(webSocket, "agent添加失败");
                    }
                } else if (operate.equals("update")) {
                    agentService.updateAgent(agentVo);
                }
            } catch (Exception e) {
                String msg = "插入Agent记录失败，异常信息如下：" + e.getMessage();
                sentText(webSocket, msg);
            }
            sentText(webSocket, "------------------------------------");
        } finally {
            File tmpFile = new File(tempPath);
            if (tmpFile.exists()) {
                FileUtil.deleteDir(tmpFile);
                if (tmpFile.exists()) {
                    try {
                        FileUtils.forceDelete(tmpFile);
                    } catch (IOException e) {
                        Log.high.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    private boolean sendHeartbeat(AgentVo agentVo, WebSocket webSocket, String name) {
        try {
            return true;
        } catch (Exception e) {
            Session sshSession = null;
            try {
                sshSession = getSession(agentVo);
                String msg = "接收心跳失败，" + name + "启动失败：" + e.getMessage();
                sentText(webSocket, msg);
            } catch (Exception e1) {
                Log.high.error("sendHeartbeat exception", e1);
            } finally {
                if (sshSession != null && sshSession.isConnected()) {
                    sshSession.disconnect();
                }
            }

            return false;
        }
    }

    @Override
    public boolean tryLock() throws ServiceException {
        return super.tryLock(lock);
    }

    @Override
    public boolean unLock() throws ServiceException {
        return super.unLock(lock);
    }

    private Map<String, Object> prepareAddAgent(AgentVo agentVo) throws ServiceException {
        try {
            String version = agentVo.getVersion();
            if (version != null) {
                BigDecimal bigDecimal = new BigDecimal(version);
                version = bigDecimal.add(BigDecimal.valueOf(0.1)).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
            } else {
                List<AgentVo> list = agentService.getAgentList(null);
                if (ListUtil.isEmpty(list)) {
                    version = "1.0";
                } else {
                    AgentVo tempAgent = list.get(0);
                    //铁定不为null
                    version = tempAgent.getVersion();
                }
            }
            agentVo.setVersion(version);
        } catch (ServiceException e) {
            Log.high.error(e.getMessage(), e);
            throw new ServiceException(e);
        }
        if (ListUtil.isEmpty(agentVo.getRoleIds())) {
            if (StringUtils.isEmpty(agentVo.getRole())) {
                throw new ServiceException("角色信息不能为空");
            }
        } else if (StringUtils.isEmpty(agentVo.getRole())) {
            List<Map<String, Object>> list = agentService.getPlatformRole(agentVo.getRoleIds());
            if (ListUtil.isEmpty(list)) {
                throw new ServiceException("平台角色列表为空");
            }
            String role = "";
            for (Map<String, Object> roleMap : list) {
                Object obj = roleMap.get("name");
                if (obj != null) {
                    role = role.concat(obj.toString()).concat(",");
                }
            }
            role = role.substring(0, role.lastIndexOf(","));
            agentVo.setRole(role);
        }
        String localPath = "";
        String name = "MaintainAgent";
        if (StringUtils.isEmpty(agentVo.getIp())) {
            throw new ServiceException("Agent的IP不能为空");
        }
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        if (LINUX.equals(osId)) {
            localPath = MAINTAIN_LINUX_TEMP + name;
        } else if (WINDOWS.equals(osId)) {
            localPath = MAINTAIN_WINDOWS_TEMP + name;
        } else {
            throw new ServiceException("未知操作类型");
        }
        File file = new File(localPath);
        if (!file.exists()) {
            throw new ServiceException("找不到路径" + localPath + ",请确保待上传程序已经存放于此。");
        }
        Integer autoNtp = agentVo.getAutoNTP() == null ? 1 : agentVo.getAutoNTP();
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("ip", agentVo.getIp());
        map.put("autoNtp", autoNtp);
        //发送硬件信息的时间间隔（秒）强制！
        map.put("sendHardInfoTime", SEND_DATE_INTERVAL);
        map.put("sendMonitorTime", SEND_DATE_INTERVAL);
        String ftpPath = "";
        if (agentVo.getOs().equals(LINUX)) {
            ftpPath = FTP_PATH_LINUX;
        } else if (agentVo.getOs().equals(WINDOWS)) {
            ftpPath = FTP_PATH_WINDOWS;
        }
        map.put("ftpPath", ftpPath);
        return map;
    }

    private AgentVo getAgentVo(String param) throws ServiceException {
        Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
        });
        Object id = map.get("id");
        if (id == null) {
            throw new ServiceException("Agent的ID不能为空");
        }
        AgentCriteria criteria = new AgentCriteria();
        criteria.setId(Integer.valueOf(id.toString()));
        AgentVo agentVo = agentService.getAgentByCriteria(criteria);
        if (agentVo == null) {
            throw new ServiceException("找不到Agent");
        }
        return agentVo;
    }

    private void closeAgent(AgentVo agentVo, WebSocket conn) throws ServiceException {
        //1、ICE直连Agent
        Session session = null;
        try {
            sentText(conn, agentVo.getIp() + "上Agent连通性测试");
            sentText(conn, "连接Agent" + agentVo.getIp() + "成功，准备关闭Agent");
            String result = ServerRequestUtil.sendRequest(agentVo.getIp(), IceFlag.CLOSE_AGENT, null);
            if (StringUtils.isNotEmpty(result) && ResponseType.SUCCESS.getCode().equals(JSON.parseObject(result, ResponseVo.class).getCode())) {
                sentText(conn, agentVo.getIp() + "上Agent关闭成功");
                Log.low.info("closeAgent result: " + result);
                agentVo.setDescription("agent已关闭");
                agentVo.setStatus(AlarmStatusType.GRAY);
                agentVo.setProgramStatus(ProgramStatusType.CLOSE);
                agentVo.setPid(-1);
                agentVo.setLastHeartbeatTime(null);
                agentService.updateAgent(agentVo);
                return;
            }

        } catch (Exception e) {
            Log.low.warn("尝试ice连接关闭agent失败");
        }
        try {
            sentText(conn, "连接Agent失败，尝试通过SSH结束Agent进程");
            // 2、直连SSH
            if (StringUtils.isEmpty(OsType.getName(agentVo.getOs()))) {
                throw new ServiceException("关闭Agent失败,未知操作系统类型");
            }
            sentText(conn, "通过SSH连接" + agentVo.getIp() + "服务器");
            session = BaseWebSocket.getSession(agentVo);
            sentText(conn, "连接成功");
            ProcessUtil.initChannelShell(session, agentVo, 10000L);
            ProcessUtil.closeAgent(agentVo);
            agentVo.setDescription("agent已关闭");
            agentVo.setStatus(AlarmStatusType.GRAY);
            agentVo.setProgramStatus(ProgramStatusType.CLOSE);
            agentVo.setPid(-1);
            agentVo.setLastHeartbeatTime(null);
            agentService.updateAgent(agentVo);
            sentText(conn, "关闭agent，断开SSH连接");
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        } finally {
            try {
                ProcessUtil.destroyChannelShell(agentVo);
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
            sentText(conn, "断开完毕，程序结束");

        }
    }

    private void openAgent(AgentVo agentVo, WebSocket conn) throws ServiceException {
        Session sshSession = BaseWebSocket.getSession(agentVo);
        try {
            sentText(conn, "检验" + agentVo.getIp() + "上Agent存活状态");
            if (ProgramStatusType.OPEN.equals(agentVo.getProgramStatus()) && AlarmStatusType.GREEN.equals(agentVo.getStatus()) && agentVo.getPid() != -1) {
                sentText(conn, "当前Agent存活状态为Green，不需要启动");
                return;
            }
            agentVo.setProgramStatus(ProgramStatusType.OPEN);
            sentText(conn, "连接远程服务器成功");
            ProcessUtil.initChannelShell(sshSession, agentVo, 30000L);
            sentText(conn, "远程开启 MaintainAgent");
            agentVo.setPid(-1);
            try {
                ProcessUtil.agentLive(agentVo);
            } catch (Exception e) {
                Log.low.warn(agentVo.getIp() + " 开启agent异常", e);
            }
            if (agentVo.getPid().equals(-1)) {
                agentVo.setStatus(AlarmStatusType.RED);
                agentVo.setDescription("心跳为空");
                agentVo.setLastHeartbeatTime(null);
                agentVo.setOperateType(SoftwareOperateType.CLOSE);
                sentText(conn, "暂未收到 MaintainAgent 心跳，开启结果以心跳时间为准");
            } else {
                agentVo.setLastHeartbeatTime(new Date());
                agentVo.setStatus(AlarmStatusType.GREEN);
                agentVo.setOperateType(SoftwareOperateType.CLOSE);
                sentText(conn, "开启 MaintainAgent 成功");
            }
            agentService.updateAgent(agentVo);
        } catch (Exception e) {
            String msg = e.getMessage() + agentVo.getIp();
            sentText(conn, msg);
            Log.high.error(msg, e);
            throw new ServiceException(e);
        } finally {
            try {
                ProcessUtil.destroyChannelShell(agentVo);
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
        }
    }

    @Override
    protected void updateConfigFile(File tempFile, Map<String, Object> map) throws ServiceException {
        String ip = map.get("ip").toString();
        String configPath = tempFile.getPath() + File.separator + "conf" + File.separator + "config.json";
        Log.low.info("configPath:" + configPath);
        Map<String, Object> configMap = new HashMap<>();
        configMap.put("maintainServer", "MaintainServer:tcp -h " + this.maintainServerIp + " -p " + getIceServerPort());
        configMap.put("maintainAgent", "MaintainAgent:tcp -h " + ip + " -p 60001");
        configMap.put("ftpPrefix", "ftp://" + ip);
        configMap.put("maintainServerIp", this.maintainServerIp);
        configMap.put("sendMonitorTime", SEND_MONITOR_TIME);
        configMap.put("ntpServer", this.ntpServer);
        configMap.put("ftpDir", map.get("ftpPath"));
        configMap.put("agentDir", map.get("remotePath"));
        //默认开启NTP服务
        configMap.put("autoNtp", map.get("autoNtp"));
        FileUtil.updateJson(configPath, configMap);
        String icepoolPath = tempFile.getPath() + File.separator + "deploy" + File.separator + "icepool.conf";
        Log.low.info("icepoolPath:" + icepoolPath);
        Map<String, String> icepoolMap = new HashMap<>();
        icepoolMap.put("Endpoints", "tcp -h " + ip + " -p 60001");
        FileUtil.updateKeyValue(icepoolPath, icepoolMap);
    }

    @Override
    protected String autoDeployCmdInWindows(String processName) {
        String cmd = "net start MaintainAgent";
        Log.low.info("cmdInWindows:" + cmd);
        return cmd;
    }

    @Override
    protected String autoDeployCmdInLinux(String processName) {
        if (processName == null) {
            throw new ServiceException("未知程序名");
        }
        if (processName.equals("MaintainAgent") || processName.equals("maintain-agent")) {
            return "cd /dist/" + processName + "/deploy && chmod 777 start.sh && chmod 777 start_background.sh && ./start_background.sh";
        }
        String cmd = "cd /dist/" + processName + "/deploy && chmod 777 start.sh && ./start.sh";
        Log.low.info("cmdInLinux:" + cmd);
        return cmd;

    }


    public void installBaseEvn(boolean[] result, AgentVo agentVo, WebSocket conn) throws ServiceException {
        Session sshSession = null;
        ChannelSftp sftp = null;
        try {
            Log.low.info("基础环境检查结果:" + Arrays.toString(result));
            Integer osId = agentVo.getOs();
            if (maintainServerIp.equals(agentVo.getIp())) {
                //本地肯定自带JAVA环境
                if (!result[3]) {
                    sentText(conn, "未安装Python环境，准备安装");
                    sentText(conn, "检测操作系统类型");
                    if (LINUX.equals(osId)) {
                        sentText(conn, "本地服务器操作系统类型为Linux");
                        installPythonOnLocalLinux();
                    } else {
                        sentText(conn, "本地服务器操作系统类型为Windows");
                        installPythonOnLocalWindows();
                    }
                    //sentText(conn, "安装Python环境完毕");
                } else {
                    sentText(conn, "本地服务器已安装Python");
                }
                if (!result[4]) {
                    sentText(conn, "未安装7z，准备安装");
                    sentText(conn, "检测操作系统类型");
                    if (LINUX.equals(osId)) {
                        sentText(conn, "本地服务器操作系统类型为Linux");
                        install7ZOnLocalLinux();
                    } else {
                        sentText(conn, "本地服务器操作系统类型为Windows");
                        install7ZOnLocalWindows();
                    }
                    //sentText(conn, "安装7z完毕");
                } else {
                    sentText(conn, "本地服务器已安装7z");
                }
            } else {
                sshSession = BaseWebSocket.getSession(agentVo);
                sftp = ProcessUtil.openChannelSftp(sshSession);
                ProcessUtil.initChannelShell(sshSession, agentVo, 300000L);
                //没有JAVA环境
                if (!result[2]) {

                } else {
                    sentText(conn, "远程服务器已安装Java环境");
                }
                //没有python环境
                if (!result[3]) {

                } else {
                    sentText(conn, "远程服务器已安装Python环境");
                }
                //没有7Z环境
                if (!result[4]) {
                    sentText(conn, "未安装7z，准备安装");
                    if (LINUX.equals(osId)) {
                        sentText(conn, "远程服务器操作系统类型为Linux");
                        install7ZOnRemoteLinux(sshSession, sftp, agentVo.getName());
                    } else {
                        sentText(conn, "远程服务器操作系统类型为Windows");
                        install7ZOnRemoteWindows(sshSession, sftp);
                    }
                    //sentText(conn, "安装7z完毕");
                } else {
                    sentText(conn, "远程服务器已安装7z环境");
                }
            }
        } catch (JSchException | FileNotFoundException | SftpException e) {
            String msg = "未知异常" + e.getMessage();
            sentText(conn, msg);
            Log.low.error(e.getMessage(), e);
            throw new ServiceException(msg, e);
        } catch (ServiceException e) {
            String message = e.getMessage();
            Log.low.error(message, e);
            if (message.contains("Auth fail")) {
                sentText(conn, "ssh连接服务器失败，请确认用户名密码是否正确");
            } else {
                sentText(conn, "ssh连接服务器失败，请检查服务器openssh服务和防火墙端口是否打开");
            }
            throw e;
        } catch (Exception e) {
            String msg = "执行刷新注册表命令出错" + e.getMessage();
            sentText(conn, msg);
            Log.low.error(e.getMessage(), e);
            throw new ServiceException(msg, e);
        } finally {
            if (sftp != null) {
                sftp.disconnect();
            }
        }
    }

    /**
     * 在本地LINUX机器上安装PYTHON环境
     *
     * @throws IOException
     */
    private void installPythonOnLocalLinux() throws IOException {
        String pythonPath = "./baseEvn/Python-3.6.5.tar.gz";
        File srcFile = new File(pythonPath);
        if (!srcFile.exists()) {
            throw new ServiceException("baseEvn目录下不存在ReadRegeditAgain.exe文件");
        }
        String remotePath = "/dist/MAINTAIN_TEMP/baseEvn/" + srcFile.getName();
        FileUtils.copyFile(srcFile, new File(remotePath));
        StringBuilder command = new StringBuilder();
        command.append("tar -zxf /dist/MAINTAIN_TEMP/baseEvn/Python-3.6.5.tar.gz -C /usr/local")
                .append(" && ")
                .append("echo \"export PYTHON_HOME=/usr/local/Python-3.6.5\" >> /etc/profile")
                .append(" && ")
                .append("echo \"export PATH=\\$PYTHON_HOME:\\$PATH\" >> /etc/profile");
        ProcessUtil.execLocalCommand(command.toString(), LINUX);

        ProcessUtil.execLocalCommand("source /etc/profile", LINUX);
    }

    /**
     * 在本地LINUX机器上安装7Z环境
     *
     * @throws IOException
     */
    private void install7ZOnLocalLinux() throws IOException {
        String pythonPath = "./baseEvn/p7zip.tar.gz";
        File srcFile = new File(pythonPath);
        if (!srcFile.exists()) {
            throw new ServiceException("baseEvn目录下不存在p7zip.tar.gz文件");
        }
        String remotePath = "/dist/MAINTAIN_TEMP/baseEvn/" + srcFile.getName();
        FileUtils.copyFile(srcFile, new File(remotePath));
        StringBuilder command = new StringBuilder();
        command.append("tar -zxf /dist/MAINTAIN_TEMP/baseEvn/p7zip.tar.gz -C /usr/local")
                .append(" && ")
                .append("echo \"export TOOLS_7Z_HOME=/usr/local/p7zip_16.02/bin\" >> /etc/profile")
                .append(" && ")
                .append("echo \"export PATH=\\$TOOLS_7Z_HOME:\\$PATH\" >> /etc/profile");
        ProcessUtil.execLocalCommand(command.toString(), LINUX);
        ProcessUtil.execLocalCommand("source /etc/profile", LINUX);
    }

    /**
     * 在本地WINDOWS机器上安装PYTHON环境
     */
    private void installPythonOnLocalWindows() throws IOException {
        long start = System.currentTimeMillis();
        String pythonPath = "./baseEvn/python-3.6.5-amd64.exe";
        String toolPath = "./baseEvn/ReadRegeditAgain.exe";
        File localPythonFile = new File(pythonPath);
        File localToolFile = new File(toolPath);
        if (!localPythonFile.exists()) {
            throw new ServiceException("baseEvn目录下不存在python-3.6.5-amd64.exe文件");
        }
        if (!localToolFile.exists()) {
            throw new ServiceException("baseEvn目录下不存在ReadRegeditAgain.exe文件");
        }
        String remotePythonPath = "D:\\MAINTAIN_TEMP\\baseEvn\\python-3.6.5-amd64.exe";
        String remoteToolPath = "D:\\MAINTAIN_TEMP\\baseEvn\\ReadRegeditAgain.exe";
        if (!new File(remotePythonPath).exists()) {
            FileUtils.copyFile(localPythonFile, new File(remotePythonPath));
        }
        if (!new File(remoteToolPath).exists()) {
            FileUtils.copyFile(localToolFile, new File(remoteToolPath));
        }

        StringBuilder command = new StringBuilder();
        command.append("D:\\MAINTAIN_TEMP\\baseEvn\\python-3.6.5-amd64.exe /q")
                .append(" && ")
                .append("reg add \"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\" /v Path /t REG_EXPAND_SZ /d \"%Path%;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python36;\" /f")
                .append(" && ")
                .append("D:\\MAINTAIN_TEMP\\baseEvn\\ReadRegeditAgain.exe");
        ProcessUtil.execLocalCommand(command.toString(), WINDOWS);
        Log.low.info("安装PYTHON环境耗时(ms):" + (System.currentTimeMillis() - start));
    }

    /**
     * 在本地WINDOWS机器上安装7z环境
     */
    private void install7ZOnLocalWindows() throws IOException {
        long start = System.currentTimeMillis();
        String pythonPath = "./baseEvn/7z1604-x64.exe";
        File localPythonFile = new File(pythonPath);
        if (!localPythonFile.exists()) {
            throw new ServiceException("baseEvn目录下不存在7z1604-x64.exe文件");
        }
        String remotePythonPath = "D:\\MAINTAIN_TEMP\\baseEvn\\7z1604-x64.exe";
        if (!new File(remotePythonPath).exists()) {
            FileUtils.copyFile(localPythonFile, new File(remotePythonPath));
        }

        ProcessUtil.execLocalCommand("D:\\MAINTAIN_TEMP\\baseEvn\\7z1604-x64.exe /q", WINDOWS);
        Log.low.info("安装7Z环境耗时(ms):" + (System.currentTimeMillis() - start));
    }

    /**
     * 在远程LINUX机器上安装PYTHON
     *
     * @param session
     * @param sftp
     * @throws Exception
     */
    private void installPythonOnRemoteLinux(Session session, ChannelSftp sftp, String user) throws Exception {
        String pythonPath = "./baseEvn/Python-3.6.5.tar.gz";
        String fileName = "Python-3.6.5.tar.gz";
        if (!new File(pythonPath).exists()) {
            throw new ServiceException("baseEvn目录下不存在Python-3.6.5.tar.gz文件");
        }
        String remotePath = "/dist/MAINTAIN_TEMP/baseEvn";
        String remoteUploadPath;
        if ("root".equals(user)) {
            remoteUploadPath = remotePath;
        } else {
            remoteUploadPath = "/home/" + user;
        }
        ProcessUtil.updateSingleFileToRemote(sftp, pythonPath, fileName, remotePath, remoteUploadPath);
        StringBuilder command = new StringBuilder();
        command.append("tar -zxf /dist/MAINTAIN_TEMP/baseEvn/Python-3.6.5.tar.gz -C /usr/local")
                .append(LINUX_NEW_LINE)
                .append("echo \"export PYTHON_HOME=/usr/local/Python-3.6.5\" >> /etc/profile")
                .append(LINUX_NEW_LINE)
                .append("echo \"export PATH=\\$PYTHON_HOME:\\$PATH\" >> /etc/profile")
                .append(LINUX_NEW_LINE)
                .append("source /etc/profile");
        ProcessUtil.execShell(command.toString());
    }

    /**
     * 在远程LINUX机器上安装7Z
     *
     * @param session
     * @param sftp
     * @throws Exception
     */
    private void install7ZOnRemoteLinux(Session session, ChannelSftp sftp, String user) throws Exception {
        String pythonPath = "./baseEvn/p7zip.tar.gz";
        String fileName = "p7zip.tar.gz";
        if (!new File(pythonPath).exists()) {
            throw new ServiceException("baseEvn目录下不存在p7zip.tar.gz文件");
        }
        String remotePath = "/dist/MAINTAIN_TEMP/baseEvn";
        String remoteUploadPath;
        if ("root".equals(user)) {
            remoteUploadPath = remotePath;
        } else {
            remoteUploadPath = "/home/" + user;
        }
        ProcessUtil.updateSingleFileToRemote(sftp, pythonPath, fileName, remotePath, remoteUploadPath);
        StringBuilder command = new StringBuilder();
        command.append("tar -zxf /dist/MAINTAIN_TEMP/baseEvn/p7zip.tar.gz -C /usr/local")
                .append(LINUX_NEW_LINE)
                .append("echo \"export TOOLS_7Z_HOME=/usr/local/p7zip_16.02/bin\" >> /etc/profile")
                .append(LINUX_NEW_LINE)
                .append("echo \"export PATH=\\$TOOLS_7Z_HOME:\\$PATH\" >> /etc/profile")
                .append(LINUX_NEW_LINE)
                .append("source /etc/profile");
        ProcessUtil.execShell(command.toString());
    }

    /**
     * 在远程LINUX机器上安装JDK
     *
     * @param session
     * @param sftp
     * @throws Exception
     */
    private void installJdkOnRemoteLinux(Session session, ChannelSftp sftp, String user) throws Exception {
        String localPath = "./baseEvn/jdk-8u102-linux-x64.rpm";
        String fileName = "jdk-8u102-linux-x64.rpm";
        File file = new File(localPath);
        if (!file.exists()) {
            throw new ServiceException("baseEvn目录下不存在jdk-8u102-linux-x64.rpm文件");
        }
        String remotePath = "/dist/MAINTAIN_TEMP/baseEvn";
        String remoteUploadPath;
        if ("root".equals(user)) {
            remoteUploadPath = remotePath;
        } else {
            remoteUploadPath = "/home/" + user;
        }
        ProcessUtil.updateSingleFileToRemote(sftp, localPath, fileName, remotePath, remoteUploadPath);
        StringBuilder command = new StringBuilder();
        command.append("rpm -ivh ").append(remotePath).append("/").append(file.getName())
                .append("&&")
                .append("echo \"export JAVA_HOME=/usr/java/jdk1.8.0_102\" >> /etc/profile")
                .append("&&")
                .append("echo \"export PATH=\\$JAVA_HOME/bin:\\$PATH\" >> /etc/profile")
                .append("&&")
                .append("echo \"export CLASSPATH=.:\\$JAVA_HOME/jre/lib/security/local_policy.jar:\\$JAVA_HOME/jre/lib/security/US_export_policy.jar:\\$JAVA_HOME/jre/lib/rt.jar:\\$CLASSPATH\" >> /etc/profile")
                .append("&&")
                .append("source /etc/profile");
        ProcessUtil.execShell(command.toString());
    }

    /**
     * 在远程WINDOWS机器上安装PYTHON
     *
     * @param session
     * @param sftp
     * @throws FileNotFoundException
     * @throws SftpException
     */
    private void installPythonOnRemoteWindows(Session session, ChannelSftp sftp) throws IOException, SftpException {
        String pythonPath = "./baseEvn/python-3.6.5-amd64.exe";
        String fileName = "python-3.6.5-amd64.exe";
        if (!new File(pythonPath).exists()) {
            throw new ServiceException("baseEvn目录下不存在ReadRegeditAgain.exe文件");
        }
        String remotePath = "MAINTAIN_TEMP/baseEvn";
        ProcessUtil.updateSingleFileToRemote(sftp, pythonPath, fileName, remotePath, remotePath);
        StringBuilder command = new StringBuilder();
        command.append("xcopy ")
                .append(remotePath.replaceAll("/", "\\\\"))
                .append(" D:\\MAINTAIN_TEMP\\baseEvn /Y /E /I")
                .append(" && ")
                .append("D:\\MAINTAIN_TEMP\\baseEvn\\python-3.6.5-amd64.exe /q")
                .append(" && ")
                .append("reg add \"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\" /v _HOME /t REG_SZ /d \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python36\" /f")
                .append(" && ")
                .append("reg add \"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\" /v Path /t REG_EXPAND_SZ /d \"%Path%;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python36;\" /f");
        long start = System.currentTimeMillis();
        final boolean r = ProcessUtil.execShellOnRemoteWindows(command.toString());
        Log.low.info("安装PYTHON环境耗时(ms):" + (System.currentTimeMillis() - start));
        if (!r) {
            throw new ServiceException("安装PYTHON环境失败");
        }
    }

    /**
     * 在远程WINDOWS机器上安装JDK
     *
     * @param session session
     * @param sftp    sftp
     * @throws FileNotFoundException FileNotFoundException
     * @throws SftpException         SftpException
     */
    private void installJdkOnRemoteWindows(Session session, ChannelSftp sftp) throws IOException, SftpException {
        String jdkPath = "./baseEvn/jdk-8u102-windows-x64.exe";
        String toolPath = "./baseEvn/ReadRegeditAgain.exe";
        String jdkName = "jdk-8u102-windows-x64.exe";
        String toolName = "ReadRegeditAgain.exe";
        File file = new File(jdkPath);
        if (!file.exists()) {
            throw new ServiceException("baseEvn目录下不存在jdk-8u102-windows-x64.exe文件");
        }
        if (!new File(toolPath).exists()) {
            throw new ServiceException("baseEvn目录下不存在ReadRegeditAgain.exe文件");
        }
        String remotePath = "MAINTAIN_TEMP/baseEvn";
        ProcessUtil.updateSingleFileToRemote(sftp, jdkPath, jdkName, remotePath, remotePath);
        ProcessUtil.updateSingleFileToRemote(sftp, toolPath, toolName, remotePath, remotePath);
        StringBuilder command = new StringBuilder();
        command.append("xcopy ")
                .append(remotePath.replaceAll("/", "\\\\"))
                .append(" D:\\MAINTAIN_TEMP\\baseEvn /Y /E /I")
                .append(" && ")
                .append("D:\\MAINTAIN_TEMP\\baseEvn\\jdk-8u102-windows-x64.exe /q")
                .append(" && ")
                .append("reg add \"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\" /v JAVA_HOME /t REG_SZ /d \"C:\\Program Files\\Java\\jdk1.8.0_102\" /f")
                .append(" && ")
                .append("reg add \"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\" /v Path /t REG_EXPAND_SZ /d \"%Path%;C:\\Program Files\\Java\\jdk1.8.0_102\\bin;\" /f");
        long start = System.currentTimeMillis();
        final boolean r = ProcessUtil.execShellOnRemoteWindows(command.toString());
        Log.low.info("安装JAVA环境耗时(ms):" + (System.currentTimeMillis() - start));
        if (!r) {
            throw new ServiceException("安装JAVA环境失败");
        }
    }

    /**
     * 在远程WINDOWS机器上安装7Z
     *
     * @param session
     * @param sftp
     * @throws FileNotFoundException
     * @throws SftpException
     */
    private void install7ZOnRemoteWindows(Session session, ChannelSftp sftp) throws IOException, SftpException {
        String tool7zPath = "./baseEvn/7z1604-x64.exe";
        String fileName = "7z1604-x64.exe";
        File file = new File(tool7zPath);
        if (!file.exists()) {
            throw new ServiceException("baseEvn目录下不存在7z1604-x64.exe文件");
        }
        String remotePath = "MAINTAIN_TEMP/baseEvn";
        ProcessUtil.updateSingleFileToRemote(sftp, tool7zPath, fileName, remotePath, remotePath);
        StringBuilder command = new StringBuilder();
        command.append("xcopy ")
                .append(remotePath.replaceAll("/", "\\\\"))
                .append(" D:\\MAINTAIN_TEMP\\baseEvn /Y /E /I")
                .append(" && ")
                .append("D:\\MAINTAIN_TEMP\\baseEvn\\7z1604-x64.exe /S");
        long start = System.currentTimeMillis();
//        boolean r = ExecUtil.execCmdOnRemoteWindows(session, command.toString());
//        ProcessUtil.execShell(command.toString());
        boolean r = ProcessUtil.execShellOnRemoteWindows(command.toString());
        Log.low.info("安装7Z环境耗时(ms):" + (System.currentTimeMillis() - start));
        if (!r) {
            throw new ServiceException("安装7Z环境失败");
        }
    }


    /**
     * 检查基础的expect, JAVA, python, 7z环境是否安装完成
     *
     * @param agentVo
     * @return
     * @throws IOException
     */
    private boolean[] checkBaseEnv(AgentVo agentVo, WebSocket webSocket) {
        boolean[] result = new boolean[5];
        result[0] = false; //yum
        result[1] = false; //expect
        result[2] = false; //java
        result[3] = false; //python
        result[4] = false; //7z

        try {
            String resultString;
            if (LINUX.equals(agentVo.getOs())) {
                result[0] = ProcessUtil.valiYml();
                //重建SSH连接
                ProcessUtil.destroy();
                Session session = BaseWebSocket.getSession(agentVo);
                ProcessUtil.initChannelShell(session, agentVo, 150000L);
                result[1] = ProcessUtil.valiExpect();
                String command = "cat /etc/profile";
                if (agentVo.getIp() != null && agentVo.getIp().equals(this.maintainServerIp)) {
                    resultString = ProcessUtil.execLocalCommand(command, agentVo.getOs());
                } else {
                    //重建SSH连接
                    ProcessUtil.destroy();
                    session = BaseWebSocket.getSession(agentVo);
                    ProcessUtil.initChannelShell(session, agentVo, 150000L);
                    resultString = ProcessUtil.execShellGetResult(command);
                }
                result[4] = resultString.contains(LINUX_7Z_PATH);
            } else {
                result[0] = true;
                result[1] = true;
                String command = "reg query \"hklm\\system\\currentControlSet\\control\\session manager\\environment\" /v Path";
                if (agentVo.getIp() != null && agentVo.getIp().equals(this.maintainServerIp)) {
                    resultString = ProcessUtil.execLocalCommand(command, agentVo.getOs());
                    result[4] = new File(WINDOWS_7Z_PATH).exists();
                } else {
                    command = "reg query \"hklm\\system\\currentControlSet\\control\\session Manager\\environment\" /v Path";
                    resultString = ProcessUtil.execShellGetResult(command);
                    command = "if exist \"C:\\Program Files\\7-Zip\\7z.exe\" (echo exist) else (echo nonono)";
                    String result7 = ProcessUtil.execShellGetResult(command);
                    result[4] = result7.contains("exist");
                }
            }
            if (StringUtils.isNotEmpty(resultString)) {
                resultString = resultString.toLowerCase();
                result[2] = resultString.contains("java") || resultString.contains("jdk");
                result[3] = resultString.contains("anaconda3") || resultString.contains("python3") || resultString.contains("python");
            }
        } catch (Exception e) {
            Log.high.error("基础环境检查异常", e);
            throw new ServiceException("基础环境检查异常");
        }
        return result;
    }

    /**
     * 远程部署
     *
     * @param agentVo  agentVo
     * @param map      map
     * @param tempFile tempFile
     */
    private void remoteDeploy(AgentVo agentVo, Map<String, Object> map, File tempFile, WebSocket webSocket) {
        final Session sshSession = getSession(agentVo);
        String name = map.get("name").toString();
        try {
            ProcessUtil.initChannelShell(sshSession, agentVo, 300000L);
            //关闭agent
            softwareService.iceRequest(agentVo.getIp(), IceFlag.CLOSE_AGENT, null);
            ProcessUtil.closeAgent(agentVo);
            sentText(webSocket, "通过SSH连接" + agentVo.getIp() + "服务器");
            sentText(webSocket, "连接" + agentVo.getIp() + "服务器成功");
            if (agentVo.getOs().equals(LINUX)) {
                sentText(webSocket, "远程服务器操作系统类型为Linux，开始上传程序");
                uploadLinux(map, sshSession, agentVo);
                sentText(webSocket, "上传完毕，准备执行启动脚本");
                String command = this.autoDeployCmdInLinux(map.get("name").toString());
                sentText(webSocket, "执行" + agentVo.getIp() + "上" + name + "的启动命令：" + command);
                ProcessUtil.execShell(command);
                sentText(webSocket, "启动命令执行成功");
            } else if (agentVo.getOs().equals(WINDOWS)) {
                sentText(webSocket, "远程服务器操作系统类型为Windows，开始上传程序");
                map.put("autoWatchDog", agentVo.getAutoWatchDog());
                String remoteTempPath = MAINTAIN_WINDOWS_TEMP + name;
                map.put("remoteTempPath", remoteTempPath);
                ProcessUtil.uploadFileToRemoteWindows(map, sshSession);
                sentText(webSocket, "上传完毕");
                remoteDeployWithoutGuardInWindows(webSocket, sshSession, map);
            }
        } catch (JSchException e) {
            String msg = e.getMessage();
            Log.high.error(msg, e);
            msg = "无法连接远程服务器，请确保SSH环境能正常访问！！！ " + msg;
            sentText(webSocket, msg);
            throw new ServiceException(msg);
        } catch (Exception e) {
            String msg = e.getMessage();
            Log.high.error(msg, e);
            msg = "远程部署异常！！！" + msg;
            sentText(webSocket, msg);
            throw new ServiceException(msg);
        } finally {
            try {
                ProcessUtil.destroyChannelShell(agentVo);
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 在远程Windows机器上部署不被守护的程序
     *
     * @param session session
     * @param map     map
     * @throws JSchException        JSchException
     * @throws InterruptedException InterruptedException
     * @throws SftpException        SftpException
     * @throws IOException          IOException
     */
    private void remoteDeployWithoutGuardInWindows(WebSocket socket, Session session, Map<String, Object> map) throws JSchException, InterruptedException, SftpException, IOException {
        String remoteTempPath = map.get("remoteTempPath").toString();
        String remotePath = map.get("remotePath").toString();
        String name = map.get("name").toString();
        //杀进程
        if (map.get("pid") != null) {
            updatePid(map);
        }
        //拷贝文件到目标目录
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("xcopy ").append(remoteTempPath).append(" ").append(remotePath).append(" /Y /I /E ");
        //替换文件
        ProcessUtil.execShell(stringBuffer.toString());
        String command = autoDeployCmdInWindows("MaintainAgent");
        Log.low.info("remoteDeployCmd:" + command);
        //远程执行命令

        String removeServiceCommand = "D:\\dist\\" + name + "\\deploy\\nssm\\win64\\nssm.exe remove MaintainAgent confirm";
        ProcessUtil.execShell(removeServiceCommand);
        sentText(socket, "执行卸载原服务命令：" + removeServiceCommand);
        //创建服务脚本
        String createServiceCommand = "D:\\dist\\" + name + "\\deploy\\nssm\\win64\\nssm.exe install MaintainAgent D:\\dist\\" + name + "\\deploy\\start.bat";
        ProcessUtil.execShell(createServiceCommand);
        sentText(socket, "执行创建系统服务命令：" + createServiceCommand);
        ProcessUtil.execShell(command);
        sentText(socket, "执行启动命令：" + command);
        sentText(socket, "启动命令执行成功，部署结束");
    }

    private void updatePid(Map<String, Object> map) {
        if (!"maintainagent".equalsIgnoreCase(map.get("name").toString())) {
            SoftwareVo softwareVo = new SoftwareVo();
            softwareVo.setName(map.get("name").toString());
            softwareVo.setHost(map.get("ip").toString());
            softwareVo.setPid(-1);
            softwareService.updateSoftwareInfo(softwareVo, false);
        } else {
            AgentVo agentVo = new AgentVo();
            agentVo.setPid(-1);
            agentVo.setIp(map.get("ip").toString());
            agentService.updateAgent(agentVo);
        }
    }

    /**
     * 本地部署程序
     *
     * @param map  map
     * @param osId osId
     */
    private void localDeploy(Map<String, Object> map, Integer osId, WebSocket session) {
        String tempPath = map.get("tempPath").toString();
        File tempFile = new File(tempPath);
        if (osId.equals(LINUX)) {
            ProcessUtil.execLocalCommand("jps | grep MaintainAgent | awk '{print $1}' | xargs kill -9");
            sentText(session, "关闭 MaintainAgent");
        } else if (osId.equals(WINDOWS)) {
            ProcessUtil.execLocalCommand("net stop MaintainAgent");
            sentText(session, "关闭 MaintainAgent");
        }
        try {
            if (osId.equals(LINUX)) {
                sentText(session, "本地服务器操作系统类型为Linux，开始部署程序");
                localDeployInLinux(map, tempFile, session);
//                ProcessUtil.execLocalCommand("cd /dist/MaintainAgent/conf/script/linux && sh firewall.sh");
            } else if (osId.equals(WINDOWS)) {
                sentText(session, "本地服务器操作系统类型为Windows，开始部署程序");
                localDeployInWindows(map, tempFile, session);
//                ProcessUtil.execLocalCommand("D:\\dist\\MaintainAgent\\conf\\script\\windows\\firewall.bat");
            }
            sentText(session, "启动命令执行成功，部署结束");
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            throw new ServiceException(e);
        }
    }

    /**
     * 在本地Linux机器上部署程序
     *
     * @param map      map
     * @param tempFile tempFile
     * @throws IOException IOException
     */
    private void localDeployInLinux(Map<String, Object> map, File tempFile, WebSocket session) throws IOException {
        String command;
        String name = map.get("name").toString();
        if (map.get("pid") != null) {
            updatePid(map);
        }
        File resultFile = new File(map.get("remotePath").toString());

        sentText(session, "开始拷贝" + name + "资源");
        FileUtils.copyDirectory(tempFile, resultFile);
        if (tempFile.exists()) {
            deleteLogAndSelfPidFile(tempFile);
        }
        if (tempFile.exists()) {
            deleteLogAndSelfPidFile(tempFile);
        }
        sentText(session, "拷贝" + name + "资源完毕");
        command = this.autoDeployCmdInLinux(name);
        sentText(session, "执行启动命令：" + command);
        ProcessUtil.execLocalCommand(command);
    }


    /**
     * 部署程序到本地Windows机器
     *
     * @param map      map
     * @param tempFile tempFile
     * @throws IOException IOException
     */
    private void localDeployInWindows(Map<String, Object> map, File tempFile, WebSocket session) throws IOException {
        //手动杀进程
        Object name = map.get("name");
        if (map.get("pid") != null) {
            updatePid(map);
        }

        File resultFile = new File(map.get("remotePath").toString());

        sentText(session, "开始拷贝" + name + "资源");
        FileUtils.copyDirectory(tempFile, resultFile);
        if (tempFile.exists()) {
            deleteLogAndSelfPidFile(tempFile);
        }
        sentText(session, "拷贝" + name + "资源完毕");
        //卸载服务
        String removeServiceCommand = "D:\\dist\\" + name + "\\deploy\\nssm\\win64\\nssm.exe remove " + name + " confirm";
        ProcessUtil.execLocalCommand(removeServiceCommand);
        sentText(session, "执行卸载原服务命令：" + removeServiceCommand);
        //执行部署命令
        String createServiceCommand = "D:\\dist\\" + name + "\\deploy\\nssm\\win64\\nssm.exe install " + name + " D:\\dist\\" + name + "\\deploy\\start.bat";
        ProcessUtil.execLocalCommand(createServiceCommand);
        sentText(session, "执行创建系统服务命令：" + createServiceCommand);
        String command = autoDeployCmdInWindows("MaintainAgent");
        sentText(session, "执行启动命令：" + command);
        ProcessUtil.execLocalCommand(command);
    }


    private void uploadLinux(Map<String, Object> map, Session session, AgentVo agentVo) throws Exception {
        String remoteUploadPath = map.get("remoteUploadPath").toString();
        String remotePath = map.get("remotePath").toString();
        String tempPath = map.get("tempPath").toString();
        String name = map.get("name").toString();
        ProcessUtil.initChannelShell(session, agentVo, 120000L);
        ProcessUtil.execShell("cd /dist && rm -rf MaintainAgent");
        //休眠一秒，删除原文件夹的命令是异步的，必须保证已经删除了，否则会异常
        Thread.sleep(500);
        ChannelSftp sftp = ProcessUtil.openChannelSftp(session);
        //上传新文件
        try {
            // 检查Linux服务器是否有dist目录，没则创建
            sftp.cd(MAINTAIN_LINUX_REMOTE);
        } catch (Exception e) {
            ProcessUtil.execShell("mkdir " + MAINTAIN_LINUX_REMOTE);
            sftp.cd(MAINTAIN_LINUX_REMOTE);
        }
        try {
            // 检查Linux服务器是否有程序目录，没则在dist目录下创建
            sftp.mkdir(name);
        } catch (Exception e) {
            Log.low.error(name + "文件夹已存在");
        }
        try {
            // 检查Linux服务器是否有程序目录，没则在dist目录下创建
            ProcessUtil.cdBack(sftp, MAINTAIN_LINUX_REMOTE);
            sftp.cd(remoteUploadPath);
        } catch (Exception e) {
            sftp.cd("/home/" + agentVo.getName());
            sftp.mkdir(name);
        }
        ProcessUtil.uploadFileToRemoteLinux(sftp, tempPath, remoteUploadPath);
        if (sftp.isConnected()) {

            sftp.disconnect();
        }
        if (!remoteUploadPath.equals(remotePath)) {
            try {
                ProcessUtil.execShell("mv -f " + remoteUploadPath + " " + remotePath);
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
        }
    }


    private String getIceServerPort() {
        if (StringUtils.isEmpty(iceServerPort)) {
            iceServerPort = IcepoolUtil.extractIcePort();
        }
        return iceServerPort;
    }
}