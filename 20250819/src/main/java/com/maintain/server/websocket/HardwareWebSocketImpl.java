package com.maintain.server.websocket;

import com.alibaba.fastjson.TypeReference;
import com.maintain.server.Constants;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.HardwareService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.HardwareVo;
import com.maintain.server.vo.ResponseVo;
import org.apache.commons.lang.StringUtils;
import org.java_websocket.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2019-02-28
 */
@Service("hardwareWebSocketService")
public class HardwareWebSocketImpl extends BaseWebSocket implements Constants {

    @Autowired
    private AgentService agentService;

    @Autowired
    private SoftwareService softwareService;


    @Autowired
    private HardwareService hardwareService;

    private ReentrantLock lock = new ReentrantLock();

    @Override
    @OperateRecordAnnotation(name = "开启%s上的防火墙", module = "hardware")
    public String openSoftware(String param, WebSocket webSocket) throws ServiceException {
        Map<String, String> map = JsonUtil.parseObject(param, new TypeReference<Map<String, String>>() {
        });
        String ip = map.get("ip");
        String operateType = map.get("operateType");
        if (StringUtils.isEmpty(ip)) {
            throw new ServiceException("未知IP");
        }
        AgentCriteria criteria = new AgentCriteria();
        criteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(criteria);
        if (agentVo == null) {
            throw new ServiceException("未知Agent");
        }
        if (operateType == null) {
            operateType = "1";
        }
        webSocket.send("请求Agent调用启动防火墙脚本");
        String result = softwareService.iceRequest(ip, IceFlag.OPERATE_FIREWALL, operateType);
        if (StringUtils.isNotEmpty(result)) {
            ResponseVo responseVo = JsonUtil.parseObject(result, ResponseVo.class);
            if (responseVo != null) {
                if (responseVo.getCode().equals(ResponseType.SUCCESS.getCode())) {
                    Integer osId = agentVo.getOs();
                    if (LINUX.equals(osId)) {
                        webSocket.send("请求成功，获取防火墙策略...");
                        result = softwareService.iceRequest(ip, IceFlag.OPERATE_FIREWALL, "2");
                        responseVo = JsonUtil.parseObject(result, ResponseVo.class);
                        if (responseVo != null && responseVo.getData() != null) {
                            Object data = responseVo.getData();
                            if (data != null) {
                                String dataStr = data.toString();
                                responseVo.setData(dataStr.replaceAll("\\n", "。"));
                            }
                            webSocket.send(responseVo.getData().toString());
                        }
                    } else {
                        webSocket.send("请求成功");
                    }
                    HardwareCriteria hardwareCriteria = new HardwareCriteria();
                    hardwareCriteria.setIp(ip);
                    HardwareVo hardwareVo = hardwareService.getHardwareInfo(hardwareCriteria);
                    hardwareVo.setFirewallStatus(1);
                    hardwareService.addHardwareInfo(hardwareVo);
                } else {
                    throw new ServiceException("请求失败，" + responseVo.getMsg());
                }
            }
        } else {
            throw new ServiceException("请求失败，");
        }
        webSocket.send("结束");
        return null;
    }

    @Override
    public boolean tryLock() throws ServiceException {
        return super.tryLock(lock);
    }

    @Override
    public boolean unLock() throws ServiceException {
        return super.unLock(lock);
    }
}