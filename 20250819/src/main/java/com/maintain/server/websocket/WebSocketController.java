package com.maintain.server.websocket;

import com.common.log.Log;
import com.maintain.server.Constants;
import com.maintain.server.enums.OperateTypeEnum;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.RequestRecordMapper;
import com.maintain.server.mapper.UserMapper;
import com.maintain.server.service.CommonConfigService;

import com.maintain.server.service.WebSocketService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.PermissionVo;
import com.maintain.server.vo.RequestRecordVo;
import com.maintain.server.vo.UserVo;
import com.maintain.server.vo.WebSocketRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.DefaultSSLWebSocketServerFactory;
import org.java_websocket.server.DefaultWebSocketServerFactory;
import org.java_websocket.server.WebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.security.KeyStore;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-01-21
 */
@Slf4j
public class WebSocketController extends WebSocketServer implements ApplicationListener<ContextRefreshedEvent>, Constants {

    @Autowired
    private ApplicationContext application;


    @Autowired
    private RequestRecordMapper requestRecordMapper;

    @Autowired
    private UserMapper userMapper;

    public WebSocketController(int port) {
        super(new InetSocketAddress(port), 5);
    }

    @Override
    public void onOpen(WebSocket conn, ClientHandshake handshake) {
        /*long start = System.currentTimeMillis();
        System.out.println("websocket连接打开了 " + start);
        Log.low.info("websocket连接打开了 " + start);*/
    }

    @Override
    public void onClose(WebSocket conn, int code, String reason, boolean remote) {
        /*long end = System.currentTimeMillis();
        System.out.println("websocket连接关闭了 " + end);
        Log.low.info("websocket连接关闭了 " + end);*/
    }

    @Override
    public void onMessage(WebSocket conn, String message) {
        if (CLOSE.equals(message)) {
            if (conn.isOpen()) {
                conn.close();
                return;
            } else {
                return;
            }
        }
        WebSocketService webSocketService = null;

        CommonConfigService commonConfigService = null;
        boolean lck = false;
        try {
            //处理心跳
            if ("ping".equals(message)) {
                conn.send("pong");
                return;
            }
            WebSocketRequestVo webSocketRequestVo = JsonUtil.parseObject(message, WebSocketRequestVo.class);
            UserVo user = webSocketRequestVo.getUser();
            if (user == null) {
                conn.closeConnection(ResponseType.REJECT.getCode(), CLOSE);
                return;
            }
            final List<PermissionVo> permissions = userMapper.getAllPermissionByUserId(user.getId());
            String url = "/" + webSocketRequestVo.getService() + "Websocket/" + webSocketRequestVo.getOperateType();
            boolean auth = false;
            //暂时放过，测试
            //boolean auth = true;
            //Log.high.info("暂时放过，测试!!!");

            for (PermissionVo permission : permissions) {
                if (permission.getUrl().contains(url)) {
                    auth = true;
                }
            }
            String service = webSocketRequestVo.getService();
            if (!auth) {
                conn.send(ResponseType.NO_PERMISSION.getMsg());
                conn.close();
                return;
            }
            if (StringUtils.isEmpty(service)) {
                conn.closeConnection(ResponseType.REJECT.getCode(), CLOSE);
                return;
            }
            conn.setAttachment(user);

            webSocketService = application.getBean(service.concat("WebSocketService"), WebSocketService.class);



            commonConfigService = application.getBean("commonconfigService", CommonConfigService.class);

            if (lck = webSocketService.tryLock()) {
                operate(webSocketRequestVo, webSocketService, conn,  commonConfigService);
                if (webSocketRequestVo.getOperateType() != 4) {
                    conn.send(SUCCESS);
                    conn.close();
                }
            } else {
                conn.send("其他程序正在操作，请稍后再试");
                conn.send(CLOSE);
                conn.close();
            }
        } catch (Exception e) {
            Log.high.error("websocket operate exception", e);
            handleOperateException(e, conn);
            conn.send(CLOSE);
            conn.close();
        } catch (Throwable t) {
            Log.high.error("websocket operate throwable,param:" + message, t);
            conn.send(t.getMessage());
            conn.send(CLOSE);
            conn.close();
        } finally {
            if (webSocketService != null && lck) {
                webSocketService.unLock();
            }
        }

    }

    public void operate(WebSocketRequestVo webSocketRequestVo, WebSocketService webSocketService, WebSocket conn, CommonConfigService commonConfigService) {
        long start = System.currentTimeMillis();
        try {
            String param = webSocketRequestVo.getParam();
            //operateType 从0~6分别对应关闭、开启、重启、更新、部署、分发logstash、修改logstash配置文件
            boolean frontUpgradeResult;
            String getCommonConfigResult;
            switch (webSocketRequestVo.getOperateType()) {
                //下架
                case 0:
                    webSocketService.closeSoftware(param, conn);
                    break;
                //恢复
                case 1:
                    webSocketService.openSoftware(param, conn);
                    break;
                case 2:
                    webSocketService.reloadSoftware(param, conn);
                    break;
                case 3:
                    webSocketService.updateSoftware(param, conn);
                    break;
                case 4:
                    Boolean result = webSocketService.autoDeploy(param, conn);
                    if (result) {
                        conn.send(SUCCESS);
                    }
                    break;
                case 7:
                    webSocketService.setSyschroTime(param, conn);
                    break;
                case 8:
                    frontUpgradeResult = webSocketService.executeUpgrade(param, conn);
                    if (!frontUpgradeResult) {
                        conn.send(CLOSE);
                    }
                    break;
                case 9:
                    getCommonConfigResult = commonConfigService.uploadCommonConfig(param, conn);
                    if (StringUtils.isEmpty(getCommonConfigResult)) {
                        conn.send(CLOSE);
                    }
                    break;
                case 10:
                    conn.send(commonConfigService.closePlatform(conn));
                    break;
                case 11:
                    conn.send(commonConfigService.openPlatform(conn));
                    break;
                default:
                    conn.send("未知请求");
                    conn.send(CLOSE);
                    conn.close();
                    return;
            }
        } finally {
            RequestRecordVo vo = new RequestRecordVo();
            vo.setStartTime(new Date());
            vo.setEndTime(new Date());
            vo.setTimeSpent((System.currentTimeMillis() - start));
            vo.setCreateTime(new Date());
            vo.setModule(webSocketRequestVo.getService() + "WebSocket");
            vo.setUri(OperateTypeEnum.byType(webSocketRequestVo.getOperateType()).getOperate());
            requestRecordMapper.addRequestRecord(vo);
        }
    }

    @Override
    public void onError(WebSocket conn, Exception ex) {
        Log.high.error("websocket服务异常", ex);
    }

    @Override
    public void onStart() {

    }

    private void handleOperateException(Exception e, WebSocket conn) {
        if (e instanceof ServiceException && "连接远程服务器异常".equals(e.getMessage())) {
            conn.send("连接远程服务器异常，请按照以下方法进行修复后重试：首先，确认用户名和密码正确 ; 其次，Windows - > 确认执行了openssh脚本 ; Linux - > 确认提供的用户名和密码正确后进入远程服务器，打开 /etc/ssh/sshd_config文件，找到“#UserDNS yes”这行，取消该行前面的注释，并将yes修改为no；找到“GSSAPIAuthentication yes”配置，并将yes修改为no，保存文件，然后重启sshd服务");
        } else {
            conn.send(e.getMessage());
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        this.application = contextRefreshedEvent.getApplicationContext();
        try {
            Thread.currentThread().setName("WebSocket-Thread");
            this.setWebSocketFactory(new DefaultWebSocketServerFactory());
            this.setConnectionLostTimeout(NETTY_DEFAULT_CONNECT_TIMEOUT);
            this.start();
        } catch (Exception e) {
            Log.high.error("初始化websocket服务异常", e);
        }
    }
}