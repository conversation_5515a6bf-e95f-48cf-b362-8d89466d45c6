package com.maintain.server.websocket;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.common.log.Log;
import com.maintain.server.Constants;
import com.maintain.server.MaintainServerApplication;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.AutoDeployTaskCriteria;
import com.maintain.server.criteria.ModifyConfigCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.AutoDeployService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.service.WebSocketService;
import com.maintain.server.type.*;
import com.maintain.server.utils.FileUtil;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.utils.ReqRespHelper;
import com.maintain.server.vo.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.java_websocket.WebSocket;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-01-25
 */
@Service("softwareWebSocketService")
public class SoftwareWebSocketImpl extends BaseWebSocket implements WebSocketService {

    @Autowired
    private AgentService agentService;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private MaintainServerApplication application;

    @Autowired
    private AutoDeployService autoDeployService;

    @Value("${first-open}")
    private String firstOpenSoftware;

    //公平锁
    private ReentrantLock lock = new ReentrantLock(true);

    @Override
    public String openSoftware(String param, WebSocket session) throws ServiceException {
        Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
        });
        final Object host = map.get("host");
        final Object software = map.get("software");
        final Object fuzzy = map.get("fuzzy");
        final Object status = map.get("status");
        String stautsStr = null;
        if (status != null) {
            stautsStr = status.toString();
        }
        String fuzzyStr = null;
        if (fuzzy != null) {
            fuzzyStr = fuzzy.toString();
        }
        if (host != null) {
            return openAllSoftware(host.toString(), session);
        } else if (software != null) {
            return openQuerySoftware(software.toString(), fuzzyStr ,stautsStr, session);
        } else {
            map.put("operateType", 1);
            return ((SoftwareWebSocketImpl) AopContext.currentProxy()).operateSoftware(map, session);
            //return operateSoftware(map, session);
        }
    }

    @Override
    public String closeSoftware(String param, WebSocket session) throws ServiceException {
        Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
        });
        final Object host = map.get("host");
        final Object software = map.get("software");
        final Object fuzzy = map.get("fuzzy");
        final Object status = map.get("status");
        String stautsStr = null;
        if (status != null) {
            stautsStr = status.toString();
        }
        String fuzzyStr = null;
        if (fuzzy != null) {
            fuzzyStr = fuzzy.toString();
        }
        if (host != null) {
            return closeAllSoftware(host.toString(), session);
        } else if (software != null) {
            return closeQuerySoftware(software.toString(), fuzzyStr,stautsStr, session);
        } else {
            map.put("operateType", 0);
            //return operateSoftware(map, session);
            return ((SoftwareWebSocketImpl) AopContext.currentProxy()).operateSoftware(map, session);
        }
    }

    @Override
    public String reloadSoftware(String param, WebSocket session) throws ServiceException {
        Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
        });
        map.put("operateType", 2);
        //return operateSoftware(map, session);
        return ((SoftwareWebSocketImpl) AopContext.currentProxy()).operateSoftware(map, session);
    }

    private Boolean prepareDeployPackage(WebSocket webSocket){
        DeployRecordVo failureRecord = autoDeployService.getDeployRecordVo(new DeployRecordVo());
        if (failureRecord != null && failureRecord.getStatus() >= 1) {
            CompareResultResVo compareResultResVo = JSONObject.parseObject(failureRecord.getRemark1(), CompareResultResVo.class);
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("已获取比较结果!", compareResultResVo));
            return true;
        }
        //解压部署包
        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("正在解压部署包,请耐心等待......",""));
        Boolean result = autoDeployService.unzipPackage();
        if (!result) {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("部署包解压失败!",""));
            return false;
        } else {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("部署包解压完成!",""));
            //校验部署包格式
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("校验部署包格式",""));
            CompareResultResVo compareResultResVo = null;
            try {
                UnzipResult unzipResult = autoDeployService.checkUnzipPackageFormat();
                if (!unzipResult.getResult()) {
                    sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(unzipResult.getMsg(),""));
                } else {
                    sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("部署包格式校验通过!",""));
                    //比较部署任务
                    sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("比较部署任务结果",""));
                    CompareResultVo compareResultVo = autoDeployService.compareDeployTask();
                    compareResultResVo = new CompareResultResVo();
                    compareResultResVo.setStep(1);
                    compareResultResVo.setList(compareResultVo);
                    if (compareResultVo == null) {
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("比较部署任务失败!",""));
                        return false;
                    } else {
                        sentText(webSocket,  ReqRespHelper.writeSuccessWithMsgAndContent("比较部署任务成功!",compareResultResVo));
                    }
                }
            } catch (Exception e) {
                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(e.toString(),""));
                return false;
            }
            this.lockDeployTask(AutoDeployRecordType.STEP1.getStatus(),JsonUtil.toJsonString(compareResultResVo),null,null,null,null);
            autoDeployService.lockDeployTask(autoDeployService.getRecordVo());
            return true;
        }
    }

    private Boolean getDeployTask(Map<String,Object> deploy,WebSocket webSocket){
        DeployRecordVo failureRecord = autoDeployService.getDeployRecordVo(new DeployRecordVo());
        if (failureRecord != null && failureRecord.getStatus() == 2) {
            DeployMapResVo deployMapResVo = JSONObject.parseObject(failureRecord.getRemark2(), DeployMapResVo.class);
            sentText(webSocket,  ReqRespHelper.writeSuccessWithMsgAndContent("已获取部署任务!",deployMapResVo));
            return true;
        }
        //获取部署任务
        sentText(webSocket,  ReqRespHelper.writeSuccessWithMsgAndContent("部署任务列表",""));
        Map<String, Object> deployMap;
        DeployMapResVo deployMapResVo;
        try {
            deployMap = autoDeployService.deployTask(deploy);
            deployMapResVo = new DeployMapResVo();
            deployMapResVo.setStep(2);
            deployMapResVo.setList(deployMap);
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("获取部署任务成功!",deployMapResVo));
        } catch (Exception e) {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("获取部署任务失败!",e));
            return false;
        }
        this.lockDeployTask(AutoDeployRecordType.STEP2.getStatus(),null,JsonUtil.toJsonString(deployMapResVo),null,null,null);
        return true;
    }

    private Boolean getCommonConfig(WebSocket webSocket){
        DeployRecordVo failureRecord = autoDeployService.getDeployRecordVo(new DeployRecordVo());
        if (failureRecord != null && failureRecord.getStatus() >= 3) {
            CommonConfigResultResVo commonConfigResultResVo = JSONObject.parseObject(failureRecord.getRemark3(), CommonConfigResultResVo.class);
            sentText(webSocket,  ReqRespHelper.writeSuccessWithMsgAndContent("已修改公共配置!",commonConfigResultResVo));
            return true;
        }
        //获取公共配置
        CommonConfigResultVo commonConfigResultVo = null;
        CommonConfigResultResVo commonConfigResultResVo = null;
        try {
            commonConfigResultVo = autoDeployService.getCommonConfig();
            if (commonConfigResultVo == null) {
                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("要上传的公共配置文件没有配置或不存在!",""));
                return false;
            } else {
                commonConfigResultResVo = new CommonConfigResultResVo();
                commonConfigResultResVo.setStep(3);
                commonConfigResultResVo.setList(commonConfigResultVo);
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("公共配置修改成功!",commonConfigResultResVo));
            }
        } catch (Exception e){
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("公共配置修改异常!",""));
            return false;
        }
        this.lockDeployTask(AutoDeployRecordType.STEP3.getStatus(),null,null,JsonUtil.toJsonString(commonConfigResultResVo),null,null);
        return true;
    }

    private void lockDeployTask( Integer type, String remark1,String remark2,String remark3,String remark4,String remark5){
        DeployRecordVo deployRecordVo = new DeployRecordVo();
        deployRecordVo.setRemark1(remark1);
        deployRecordVo.setRemark2(remark2);
        deployRecordVo.setRemark3(remark3);
        deployRecordVo.setRemark4(remark4);
        deployRecordVo.setRemark5(remark5);
        deployRecordVo.setStatus(type);
        autoDeployService.lockDeployTask(deployRecordVo);
    }

    @Override
    @OperateRecordAnnotation(name = "自动部署")
    public Boolean autoDeploy(String param, WebSocket webSocket) throws ServiceException {
        Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
        });
        Integer step;
        if (map != null) {
            final Object s = map.get("step");
            step = (Integer) s;
        } else {
            webSocket.send("step入参为空");
            return false;
        }
        switch (step){
            case 1:
                return prepareDeployPackage(webSocket);
            case 2:
                Map<String,Object> deploy;
                if (map == null) {
                    webSocket.send("入参为空");
                    return false;
                } else {
                    final Object d = map.get("deploy");
                    if (d == null) {
                        webSocket.send("deploy入参为空");
                        return false;
                    } else {
                        deploy = (Map<String,Object>) d;
                    }
                }
                return getDeployTask(deploy, webSocket);

            case 3:
               return getCommonConfig(webSocket);
            case 4:
                String content = "";
                String upgradeType = "";
                if (map == null) {
                    webSocket.send("入参为空");
                    return false;
                } else {
                    final Object c = map.get("content");
                    content = (String) c;
                    final Object u = map.get("upgradeType");
                    if (u == null) {
                        webSocket.send("upgradeType入参为空");
                        return false;
                    } else {
                        upgradeType = (String) u;
                    }
                }
                return startDeploy(content, upgradeType, webSocket);
            case 5:
                return getModifyConfigList(webSocket);
            case 6:
                return finishDeploy(webSocket);
            default:
                webSocket.send("非法入参");
                return false;
        }
    }

    private Boolean finishDeploy(WebSocket webSocket) {
        DeployRecordVo deployRecordVo = new DeployRecordVo();
        deployRecordVo.setStatus(0);
        deployRecordVo.setRemark1("");
        deployRecordVo.setRemark2("");
        deployRecordVo.setRemark3("");
        deployRecordVo.setRemark4("");
        deployRecordVo.setRemark5("");
        deployRecordVo.setConfigPath("");
        deployRecordVo.setProduct("");
        deployRecordVo.setProgramPaths("");
        deployRecordVo.setFullPackage("");
        deployRecordVo.setIsModifyCommonConfig(Integer.valueOf(0));
        autoDeployService.lockDeployTask(deployRecordVo);
        sentText(webSocket,  ReqRespHelper.writeSuccessWithMsgAndContent("完成一键部署！！！",""));
        return true;
    }

    private Boolean getModifyConfigList(WebSocket webSocket) {
        DeployRecordVo failureRecord = autoDeployService.getDeployRecordVo(new DeployRecordVo());
        if (failureRecord != null && failureRecord.getStatus() >= 5) {
            sentText(webSocket,  ReqRespHelper.writeSuccessWithMsgAndContent("已获取程序配置列表!",failureRecord.getRemark5()));
            return true;
        }
        try {
            String packageVersion = autoDeployService.getDeployRecordVo(new DeployRecordVo()).getVersion();
            ModifyConfigResponseVo modifyConfigResponseVo = new ModifyConfigResponseVo();
            List<ModifyConfigVo> modifys = softwareService.queryModifyConfig(new ModifyConfigCriteria());
            Map<String, List<ModifyConfigVo>> collect = modifys.stream().collect(Collectors.groupingBy(ModifyConfigVo::getProgramName));
            AgentCriteria criteria = new AgentCriteria();
            List<AgentVo> agentList = agentService.getAgentList(criteria);
            Map<String, Integer> ipToIdMap = agentList.stream().collect(Collectors.toMap(AgentVo::getIp, AgentVo::getId));
            List<ConfigResVo> configResVoList= new LinkedList<>();
            for (Map.Entry<String,Integer> ipToId: ipToIdMap.entrySet()) {
                SoftwareCriteria softwareCriteria = new SoftwareCriteria();
                softwareCriteria.setServerId(ipToId.getValue());
                List<SoftwareVo> softwareVos = softwareService.listAllSoftwares(softwareCriteria);
                if (ListUtil.isEmpty(softwareVos)) {
                    continue;
                }
                List<String> names = softwareVos.stream().map(SoftwareVo::getName).collect(Collectors.toList());
                AgentCriteria agentCriteria = new AgentCriteria();
                agentCriteria.setIp(ipToId.getKey());
                AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
                List<SoftwareConfigVo> softwareConfigVos = new LinkedList<>();
                ConfigResVo configResVo = new ConfigResVo();
                SoftwareConfigVo softwareConfigVo = new SoftwareConfigVo();
                List<ConfigContentVo> configContentVos = new LinkedList<>();
                for (String name : collect.keySet()) {
                    if (names.contains(name)) {
                        List<ModifyConfigVo> modifyConfigVos = collect.get(name);
                        for (ModifyConfigVo m : modifyConfigVos) {
                            String configPath = m.getConfigPath();
                            String configName;
                            String path;
                            if (configPath.contains("\\")) {
                                configPath = configPath.replace("\\","/");
                            }
                            if(configPath.contains("/")){
                                configName = configPath.substring(configPath.lastIndexOf("/") + 1);
                                path = configPath.substring(0 ,configPath.lastIndexOf("/"));
                            } else {
                                configName = configPath;
                                path = "";
                            }
                            String oldContent = softwareService.readFileContent(agentVo, name, path, configName, "true", packageVersion);
                            if (oldContent == null) {
                                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("读取文件失败，请检查Agent连接状态!",""));
                                return false;
                            }
                            String newContent = softwareService.readFileContent(agentVo, name, path, configName, "false", "");
                            if (newContent == null) {
                                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("读取文件失败，请检查Agent连接状态!",""));
                                return false;
                            }
                            ConfigContentVo configContentVo = new ConfigContentVo();
                            configContentVo.setId(3);
                            configContentVo.setLabel(configName);
                            configContentVo.setNewtxt(newContent);
                            configContentVo.setOldtxt(oldContent);
                            configContentVo.setConfPath(configPath);
                            if (newContent.equals(oldContent)) {
                                configContentVo.setIsEqual(true);
                            } else {
                                configContentVo.setIsEqual(false);
                                configContentVos.add(configContentVo);
                            }
                        }
                        if (ListUtil.isNotEmpty(configContentVos)) {
                            softwareConfigVo.setId(2);
                            softwareConfigVo.setLabel(name);
                            softwareConfigVo.setChildren(configContentVos);
                            softwareConfigVos.add(softwareConfigVo);
                        }
                    }
                }
                if (ListUtil.isNotEmpty(softwareConfigVos)) {
                    configResVo.setId(1);
                    configResVo.setLabel(ipToId.getKey());
                    configResVo.setChildren(softwareConfigVos);
                    configResVoList.add(configResVo);
                }
            }
            modifyConfigResponseVo.setStep(5);
            modifyConfigResponseVo.setList(configResVoList);
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("成功获取要修改程序的配置列表",modifyConfigResponseVo));
        } catch (Exception e) {
            Log.high.error(e);
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("要修改的程序配置列表获取失败",""));
            return false;
        }
        this.lockDeployTask(AutoDeployRecordType.STEP5.getStatus(),null,null,null,null,JsonUtil.toJsonString("程序部署结束"));
        return true;
    }

    private Boolean startDeploy(String content,String upgradeType,WebSocket webSocket) throws ServiceException{
        DeployRecordVo failureRecord = autoDeployService.getDeployRecordVo(new DeployRecordVo());
        if (failureRecord != null && failureRecord.getStatus() >= 4) {
            sentText(webSocket,  ReqRespHelper.writeSuccessWithMsgAndContent("已完成程序上传!",failureRecord.getRemark4()));
            return true;
        }
        long start = System.currentTimeMillis();
        Map<String, List<AutoDeployTaskVo>> task;
        Boolean depResult = true;
        try {
            if ("0".equals(upgradeType.toString())) {
                if (!StringUtils.isEmpty(content)) {
                    String commonConfigPath = autoDeployService.getDeployRecordVo(new DeployRecordVo()).getConfigPath();
                    File file = new File(commonConfigPath);
                    if (file.exists()) {
                        byte[] bytes = Base64.getDecoder().decode(content);
                        content = new String(bytes, Charset.forName(Constants.UTF_8)).trim();
                        try {
                            file.delete();
                            FileUtil.writeStringToFile(file,content,"UTF-8",false);
                        }catch (IOException e) {
                            Log.high.error("公共配置文件写入异常",e);
                            throw new ServiceException("公共配置文件写入异常");
                        }
                    }
                }
            } else {
                //TODO  test
                //Map<String, Map<String, Map<String, RoleProgramVo>>> deployTaskMap = autoDeployService.getDeployTaskMap();
                //autoDeployService.prepareDeploy(deployTaskMap);
            }
            autoDeployService.setWebSocket(webSocket);
            AutoDeployTaskCriteria deployTaskCriteria = new AutoDeployTaskCriteria();
            deployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
            this.autoDeployService = application.getApplicationContext().getBean("autoDeployService", AutoDeployService.class);
            List<AutoDeployTaskVo> autoDeployTaskList = autoDeployService.getAutoDeployTasks(deployTaskCriteria);
            // 根据最新的部署包，获取本次待部署的Task
            boolean result = true;
            if (ListUtil.isEmpty(autoDeployTaskList)) {
                Map<String, List<AutoDeployTaskVo>> prepareVo = autoDeployService.getPrepareVo();
                List<AutoDeployTaskVo>  prepareVoList = new LinkedList<>();
                for (List<AutoDeployTaskVo> list : prepareVo.values()) {
                    prepareVoList.addAll(list);
                }
                // 为本次部署创建DeployTask
                result = autoDeployService.addDeployTask(prepareVoList);
                AutoDeployTaskCriteria autoDeployTaskCriteria = new AutoDeployTaskCriteria();
                autoDeployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
                List<AutoDeployTaskVo> autoDeployTasks = autoDeployService.getAutoDeployTasks(autoDeployTaskCriteria);
                task = autoDeployTasks.stream().collect(Collectors.groupingBy(AutoDeployTaskVo::getIp));
                if (result) {
                    sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("为本次部署创建Task成功",""));
                }
            } else {
                // 获取上次未部署完成的Task
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("获取上次未部署完成的Task",""));
                task = new LinkedHashMap<>();
                for (AutoDeployTaskVo deployTaskVo : autoDeployTaskList) {
                    List<AutoDeployTaskVo> deployList = task.get(deployTaskVo.getIp());
                    if (deployList == null) {
                        deployList = new LinkedList<>();
                    }
                    deployList.add(deployTaskVo);
                    task.put(deployTaskVo.getIp(), deployList);
                }
            }
            if (result) {
                Set<String> ips = task.keySet();
                List<String> ipList = new ArrayList<>(ips);
                AgentCriteria criteria = new AgentCriteria();
                criteria.setIps(ipList);
                List<AgentVo> agentList = agentService.getAgentList(criteria);
                if (ListUtil.isEmpty(agentList)) {
                    String msg = "未知Agent：" + JsonUtil.toJsonString(ipList);
                    sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent( msg,""));
                    Log.low.info(msg);
                    return false;
                }
                Boolean resu = true;
                Boolean re = true;
                StringJoiner stringJoiner = new StringJoiner(",","","");
                StringJoiner stringJoiner2 = new StringJoiner(",","","");
                for (AgentVo agentVo : agentList) {
                    String ip = agentVo.getIp();
                    if (agentVo.getStatus() == AlarmStatusType.GRAY) {
                        stringJoiner.add(ip);
                        resu = false;
                    } else {
                        //检查agent连接状态
                        String iceRequest = softwareService.iceRequest(ip, IceFlag.PING_AGENT, "");
                        if (iceRequest == null) {
                            stringJoiner2.add(ip);
                            re = false;
                        }
                    }
                }
                if (!resu) {
                    sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent( stringJoiner.toString() + "的Agent已关闭,请开启后再部署",""));
                    return false;
                }
                if (!re) {
                    sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent( stringJoiner2.toString() + "的Agent无法连接,请确保能正常连接后再部署",""));
                    return false;
                }
                Map<String, AgentVo> agentVoMap = new LinkedHashMap<>();
                agentList.stream().forEach(e -> agentVoMap.put(e.getIp(),e));
                //循环遍历，为每个机器部署程序
                Log.low.info("本次部署的任务有： " + task.entrySet());
                for (Map.Entry<String, List<AutoDeployTaskVo>> entry : task.entrySet()) {
                    try {
                        String ip = entry.getKey();
                        AgentVo agentVo = agentVoMap.get(ip);
                        if (agentVo == null) {
                            continue;
                        }
                        Map<String, Object> linkedHashMap = new LinkedHashMap<>();
                        linkedHashMap.put("ip", ip);
                        linkedHashMap.put("deploy", entry.getValue());
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("-------------开始向<" + ip + ">部署程序-------------",""));
                        Boolean deployResult = autoDeployService.autoDeploy(agentVo, linkedHashMap);
                        if (!deployResult) {
                            depResult = false;
                            break;
                        }
                    } catch (Exception e) {
                        Log.high.error(e.getMessage(), e);
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(e.getMessage(),""));
                        return false;
                    }
                }
            }
        } catch (ServiceException e) {
            Log.high.error(e.getMessage(), e);
            return false;
        }
        if (depResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("部署结束!!!!!",""));
            long end = System.currentTimeMillis();
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("部署用时：" + (end - start)/1000 + "s",""));
            Map<String, List<String>> needManulHandleExceptionInfo = autoDeployService.getNeedManulHandleExceptionInfo();
            if (!MapUtils.isEmpty(needManulHandleExceptionInfo)) {
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("！！！*********需手动处理异常**********！！！",""));
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("如下角色机,已存在占用文件,请手动处理:" + needManulHandleExceptionInfo,""));
            }
            this.lockDeployTask(AutoDeployRecordType.STEP4.getStatus(),null,null,null,JsonUtil.toJsonString("程序上传结束"),null);
        }
        return depResult;
    }

    @Override
    public boolean tryLock() throws ServiceException {
        return super.tryLock(lock);
    }

    @Override
    public boolean unLock() throws ServiceException {
        return super.unLock(lock);
    }

    private String openAllSoftware(String host, WebSocket webSocket) {
        AutoDeployTaskCriteria deployTaskCriteria = new AutoDeployTaskCriteria();
        deployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
        List<AutoDeployTaskVo> deployingTask = autoDeployService.getAutoDeployTasks(deployTaskCriteria);
        if (ListUtil.isNotEmpty(deployingTask)) {
            String names = "";
            for (AutoDeployTaskVo taskVo : deployingTask) {
                names = names.concat(taskVo.getName()).concat(",");
            }
            throw new ServiceException(names + "程序部署中，暂不支持操作");
        }
        final List<SoftwareInfoVo> softwareInfoList = softwareService.getSoftwareInfoList(host);
        if (softwareInfoList.isEmpty()) {
            throw new ServiceException(host + " 不存在守护的程序");
        }
        final HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("operateType", 1);
        requestMap.put("send", Boolean.FALSE);
        final LinkedHashSet<SoftwareInfoVo> set = new LinkedHashSet<>();
        final String[] softwares = firstOpenSoftware.toLowerCase().split(",");
        if(softwares.length > 0){
            for (String software : softwares) {
                for (SoftwareInfoVo softwareInfoVo : softwareInfoList) {
                    if (softwareInfoVo.getName().toLowerCase().contains(software)) {
                        set.add(softwareInfoVo);
                    }
                }
            }
        }
        set.addAll(softwareInfoList);
        final AtomicInteger count = new AtomicInteger(0);
        for (SoftwareInfoVo softwareInfoVo : set) {
            if (softwareInfoVo.getProgramStatus().equals(ProgramStatusType.OPEN.getValue())) {
                continue;
            }
            try {
                requestMap.put("softwareId", softwareInfoVo.getId());
                sentText(webSocket, "请求开启 " + host + " - " + softwareInfoVo.getName() + " 程序");
                //operateSoftware(requestMap, webSocket);
                ((SoftwareWebSocketImpl) AopContext.currentProxy()).operateSoftware(requestMap, webSocket);
            } catch (Exception e) {
                sentText(webSocket, "开启 " + softwareInfoVo.getName() + " 失败，" + e.getMessage());
            }
            count.incrementAndGet();
        }
        if (count.get() == 0) {
            sentText(webSocket, "所有程序均已开启，无须重复开启");
        } else {
            sentText(webSocket, "操作完成");
        }
        return null;
    }

    private String openQuerySoftware(String software, String fuzzy, String status,WebSocket webSocket) {
        AutoDeployTaskCriteria deployTaskCriteria = new AutoDeployTaskCriteria();
        deployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
        List<AutoDeployTaskVo> deployingTask = autoDeployService.getAutoDeployTasks(deployTaskCriteria);
        if (ListUtil.isNotEmpty(deployingTask)) {
            String names = "";
            for (AutoDeployTaskVo taskVo : deployingTask) {
                names = names.concat(taskVo.getName()).concat(",");
            }
            throw new ServiceException(names + "程序部署中，暂不支持操作");
        }
        final SoftwareCriteria softwareCriteria = new SoftwareCriteria();
        if ("0".equals(fuzzy)) {
            softwareCriteria.setFuzzyName(software);
        } else if ("2".equals(fuzzy)) {
            softwareCriteria.setNotName(software);
        } else {
            softwareCriteria.setName(software);
        }
        if (!StringUtils.isEmpty(status)) {
            softwareCriteria.setStatus(AlarmStatusType.getId(status));
        }
        final List<SoftwareVo> list = softwareService.getSoftwareInfos(softwareCriteria).getList();
        if (list.isEmpty()) {
            throw new ServiceException("没有找到需要开启的程序");
        }
        final HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("operateType", 1);
        requestMap.put("send", Boolean.FALSE);
        final AtomicInteger count = new AtomicInteger(0);
        for (SoftwareVo softwareInfoVo : list) {
            if (softwareInfoVo.getProgramStatus().equals(ProgramStatusType.OPEN.getValue()) || Objects.isNull(softwareInfoVo.getHost())) {
                continue;
            }
            try {
                requestMap.put("softwareId", softwareInfoVo.getId());
                sentText(webSocket, "请求开启 " + softwareInfoVo.getHost() + " - " + softwareInfoVo.getName() + " 程序");
                ((SoftwareWebSocketImpl) AopContext.currentProxy()).operateSoftware(requestMap, webSocket);
            } catch (Exception e) {
                sentText(webSocket, "开启 " + softwareInfoVo.getName() + " 失败，" + e.getMessage());
            }
            count.incrementAndGet();
        }
        if (count.get() == 0) {
            sentText(webSocket, "所有程序均已开启，无须重复开启");
        } else {
            sentText(webSocket, "操作完成");
        }
        return null;
    }

    private String closeAllSoftware(String host, WebSocket webSocket) {
        AutoDeployTaskCriteria deployTaskCriteria = new AutoDeployTaskCriteria();
        deployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
        List<AutoDeployTaskVo> deployingTask = autoDeployService.getAutoDeployTasks(deployTaskCriteria);
        if (ListUtil.isNotEmpty(deployingTask)) {
            String names = "";
            for (AutoDeployTaskVo taskVo : deployingTask) {
                names = names.concat(taskVo.getName()).concat(",");
            }
            throw new ServiceException(names + "程序部署中，暂不支持操作");
        }
        final List<SoftwareInfoVo> softwareInfoList = softwareService.getSoftwareInfoList(host);
        if (softwareInfoList.isEmpty()) {
            throw new ServiceException(host + " 不存在守护的程序");
        }
        final HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("operateType", 0);
        requestMap.put("send", Boolean.FALSE);
        final AtomicInteger count = new AtomicInteger(0);
        for (SoftwareInfoVo softwareInfoVo : softwareInfoList) {
            if (softwareInfoVo.getProgramStatus().equals(ProgramStatusType.OPEN.getValue())) {
                try {
                    requestMap.put("softwareId", softwareInfoVo.getId());
                    sentText(webSocket, "请求关闭 " + host + " - " + softwareInfoVo.getName() + " 程序");
                    //operateSoftware(requestMap, webSocket);
                    ((SoftwareWebSocketImpl) AopContext.currentProxy()).operateSoftware(requestMap, webSocket);
                } catch (Exception e) {
                    sentText(webSocket, "关闭 " + softwareInfoVo.getName() + " 失败，" + e.getMessage());
                }
                count.incrementAndGet();
            }
        }
        if (count.get() == 0) {
            sentText(webSocket, "所有程序均已关闭，无须重复关闭");
        } else {
            sentText(webSocket, "操作完成");
        }
        return null;
    }

    private String closeQuerySoftware(String software, String fuzzy, String status,WebSocket webSocket) {
        AutoDeployTaskCriteria deployTaskCriteria = new AutoDeployTaskCriteria();
        deployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
        List<AutoDeployTaskVo> deployingTask = autoDeployService.getAutoDeployTasks(deployTaskCriteria);
        if (ListUtil.isNotEmpty(deployingTask)) {
            String names = "";
            for (AutoDeployTaskVo taskVo : deployingTask) {
                names = names.concat(taskVo.getName()).concat(",");
            }
            throw new ServiceException(names + "程序部署中，暂不支持操作");
        }
        final SoftwareCriteria softwareCriteria = new SoftwareCriteria();
        if ("0".equals(fuzzy)) {
            softwareCriteria.setFuzzyName(software);
        } else if ("2".equals(fuzzy)) {
            softwareCriteria.setNotName(software);
        } else {
            softwareCriteria.setName(software);
        }
        if (!StringUtils.isEmpty(status)) {
            softwareCriteria.setStatus(AlarmStatusType.getId(status));
        }
        final List<SoftwareVo> list = softwareService.getSoftwareInfos(softwareCriteria).getList();
        if (list.isEmpty()) {
            throw new ServiceException("没有找到关闭的程序");
        }
        final HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("operateType", 0);
        requestMap.put("send", Boolean.FALSE);
        final AtomicInteger count = new AtomicInteger(0);
        for (SoftwareVo softwareInfoVo : list) {
            if (softwareInfoVo.getProgramStatus().equals(ProgramStatusType.OPEN) && !Objects.isNull(softwareInfoVo.getHost())) {
                try {
                    requestMap.put("softwareId", softwareInfoVo.getId());
                    sentText(webSocket, "请求关闭 " + softwareInfoVo.getHost() + " - " + softwareInfoVo.getName() + " 程序");
                    //operateSoftware(requestMap, webSocket);
                    ((SoftwareWebSocketImpl) AopContext.currentProxy()).operateSoftware(requestMap, webSocket);
                } catch (Exception e) {
                    sentText(webSocket, "关闭 " + softwareInfoVo.getName() + " 失败，" + e.getMessage());
                }
                count.incrementAndGet();
            }
        }
        if (count.get() == 0) {
            sentText(webSocket, "所有程序均已关闭，无须重复关闭");
        } else {
            sentText(webSocket, "操作完成");
        }
        return null;
    }

    @Override
    @OperateRecordAnnotation(name = "%s%s上%s程序", module = "softwareWithWebSocket")
    public String operateSoftware(Map<String, Object> map, WebSocket webSocket) throws ServiceException {
        AutoDeployTaskCriteria deployTaskCriteria = new AutoDeployTaskCriteria();
        deployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
        List<AutoDeployTaskVo> deployingTask = autoDeployService.getAutoDeployTasks(deployTaskCriteria);
        if (ListUtil.isNotEmpty(deployingTask)) {
            String names = "";
            for (AutoDeployTaskVo taskVo : deployingTask) {
                names = names.concat(taskVo.getName()).concat(",");
            }
            throw new ServiceException(names + "程序部署中，暂不支持操作");
        }
        final Object send = map.get("send");
        boolean sendMessage = send == null;
        Integer operateType = Integer.valueOf(map.get("operateType").toString());
        Integer softwareId = Integer.valueOf(map.get("softwareId").toString());
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setId(softwareId);
        SoftwareVo softwareVo = softwareService.getSoftwareInfo(criteria);
        if (softwareVo == null || StringUtils.isEmpty(softwareVo.getName())) {
            throw new ServiceException(ResponseType.OPERATE_ERROR.getMsg() + "，未知程序ID：" + softwareId);
        }
        String ip = softwareVo.getHost();
        Integer pid = softwareVo.getPid();
        if (StringUtils.isEmpty(ip)) {
            throw new ServiceException(ResponseType.OPERATE_ERROR.getMsg() + "，未知程序IP " + softwareVo.getName());
        }
        Integer serverId = softwareVo.getServerId();
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setId(serverId);
        AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
        if (agentVo == null) {
            throw new ServiceException("未知Agent");
        }
        if (agentVo.getProgramStatus().equals(ProgramStatusType.CLOSE)) {
            throw new ServiceException("请确定Agent程序正常运行");
        }
        if (softwareVo.getName().toLowerCase().contains("agent")) {
            throw new ServiceException("开启Agent请在【Agent管理】中操作");
        }
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("operateType", operateType);
            paramMap.put("name", softwareVo.getName());
            paramMap.put("pid", pid);
            if (sendMessage) {
                sentText(webSocket, "请求" + ip + "的Agent" + SoftwareOperateType.getSoftwareOperateName(operateType) + softwareVo.getName() + "程序");
            }
            String request = JsonUtil.toJsonString(paramMap);
            if (sendMessage) {
                sentText(webSocket, "入参如下：" + request);
            }
            String result = softwareService.iceRequest(ip, IceFlag.SOFTWARE_OPERATE, request);
            if (StringUtils.isEmpty(result)) {
                throw new ServiceException(ResponseType.OPERATE_ERROR.getMsg() + "，ICE访问失败 " + ip);
            } else {
                if (sendMessage) {
                    sentText(webSocket, "请求成功，出参如下：" + result);
                }
                ResponseVo agentResponse = JsonUtil.parseObject(result, ResponseVo.class);
                Object data = null;
                if (agentResponse != null) {
                    data = agentResponse.getData();
                }
                Object omsg = map.get("msg");
                if (agentResponse.getCode() != null && agentResponse.getCode().equals(ResponseType.SUCCESS.getCode())) {
                    if (SoftwareOperateType.CLOSE.getId().equals(operateType)) {
                        softwareVo.setPid(-1);
                        softwareVo.setStatus(AlarmStatusType.GRAY);
                        String msg;
                        if (omsg != null && !"".equals(msg = omsg.toString().trim())) {
                            softwareVo.setDescription("程序已关闭," + msg);
                        } else {
                            softwareVo.setDescription("程序已关闭");
                        }
                        softwareVo.setLastUpdateTime(null);
                    } else {
                        //首次启动未知PID
                        softwareVo.setPid(-1);

                        if (data != null) {
                            if (sendMessage) {
                                sentText(webSocket, "开启程序成功，进程id: " + data.toString());
                            }
                        } else {
                            if (sendMessage) {
                                sentText(webSocket, "程序已开启守护");
                            }
                        }
                        softwareVo.setDescription("心跳为空");
                        softwareVo.setStatus(AlarmStatusType.RED);
                    }
                    if (sendMessage) {
                        sentText(webSocket, "更新" + softwareVo.getName() + "程序信息");
                    }
                    if (SoftwareOperateType.RELOAD.getId().equals(operateType)) {
                        softwareVo.setRestartCount(softwareVo.getRestartCount() + 1);
                    }
                    if (operateType.equals(SoftwareOperateType.CLOSE.getId())) {
                        softwareVo.setProgramStatus(ProgramStatusType.CLOSE);
                        softwareVo.setStartType(OperateType.MANUAL);
                        softwareVo.setPid(-1);
                        if (omsg != null && !"".equals(omsg.toString().trim())) {
                            softwareVo.setNote(omsg.toString().trim());
                        }
                    } else if (operateType.equals(SoftwareOperateType.OPEN.getId()) || operateType.equals(SoftwareOperateType.RELOAD.getId())) {
                        softwareVo.setProgramStatus(ProgramStatusType.OPEN);
                        softwareVo.setStartType(OperateType.MANUAL);
                        Integer p = null;
                        if (data != null) {
                            p = Integer.valueOf(data.toString());
                        }
                        softwareVo.setPid(p);
                    }
                    softwareService.updateSoftwareInfo(softwareVo, false);
                    if (sendMessage) {
                        sentText(webSocket, "更新完毕，操作完成");
                    }
                } else {
                    throw new ServiceException(ResponseType.OPERATE_ERROR.getMsg() + ",失败原因：" + agentResponse.getMsg());
                }
            }
        } catch (Exception e) {
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException(e);
        }
        return softwareVo.getName();
    }
}