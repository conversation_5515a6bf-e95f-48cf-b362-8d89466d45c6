package com.maintain.server.websocket;

import com.common.log.Log;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.maintain.server.Constants;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.service.WebSocketService;
import com.maintain.server.utils.FileUtil;
import com.maintain.server.vo.AgentVo;
import org.apache.commons.lang.StringUtils;
import org.java_websocket.WebSocket;

import java.io.File;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2019-01-21
 */
public abstract class BaseWebSocket implements WebSocketService, Constants {

    protected boolean tryLock(ReentrantLock lock) {
        try {
            if (!lock.tryLock(TRY_LOCK, TimeUnit.SECONDS)) {
                throw new ServiceException("当前正在进行其他操作，请稍后再试");
            }
        } catch (InterruptedException e) {
            Log.high.error(e.getMessage(),e);
        }
        return true;
    }

    protected boolean unLock(ReentrantLock lock) {
        try {
            lock.unlock();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return true;
    }

    public void sentText(WebSocket conn, String msg) {
        try {
            if (conn == null || StringUtils.isEmpty(msg)) {
                return;
            }
            //synchronized (conn) {
                try {
                    if (StringUtils.isEmpty(msg)) {
                        return;
                    }
//                    Thread.sleep(2000L);
                    conn.send(msg);
                } catch (Exception e) {
                    Log.high.error(e.getMessage(), e);
                }
            //}
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    /**
     * 线程过多。
     * @param agentVo
     * @return
     */
    public static Session getSession(AgentVo agentVo) {
        try {
            Session session;
            JSch jsch = new JSch();
            String name = agentVo.getName();
            String ip = agentVo.getIp();
            Integer port = agentVo.getPort();
            if (port <= 0) {
                // 连接服务器，采用默认端口
                session = jsch.getSession(name, ip);
            } else {
                // 采用指定的端口连接服务器
                session = jsch.getSession(name, ip, port);
            }
            if (StringUtils.isNotEmpty(agentVo.getPswd())) {
                session.setPassword(agentVo.getPswd());
            }
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect(Constants.CONNECT_TIMEOUT);
            return session;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    protected void deleteLogAndSelfPidFile(File tempFile) {
        File[] files = tempFile.listFiles();
        if (files != null) {
            for (File file : files) {
                String fileName = file.getName();
                if ("log".equals(fileName) || "logs".equals(fileName) || "self.pid".equals(fileName)) {
                    FileUtil.deleteDir(file);
                }
            }
        }
    }

    /**
     * 修改配置文件
     *
     * @param tempFile tempFile
     * @param map      map
     * @throws ServiceException ServiceException
     */
    protected void updateConfigFile(File tempFile, Map<String, Object> map) throws ServiceException {
    }

    protected String autoDeployCmdInWindows(String processName) {
        return null;
    }

    protected String autoDeployCmdInLinux(String processName) {
        return null;
    }

    @Override
    public String openSoftware(String param, WebSocket webSocket) throws ServiceException {
        return null;
    }

    @Override
    public String closeSoftware(String param, WebSocket webSocket) throws ServiceException {
        return null;
    }

    @Override
    public String reloadSoftware(String param, WebSocket webSocket) throws ServiceException {
        return null;
    }

    @Override
    public void updateSoftware(String param, WebSocket webSocket) throws ServiceException {
    }

    @Override
    public Boolean autoDeploy(String param, WebSocket webSocket) throws ServiceException {
        return true;
    }

    @Override
    public void setSyschroTime(String param, WebSocket webSocket) throws ServiceException {
    }

    @Override
    public boolean tryLock() throws ServiceException {
        return false;
    }

    @Override
    public boolean unLock() throws ServiceException {
        return false;
    }

    @Override
    public boolean executeUpgrade(String param, WebSocket conn) {
        return false;
    }
}