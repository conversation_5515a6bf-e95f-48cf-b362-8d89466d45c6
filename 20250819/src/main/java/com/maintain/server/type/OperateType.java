package com.maintain.server.type;

public enum OperateType {

    /** 手动 */
    MANUAL(1),
    /** 自动 */
    AUTO(2);

    private Integer value;

    OperateType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static OperateType parse(Integer value){
        for(OperateType o:OperateType.values()){
            if(o.getValue().equals(value)){
                return o;
            }
        }
        return null;
    }
}
