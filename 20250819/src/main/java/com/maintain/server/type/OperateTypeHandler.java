package com.maintain.server.type;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class OperateType<PERSON><PERSON><PERSON>  extends BaseTypeHandler<OperateType> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, OperateType operateType, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i,operateType.getValue());
    }

    @Override
    public OperateType getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return OperateType.parse(resultSet.getInt(s));
    }

    @Override
    public OperateType getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return OperateType.parse(resultSet.getInt(i));
    }

    @Override
    public OperateType getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return OperateType.parse(callableStatement.getInt(i));
    }
}
