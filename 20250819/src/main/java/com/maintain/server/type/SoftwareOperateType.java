package com.maintain.server.type;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/11/30.
 * 程序操作
 */
public enum SoftwareOperateType {

    CLOSE(0, "关闭"),
    OPEN(1, "开启"),
    RELOAD(2, "重启"),
    UPDATE(3, "更新"),
    DEPLOY(4, "部署");

    private Integer id;
    private String name;

    SoftwareOperateType(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getSoftwareOperateName(Integer id) {
        for (SoftwareOperateType type : SoftwareOperateType.values()) {
            if (type.getId().equals(id)) {
                return type.getName();
            }
        }
        return null;
    }

    public static SoftwareOperateType parse(Integer id){
        for (SoftwareOperateType type : SoftwareOperateType.values()) {
            if (type.getId().equals(id)) {
                return type;
            }
        }
        return null;
    }
}
