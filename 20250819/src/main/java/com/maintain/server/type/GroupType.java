package com.maintain.server.type;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
public enum GroupType {

    ES(0, "ELASTICSEARCH"), CODIS(1, "Codis管理"), CDH(2, "CDH管理"),ES2(3,"ES管理2"),
    HDFS(3, "HDFS"), YARN(4, "YARN"), HIVE(5, "HIVE"), HBASE(6, "HBASE"),
    ZK(7, "ZOOKEEPER"), KAFKA(8, "KAFKA"), RANGER(9, "RANGER"), FLINK(10, "FLINK"), SPARK(11, "SPARK3"), TRINO(12, "TRINO");

    private Integer id;

    private String name;

    GroupType(Integer id, String value) {
        this.id = id;
        this.name = value;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    public static GroupType getType(Integer value) {
        for (GroupType type : GroupType.values()) {
            if (type.getId().equals(value)) {
                return type;
            }
        }
        return null;
    }

}