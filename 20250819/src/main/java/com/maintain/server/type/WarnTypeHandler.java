package com.maintain.server.type;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class Warn<PERSON><PERSON><PERSON><PERSON>ler extends BaseTypeHandler<WarnType> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, WarnType warnType, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i,warnType.getValue());
    }

    @Override
    public WarnType getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return WarnType.parse(resultSet.getInt(s));
    }

    @Override
    public WarnType getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return WarnType.parse(resultSet.getInt(i));
    }

    @Override
    public WarnType getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return WarnType.parse(callableStatement.getInt(i));
    }
}
