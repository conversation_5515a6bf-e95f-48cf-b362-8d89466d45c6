package com.maintain.server.type;

public enum ReportType {

    /** 日报 */
    DAY(1,"日报"),
    /** 周报 */
    WEEK(2,"周报"),
    /** 月报 */
    MONTH(3,"月报");

    private Integer value;

    private String title;

    ReportType(Integer value, String title) {
        this.value = value;
        this.title = title;
    }

    public Integer getValue() {
        return value;
    }

    public String getTitle() {
        return title;
    }

    /**
     * 转换；默认DAY
     * @param value
     * @return
     */
    public static ReportType parse(Integer value){
        final ReportType[] values = ReportType.values();
        for (ReportType reportType : values) {
            if(reportType.getValue().equals(value)){
                return reportType;
            }
        }
        return DAY;
    }

}
