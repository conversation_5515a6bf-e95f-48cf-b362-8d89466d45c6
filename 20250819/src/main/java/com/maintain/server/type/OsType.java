package com.maintain.server.type;

/**
 * <AUTHOR>
 * @date 2018-10-08
 */
public enum OsType {

    LINUX(0, "LINUX"), WINDOWS(1, "WINDOWS");

    private Integer id;
    private String name;

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    OsType(Integer id, String name) {
        this.id = id;
        this.name = name;
    }


    public static String getName(Integer id) {
        for (OsType type : OsType.values()) {
            if (type.getId().equals(id)) {
                return type.getName();
            }
        }
        return null;
    }

    public static Integer getId(String name){
        for (OsType type : OsType.values()) {
            if (name.contains(type.getName())) {
                return type.getId();
            }
        }
        return null;
    }
}