package com.maintain.server.type;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/10/10.
 * 告警状态
 */
public enum AlarmStatusType {

    GREEN(0, "GREEN"), YELLOW(1, "YELLOW"), RED(2, "RED"),GRAY(-1,"GRAY");

    private Integer id;

    private String value;

    private String description;

    AlarmStatusType(Integer id, String value) {
        this.id = id;
        this.value = value;
    }

    public Integer getId() {
        return id;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property <tt>description</tt>.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    public static AlarmStatusType getType(String value){
        for (AlarmStatusType type: AlarmStatusType.values()) {
            if(type.getValue().equals(value)){
                return type;
            }
        }
        return null;
    }


    public static AlarmStatusType getType(Integer value){
        if (value == null) {
            return null;
        }
        for (AlarmStatusType type: AlarmStatusType.values()) {
            if(type.getId().equals(value)){
                return type;
            }
        }
        return null;
    }

    public static Integer getId(String value){
        for (AlarmStatusType type: AlarmStatusType.values()) {
            if (type.getValue().equals(value)){
                return type.getId();
            }
        }
        return null;
    }

    public String getLevel() {
        switch (this) {
            case GREEN:
                return "良好";
            case YELLOW:
                return "告警";
            case RED:
            case GRAY:
                return "错误";
            default:
                return "良好";
        }
    }

    public static boolean isAlarm(AlarmStatusType type) {
        return AlarmStatusType.YELLOW.equals(type) || AlarmStatusType.RED.equals(type);
    }
}
