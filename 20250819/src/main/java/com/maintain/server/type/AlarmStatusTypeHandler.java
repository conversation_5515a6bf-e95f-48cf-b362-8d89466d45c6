package com.maintain.server.type;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2018-11-05
 */
public class AlarmStatusTypeHandler extends BaseTypeHandler<AlarmStatusType> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, AlarmStatusType alarmStatusType, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i, alarmStatusType.getId());
    }

    @Override
    public AlarmStatusType getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int id = resultSet.getInt(s);
        return AlarmStatusType.getType(id);
    }

    @Override
    public AlarmStatusType getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int id = resultSet.getInt(i);
        return AlarmStatusType.getType(id);
    }

    @Override
    public AlarmStatusType getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int id = callableStatement.getInt(i);
        return AlarmStatusType.getType(id);
    }
}