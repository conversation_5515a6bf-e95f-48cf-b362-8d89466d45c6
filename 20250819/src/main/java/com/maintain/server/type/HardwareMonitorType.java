package com.maintain.server.type;

import com.common.log.Log;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 服务器硬盘警告判断策略
 */
public enum HardwareMonitorType {


    分析机(500,200),

    默认(1000,500);

    public static void main(String[] args) {
        System.out.println(HardwareMonitorType.分析机.name());
    }


    private Integer warn;

    private Integer error;

    private String low = "80";

    private String high = "90";

    public String getLow() {
        return low;
    }

    public String getHigh() {
        return high;
    }

    HardwareMonitorType(Integer warn, Integer error) {
        this.warn = warn;
        this.error = error;
    }

    public void judgeDiskInfo(String module, String diskUsedPercent, String total, String used, Map<String, AlarmStatusType> statusMap, Map<AlarmStatusType, String> descriptionMap){
        diskUsedPercent = diskUsedPercent.replace("%", "").trim();
        BigDecimal bigDecimal = new BigDecimal(diskUsedPercent);
        //计算磁盘容量应该用字节！
        BigDecimal T = BigDecimal.valueOf(1024 * 1024 * 1024 * 1024L);
        BigDecimal G = BigDecimal.valueOf(1024 * 1024 * 1024L);
        BigDecimal M = BigDecimal.valueOf(1024 * 1024L);
        BigDecimal totalDisk = new BigDecimal(0);
        BigDecimal usedDisk = new BigDecimal(0);
        if (StringUtils.isNotEmpty(total)) {
            boolean containG = total.contains("g") || total.contains("G");
            boolean containM = total.contains("m") || total.contains("M");
            boolean containT = total.contains("t") || total.contains("T");
            if (containG) {
                total = total.replace("g", "").replace("G", "");
                totalDisk = totalDisk.add(new BigDecimal(total).multiply(G));
            } else if (containM) {
                total = total.replace("m", "").replace("M", "");
                Log.low.debug("");
                totalDisk = totalDisk.add(new BigDecimal(total).multiply(M));
            } else if (containT) {
                total = total.replace("t", "").replace("T", "");
                Log.low.debug("");
                totalDisk = totalDisk.add(new BigDecimal(total).multiply(T));
            }
        }
        if (StringUtils.isNotEmpty(used) && used.contains("g") || used.contains("G") || used.contains("t") || used.contains("T")) {
            boolean containG = used.contains("g") || used.contains("G");
            boolean containM = used.contains("m") || used.contains("M");
            boolean containT = used.contains("t") || used.contains("T");
            if (containG) {
                used = used.replace("g", "").replace("G", "");
                usedDisk = usedDisk.add(new BigDecimal(used).multiply(G));
            } else if (containM) {
                used = used.replace("m", "").replace("M", "");
                usedDisk = usedDisk.add(new BigDecimal(used).multiply(M));
            } else if (containT) {
                used = used.replace("t", "").replace("T", "");
                usedDisk = usedDisk.add(new BigDecimal(used).multiply(T));
            }
        }
        //可用磁盘容量
        BigDecimal availableDisk = totalDisk.subtract(usedDisk);
        AlarmStatusType availableDiskStatus = AlarmStatusType.GREEN;
        if (availableDisk.compareTo(new BigDecimal(getLow())) >= 0) {
            availableDiskStatus = AlarmStatusType.GREEN;
        } else if (availableDisk.compareTo(T) < 0 && bigDecimal.compareTo(BigDecimal.valueOf(80.0)) >= 0 && bigDecimal.compareTo(BigDecimal.valueOf(90.0)) < 0) {
            availableDiskStatus = AlarmStatusType.YELLOW;
        } else if (availableDisk.compareTo(G.multiply(new BigDecimal(getHigh()))) < 0 && bigDecimal.compareTo(BigDecimal.valueOf(90.0)) >= 0) {
            availableDiskStatus = AlarmStatusType.RED;
        }
        setType(module, statusMap, getLow(), getHigh(), bigDecimal,availableDiskStatus);
        setDescription(module, descriptionMap, statusMap);
    }


    protected void setDescription(String module, Map<AlarmStatusType, String> descriptionMap, Map<String, AlarmStatusType> statusMap) {
        AlarmStatusType type = statusMap.get(module);
        if (type == null) {
            return;
        }
        String description = descriptionMap.get(type);
        if (type == AlarmStatusType.RED) {
            String desc = module + "用量超过" + high + "%";
            if (module.contains("硬盘") || module.contains("目录")) {
                desc += ",且可用空间小于" + getHigh() + "G";
            }
            if (description != null) {
                description = description + "," + desc;
            } else {
                description = desc;
            }
            descriptionMap.put(type, description);
        } else if (type == AlarmStatusType.YELLOW) {
            String desc = module + "用量超过" + low + "%";
            if (module.contains("硬盘") || module.contains("目录")) {
                desc += ",且可用空间小于" + getLow() + "G";
            }
            if (description != null) {
                description = description + "," + desc;
            } else {
                description = desc;
            }
            descriptionMap.put(type, description);
        } else {
            descriptionMap.put(type, "/");
        }
    }


    protected void setType(String module, Map<String, AlarmStatusType> statusTypeMap, String low, String high, BigDecimal bigDecimal, AlarmStatusType availDiskStatus) {
        int warming = bigDecimal.compareTo(new BigDecimal(low));
        int alarm = bigDecimal.compareTo(new BigDecimal(high));
        if ((warming < 0 && alarm < 0) || availDiskStatus == AlarmStatusType.GREEN) {
            statusTypeMap.put(module, AlarmStatusType.GREEN);
        } else if (warming > 0 && alarm < 0 && availDiskStatus == AlarmStatusType.YELLOW) {
            statusTypeMap.put(module, AlarmStatusType.YELLOW);
        } else if (warming == 0 && alarm < 0 && availDiskStatus == AlarmStatusType.YELLOW) {
            statusTypeMap.put(module, AlarmStatusType.YELLOW);
        } else if (availDiskStatus == AlarmStatusType.RED) {
            statusTypeMap.put(module, AlarmStatusType.RED);
        } else {
            statusTypeMap.put(module, AlarmStatusType.GREEN);
        }
    }



    public Integer getWarn() {
        return warn;
    }

    public Integer getError() {
        return error;
    }
}
