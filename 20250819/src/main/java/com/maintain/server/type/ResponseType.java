package com.maintain.server.type;

/**
 * <AUTHOR>
 * @date 2018-10-10
 */
public enum ResponseType {

    SUCCESS(0,"请求成功"),FAILURE(1,"请求失败"),REJECT(1001,"参数错误"),NO_PERMISSION(1002,"无权限"), SYSTEM_ERROR(1003,"系统错误"),OPERATE_ERROR(1004,"操作失败"),
    LOGIN_FIRST(1005, "请先登录"), FAIL_REQUEST(500,"Agent专属");

    private Integer code;

    private String msg;

    ResponseType(Integer code,String msg){
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}