package com.maintain.server.type;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class SoftwareOperateTypeHandler extends BaseTypeHandler<SoftwareOperateType> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, SoftwareOperateType softwareOperateType, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i,softwareOperateType.getId());
    }

    @Override
    public SoftwareOperateType getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return SoftwareOperateType.parse(resultSet.getInt(s));
    }

    @Override
    public SoftwareOperateType getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return SoftwareOperateType.parse(resultSet.getInt(i));
    }

    @Override
    public SoftwareOperateType getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return SoftwareOperateType.parse(callableStatement.getInt(i));
    }
}
