package com.maintain.server.type;

public enum WarnType {

    /** 服务器磁盘空间警告 */
    HARDWARE_DISK_SPACE(1),
    /** 预处理目录警告 */
    STATIC_MONITOR(2),
    /** cpu警告 */
    CPU_SPACE(3),
    /** 内存警告 */
    MEMORY_SPACE(4);

    private Integer value;

    WarnType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static WarnType parse(Integer value){
        for (WarnType warnType : WarnType.values()) {
            if(warnType.value.equals(value))
                return warnType;
        }
        return null;
    }
}
