package com.maintain.server.type;

public enum ProgramStatusType {

    /** 关闭 */
    CLOSE(0),
    /** 开启 */
    OPEN(1);

    private Integer value;

    ProgramStatusType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static ProgramStatusType parse(Integer value){
        for(ProgramStatusType p: ProgramStatusType.values()){
            if(p.getValue().equals(value)){
                return p;
            }
        }
        return null;
    }
}
