package com.maintain.server.type;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ProgramStatusTypeHand<PERSON> extends BaseTypeHandler<ProgramStatusType> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, ProgramStatusType programStatusType, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i,programStatusType.getValue());
    }

    @Override
    public ProgramStatusType getNullableResult(ResultSet resultSet, String s) throws SQLException {
        int id = resultSet.getInt(s);
        return ProgramStatusType.parse(id);
    }

    @Override
    public ProgramStatusType getNullableResult(ResultSet resultSet, int i) throws SQLException {
        int id = resultSet.getInt(i);
        return ProgramStatusType.parse(id);
    }

    @Override
    public ProgramStatusType getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        int id = callableStatement.getInt(i);
        return ProgramStatusType.parse(id);
    }
}
