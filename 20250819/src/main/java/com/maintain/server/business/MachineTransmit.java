package com.maintain.server.business;

import com.maintain.server.type.OsType;
import com.maintain.server.vo.AgentVo;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
public abstract class MachineTransmit {

    protected AgentVo agentVo;

    public MachineTransmit(AgentVo agentVo) {
        this.agentVo = agentVo;
    }

    /**
     * 传输目录
     *
     * @param srcPath
     * @param destPath
     * @return
     */
    public abstract boolean transmitDirectory(String srcPath, String destPath);

    public static MachineTransmit getTransfer(AgentVo agentVo) {
        if (OsType.WINDOWS.getId().equals(agentVo.getOs())) {
            return new WindowsTransmit(agentVo);
        }
        return new LinuxTransmit(agentVo);
    }
}