package com.maintain.server.business;

import com.maintain.server.Constants;
import com.maintain.server.config.DbBackupConfig;
import com.maintain.server.service.MysqlBackupService;
import com.maintain.server.type.OsType;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.DbBackupManageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
public class DbBackupInLinux extends DbBackup {

    public DbBackupInLinux(List<DbBackupManageVo> voList, List<AgentVo> agentVos, int backupType,
                           MysqlBackupService mysqlBackupService, DbBackupConfig backupConfig) {
        super(voList, agentVos, backupType, mysqlBackupService, backupConfig);
    }

    @Override
    protected void init() {
        rootPath = Constants.MAINTAIN_LINUX_TEMP;
        separator = Constants.LINUX_FILE_SEPARATOR;
        localOs = OsType.LINUX;
    }


    /**
     * 生成备份的命令
     *
     * @param dbVo           数据库相关信息
     * @param backupFilePath 备份文件存储的路径
     * @return
     */
    @Override
    protected String getBackupCommand(DbBackupManageVo dbVo, String backupFilePath) {
        return "chmod +x ./deploy/mysql* && for dbname in `" + "./deploy/" + "mysql -h" + dbVo.getLocalIp() + " -u" + dbVo.getDbUser() + " -p" + dbVo.getDbPass() +
                " -e \"show databases;\" |grep -Evi \"Database\" |grep -v \"information_schema\" " +
                "|grep -v \"accounts\"|grep -v \"performance_schema\"`;do " + "./deploy/" + "mysqldump -R -E -h" + dbVo.getLocalIp() + " -u" +
                dbVo.getDbUser() + " -p" + dbVo.getDbPass() + " $dbname >> " +
                backupFilePath + "${dbname}.sql; done";
    }
}