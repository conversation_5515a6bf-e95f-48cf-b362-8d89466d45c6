package com.maintain.server.business;

import com.maintain.server.Constants;
import com.maintain.server.type.OsType;
import com.maintain.server.utils.DateUtil;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
public class DbBackupPathProducer {
    private Integer osType;
    private String rootPath;
    private String tempPath;

    public DbBackupPathProducer(Integer osType, String rootPath, String tempPath) {
        this.osType = osType;
        this.rootPath = rootPath;
        this.tempPath = tempPath;
    }

    public String produceTempPath(String ip, String date) {
        String separator = getSeparator(osType);
        if (rootPath.endsWith(separator)) {
            rootPath = rootPath.substring(0, rootPath.length() - separator.length());
        }
        String[] paths = new String[]{rootPath, tempPath, ip, date};
        return StringUtils.join(paths, separator) + separator;
    }


    private String getSeparator(Integer osType) {
        if (OsType.WINDOWS.getId().equals(osType)) {
            return Constants.WINDOWS_FILE_SEPARATOR;
        }
        return Constants.LINUX_FILE_SEPARATOR;
    }
}