package com.maintain.server.business;

import com.common.log.Log;
import com.jcraft.jsch.Session;
import com.maintain.server.Constants;
import com.maintain.server.config.DbBackupConfig;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.service.MysqlBackupService;
import com.maintain.server.type.OsType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.FileUtil;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.DbBackupManageVo;
import com.maintain.server.vo.MysqlBackupVo;
import com.maintain.server.websocket.BaseWebSocket;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
public abstract class DbBackup {
    protected String rootPath;
    protected String separator;
    protected OsType localOs;

    protected DbBackupConfig backupConfig;
    protected List<DbBackupManageVo> backupList;
    protected List<AgentVo> agentVos;
    protected int backupType;
    protected MysqlBackupService mysqlBackupService;

    public DbBackup(List<DbBackupManageVo> voList, List<AgentVo> agentVos
            , int backupType, MysqlBackupService mysqlBackupService, DbBackupConfig backupConfig) {
        init();
        this.backupList = voList;
        this.agentVos = agentVos;
        this.backupType = backupType;
        this.mysqlBackupService = mysqlBackupService;
        this.backupConfig = backupConfig;
    }

    public void backup(String maintainServerIp) {
        Log.low.info("开始执行" + (backupType == 0 ? "每日备份" : "每周备份"));
        backupOnLocalTemp();
        Log.low.info("备份到临时目录成功");
        moveBackupToLocal(maintainServerIp);

        moveBackupToRemote();

        deleteOverdueBackup();

    }

    /**
     * 备份到本地
     *
     * @return
     */
    public boolean backupOnLocalTemp() {
        for (DbBackupManageVo dbVo : backupList) {
            try {
                dbVo.setSuccess(backupOnTemp(dbVo));
            } catch (Exception e) {
                MysqlBackupVo errorVo = new MysqlBackupVo();
                errorVo.setRemoteSuccess(0);
                errorVo.setIp(dbVo.getLocalIp());
                errorVo.setBackupType(backupType);
                errorVo.setBackupDatabases(e.getMessage().length() > 250 ? e.getMessage().substring(0, 250) : e.getMessage());
                errorVo.setBackupTime(DateUtil.currentDay());
                mysqlBackupService.insertMysqlBackup(Collections.singletonList(errorVo));
            }
        }
        return true;
    }

    /**
     * 将备份文件移动到本机
     *
     * @return
     */
    public boolean moveBackupToLocal(String maintainServerIp) {
        //上一步失败的不再继续执行下一步
        List<DbBackupManageVo> vos = backupList.stream().filter(DbBackupManageVo::isSuccess).collect(Collectors.toList());
        vos.forEach(dbVo -> dbVo.setSuccess(false));
        for (DbBackupManageVo dbVo : vos) {
            Log.low.info("开始转发" + dbVo.getLocalIp() + "备份文件到" + dbVo.getLocalIp());
            String tempFilePath = produceTempPath(dbVo.getLocalIp(), DateUtil.currentDay());
            //1. 通过localIP获取Agent
            agentVos.stream().filter(a -> a.getIp().equals(dbVo.getLocalIp()))
                    .findAny().ifPresent(a -> {
                //2. 通过Agent和dbVo获取远程路径
                String remotePath = produceLocalPath(a.getOs(), dbVo.getLocalPath(), dbVo.getLocalIp(), DateUtil.currentDay());
                boolean flag = false;
                if (maintainServerIp.equals(dbVo.getLocalIp())) {
                    try {
                        File file = new File(tempFilePath);
                        File file1 = new File(remotePath);
                        FileUtils.copyDirectory(file, file1);
                        long uploadFileSize = FileUtil.getTotalSizeOfFilesInDir(file);
                        long fileSize = FileUtil.getTotalSizeOfFilesInDir(file1);
                        Boolean compare = uploadFileSize == fileSize;
                        if (compare) {
                            flag = true;
                        } else {
                            flag = false;
                        }
                        dbVo.setSuccess(flag);
                    } catch (IOException e) {
                        Log.high.info(e);
                    }
                } else {
                    //3. 通过Agent上传文件夹
                    flag = MachineTransmit.getTransfer(a).transmitDirectory(tempFilePath, remotePath);
                    dbVo.setSuccess(flag);
                }
                Log.low.info("转发结果:" + flag);
            });
        }
        return true;
    }

    /**
     * 移动备份到远程机器，实现异机备份
     *
     * @return
     */
    public boolean moveBackupToRemote() {
        List<DbBackupManageVo> vos = backupList.stream().filter(DbBackupManageVo::isSuccess).collect(Collectors.toList());
        vos.forEach(dbVo -> dbVo.setSuccess(false));
        for (DbBackupManageVo dbVo : vos) {
            Log.low.info("开始转发" + dbVo.getLocalIp() + "备份文件到异机：" + dbVo.getRemoteIp());
            String tempFilePath = produceTempPath(dbVo.getLocalIp(), DateUtil.currentDay());
            agentVos.stream().filter(a -> a.getIp().equals(dbVo.getRemoteIp()))
                    .findAny().ifPresent(a -> {
                String remotePath = produceLocalPath(a.getOs(), dbVo.getRemotePath(), dbVo.getLocalIp(), DateUtil.currentDay());

                //3. 通过Agent上传文件夹
                boolean flag = MachineTransmit.getTransfer(a).transmitDirectory(tempFilePath, remotePath);
                Log.low.info("转发结果：" + flag);
                dbVo.setSuccess(flag);
            });
        }
        return true;
    }

    /**
     * 删除过期备份
     *
     * @return
     */
    public boolean deleteOverdueBackup() {
        for (DbBackupManageVo dbVo : backupList) {
            //其父级 是 某台机器的所有备份文件的路径
            String tempFilePath = produceTempPath(dbVo.getLocalIp(), DateUtil.currentDay());
            //找出过期的文件夹名称
            List<File> overdueFileList = getOverdueBackupFile(tempFilePath);
            if (overdueFileList.isEmpty()) {
                continue;
            }
            overdueFileList.forEach(f -> {
                try {
                    FileUtils.deleteDirectory(f);
                } catch (IOException e) {
                    Log.high.error("删除" + f.getAbsolutePath() + " 目录出错");
                }
            });
            //在本机上和远程机删除对应的文件夹
            agentVos.stream().filter(a -> a.getIp().equals(dbVo.getLocalIp()))
                    .findAny().ifPresent(a -> deleteDirInRemote(a, dbVo.getLocalPath(), dbVo.getLocalIp(), overdueFileList));

            agentVos.stream().filter(a -> a.getIp().equals(dbVo.getRemoteIp()))
                    .findAny().ifPresent(a -> deleteDirInRemote(a, dbVo.getRemotePath(), dbVo.getLocalIp(), overdueFileList));

        }
        return true;
    }

    /**
     * 在远程机器上删除目录
     *
     * @param a
     * @param rootPath
     * @param ip
     * @param files
     */
    public void deleteDirInRemote(AgentVo a, String rootPath, String ip, List<File> files) {
        for (File f : files) {
            String backupPath = produceLocalPath(a.getOs(), rootPath, ip, f.getName());
            String command = getDeleteCommand(a.getOs(), backupPath);
            try {
                Session session = BaseWebSocket.getSession(a);
                ProcessUtil.initChannelShell(session, a, 60000L);
                Log.low.info(a.getIp() + " delete dir command:" + command);
                boolean flag = ProcessUtil.execShellOnRemoteWindows(command);
            } catch (Exception e) {
                Log.high.error("delete dir: " + f.getAbsolutePath() + " in " + a.getIp() + " failed", e);
            } finally {
                try {
                    ProcessUtil.destroyChannelShell(a);
                    ProcessUtil.destroy();
                } catch (IOException e) {
                    Log.high.error("清理" + a.getIp() + " SSH连接失败", e);
                }
            }
        }
    }

    /**
     * 获取过期的备份文件
     *
     * @param tempPath
     * @return
     */
    public List<File> getOverdueBackupFile(String tempPath) {
        File file = new File(tempPath);
        List<File> fileList = new ArrayList<>();
        if (file.exists()) {
            File[] files = file.getParentFile().listFiles();
            if (files != null) {
                if (files.length > backupConfig.getOverdueDay()) {
                    int deleteCount = files.length - backupConfig.getOverdueDay();
                    fileList = Arrays.stream(files)
                            .filter(f -> f.getName().contains("-"))
                            .sorted(Comparator.comparing(File::getName))
                            .limit(deleteCount)
                            .collect(Collectors.toList());
                    Log.low.info("待删除的数据库备份路径：" + fileList.stream().map(File::getName).collect(Collectors.joining(",")));
                }
            }
        }
        return fileList;
    }

    /**
     * 获取备份命令
     *
     * @param dbVo
     * @param backupFilePath
     * @return
     */
    protected abstract String getBackupCommand(DbBackupManageVo dbVo, String backupFilePath);

    /**
     * 获取删除文件夹的命令（过期备份）
     *
     * @param deletedPath
     * @return
     */
    private String getDeleteCommand(Integer osType, String deletedPath) {
        if (OsType.WINDOWS.getId().equals(osType)) {
            return "rd " + deletedPath + " /S /Q";
        } else {
            return "rm -rf " + deletedPath;
        }
    }

    /**
     * 初始化一些变量
     */
    protected abstract void init();

    /**
     * 将数据库备份到本地
     *
     * @param dbVo
     * @return
     */
    protected boolean backupOnTemp(DbBackupManageVo dbVo) {
        String backupFilePath = produceTempPath(dbVo.getLocalIp(), DateUtil.currentDay());
        if (!new File(backupFilePath).exists()) {
            new File(backupFilePath).mkdirs();
        }
        String command = getBackupCommand(dbVo, backupFilePath);
        try {
            String result = ProcessUtil.execLocalCommand(command, localOs.getId());
            Log.low.info("执行数据库备份命令： " + command + ",结果：" + result);
        } catch (ServiceException e) {
            String result = e.getMessage()
                    .replace("mysql: [Warning] Using a password on the command line interface can be insecure.", "")
                    .replace("mysqldump: [Warning] Using a password on the command line interface can be insecure.", "")
                    .replace("Warning: Using a password on the command line interface can be insecure.", "")
                    .replace("[Warning] Using a password on the command line interface can be insecure.", "");
            if (StringUtils.isNotEmpty(result)) {
                Log.high.error("备份数据库出错，备份命令：" + command, e);
                throw new ServiceException(result);
            }
        } catch (Exception e) {
            Log.high.error("exec " + command + " failed", e);
            throw new ServiceException("执行数据库备份命令出错");
        }
        return true;
    }

    /**
     * 获取本地临时目录（用于存放mysql备份文件）
     *
     * @param ip
     * @param date
     * @return
     */
    public String produceTempPath(String ip, String date) {
        return new DbBackupPathProducer(localOs.getId(), rootPath, produceBackupTypePath(backupType))
                .produceTempPath(ip, date);
    }


    private String produceLocalPath(Integer osType, String rootPath, String ip, String date) {
        return new DbBackupPathProducer(osType, rootPath, produceBackupTypePath(backupType))
                .produceTempPath(ip, date);
    }


    private String produceBackupTypePath(int backupType) {
        if (backupType == 0) {
            return Constants.MYSQL_BACKUP_DAILY;
        }
        return Constants.MYSQL_BACKUP_WEEKLY;
    }
}