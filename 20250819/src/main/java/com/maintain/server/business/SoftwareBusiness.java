package com.maintain.server.business;

import com.common.log.Log;
import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.BusinessMonitorCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.service.IndexService;
import com.maintain.server.service.SoftwareHistoryService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.BusinessMonitorVo;
import com.maintain.server.vo.SoftwareRestartVo;
import com.maintain.server.vo.SoftwareVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2019-03-07
 */
@Service
public class SoftwareBusiness implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private SoftwareHistoryService softwareHistoryService;

    @Autowired
    private IndexService indexService;

    private ExecutorService executorService = new ThreadPoolExecutor(5, 20, 2, TimeUnit.HOURS, new ArrayBlockingQueue<>(20), new ThreadPoolExecutor.DiscardOldestPolicy());

    private CyclicBarrier cyclicBarrier = new CyclicBarrier(5);

    public ExecutorService getExecutorService() {
        return this.executorService;
    }

    public Map<String, Object> getSoftwareDetail(SoftwareCriteria criteria) {
        Map<String, Object> data = new HashMap<>();
        //基础信息
        SoftwareCriteria newCriteria = new SoftwareCriteria();
        newCriteria.setId(criteria.getId());
        SoftwareVo softwareVo = softwareService.getSoftwareInfo(newCriteria);
        if (softwareVo == null) {
            throw new ServiceException(ResponseType.REJECT.getMsg());
        }
        data.put("softwareInfo", softwareVo);
        // 端口
        if (softwareVo.getPid() != null && softwareVo.getPid() != -1) {
            Map<String, Object> iceParam = new HashMap<>();
            iceParam.put("pid", softwareVo.getPid());
            String iceResult = softwareService.iceRequest(softwareVo.getHost(), IceFlag.GET_PROCESS_PORT_INFO, JsonUtil.toJsonString(iceParam));
            if (StringUtils.isNotEmpty(iceResult)) {
                Map map = JsonUtil.parseObject(iceResult, Map.class);
                data.put("portInfo", map.get("data"));
            }
        }
        // 趋势图
        Map<String, Object> historyResourceResult = getHistoryResource(criteria);
        if (historyResourceResult != null) {
            data.put("historyResource", historyResourceResult);
        }
        //业务监控
        BusinessMonitorCriteria businessMonitorCriteria = new BusinessMonitorCriteria();
        businessMonitorCriteria.setPn(criteria.getPn()).setPs(criteria.getPs());
        businessMonitorCriteria.setSoftwareName(softwareVo.getName());
        businessMonitorCriteria.setIp(softwareVo.getHost());
        PageInfo<BusinessMonitorVo> businessMonitorVoPageInfo = softwareService.getBusinessMonitor(businessMonitorCriteria);
        if (businessMonitorVoPageInfo != null && ListUtil.isNotEmpty(businessMonitorVoPageInfo.getList())) {
            data.put("businessMonitor", businessMonitorVoPageInfo);
        }
        // 重启情况
        PageInfo<SoftwareRestartVo> restartVoPageInfo = softwareHistoryService.getRestartHistory(criteria);
        if (restartVoPageInfo != null && ListUtil.isNotEmpty(restartVoPageInfo.getList())) {
            data.put("restartHistory", restartVoPageInfo.getList());
        }
        return data;
    }

    public Map<String,Object> getHistoryResource(String startTime,String endTime,Integer softwareId,String software){
        //程序的状态信息
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setId(softwareId);
        criteria.setStartTime(startTime);
        criteria.setEndTime(endTime);
        final Map<String, Object> historyResource = getHistoryResource(criteria);
        historyResource.put("software",software);
        return historyResource;
    }

    public Map<String, Map<String, Object>> listAllSoftwares(SoftwareCriteria softwareCriteria) {
        return softwareService.listAllSoftwares(softwareCriteria, false);
    }

    private void await(CyclicBarrier cyclicBarrier) {
        try {
            cyclicBarrier.await();
        } catch (InterruptedException | BrokenBarrierException e) {
            Log.high.error(e.getMessage(),e);
        }
    }

    public Map<String, Object> getHistoryResource(SoftwareCriteria criteria) {
        PageInfo<SoftwareVo> historyResource = softwareHistoryService.getSoftwareHistoryInfo(criteria);
        Map<String, Object> historyResourceResult = null;
        if (historyResource != null && ListUtil.isNotEmpty(historyResource.getList())) {
            historyResourceResult = new HashMap<>();
            Map<String, Object> cpuMap = new HashMap<>();
            Map<String, Object> diskMap = new HashMap<>();
            Map<String, Object> memoryMap = new HashMap<>();
            Map<String, Object> usedMemoryMap = new HashMap<>();
            List<SoftwareVo> historyResourceList = historyResource.getList();
            List<BigDecimal> cpuYList = new ArrayList<>();
            List<BigDecimal> usedMemoryYList = new ArrayList<>();
            List<BigDecimal> diskYList = new ArrayList<>();
            List<BigDecimal> memoryYList = new ArrayList<>();
            List<Date> timeXList = new ArrayList<>();
            String unit = "%";
            for (SoftwareVo softwareInfoVo : historyResourceList) {
                if (StringUtils.isEmpty(softwareInfoVo.getCpuPercent()) /*|| StringUtils.isEmpty(softwareInfoVo.getDiskPercent())*/ ||
                        StringUtils.isEmpty(softwareInfoVo.getMemoryPercent()) ||
                        StringUtils.isEmpty( softwareInfoVo.getUsedMemory())) {
                    continue;
                }
                cpuYList.add(new BigDecimal(softwareInfoVo.getCpuPercent().replace(unit, "")));
                if (softwareInfoVo.getTotalDisk() == null || "null".equals(softwareInfoVo.getTotalDisk())) {
                    continue;
                }
                diskYList.add(new BigDecimal(Long.parseLong(softwareInfoVo.getTotalDisk())));
                String userMemory = softwareInfoVo.getUsedMemory();
                userMemory = userMemory.replace("G","").replace("g","");
                usedMemoryYList.add(new BigDecimal(userMemory));
                memoryYList.add(new BigDecimal((softwareInfoVo.getMemoryPercent().replace(unit, ""))));
                timeXList.add(softwareInfoVo.getCreateTime());
            }
            cpuMap.put("x", timeXList);
            cpuMap.put("y", cpuYList);
            cpuMap.put("content", "cpu使用率");
            diskMap.put("x", timeXList);
            diskMap.put("y", diskYList);
            diskMap.put("content", "硬盘占用");
            memoryMap.put("x", timeXList);
            memoryMap.put("y", memoryYList);
            memoryMap.put("content", "内存使用率");
            usedMemoryMap.put("x",timeXList);
            usedMemoryMap.put("y",usedMemoryYList);
            usedMemoryMap.put("content","内存使用");
            historyResourceResult.put("cpuResource", cpuMap);
            historyResourceResult.put("diskResource", diskMap);
            historyResourceResult.put("memoryResource", memoryMap);
            historyResourceResult.put("usedMemoryResource", usedMemoryMap);
        }
        return historyResourceResult;
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        executorService.execute(() -> {
            try {
                indexService.getMonitorData();
                this.listAllSoftwares(null);
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        });
    }
}