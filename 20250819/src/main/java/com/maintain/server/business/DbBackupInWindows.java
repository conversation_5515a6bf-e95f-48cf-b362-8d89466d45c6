package com.maintain.server.business;

import com.maintain.server.Constants;
import com.maintain.server.config.DbBackupConfig;
import com.maintain.server.service.MysqlBackupService;
import com.maintain.server.type.OsType;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.DbBackupManageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
public class DbBackupInWindows extends DbBackup {

    public DbBackupInWindows(List<DbBackupManageVo> voList, List<AgentVo> agentVos,
                             int backupType, MysqlBackupService mysqlBackupService, DbBackupConfig backupConfig) {
        super(voList, agentVos, backupType, mysqlBackupService, backupConfig);
    }

    @Override
    protected void init() {
        rootPath = Constants.MAINTAIN_WINDOWS_TEMP;
        separator = Constants.WINDOWS_FILE_SEPARATOR;
        localOs = OsType.WINDOWS;
    }

    /**
     * 生成备份的命令
     *
     * @param dbVo           数据库相关信息
     * @param backupFilePath 备份文件存储的路径
     * @return
     */
    @Override
    protected String getBackupCommand(DbBackupManageVo dbVo, String backupFilePath) {
        return "cd deploy && " + " for /F %i IN ('" +
                "mysql" +
                " -h" + dbVo.getLocalIp() +
                " -u" + dbVo.getDbUser() +
                " -p" + dbVo.getDbPass() +
                " -e \"show databases;\"^|findStr /V \"Database\"^|findStr /V "
                + "\"information_schema\"^|findStr /V \"accounts\"^|findStr /V " +
                "\"performance_schema\"') do (" +
                "mysqldump" +
                " -h" + dbVo.getLocalIp() +
                " -R -E -u" + dbVo.getDbUser() + " -p" + dbVo.getDbPass() + " %i >> " + backupFilePath + "%i.sql)";
    }
}