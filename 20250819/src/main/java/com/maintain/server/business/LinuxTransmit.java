package com.maintain.server.business;

import com.common.log.Log;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.websocket.BaseWebSocket;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
public class LinuxTransmit extends MachineTransmit {

    public LinuxTransmit(AgentVo agentVo) {
        super(agentVo);
    }

    @Override
    public boolean transmitDirectory(String srcPath, String destPath) {
        try {
            Session session = BaseWebSocket.getSession(agentVo);
            ProcessUtil.initChannelShell(session, agentVo, 3600000L);

            String dirExistInLinuxCommand = "if [ ! -d \"" + destPath + "\" ]; then  mkdir -p \"" + destPath + "\"; fi;";
            ProcessUtil.execShellGetResult(dirExistInLinuxCommand);
            ProcessUtil.execShellGetResult("chmod -R 777 " + destPath);

            ChannelSftp sftp = ProcessUtil.openChannelSftp(session);
            Log.low.info("tempPath:" + srcPath + ", remotePath:" + destPath);
            ProcessUtil.uploadFileToRemoteLinux(sftp, srcPath, destPath);
        } catch (Exception e) {
            Log.high.error("传输数据库备份文件出错", e);
            return false;
        } finally {
            try {
                ProcessUtil.destroyChannelShell(agentVo);
            } catch (IOException e) {
                Log.high.error("清理" + agentVo.getIp() + " ssh连接出错", e);
            }
            ProcessUtil.destroy();
        }
        return true;
    }
}