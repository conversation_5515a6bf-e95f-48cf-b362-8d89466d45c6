package com.maintain.server.business;

import com.common.log.Log;
import com.jcraft.jsch.Session;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.websocket.BaseWebSocket;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
public class WindowsTransmit extends MachineTransmit {

    public WindowsTransmit(AgentVo agentVo) {
        super(agentVo);
    }

    @Override
    public boolean transmitDirectory(String srcPath, String destPath) {
        try {
            Session session = BaseWebSocket.getSession(agentVo);
            ProcessUtil.initChannelShell(session, agentVo, 3600000L);

            String dirExistInWindowsCommand = "if not exist \"" + destPath + "\" (mkdir \"" + destPath + "\")";
            ProcessUtil.exeCommand(session, dirExistInWindowsCommand);

            Map<String, Object> map = new HashMap<>(4);
            map.put("tempPath", srcPath);
            map.put("remoteTempPath", destPath);
            Log.low.info("tempPath:" + srcPath + ", remotePath" + destPath);
            ProcessUtil.uploadFileToRemoteWindows(map, session);
        } catch (Exception e) {
            Log.high.error("传输数据库备份文件出错", e);
            return false;
        } finally {
            try {
                ProcessUtil.destroyChannelShell(agentVo);
            } catch (IOException e) {
                Log.high.error("清理" + agentVo.getIp() + " ssh连接出错", e);
            }
            ProcessUtil.destroy();
        }
        return true;
    }
}