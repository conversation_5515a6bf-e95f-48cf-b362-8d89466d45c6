package com.maintain.server.schedule;

import com.common.log.Log;
import com.jcraft.jsch.Session;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.mapper.AnalysisMapper;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.AnalysisVo;
import com.maintain.server.websocket.BaseWebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class AnalysisProcessSchedule {

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private AnalysisMapper analysisMapper;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void refreshAnalysisStatus(){
        List<AgentVo> list = agentMapper.getAgent(new AgentCriteria());
        final Date date = new Date();
        list.stream().filter(l -> l.getRole().contains("分析机")).forEach(l -> {
            try {
                execute(l,date);
            } catch (Exception e) {
                Log.high.error(e);
            }
        });
    }

    private void execute(AgentVo agentVo, Date date) throws Exception {
        try {
            Session session = BaseWebSocket.getSession(agentVo);
            ProcessUtil.initChannelShell(session,agentVo,60000L);
            String result = ProcessUtil.execShellGetResult("wmic process where caption=\"vmware-vmx.exe\" get processid");
            Pattern pattern = Pattern.compile("\\d+");
            AtomicInteger atomicInteger = new AtomicInteger(0);
            for (String s : result.split("\n")) {
                final Matcher matcher = pattern.matcher(s);
                if(matcher.find()){
                    atomicInteger.incrementAndGet();
                }
            }
            int count = atomicInteger.get();
            AnalysisVo analysisVo = new AnalysisVo();
            analysisVo.setIp(agentVo.getIp());
            analysisVo.setProcess(count);
            analysisVo.setStatus(count == 0?AlarmStatusType.RED:AlarmStatusType.GREEN);
            analysisVo.setUpdateTime(date);
            analysisMapper.addAnalysis(analysisVo);
        }finally {
            ProcessUtil.destroyChannelShell(agentVo);
        }
    }

}

