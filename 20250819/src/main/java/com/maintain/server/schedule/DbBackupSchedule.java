package com.maintain.server.schedule;

import com.common.log.Log;
import com.maintain.server.config.DbBackupConfig;
import com.maintain.server.service.DbBackupManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020-12-22
 * 数据库定时备份任务
 */
@Component
public class DbBackupSchedule {

    private DbBackupManageService dbBackupManageService;

    private DbBackupConfig backupConfig;

    @Autowired
    public DbBackupSchedule(DbBackupManageService dbBackupManageService, DbBackupConfig backupConfig) {
        this.dbBackupManageService = dbBackupManageService;
        this.backupConfig = backupConfig;
    }

    /**
     * 每日凌晨两点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    //@Scheduled(cron = "30 * * * * ?")  //测试
    public void runPerDay() {
        Log.low.info("开始进行每日数据库备份");
        try {
            if (!backupConfig.isOpen()) {
                Log.low.info("未开启数据库备份功能");
                return;
            }
            dbBackupManageService.dbBackup(0);
        } catch (Exception e) {
            Log.high.error("每日备份数据库异常", e);
        }
        Log.low.info("每日数据库备份完成");
    }

    /**
     * 每周日晚上10点执行
     */
    @Scheduled(cron = "0 0 22 ? * SUN")
    public void runPerWeek() {
        Log.low.info("开始进行每周数据库备份");
        try {
            if (!backupConfig.isOpen()) {
                Log.low.info("未开启数据库备份功能");
                return;
            }
            dbBackupManageService.dbBackup(1);
        } catch (Exception e) {
            Log.high.error("每周备份数据库异常", e);
        }
        Log.low.info("每周数据库备份完成");
    }



}