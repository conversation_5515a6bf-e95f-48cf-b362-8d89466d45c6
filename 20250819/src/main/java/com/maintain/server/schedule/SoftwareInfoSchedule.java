package com.maintain.server.schedule;

import com.common.log.Log;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.mapper.SoftwareHistoryMapper;
import com.maintain.server.mapper.SoftwareMapper;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.SoftwareVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-03-07
 */
@Component
public class SoftwareInfoSchedule {

    @Value("${heartbeatTimeout}")
    private Integer heartbeatTimeout;

    @Value("${restart-count-monitor.red}")
    private Integer restartCountRed;

    @Value("${restart-count-monitor.yellow}")
    private Integer restartCountYellow;


    @Autowired
    private SoftwareMapper softwareMapper;

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private SoftwareHistoryMapper historyMapper;

    @Scheduled(cron = "0/30 * * * * ?")
    public void refreshSoftwareInfo() {
        healthMonitor();
    }

    private void healthMonitor() {
        List<SoftwareVo> softwareVoList = softwareMapper.getSoftwareInfos(new SoftwareCriteria());
        long start = System.currentTimeMillis();
        judgeSoftwareVoStatus(softwareVoList);
        //Log.low.info("judge software health timeout(ms):" + (System.currentTimeMillis() - start));
    }

    private void judgeSoftwareVoStatus(List<SoftwareVo> softwareVoList) {
        try {
            //内存、CPU使用的判断
            for (SoftwareVo soft : softwareVoList) {
                final SoftwareVo clone = soft.clone();
                soft.setStatus(null);
                if (soft.getProgramStatus().equals(ProgramStatusType.CLOSE)) {
                    continue;
                }
                final StringJoiner sj = new StringJoiner(",", "", "");
                if (!judgeConnStatus(soft.getServerIp())) {
                    soft.setStatus(AlarmStatusType.RED);
                    sj.add("无法连接agent服务");
                } else {
                    if (soft.getIsExists() == 0) {
                        soft.setStatus(AlarmStatusType.RED);
                        sj.add("启动脚本不存在");
                    } else {
                        if (soft.getLogCount() >= 2) {
                            soft.setStatus(AlarmStatusType.YELLOW);
                            sj.add("当前产生 " + soft.getLogCount() + " 个error日志");
                        }
                        AlarmStatusType red = AlarmStatusType.RED;
//                final Integer integer = historyMapper.countRestartHistory(soft.getId());
                        Integer restartCount = soft.getRestartCount();
                        if (Objects.isNull(restartCount)) {

                        } else if (restartCount >= restartCountYellow && restartCount < restartCountRed) {
                            sj.add("当前重启次数异常：" + restartCount);
                            soft.setStatus(AlarmStatusType.YELLOW);
                        } else if (restartCount >= restartCountRed) {
                            sj.add("当前重启次数异常：" + restartCount);
                            soft.setStatus(red);
                        }
                        if (soft.getHeartMonitor()) {
                            //信息详情
                            Map<String, AlarmStatusType> alarmStatusTypeMap = new HashMap<>();
                            if (soft.getCpuPercent() == null && soft.getMemoryPercent() == null && soft.getLastHeartbeatTime() == null) {
                                soft.setStatus(red);
                                sj.add("监控信息不存在");
                                alarmStatusTypeMap.put("cpuPercent", red);
                                alarmStatusTypeMap.put("memoryPercent", red);
                                alarmStatusTypeMap.put("lastHeartbeatTime", red);
                            }
                            if (soft.getCpuPercent() == null) {
                                sj.add("cpu使用率为空");
                                soft.setStatus(red);
                                alarmStatusTypeMap.put("cpuPercent", red);
                            }
                            if (soft.getMemoryPercent() == null) {
                                sj.add("内存使用率为空");
                                soft.setStatus(red);
                                alarmStatusTypeMap.put("memoryPercent", red);
                            }
                            if (soft.getLastHeartbeatTime() == null) {
                                sj.add("心跳为空");
                                soft.setStatus(red);
                                alarmStatusTypeMap.put("lastHeartbeatTime", red);
                            } else if ((System.currentTimeMillis() - soft.getLastHeartbeatTime().getTime()) >= heartbeatTimeout * 1000) {
                                soft.setStatus(red);
                                sj.add("心跳超时，阈值为：" + heartbeatTimeout + "s");
                                alarmStatusTypeMap.put("runningTime", red);
                            }
                        }
                    }
                }


                if (soft.getStatus() == null || "logstash".equals(soft.getName())) {
                    soft.setStatus(AlarmStatusType.GREEN);
                    soft.setDescription("良好");
                } else {
                    soft.setDescription(sj.toString());
                }
                if (!clone.equals(soft)) {
                    soft.setPid(null);
                    soft.setLogCount(null);
                    soft.setIsExists(null);
                    soft.setRestartCount(null);
                    soft.setRestartTime(null);
                    soft.setStartType(null);
                    soft.setProgramStatus(null);
                    soft.setProcessCount(null);
                    soft.setHeartMonitor(null);
                    softwareMapper.updateSoftwareInfo(soft);
                }
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    private boolean judgeConnStatus(String serverIp) {
        try {
            final String result = ProcessUtil.iceRequest(serverIp, IceFlag.PONG, null);
            if (Objects.isNull(result)) {
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            return false;
        }
    }

}