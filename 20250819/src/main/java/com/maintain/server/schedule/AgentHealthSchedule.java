package com.maintain.server.schedule;

import com.common.log.Log;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.maintain.server.Constants;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.service.WebSocketService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.SoftwareOperateType;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.utils.ServerRequestUtil;
import com.maintain.server.vo.AgentVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.concurrent.*;

@Component
public class AgentHealthSchedule {

    @Autowired
    private ApplicationContext application;

    @Autowired
    private AgentMapper agentMapper;

    @Value("${ntpServer}")
    private String ntpServer;

    @Value("${heartbeatTimeout}")
    private Long heartbeatTimeout;

    private ThreadPoolExecutor service = new ThreadPoolExecutor(5, 10, 120, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), r -> {
        final Thread thread = new Thread(r);
        thread.setName("agentHealthCallThread");
        return thread;
//        new ThreadPoolExecutor.AbortPolicy()
    }, (task, ser) -> {
        Log.low.warn("agentHealthCallThread线程池耗尽...............");
    });

    @Scheduled(cron = "0 0/2 * * * ?")
    //@Scheduled(cron = "*/10 * * * * ?")  //测试
    public void agentHealth() {
        WebSocketService webSocketService = application.getBean("agentWebSocketService", WebSocketService.class);
        List<AgentVo> agentVoList;
        try {
            agentVoList = agentMapper.getAgent(new AgentCriteria());
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            return;
        }
        String timeStr = ProcessUtil.iceRequest(ntpServer, IceFlag.TIME, null);
        Long time = StringUtils.isNumeric(timeStr) ? Long.valueOf(timeStr) : null;
        final long now = System.currentTimeMillis();
        for (AgentVo agentVo : agentVoList) {
            try {
                //Log.low.info("检测 " + agentVo.getIp() + " agent");
                AgentVo clone = null;
                agentVo.setPid(null);
                try {
                    clone = agentVo.clone();
                } catch (CloneNotSupportedException e) {
                    Log.high.error(e.getMessage(), e);
                    continue;
                }
                if (agentVo.getProgramStatus().equals(ProgramStatusType.CLOSE)) {
                    agentVo.setStatus(AlarmStatusType.GRAY);
                    agentVo.setDescription("agent已关闭");
                    if (!agentVo.equals(clone)) {
                        agentVo.setProgramStatus(null);
                        agentVo.setOperateType(null);
                        agentMapper.updateAgent(agentVo);
                    }
                    continue;
                }
                agentVo.setStatus(AlarmStatusType.GREEN);
                agentVo.setOperateType(SoftwareOperateType.CLOSE);
                final StringJoiner sj = new StringJoiner(",", "", "");
                try {
                    final String result = iceRequest(agentVo.getIp(), IceFlag.PONG, null);
                    if (Objects.isNull(result)) {
                        Callable<Boolean> call = () -> {
                            try {
                                final JSch jSch = new JSch();
                                Session session = jSch.getSession(agentVo.getName(), agentVo.getIp(), agentVo.getPort());
                                session.setPassword(agentVo.getPswd());
                                session.setConfig("StrictHostKeyChecking", "no");
                                session.connect(Constants.CONNECT_TIMEOUT);
                                ProcessUtil.initChannelShell(session, agentVo, 60000L);
                                ProcessUtil.agentLive(agentVo);
                                return true;
                            } catch (Exception e1) {
                                return false;
                            } finally {
                                ProcessUtil.destroyChannelShell(agentVo);
                            }
                        };
                        final Future<Boolean> booleanFutureTask = service.submit(call);
                        try {
                            Boolean aBoolean = booleanFutureTask.get(65, TimeUnit.SECONDS);
                            agentVo.setConnect(aBoolean);
                        } catch (Exception e1) {
                            Log.low.warn("检测 " + agentVo.getIp() + " 远程连接超时");
                            booleanFutureTask.cancel(true);
                            //只有接收到正常返回的boolean值才设置为false
                            agentVo.setConnect(Boolean.TRUE);
                        }
                    } else {
                        agentVo.setConnect(Boolean.TRUE);
                    }
                } catch (Exception e) {
                    agentVo.setConnect(Boolean.FALSE);
                    Log.high.error("connect " + agentVo.getIp() + " agent exception", e);
                }
                String result = ProcessUtil.iceRequest(agentVo.getIp(), IceFlag.TIME, null);
                if (agentVo.getNtpStatus() != null) {
                    if (agentVo.getNtpStatus() == 0) {
                        sj.add("ntp服务异常");
                        agentVo.setStatus(AlarmStatusType.RED);
                    } else {
                        if (StringUtils.isNumeric(result) && time != null) {
                            Long t = Long.valueOf(result);
                            long x = t - time;
                            if (x > 300000 || x < -300000) {
                                sj.add("服务器时间与ntpServer时差超过5分钟");
                                agentVo.setStatus(AlarmStatusType.RED);
                            }
                        }
                    }
                }
                if (agentVo.getConnect() == null || !agentVo.getConnect()) {
                    sj.add("无法远程连接");
                    agentVo.setStatus(AlarmStatusType.RED);
                }
                final Date lastHeartbeatTime = agentVo.getLastHeartbeatTime();
                if (lastHeartbeatTime == null) {
                    sj.add("心跳为空");
                    agentVo.setStatus(AlarmStatusType.RED);
                } else {
                    if ((now - lastHeartbeatTime.getTime()) >= heartbeatTimeout * 1000) {
                        if ((now - lastHeartbeatTime.getTime()) >= 15 * 60 * 1000) {
                            agentVo.setStatus(AlarmStatusType.RED);
                            sj.add("超过 " + 15 + " 分钟没有心跳");
                        } else {
                            sj.add("超过 " + heartbeatTimeout / 60 + " 分钟没有心跳");
                            if (AlarmStatusType.RED != agentVo.getStatus()) {
                                agentVo.setStatus(AlarmStatusType.YELLOW);
                            }
                        }
                        agentVo.setOperateType(SoftwareOperateType.OPEN);
                    }
                }
                agentVo.setDescription(sj.toString());
                if (!agentVo.equals(clone)) {
                    try {
                        agentVo.setProgramStatus(null);
                        agentVo.setOperateType(null);
                        agentMapper.updateAgent(agentVo);
                    } catch (Exception e) {
                        Log.high.error(e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                Log.low.info("当前正在部署其他节点，定时任务暂停执行....");
                return;
            }
        }
    }

    private String iceRequest(String agentIp, int flag, String param) throws ServiceException {
        try {
            return ServerRequestUtil.sendRequest(agentIp, flag, param);
        } catch (Exception e) {
            String msg;
            if (StringUtils.isNotEmpty(e.getMessage())) {
                msg = e.getMessage() + " " + agentIp;
            } else {
                msg = agentIp;
            }
            Log.high.error(agentIp + msg + " Agent连接失败", e);
            return null;
        }
    }
}
