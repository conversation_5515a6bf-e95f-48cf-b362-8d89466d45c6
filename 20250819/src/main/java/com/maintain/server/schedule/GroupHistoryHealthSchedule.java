package com.maintain.server.schedule;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.log.Log;
import com.maintain.server.service.GroupHistoryService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.GroupType;
import com.maintain.server.vo.GroupHistoryHealthVo;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.maintain.server.type.GroupType.*;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Component
public class GroupHistoryHealthSchedule {

    @Value("${ambari.address:***************:8080}")
    private String address;

    @Value("${ambari.auth:admin:Cestc_01}")
    private String auth;

    @Value("${es.secondes.enable}")
    private Boolean enableSecondEs;

    @Autowired
    private GroupHistoryService groupHistoryService;

    @Scheduled(cron = "0 0/10 * * * ?")
    public void historyHealthSchedule() {
        Thread.currentThread().setName("GroupHistoryHealth-Thread");

        List<GroupHistoryHealthVo> list = queryServiceHealth();

        groupHistoryService.addGroupHistoryHealth(list);

    }

    public List<GroupHistoryHealthVo> queryServiceHealth() {
        List<GroupHistoryHealthVo> list = new ArrayList<>();

        List<GroupType> groupTypeList = Arrays.asList(ES, HDFS, YARN, HIVE, HBASE, ZK, KAFKA, RANGER, FLINK, SPARK, TRINO);

        for (GroupType groupType : groupTypeList) {
            Map<AlarmStatusType, String> groupTypeMap = queryStatusForService(groupType.getName());
            list.add(setHistoryHealthInfo(groupTypeMap, groupType));
        }
        return list;
    }

    private Map<AlarmStatusType, String> queryStatusForService(String service) {
        Map<AlarmStatusType, String> result = new HashMap<>(8);
        String urlFormat = "http://%s/api/v1/clusters/CIE_NJ/services/%s/alerts?fields=Alert/component_name,Alert/definition_id,Alert/definition_name&Alert/state.in(CRITICAL,WARNING)";
        String url = String.format(urlFormat, address, service);
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            byte[] encodeAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodeAuth);
            httpGet.setHeader("Authorization", authHeader);

            CloseableHttpResponse response = client.execute(httpGet);
            String value = EntityUtils.toString(response.getEntity());
            JSONObject obj = JSONObject.parseObject(value);
            JSONArray items = obj.getJSONArray("items");
            if (items.isEmpty()) {
                result.put(AlarmStatusType.GREEN, AlarmStatusType.GREEN.getValue());

            } else {
                String status = "";
                String reason = "";
                for (int i = 0; i < items.size(); i++) {
                    JSONObject item = items.getJSONObject(i);
                    JSONObject alert = item.getJSONObject("Alert");
                    String state = alert.getString("state");
                    if ("".equals(status)) {
                        status = state;
                        reason = alert.getString("definition_name");
                    } else if ("CRITICAL".equals(state)) {
                        //最高告警，直接返回
                        status = state;
                        reason = alert.getString("definition_name");
                        break;
                    }
                }
                if ("WARNING".equals(status)) {
                    result.put(AlarmStatusType.YELLOW, reason);
                } else {
                    result.put(AlarmStatusType.RED, reason);
                }
            }
        } catch (Exception e) {
            Log.high.error("check " + service + " status error", e);
            result.put(AlarmStatusType.GRAY, "");
        }

        return result;
    }

    private GroupHistoryHealthVo setHistoryHealthInfo(Map<AlarmStatusType, String> map, GroupType type) {
        GroupHistoryHealthVo groupHistoryHealthVo = new GroupHistoryHealthVo();
        groupHistoryHealthVo.setName(type.getName());
        groupHistoryHealthVo.setGroupId(type.getId());
        if (map == null || map.isEmpty()) {
            groupHistoryHealthVo.setDescription("未知");
            groupHistoryHealthVo.setStatus(AlarmStatusType.RED.getId());
            return groupHistoryHealthVo;
        }
        for (Map.Entry<AlarmStatusType, String> entry : map.entrySet()) {
            groupHistoryHealthVo.setDescription(entry.getValue());
            groupHistoryHealthVo.setStatus(entry.getKey().getId());
        }
        groupHistoryHealthVo.getId();
        return groupHistoryHealthVo;
    }
}