package com.maintain.server.schedule;

import com.common.log.Log;
import com.maintain.server.Constants;
import com.maintain.server.service.IndexService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.utils.BaseConfigUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class MaintainHealthMonitorSchedule {

    @Autowired
    private IndexService indexService;

    @Value("${maintainHealth.isTurnOn}")
    private boolean isTurnOn;

    @Value("${maintainHealth.platform}")
    private String platform;

    @Value("${maintainHealth.monitoringDirPath}")
    private String monitoringDirPath;

    private SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat(Constants.YYYYMMDDHH);
    private SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat(Constants.YYYYMMDDHHMMSS);

    public MaintainHealthMonitorSchedule(){
    }

    @Scheduled(cron = "0 0 */1 * * ?")
    //@Scheduled(cron = "*/5 * * * * ?")
    public void maintainHealth() {
        if (isTurnOn){
            List<Map<String, Object>> allModulMonitorData = indexService.getMonitorData();
            String content = "";
            Date date = new Date();
            String formatStr = simpleDateFormat1.format(date);
            String timeStr = simpleDateFormat2.format(date).substring(0,10) + "0000";
            String platformCode = BaseConfigUtil.platformMap.get(platform);

        /*int[] arr = {1,3,9,11,12,16,18,19,20,22,30};
        int index = (int)(Math.random()*arr.length);
        int code = arr[index];
        String platformCode = String.valueOf(code);*/

            for(Map<String, Object> modulMonitorData : allModulMonitorData){
                long errorNum = 0;//错误数量
                long alarmNum = 0;//报警数量
                long healthNum = 0;//良好数量
                Map<String, AlarmStatusType> statusTypeMap = (Map<String, AlarmStatusType>) modulMonitorData.get("status");
                for (AlarmStatusType value : statusTypeMap.values()){
                    String strValue = value.getValue();
                    switch (strValue){
                        case "GREEN":
                            healthNum++;
                            break;
                        case "RED":
                            errorNum++;
                            break;
                        case "YELLOW":
                            alarmNum++;
                            break;
                        default:
                            break;
                    }
                }
                content += BaseConfigUtil.moduleMap.get(modulMonitorData.get("moduleName").toString()) + " " + errorNum + " " + alarmNum + " " + healthNum + "\r\n";
            }

            content =  timeStr + "\r\n" + platformCode + "\r\n"+ content;
            //content =  "**********0000" + "\r\n" + platformCode + "\r\n"+ content;
            //加密
            //byte[] encryptContent = Base64Utils.encode(content.getBytes());
            String md5Hex = DigestUtils.md5Hex(content);
            String fileName = "1-" + formatStr + "-" + md5Hex ;
            //String fileName = "1-" + "**********" + "-" + md5Hex ;
            final File file = new File(monitoringDirPath);
            String filePath = monitoringDirPath + File.separator + fileName;
            try {
                if(!file.exists()){
                    file.mkdirs();
                }
                FileWriter fileWriter = new FileWriter(filePath,true);
                fileWriter.write(content);
                fileWriter.close();
                Log.low.info("生成运维系统监控文件： " + fileName);
            } catch (IOException e) {
                Log.low.info("文件写入异常",e);
            }
        }
    }
}
