package com.maintain.server.schedule;

import com.common.log.Log;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.maintain.server.Constants;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.criteria.PingResultCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.PingResultMapper;
import com.maintain.server.service.HardwareService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.HardwareHistoryHealth;
import com.maintain.server.vo.HardwareVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@Component
public class HardwareHistoryHealthSchedule implements Constants {

    @Autowired
    private HardwareService hardwareService;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void historyHealthSchedule() {
        try {
            Thread.currentThread().setName("HardwareHistoryHealth-Thread");
            HardwareCriteria criteria = new HardwareCriteria();

            List<HardwareVo> list = hardwareService.getHardwareInfos(criteria);
            if (ListUtil.isNotEmpty(list)) {
                List<HardwareHistoryHealth> result = new ArrayList<>();
                for (HardwareVo vo : list) {
                    result.add(setHistoryHealth(vo));
                }
                hardwareService.addHardwareHistoryHealth(result);
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    private HardwareHistoryHealth setHistoryHealth(HardwareVo vo) {
        HardwareHistoryHealth historyHealth = new HardwareHistoryHealth();
        historyHealth.setStatus(vo.getStatus());
        historyHealth.setAgentId(vo.getAgentId());
        historyHealth.setDescription(vo.getDescription());
        historyHealth.setCpuPercent(vo.getUsedCpu());
        historyHealth.setMemoryPercent(vo.getMemoryUsedPercent());
        historyHealth.setUsedDisk(vo.getUsedDisk());
        historyHealth.setUsedMemory(vo.getUsedMemory());
        historyHealth.setDiskPercent(vo.getDiskUsedPercent());
        historyHealth.setReceiveBytePerSecond(vo.getReceiveBytePerSecond());
        historyHealth.setSendBytePerSecond(vo.getSendBytePerSecond());
        return historyHealth;
    }
}
