package com.maintain.server.schedule;

import com.alibaba.fastjson.JSON;
import com.common.log.Log;
import com.maintain.server.config.DbBackupConfig;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.DbBackupManageCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.ice.request.CheckDbBackupRequest;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.mapper.DbBackupManageMapper;
import com.maintain.server.service.MysqlBackupService;
import com.maintain.server.type.OsType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.DbBackupManageVo;
import com.maintain.server.vo.MysqlBackupVo;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.maintain.server.Constants.MYSQL_BACKUP_DAILY;
import static com.maintain.server.Constants.MYSQL_BACKUP_WEEKLY;

/**
 * <AUTHOR>
 * @date 2020-06-11
 */
@Component
public class MysqlBackupMonitorSchedule {

    @Autowired
    private MysqlBackupService mysqlBackupService;

    @Autowired
    private DbBackupManageMapper dbBackupManageMapper;

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private DbBackupConfig backupConfig;


    /**
     * 获取数据库备份情况
     *
     * @param a
     * @param backupPath
     * @return
     */
    private List<MysqlBackupVo> getMysqlBackup(AgentVo a, String backupPath, int backupType) {
        CheckDbBackupRequest request = new CheckDbBackupRequest(backupPath, backupType, DateUtil.currentDay());
        String result = ProcessUtil.iceRequest(a.getIp(), IceFlag.CHECK_DB_BACKUP, JSON.toJSONString(request));
        if (result == null) {
            throw new ServiceException("请求" + a.getIp() + "Agent失败");
        }
        ResponseVo responseVo = JsonUtil.parseObject(result, ResponseVo.class);
        if (responseVo == null) {
            throw new ServiceException(result + "解析失败");
        }
        if (responseVo.getCode() != null && responseVo.getCode() == 0) {
            List<MysqlBackupVo> list = JSON.parseArray(JSON.toJSONString(responseVo.getData()), MysqlBackupVo.class);
            if (list != null) {
                list.forEach(vo -> {
                    vo.setBackupType(0);
                    vo.setIp(a.getIp());
                });
            }
            return list;
        }
        throw new ServiceException(responseVo.getMsg());
    }

    //每天01:00:00执行
    //@Scheduled(cron = "*/10 * * * * ?")
    @Scheduled(cron = "0 0 5 * * ?") //按天备份是在每天凌晨2点或3点进行，故每天凌晨5点执行一次
    public void monitorMysqlDailyBackup() {
        try {
            if (!backupConfig.isOpen()) {
                Log.low.info("未开启数据库备份功能");
                return;
            }
            Log.low.info("开始检测数据库每日备份的结果");
            checkDbBackup(0);
            Log.low.info("检测数据库每日备份的结果完成");
        } catch (Exception e) {
            Log.high.error("数据库备份每日检测失败", e);
        }
    }

    private void checkDbBackup(int backupType) {
        List<DbBackupManageVo> dbBackupManageVos = dbBackupManageMapper.selectList(new DbBackupManageCriteria());
        if (dbBackupManageVos.isEmpty()) {
            Log.low.info("没有配置需要备份的数据库");
            return;
        }
        for (DbBackupManageVo dbVo : dbBackupManageVos) {
            checkDbBackup(backupType, dbVo);
        }
    }

    private void checkDbBackup(int backupType, DbBackupManageVo dbVo) {
        try {
            //获取本机备份
            List<AgentVo> agentVos = agentMapper.getAgent(new AgentCriteria());
            String currentDay = DateUtil.currentDay();
            List<MysqlBackupVo> localList = new ArrayList<>();
            Optional<AgentVo> agentVoOptional = agentVos.stream().filter(a -> a.getIp().equals(dbVo.getLocalIp()))
                    .findAny();
            if (agentVoOptional.isPresent()) {
                AgentVo a = agentVoOptional.get();
                String backupPath = getBackupPath(a.getOs(), backupType, dbVo.getLocalIp(), currentDay, dbVo.getLocalPath());
                try {
                    localList = getMysqlBackup(a, backupPath, backupType);
                } catch (ServiceException e) {
                    MysqlBackupVo errorVo = new MysqlBackupVo();
                    errorVo.setRemoteSuccess(0);
                    errorVo.setIp(a.getIp());
                    errorVo.setBackupType(backupType);
                    errorVo.setBackupDatabases(e.getMessage());
                    errorVo.setBackupTime(currentDay);
                    mysqlBackupService.insertMysqlBackup(Collections.singletonList(errorVo));
                }
            }
            //获取异机备份
            List<MysqlBackupVo> remoteList = new ArrayList<>();
            Optional<AgentVo> remoteAgentVoOptional = agentVos.stream().filter(a -> a.getIp().equals(dbVo.getRemoteIp()))
                    .findAny();
            if (remoteAgentVoOptional.isPresent()) {
                AgentVo a = remoteAgentVoOptional.get();
                String backupPath = getBackupPath(a.getOs(), backupType, dbVo.getLocalIp(), currentDay, dbVo.getRemotePath());
                try {
                    remoteList = getMysqlBackup(a, backupPath, backupType);
                } catch (ServiceException e) {
                    //当作异机备份失败
                    Log.high.error("异机备份记录获取失败");
                }
            }
            //比较二者差异
            if (localList != null && !localList.isEmpty()) {
                if (remoteList == null || remoteList.isEmpty()) {
                    localList.forEach(t -> t.setRemoteSuccess(0));
                } else {
                    for (MysqlBackupVo vo : localList) {
                        vo.setRemoteSuccess(remoteList.contains(vo) ? 1 : 0);
                    }
                }
                mysqlBackupService.insertMysqlBackup(localList);
            }
        } catch (Exception e) {
            Log.high.error("check dbbackup failed", e);
        }
    }

    @Scheduled(cron = "0 59 23 ? * SUN") //按周备份是在每周日晚22点进行，故在每周日晚23点59分执行
    public void monitorMysqlWeeklyBackup() {
        try {
            Log.low.info("开始检测数据库每周备份的结果");
            checkDbBackup(1);
            Log.low.info("检测数据库每周备份的结果完成");
        } catch (Exception e) {
            Log.high.error("数据库备份每周检测失败", e);
        }
    }


    private String getBackupPath(Integer osType, int backupType, String ip, String date, String backupRootPath) {
        if (OsType.WINDOWS.getId().equals(osType)) {
            return backupRootPath + (backupType == 0 ? MYSQL_BACKUP_DAILY : MYSQL_BACKUP_WEEKLY) +
                    "\\" + ip + "\\" + date + "\\";
        } else {
            return backupRootPath + (backupType == 0 ? MYSQL_BACKUP_DAILY : MYSQL_BACKUP_WEEKLY) +
                    "/" + ip + "/" + date + "/";
        }
    }

    public void setMysqlBackupService(MysqlBackupService mysqlBackupService) {
        this.mysqlBackupService = mysqlBackupService;
    }

    public void setDbBackupManageMapper(DbBackupManageMapper dbBackupManageMapper) {
        this.dbBackupManageMapper = dbBackupManageMapper;
    }

    public void setAgentMapper(AgentMapper agentMapper) {
        this.agentMapper = agentMapper;
    }
}