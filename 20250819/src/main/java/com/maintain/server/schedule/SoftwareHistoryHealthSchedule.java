package com.maintain.server.schedule;

import com.common.log.Log;
import com.github.pagehelper.PageInfo;
import com.maintain.server.Constants;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.service.SoftwareHistoryService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.SoftwareOperateType;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.SoftwareVo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-22
 */
@Component
public class SoftwareHistoryHealthSchedule implements Constants {

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private SoftwareHistoryService softwareHistoryService;

    /**
     * 软件历史监控
     */
    @Scheduled(cron = "0 0/15 * * * ?")
    public void historyHealthSchedule() {
        //Log.low.info("开始进行软件资源使用情况的收集");
        Thread.currentThread().setName("SoftwareHistoryHealth-Thread");
        SoftwareCriteria softwareCriteria = new SoftwareCriteria();
        try {
            PageInfo<SoftwareVo> pageInfo = softwareService.getSoftwareInfos(softwareCriteria);
            if (pageInfo == null || ListUtil.isEmpty(pageInfo.getList())) {
                return;
            }
            List<SoftwareVo> softwareInfoVoList = pageInfo.getList();
            Iterator<SoftwareVo> iterator = softwareInfoVoList.iterator();
            while (iterator.hasNext()) {
                SoftwareVo softwareVo = iterator.next();
                if (softwareVo == null || softwareVo.getProgramStatus().equals(ProgramStatusType.CLOSE)) {
                    iterator.remove();
                    continue;
                }
                softwareVo.setTotalDisk(String.valueOf(softwareVo.getProgramSize()));
                try {
                    String usedMemory = softwareVo.getUsedMemory();
                    if (StringUtils.isEmpty(softwareVo.getUsedMemory())) {
                        continue;
                    }
                    if (usedMemory.contains("G") || usedMemory.contains("g")) {
                        continue;
                    }
                    usedMemory = usedMemory.replace("M", "").replace("m", "");
                    usedMemory = new BigDecimal(usedMemory).divide(new BigDecimal(1024), 2, BigDecimal.ROUND_HALF_UP).toString() + "G";
                    softwareVo.setUsedMemory(usedMemory);
                } catch (Exception e) {
                    Log.high.error(e.getMessage(), e);
                }
            }

            softwareHistoryService.addSoftwareResource(softwareInfoVoList);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
        //Log.low.info(startTime + "\t" + endTime + " 该阶段软件信息收集完成");
    }
}