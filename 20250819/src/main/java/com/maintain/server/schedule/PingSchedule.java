package com.maintain.server.schedule;

import com.alibaba.fastjson.JSON;
import com.common.log.Log;
import com.common.util.ObjectMapperUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.mapper.PingResultMapper;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.PingResponse;
import com.maintain.server.vo.PingTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-11-20
 */
@Component
@Slf4j
public class PingSchedule {

    @Value("${ping.timeout}")
    private Integer pingTimeout;

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private PingResultMapper pingResultMapper;

    @Scheduled(cron = "0/30 * * * * ?")
    public void pingAllAgent() {
        try {
            AgentCriteria agentCriteria = new AgentCriteria();
            agentCriteria.setProgramStatus(ProgramStatusType.OPEN.getValue());
            List<AgentVo> agentVoList = agentMapper.getAgent(agentCriteria);
            List<String> agentIpList = agentVoList.stream().filter(f ->  f != null).map(AgentVo::getIp).collect(Collectors.toList());
            PingTask task = new PingTask();
            task.setAgentList(agentIpList);
            task.setCallTime(DateUtil.format(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS));
            //获取30天前现在的时间
            String overdueTime = DateUtil.formDate(LocalDateTime.now().minusDays(30));
            if (pingTimeout != null) {
                task.setTimeLimit(pingTimeout);
            }
            String param;
            try {
                param = ObjectMapperUtil.objectMapper.writeValueAsString(task);
            } catch (JsonProcessingException e) {
                Log.high.error("参数序列化失败,参数：" + task, e);
                return;
            }
            for (String ip : agentIpList) {
                String result = ProcessUtil.iceRequest(ip, IceFlag.PONG_ALL_AGENT, param);
                handleResult(ip, result);
            }
            pingResultMapper.deleteOverdueData(overdueTime);
        } catch (Exception e) {
            Log.high.error("ping agent exception", e);
        }
    }

    private void handleResult(String ip, String result) {
        if (result == null) {
            return;
        }
        PingResponse resp = JSON.parseObject(result, PingResponse.class);
        if (resp.getCode() == 0) {
            if (resp.getData() != null && !resp.getData().isEmpty()) {
                pingResultMapper.batchInsert(resp.getData());
            }
        } else {
            Log.high.error("向" + ip + "下发任务失败");
        }
    }
}