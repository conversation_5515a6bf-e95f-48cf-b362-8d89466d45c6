package com.maintain.server.datasource.druid;

import com.alibaba.druid.filter.logging.Log4j2Filter;
import com.alibaba.druid.filter.logging.Log4jFilter;
import com.alibaba.druid.filter.logging.Slf4jLogFilter;
import com.alibaba.druid.filter.stat.StatFilter;
import com.alibaba.druid.pool.xa.DruidXADataSource;
import com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration;
import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.wall.WallFilter;
import com.atomikos.icatch.jta.UserTransactionImp;
import com.atomikos.icatch.jta.UserTransactionManager;
import com.atomikos.jdbc.AtomikosDataSourceBean;
import com.common.log.Log;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInterceptor;
import com.maintain.server.Constants;
import com.maintain.server.datasource.DynamicSqlSessionTemplate;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.jta.JtaTransactionManager;

import javax.transaction.SystemException;
import javax.transaction.TransactionManager;
import javax.transaction.UserTransaction;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;


@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = Constants.MAPPER_SCAN, sqlSessionTemplateRef = "sqlSessionTemplate")
public class DruidConfig implements Constants {

    @Bean(name = "userTransaction")
    public UserTransaction userTransaction() throws SystemException {
        UserTransactionImp userTransactionImp = new UserTransactionImp();
        userTransactionImp.setTransactionTimeout(CONNECT_TIMEOUT);
        return userTransactionImp;
    }

    @Bean(name = "atomikosTransactionManager"/*, initMethod = "init", destroyMethod = "close"*/)
    public TransactionManager atomikosTransactionManager() {
        UserTransactionManager userTransactionManager = new UserTransactionManager();
        userTransactionManager.setForceShutdown(false);
        return userTransactionManager;
    }

    @Bean(name = "jta")
    @DependsOn({"userTransaction", "atomikosTransactionManager"})
    public PlatformTransactionManager transactionManager() throws SystemException {
        return new JtaTransactionManager(userTransaction(), atomikosTransactionManager());
    }

    @Bean(name = MYSQL, destroyMethod = "close", initMethod = "init")
    @Primary
    @ConfigurationProperties(prefix = DRUID_PREFIX + MYSQL)
    public DruidXADataSource mysql() {
        return getDataSource();
    }

    @Bean(name = MYSQL2, destroyMethod = "close", initMethod = "init")
    @ConfigurationProperties(prefix = DRUID_PREFIX + MYSQL2)
    public DruidXADataSource mysql2() {
        return getDataSource();
    }

    private DruidXADataSource getDataSource() {
        DruidXADataSource druidDataSource = new DruidXADataSource();
        try {
            druidDataSource.setUseGlobalDataSourceStat(true);
            druidDataSource.setProxyFilters(Arrays.asList(statFilter(),log4jFilter(),log4j2Filter(), slf4jFilter(), wallFilter()));
            druidDataSource.setFilters(DRUID_FILTER);
//            druidDataSource.setValidationQuery("select 1");
        } catch (SQLException e) {
            Log.high.error(e.getMessage(), e);
        }
        return druidDataSource;
    }

    private AtomikosDataSourceBean getAtomikosDataSource(DruidXADataSource dataSource) {
        AtomikosDataSourceBean ds = new AtomikosDataSourceBean();
        ds.setXaDataSource(dataSource);
        ds.setUniqueResourceName(dataSource.getName());
        ds.setMaxIdleTime(dataSource.getMaxIdle());
        ds.setMaxPoolSize(dataSource.getMaxActive());
        ds.setMinPoolSize(dataSource.getMinIdle());
        ds.setTestQuery(dataSource.getValidationQuery());
        ds.setBorrowConnectionTimeout(Long.valueOf(dataSource.getMaxWait()).intValue());
        return ds;
    }

    @Bean(name = "pageInterceptor")
    public PageInterceptor pageInterceptor() {
        PageInterceptor pageInterceptor = new PageInterceptor();
        pageInterceptor.setProperties(getProperties());
        return pageInterceptor;
    }

    @Bean(name = "sqlSessionFactory1")
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier(MYSQL) DruidXADataSource mysql) throws IOException {
        return createSqlSessionFactory(mysql);
    }

    @Bean(name = "sqlSessionFactory2")
    public SqlSessionFactoryBean sqlSessionFactory2(@Qualifier(MYSQL2) DruidXADataSource mysq2) throws IOException {
        return createSqlSessionFactory(mysq2);
    }


    private SqlSessionFactoryBean createSqlSessionFactory(DruidXADataSource mysql) throws IOException {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(getAtomikosDataSource(mysql));
        sqlSessionFactoryBean.setPlugins(new Interceptor[]{pageInterceptor()});
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(LOCATION_PATTERN));
        sqlSessionFactoryBean.setTypeAliasesPackage(TYPE_ALIASES_PACKAGE);
        sqlSessionFactoryBean.setVfs(SpringBootVFS.class);
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setCacheEnabled(false);
        configuration.setJdbcTypeForNull(JdbcType.NULL);
        configuration.setMapUnderscoreToCamelCase(true);
        sqlSessionFactoryBean.setConfiguration(configuration);
        return sqlSessionFactoryBean;
    }

    // 替代线程不安全的defaultSqlSessionFactory
    @Bean(name = "sqlSessionTemplate")
    @DependsOn({"pageInterceptor"})
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("sqlSessionFactory1") SqlSessionFactory sqlSessionFactory, @Qualifier("sqlSessionFactory2") SqlSessionFactory sqlSessionFactory2) {
        DynamicSqlSessionTemplate dynamicSqlSessionTemplate = new DynamicSqlSessionTemplate(sqlSessionFactory);
        Map<Object, SqlSessionFactory> sqlSessionFactoryMap = new HashMap<>();
        sqlSessionFactoryMap.put(MYSQL, sqlSessionFactory);
        sqlSessionFactoryMap.put(MYSQL2, sqlSessionFactory2);
        dynamicSqlSessionTemplate.setTargetSqlSessionFactorys(sqlSessionFactoryMap);
        return dynamicSqlSessionTemplate;
    }

    //配置mybatis的分页插件pageHelper
    @Bean(name = "pageHelper")
    public PageHelper pageHelper() {
        PageHelper pageHelper = new PageHelper();
        Properties properties = getProperties();
        pageHelper.setProperties(properties);
        return pageHelper;
    }


    private Properties getProperties() {
        Properties property = new Properties();
        //property.setProperty("offsetAsPageNum", "true");
        property.setProperty("rowBoundsWithCount", "true");
        property.setProperty("reasonable", "true");
        //动态数据源自动切换
        property.setProperty("autoRuntimeDialect", "true");
        return property;
    }


    /**
     * 注册StatViewServlet
     * @return
     */
    @Bean
    public ServletRegistrationBean druidStatViewServlet(){
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(new StatViewServlet(),"/druid/*");
        servletRegistrationBean.addInitParameter("loginUsername","druid");
        servletRegistrationBean.addInitParameter("loginPassword","druid");
        servletRegistrationBean.addInitParameter("resetEnable","false");
        return servletRegistrationBean;
    }

    /**
     * 注册filterRegistrationBean
     * @return
     */
    /*@Bean
    public FilterRegistrationBean druidStatFilter(){
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new WebStatFilter());
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.addInitParameter("exclusions","*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*");
        return filterRegistrationBean;
    }*/

    @Bean(name = "statFilter")
    @ConfigurationProperties(prefix = DRUID_PREFIX + ".filter.stat")
    public StatFilter statFilter(){
        final StatFilter statFilter = new StatFilter();
        return statFilter;
    }

    @Bean(name = "log4j2Filter")
    @ConfigurationProperties(prefix = DRUID_PREFIX + ".filter.log4j2")
    public Log4j2Filter log4j2Filter(){
        final Log4j2Filter log4j2Filter = new Log4j2Filter();
        return log4j2Filter;
    }

    @Bean(name = "log4jFilter")
    @ConfigurationProperties(prefix = DRUID_PREFIX + ".filter.log4j")
    public Log4jFilter log4jFilter(){
        final Log4jFilter log4jFilter = new Log4jFilter();
        return log4jFilter;
    }

    @Bean(name = "slfFilter")
    @ConfigurationProperties(prefix = DRUID_PREFIX + ".filter.slf4j")
    public Slf4jLogFilter slf4jFilter(){
        final Slf4jLogFilter slf4jLogFilter = new Slf4jLogFilter();
        return slf4jLogFilter;
    }

    @Bean(name = "wallFilter")
    @ConfigurationProperties(prefix = DRUID_PREFIX + ".filter.wall")
    public WallFilter wallFilter(){
        final WallFilter wallFilter = new WallFilter();
        return wallFilter;
    }


}
