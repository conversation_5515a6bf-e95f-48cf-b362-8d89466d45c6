package com.maintain.server.datasource;

import com.alibaba.druid.util.StringUtils;
import com.common.log.Log;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Order(-10) // 该AOP在@Transactional之前执行
@Component
public class DynamicDataSourceAspect {

    @Pointcut(value = "@annotation(com.maintain.server.datasource.DynamicAnnotation)")
    public void pointcut() {
    }

    @Before(value = "pointcut()&& @annotation(DynamicAnnotation)", argNames = "joinPoint,DynamicAnnotation")
    public void before(JoinPoint joinPoint, DynamicAnnotation DynamicAnnotation) {
        String dbName = DynamicAnnotation.name();
        if (!StringUtils.isEmpty(dbName)) {
            DynamicDataSourceHolder.setDataSource(dbName);
        } else {
            Log.low.error("dbName is null");
        }
    }

    @After(value = ("pointcut() && @annotation(DynamicAnnotation)"), argNames = "joinPoint,DynamicAnnotation")
    public void doAfterAdvice(JoinPoint joinPoint, DynamicAnnotation DynamicAnnotation) {
        DynamicDataSourceHolder.destroy();
    }

}
