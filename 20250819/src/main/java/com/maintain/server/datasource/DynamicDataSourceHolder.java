package com.maintain.server.datasource;

import com.maintain.server.Constants;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

public class DynamicDataSourceHolder {

    private static final ThreadLocal<String> holder = new ThreadLocal<String>() {
        @Override
        protected String initialValue() {
            holder.set(Constants.MYSQL);
            return holder.get();
        }
    };

    private static final BlockingQueue<String> queue = new ArrayBlockingQueue<>(1);


    public static void setDataSource(String name) {
        holder.set(name);
        queue.offer(name);
    }

    public static String getDataSource() {
        String type = holder.get();
        return type;
    }

    public static void destroy() {
        holder.remove();
        queue.clear();
    }

    public static boolean contain(String name) {
        return name != null && name.equals(holder.get());
    }
}
