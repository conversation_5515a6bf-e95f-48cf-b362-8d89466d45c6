package com.maintain.server.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class DynamicAndStaticMonitorCriteria extends BaseCriteria{

    private Long id;

    /**
     * 分析类型 0：动态分析 ，1：静态分析
     */
    private Integer analysisType;

    /**
     * 告警状态 0：未告警 1：正在告警
     */
    private Integer status;

    private Boolean lastOne;

}
