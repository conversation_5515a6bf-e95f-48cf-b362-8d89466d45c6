package com.maintain.server.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2018-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SoftwareCriteria extends BaseCriteria {

    private Integer id;

    private Integer serverId;

    private String name;

    private String notName;

    private String fuzzyName;

    private Collection<String> names;

    private Collection<String> notNames;

    private String[] timePoint;

    private Integer status;
    /**
     * 程序守护状态
     */
    private Integer programStatus;
    /**
     * add by xz,2020.11.16
     * 平台操作标识，0:开启，1：关闭
     */
    private Integer platformFlag;

}