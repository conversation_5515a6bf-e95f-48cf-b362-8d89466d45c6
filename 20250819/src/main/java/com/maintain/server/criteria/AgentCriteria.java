package com.maintain.server.criteria;

import com.maintain.server.type.AlarmStatusType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentCriteria extends BaseCriteria {

    private String ip;

    private List<String> ips;

    private Integer id;

    private List<Integer> ids;

    private String roleFuzzy;

    private Integer programStatus;

    private AlarmStatusType status;

    private Integer existsVnp;
}