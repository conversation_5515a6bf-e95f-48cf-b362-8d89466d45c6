package com.maintain.server.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AutoDeployTaskCriteria extends BaseCriteria{

    private Integer id;

    private String ip;

    private Integer status;

    private Integer notInStatus;

    private String name;

    private String product;

}