package com.maintain.server.config;

import com.maintain.server.vo.AlarmThreshold;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020-12-02
 */
@Configuration
public class ThreshlodConfig {

    @Bean
    @ConfigurationProperties(prefix = "dynamic-no-monitor")
    public AlarmThreshold dynamicNoAnalysisThreshold() {
        return new AlarmThreshold();
    }
}