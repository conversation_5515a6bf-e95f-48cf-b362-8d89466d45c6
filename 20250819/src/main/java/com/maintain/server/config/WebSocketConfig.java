package com.maintain.server.config;

import com.maintain.server.websocket.WebSocketController;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

/**
 * <AUTHOR>
 * @date 2019-03-08
 */
@Configuration
public class WebSocketConfig {
    @Value("${server.web-socket-port}")
    private Integer port;

    @Bean(name="webSocketController")
    public WebSocketController webSocketConfig(){
        return new WebSocketController(port);
    }

    @Bean("multipartResolver")
    public MultipartResolver multipartResolver(){
        final CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
        commonsMultipartResolver.setMaxUploadSize(1000000000);
        commonsMultipartResolver.setDefaultEncoding("utf-8");
        return commonsMultipartResolver;
    }
}