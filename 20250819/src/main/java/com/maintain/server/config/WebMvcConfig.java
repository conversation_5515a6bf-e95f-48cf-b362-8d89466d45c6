package com.maintain.server.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-01-23
 * 内容协商管理器
 */
@Configuration
public class WebMvcConfig extends WebMvcConfigurationSupport {

    /*@Bean(name = "filterMultipartResolver")
    public CommonsMultipartResolver commonsMultipartResolver(){
        final CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
        commonsMultipartResolver.setDefaultEncoding("utf-8");
        commonsMultipartResolver.setMaxUploadSize(100000000L);
        return commonsMultipartResolver;
    }*/

    private MappingJackson2HttpMessageConverter jacksonMessageConverter() {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        List<MediaType> mediaTypeList = new ArrayList<>();
        mediaTypeList.add(MediaType.TEXT_HTML);
        mediaTypeList.add(MediaType.APPLICATION_JSON_UTF8);
        converter.setSupportedMediaTypes(mediaTypeList);
        return converter;
    }

    @Override
    protected void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(jacksonMessageConverter());
    }

    @Override
    protected void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        Map<String, MediaType> types = new HashMap<>();
        types.put("html", MediaType.TEXT_HTML);
        types.put("json", MediaType.APPLICATION_JSON_UTF8);
        configurer.defaultContentType(MediaType.APPLICATION_JSON_UTF8)
                .favorParameter(true)
                .mediaTypes(types)
                .ignoreAcceptHeader(false)
                .favorPathExtension(false);
    }

}