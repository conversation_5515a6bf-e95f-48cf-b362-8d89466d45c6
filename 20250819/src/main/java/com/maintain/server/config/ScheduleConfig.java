package com.maintain.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 配置@schedule异步执行
 */
@Configuration
public class ScheduleConfig implements SchedulingConfigurer {

    @Bean
    public Executor taskExecutor() {
        //指定定时任务线程数量
        return Executors.newScheduledThreadPool(35);
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.setScheduler(taskExecutor());
    }
}