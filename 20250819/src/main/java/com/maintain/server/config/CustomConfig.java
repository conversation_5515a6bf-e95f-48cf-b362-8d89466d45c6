package com.maintain.server.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.List;


@Configuration
@ConfigurationProperties("custom")
public class CustomConfig {

    private List<HistoryTableInfo> historyTableInfoList;

    public List<HistoryTableInfo> getHistoryTableInfoList() {
        return historyTableInfoList;
    }

    public void setHistoryTableInfoList(List<HistoryTableInfo> historyTableInfoList) {
        this.historyTableInfoList = historyTableInfoList;
    }
}
