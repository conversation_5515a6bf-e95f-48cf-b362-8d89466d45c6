package com.maintain.server;


import java.io.File;

public interface Constants {

    String JSON_SUFFIX = "json";

    String XML_SUFFIX = "xml";

    String WINDOWS_7Z_PATH = "C:\\Program Files\\7-Zip\\7z.exe";

    String PHANTOMJS_PATH = "./phantomjs/bin/phantomjs.exe";

    String LINUX_7Z_PATH = "/usr/local/p7zip_16.02/bin";

    String UTF_8 = "UTF-8";

    String GBK = "GBK";

    String MYSQL = ".maintain";

    String MYSQL2 = ".guard";

    String DRUID_PREFIX = "spring.datasource.druid";

    String ES_PREFIX = "es";

    String KAFKA_PREFIX = "kafka";

    //stat,log4j2,log4j,wall
    String DRUID_FILTER = "stat,log4j,wall";

    String TYPE_ALIASES_PACKAGE = "com.maintain.server.vo";

    String MAPPER_SCAN = "com.maintain.server.mapper";

    String LOCATION_PATTERN = "classpath:mapper/**/*.xml";

    String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    String YYYYMMDDHH = "yyyyMMddHH";

    String YYYYMMDD = "yyyyMMdd";

    String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    String YYYY_MM_DD = "yyyy-MM-dd";

    String HTTP_GET = "GET";

    String HTTP_POST = "POST";

    String MAINTAIN_SERVER = "MaintainServer";

    String MAINTAIN_AGENT = "MaintainAgent";

    String PROC_RESULT_OUT_TYPE_ERROR = "error";

    String PROC_RESULT_OUT_TYPE_OUT = "out";

    Long SLEEP_TIME = 1000L;

    Long SCROLL_TIMEOUT = 10000L;

    Integer MAXRETRY_TIMEOUT = 300000;

    Integer CONNECT_TIMEOUT = 5000;

    Integer SOCKET_TIMEOUT = 300000;

    Integer CONNECTION_REQUEST_TIMEOUT = 3000;


    //自动部署时，需要将程序放在该目录
    //Windows:D:\MAINTAIN_TEMP
    //Linux:/usr/local/MAINTAIN_TEMP
    String MAINTAIN_TEMP = "MAINTAIN_TEMP";
    String COMMON_CONFIG_FILE_PATH = "config" + File.separator + "common-config.json";
    String REMOTE_WINDOWS_COMMON_CONFIG_PATH = "D:\\dist\\config\\";
    String REMOTE_LINUX_CONFIG_PATH = "/dist/config/";
    String MAINTAIN_LINUX_TEMP = "/dist/MAINTAIN_TEMP/";
    String MAINTAIN_WINDOWS_TEMP = "D:\\MAINTAIN_TEMP\\";
    //目标程序所在目录
    String MAINTAIN_WINDOWS_REMOTE = "D:\\dist\\";
    String MAINTAIN_LINUX_REMOTE = "/dist/";

    /**
     * ftp输出路径——windows
     */
    String FTP_PATH_WINDOWS = "D:\\ftp";

    /**
     * ftp输出路径——linux
     */
    String FTP_PATH_LINUX = "/var/ftp";

    Integer LINUX = 0;

    Integer WINDOWS = 1;

    String CM_SESSION_KEY = "CLOUDERA_MANAGER_SESSIONID";

    String HTTP_PREFIX = "http://";

    String FRONT_DEVICE_INDEX = "inf-state-monitor";

    String FRONT_TRANSMIT_INDEX = "inf-transmit-monitor";

    String STACK_LOG_INDEX = "log-log4j-";

    String SERVICE_MANAGE_INDEX = "log-service-";

    String STRACK_ES_LOG_INDEX = "log-es-log-";

    String STRACK_ES_SLOWLOG_INDEX = "log-es-slowlog-";

    int SEND_DATE_INTERVAL = 10;

    int DATA_SIZE = 1000;

    /**
     * 发送监控数据，单位：秒
     */
    int SEND_MONITOR_TIME = 10;

    /**
     * 初始密码
     */
    String INIT_PASSWORD = "security_!@#$%^&*()";

    String SEVEN_PASSWORD = "有一所房子，面朝大海，春暖花开。";

    String LINUX_NEW_LINE = "\n";
    /**
     * 逗号
     */
    String COMMA = ",";

    /**
     * 10M的字节数
     */
    int TEN_MEGABYTES = 10 * 1024 * 1024;

    String LINUX_LOG_PATH = "/dist/{0}/log";
    String WINDOWS_LOG_PATH = "D:\\dist\\{0}\\log";

    String LINUX_LOGS_PATH = "/dist/{0}/logs";
    String WINDOWS_LOGS_PATH = "D:\\dist\\{0}\\logs";

    String CLOSE = "CLOSE";
    String FAILURE = "FAILURE";
    String SUCCESS = "SUCCESS";

    Integer TRY_LOCK = 10;

    Integer DEFAULT_PID = 10088;

    Integer NETTY_DEFAULT_CONNECT_TIMEOUT = 3600;

    String LICENSE = "./config/license.xml";

    int HTTP_CONNECT_TIMEOUT = 10 * 60 * 1000;//连接超时时间(单位毫秒)
    int HTTP_SOCKET_TIMEOUT = 10 * 60 * 1000;//socket读写超时时间(单位毫秒)

    int MAX_CONN = 100; // 最大连接数
    int MAX_PRE_ROUTE = 3000;
    int MAX_ROUTE = 2000;

    String SOFT_LOST_RATIO_DESC = "丢包率为:{0}%,阈值为:{1}%--{2}%";
    String RESTART_ALARM_DESC = "S-Project重启次数为:{0},BuildSendFile.exe重启次数为:{1},LinuxSendClient.exe重启次数为:{2}";
    String DATA_HEAP_UP_DESC = "当前数据堆积达到:{0}G,阈值为:{1}G--{2}G";
    String DATA_TRANSMIT_EXCEPTION_DESC = "昨日数据传输比:{0},阈值为:{1}--{2}";

    String NEXT_LINE_SEPARATOR = "\r\n";

    String WINDOWS_FILE_SEPARATOR = "\\";

    String LINUX_FILE_SEPARATOR = "/";

    /**
     * 数据库每日备份路径
     */
    String MYSQL_BACKUP_DAILY = "mysqlDataBackupDaily";
    /**
     * 数据库每周备份路径
     */
    String MYSQL_BACKUP_WEEKLY = "mysqlDataBackupWeekly";

    /**
     * linux上mysql备份文件的根路径
     */
    String DB_BACKUP_PATH_IN_LINUX = "/data/mysqlbackup/";
    /**
     * windows上mysql备份文件的根路径
     */
    String DB_BACKUP_PATH_IN_WINDOWS = "D:\\Data\\mysqlbackup\\";

    /**
     * 状态————返回成功
     */
    int SUCCESS_RESPONSE_CODE = 200;
    /**
     * 状态————返回失败
     */
    int FAIL_RESPONSE_CODE = 500;

    long ONE_GB = 1073741824L;
}
