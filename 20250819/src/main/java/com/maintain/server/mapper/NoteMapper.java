package com.maintain.server.mapper;

import com.maintain.server.criteria.NoteCriteria;
import com.maintain.server.vo.NoteVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface NoteMapper {

    List<NoteVo> queryNotes(NoteCriteria noteCriteria);

    void addNote(NoteCriteria noteCriteria);

    void deleteNote(NoteCriteria noteCriteria);

    void updateNote(NoteCriteria noteCriteria);

    Integer count(NoteCriteria noteCriteria);
}
