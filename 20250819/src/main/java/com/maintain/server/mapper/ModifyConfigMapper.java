package com.maintain.server.mapper;

import com.maintain.server.criteria.ModifyConfigCriteria;
import com.maintain.server.vo.ModifyConfigVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ModifyConfigMapper {

    List<ModifyConfigVo> queryModifyConfig(ModifyConfigCriteria modifyConfigCriteria);

    int addModifyConfig(ModifyConfigCriteria modifyConfigCriteria);

    void deleteModifyConfig(ModifyConfigCriteria modifyConfigCriteria);

    int updateModifyConfig(ModifyConfigCriteria modifyConfigCriteria);
}
