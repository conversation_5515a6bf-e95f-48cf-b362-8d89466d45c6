package com.maintain.server.mapper.bigdata;

import com.maintain.server.dto.Tuple;
import com.maintain.server.dto.req.BdAlertReq;
import com.maintain.server.entity.BdAlertPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
public interface BdAlertMapper {
    void batchSaveOrUpdate(@Param("list") List<BdAlertPo> list);

    List<BdAlertPo> findPage(BdAlertReq req);

    List<Tuple<String,Integer>> hostAlertCount();

    List<Tuple<String,Integer>> serviceAlertCount();

    List<Tuple<String,Integer>> componentAlertCount(String serviceName);

}
