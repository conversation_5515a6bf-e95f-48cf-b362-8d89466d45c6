package com.maintain.server.mapper;

import com.maintain.server.criteria.GroupCriteria;
import com.maintain.server.vo.GroupHistoryHealthVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Azurio on 2019/2/26.
 */
@Mapper
public interface GroupMapper extends ClearHistoryDataMapper {

    void addGroupHistoryHealth(@Param(value = "list") List<GroupHistoryHealthVo> list);

    List<GroupHistoryHealthVo> getGroupHistoryHealth(GroupCriteria criteria);

}
