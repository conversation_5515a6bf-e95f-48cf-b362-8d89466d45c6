package com.maintain.server.mapper;


import com.maintain.server.criteria.AutoDeployTaskCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.vo.AutoDeployTaskVo;
import com.maintain.server.vo.DeployRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by Azurio on 2018/11/19.
 */
@Mapper
public interface AutoDeployMapper {

    void addDeployTask(List<AutoDeployTaskVo> prepareVo) throws ServiceException;

    List<DeployRecordVo> getPlatformVersionInfo(@Param(value = "param") Map<String, String> param);

    List<AutoDeployTaskVo> getAutoDeployTasks(AutoDeployTaskCriteria criteria);

    void lockTask(AutoDeployTaskVo taskVo);

    void lockDeployTask(DeployRecordVo deployRecordVo);

    void addDeployRecord(DeployRecordVo deployRecordVo);

    DeployRecordVo getDeployRecordInfo(DeployRecordVo deployRecordVo);
}
