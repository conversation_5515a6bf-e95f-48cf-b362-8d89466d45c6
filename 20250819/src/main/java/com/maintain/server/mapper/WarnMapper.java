package com.maintain.server.mapper;
import com.maintain.server.vo.WarnVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface WarnMapper {


    void addWarnRule(WarnVo vo);

    /**
     * param1 和 param2 和 type是限定条件不可修改
     */
    void updateWarnRule(WarnVo vo);

    //void removeWarnRule(WarnVo vo);

    WarnVo getWarn(WarnVo vo);

    List<WarnVo> getWarnList(WarnVo vo);
}
