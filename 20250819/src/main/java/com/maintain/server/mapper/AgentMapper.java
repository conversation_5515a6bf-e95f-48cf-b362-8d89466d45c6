package com.maintain.server.mapper;

import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.IpRoleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by Azurio on 2018/10/8.
 */
@Mapper
public interface AgentMapper {

     int addAgent(AgentVo agentVo);

     int updateAgent(AgentVo agentVo);

     void deleteAgent(String ip);

     List<AgentVo> getAgent(AgentCriteria criteria);

     List<Map<String, Object>> getPlatformRole(@Param(value = "ids") List<Integer> ids);

     List<Map<String,Object>> getAllHosts(AgentCriteria criteria);

     List<AgentVo> getAgentForPlugin(@Param("plugin") String plugin);

    List<IpRoleVo> getIpRoleVoList();
}
