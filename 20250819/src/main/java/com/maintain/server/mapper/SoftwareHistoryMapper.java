package com.maintain.server.mapper;

import com.maintain.server.criteria.BaseCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.vo.SoftwareRestartVo;
import com.maintain.server.vo.SoftwareVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Azurio on 2019/3/5.
 */
@Mapper
public interface SoftwareHistoryMapper extends ClearHistoryDataMapper {

    void addRestartHistory(List<SoftwareRestartVo> list);

    void clearRestartHistoryData(BaseCriteria criteria);

    List<SoftwareRestartVo> getRestartHistory(SoftwareCriteria criteria);

    void addSoftwareHistoryInfo(@Param(value = "list") List<SoftwareVo> softwareInfoVos);

    List<SoftwareVo> getSoftwareHistoryInfo(SoftwareCriteria softwareCriteria);

    Integer countRestartHistory(@Param("id") Integer id);
}
