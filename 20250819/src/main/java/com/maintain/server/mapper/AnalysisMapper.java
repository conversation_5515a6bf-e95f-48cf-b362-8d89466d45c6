package com.maintain.server.mapper;

import com.maintain.server.vo.AnalysisVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AnalysisMapper {

    void addAnalysis(AnalysisVo analysisVo);

    void updateAnalysis(AnalysisVo analysisVo);

    void deleteAnalysis(AnalysisVo analysisVo);

    List<AnalysisVo> getAnalysisList(@Param("startTime") String startTime,@Param("endTime") String endTime);

    AnalysisVo getAnalysis(String ip);
}
