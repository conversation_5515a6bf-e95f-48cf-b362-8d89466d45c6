package com.maintain.server.mapper;

import com.maintain.server.criteria.BusinessMonitorCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.vo.BusinessMonitorVo;
import com.maintain.server.vo.SoftwareInfoVo;
import com.maintain.server.vo.SoftwareVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-07
 */
@Mapper
public interface SoftwareMapper extends ClearHistoryDataMapper{

    void addSoftwareInfo(SoftwareVo softwareVo);

    void updateSoftwareInfo(SoftwareVo softwareVo);

    void updateSoftware(SoftwareVo softwareVo);

    void updateSoftwareMonitorInfo(SoftwareInfoVo softwareInfoVo);

    List<SoftwareVo> getSoftwareInfos(SoftwareCriteria criteria);

    void deleteSoftware(SoftwareVo softwareVo);

    SoftwareVo getSoftware(Integer id);

    List<SoftwareVo> listAllSoftwares(SoftwareCriteria softwareCriteria);

    /**
     * 程序资源信息
     *
     * @return
     */
    List<SoftwareVo> listSoftwaresResource(SoftwareCriteria softwareCriteria);


    void addBusinessMonitor(BusinessMonitorVo businessMonitorVoList);

    List<BusinessMonitorVo> getBusinessMonitor(BusinessMonitorCriteria criteria);

    List<SoftwareInfoVo> getSoftwareInfoList(@Param("ip") String ip);

    /**
     * 批量修改平台标识（一键对平台操作的标识）
     * add by xz,2020.11.16，一键关闭平台时设置，便于后续一键启动平台时能够恢复到关闭平台的状态
     * @param idList
     * @param flag
     */
    void batchUpdatePlatformFlag(@Param("idList") List<Integer> idList, @Param("flag") Integer flag, @Param("programStatus") Integer programStatus);
}