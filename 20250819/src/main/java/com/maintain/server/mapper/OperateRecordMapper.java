package com.maintain.server.mapper;

import com.maintain.server.vo.OperateRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Azurio on 2018/11/28.
 */
@Mapper
public interface OperateRecordMapper {

    List<OperateRecordVo> getOperateRecord();

    void addOperateRecord(OperateRecordVo operateRecordVo);

    List<OperateRecordVo> selectOperateRecord(@Param("startTime") String startTime, @Param("endTime") String endTime,@Param("login") boolean login);
}
