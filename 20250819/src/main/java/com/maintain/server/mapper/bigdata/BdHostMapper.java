package com.maintain.server.mapper.bigdata;

import com.maintain.server.dto.req.BdHostReq;
import com.maintain.server.entity.BdHostPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
@Mapper
public interface BdHostMapper {
    void batchSaveOrUpdate(@Param("list") List<BdHostPo> list);

    List<BdHostPo> findPage(BdHostReq req);
}
