package com.maintain.server.mapper;

import com.maintain.server.criteria.DynamicAndStaticCriteria;
import com.maintain.server.criteria.UserCriteria;
import com.maintain.server.vo.DynamicAndStaVoticThresholdVo;
import com.maintain.server.vo.PermissionVo;
import com.maintain.server.vo.RoleVo;
import com.maintain.server.vo.UserVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-21
 */
@Mapper
public interface UserMapper {
    /**
     * 通过用户名查找用户及其关联的角色
     *
     * @param criteria
     * @return
     */
    UserVo findByUsername(UserCriteria criteria);

    /**
     * 通过用户名判断用户是否存在
     *
     * @param name
     * @return
     */
    int userExistByName(String name);

    /**
     * 插入用户
     *
     * @param user
     * @return
     */
    int insertUser(UserVo user);

    /**
     * 更新用户(密码)
     *
     * @param user
     * @return
     */
    int updateUser(UserVo user);

    /**
     * 删除用户(注销)
     *
     * @param userId
     * @return
     */
    int deleteUser(int userId);

    /**
     * 用户列表
     *
     * @return
     */
    List<UserVo> listUsers();

    /**
     * 角色列表
     *
     * @return
     */
    List<RoleVo> listRoles();

    /**
     * 通过ID寻找角色
     *
     * @param roleIds
     * @return
     */
    List<RoleVo> findRoleByIds(List<Integer> roleIds);

    /**
     * 插入用户关联的角色信息
     *
     * @param user
     * @return
     */
    int insertRolesOfUser(UserVo user);

    /**
     * 插入角色与权限的关联信息
     *
     * @param role
     * @return
     */
    int insertPermissionOfRole(RoleVo role);

    /**
     * 删除指定用户关联的角色
     *
     * @param userId
     * @return
     */
    int deleteRolesOfUser(int userId);

    /**
     * 删除指定角色的权限
     *
     * @param roleId
     * @return
     */
    int deletePermissionOfRole(int roleId);

    /**
     * 获取根权限
     *
     * @return
     */
    List<PermissionVo> getRootPermission();

    /**
     * 获取子类权限
     *
     * @param parentId
     * @return
     */
    List<PermissionVo> getChildPermission(int parentId);

    /**
     * 查找用户的一级权限
     *
     * @param username
     * @return
     */
    List<PermissionVo> getFirstPermissionByUsername(String username);

    /**
     * 返回用户的所有权限
     *
     * @param username
     * @return
     */
    List<PermissionVo> getAllPermissionByUsername(String username);

    /**
     * 返回用户的所有权限
     *
     * @param userId
     * @return
     */
    List<PermissionVo> getAllPermissionByUserId(int userId);

    /**
     * 根据用户ID查找用户
     *
     * @param userId
     * @return
     */
    UserVo findUserById(int userId);

    int addDynamicAndStatic(DynamicAndStaVoticThresholdVo dynamicAndStaVoticThresholdVo);

    int updateDynamicAndStatic(DynamicAndStaVoticThresholdVo dynamicAndStaVoticThresholdVo);

    List<DynamicAndStaVoticThresholdVo> findThreshold(DynamicAndStaticCriteria dynamicAndStaticCriteria);

    /**
     * 通过条件查询用户
     *
     * @param criteria
     * @return
     */
    UserVo getByName(UserCriteria criteria);
}