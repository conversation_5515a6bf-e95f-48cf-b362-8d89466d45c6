package com.maintain.server.mapper;

import com.maintain.server.criteria.MysqlBackupCriteria;
import com.maintain.server.vo.MysqlBackupVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */
@Mapper
public interface MysqlBackupMapper {

    List<MysqlBackupVo> findMysqlBackup(MysqlBackupCriteria mysqlBackupCriteria);

    boolean insertMysqlBackup(List<MysqlBackupVo> mysqlBackupVoList);

    List<MysqlBackupVo> findByBackupTime(@Param("backupTime") String backupTime, @Param("backupType") int backupType, @Param("backupDatabases") String backupDatabases);

    void updateIgnoreStatusByIdList(@Param("idList") List<Integer> idList, @Param("ignoreStatus") int ignoreStatus);

    void deleteRecords(MysqlBackupCriteria mysqlBackupCriteria);
}