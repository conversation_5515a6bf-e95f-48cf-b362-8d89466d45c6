package com.maintain.server.mapper;

import com.maintain.server.criteria.RequestRecordCriteria;
import com.maintain.server.vo.RequestRecordStatVo;
import com.maintain.server.vo.RequestRecordVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-11-09
 */
public interface RequestRecordMapper {
    /**
     * 新增
     *
     * @param requestRecord
     * @return
     */
    int addRequestRecord(RequestRecordVo requestRecord);

    /**
     * 查询统计信息
     *
     * @param criteria
     * @return
     */
    List<RequestRecordStatVo> selectStatic(RequestRecordCriteria criteria);
}