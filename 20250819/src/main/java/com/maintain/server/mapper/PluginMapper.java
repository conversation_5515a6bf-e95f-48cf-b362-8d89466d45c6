package com.maintain.server.mapper;

import com.maintain.server.criteria.PluginCriteria;
import com.maintain.server.vo.PluginVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Created by Azurio on 2019/3/18.
 */
@Mapper
public interface PluginMapper {

    void addPlugin(PluginVo plugin);

    void updatePlugin(PluginVo pluginVo);

    List<PluginVo> getPlugins(PluginCriteria criteria);

}
