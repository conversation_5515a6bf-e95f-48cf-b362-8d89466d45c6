package com.maintain.server.mapper.bigdata;

import com.maintain.server.dto.req.BdHistoryAlertReq;
import com.maintain.server.entity.BdHistoryAlertPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
public interface BdHistoryAlertMapper {
    void batchSaveOrUpdate(@Param("list") List<BdHistoryAlertPo> list);

    List<BdHistoryAlertPo> findPage(BdHistoryAlertReq req);
}
