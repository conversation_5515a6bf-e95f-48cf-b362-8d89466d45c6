package com.maintain.server.mapper;

import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.vo.HardwareHistoryHealth;
import com.maintain.server.vo.HardwareVo;
import com.maintain.server.vo.OsOperateLogVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-24
 */
@Mapper
public interface HardwareMapper extends ClearHistoryDataMapper{

    void addHardwareInfo(HardwareVo hardwareVo);

    List<HardwareVo> getHardwareInfos(HardwareCriteria criteria);

    void addHardwareHistoryHealth(List<HardwareHistoryHealth> hardwareList);

    List<HardwareHistoryHealth> getHardwareHistoryHealth(HardwareCriteria hardwareCriteria);

    void updateHardware(HardwareVo hardwareVo);

    HardwareVo getHardwareInfo(String ip);

    List<OsOperateLogVo> getOsOperateLog(OsOperateLogVo osOperateLogVo);
}