package com.maintain.server.mapper;

import com.maintain.server.criteria.DbBackupManageCriteria;
import com.maintain.server.vo.DbBackupManageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-22
 */
public interface DbBackupManageMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(DbBackupManageVo record);

    int insertSelective(DbBackupManageVo record);

    DbBackupManageVo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DbBackupManageVo record);

    int updateByPrimaryKey(DbBackupManageVo record);

    List<DbBackupManageVo> selectList(DbBackupManageCriteria criteria);
}