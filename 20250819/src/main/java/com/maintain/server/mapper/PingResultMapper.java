package com.maintain.server.mapper;

import com.maintain.server.criteria.PingResultCriteria;
import com.maintain.server.vo.PingResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-11-20
 */
public interface PingResultMapper {

    List<PingResult> list(PingResultCriteria criteria);

    /**
     * 批量插入
     *
     * @param results
     */
    void batchInsert(@Param("results") List<PingResult> results);

    /**
     * 删除过期数据
     *
     * @param time
     */
    void deleteOverdueData(@Param("time") String time);
}