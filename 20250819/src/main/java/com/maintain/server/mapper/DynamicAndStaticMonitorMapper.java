package com.maintain.server.mapper;

import com.maintain.server.criteria.DynamicAndStaticMonitorCriteria;
import com.maintain.server.vo.DynamicAndStaticMonitorVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;


public interface DynamicAndStaticMonitorMapper {

    int addDynamicAndStaticMonitor(DynamicAndStaticMonitorVo dynamicAndStaticMonitorVo );

    int updateDynamicAndStaticMonitor(DynamicAndStaticMonitorVo dynamicAndStaticMonitorVo );

    List<DynamicAndStaticMonitorVo> findDynamicAndStaticMonitor(DynamicAndStaticMonitorCriteria dynamicAndStaticMonitorCriteria);

    int deleteHistoryTableInfo(@Param("tableName") String tableName, @Param("timeField")String timeField, @Param("time")String time);


}
