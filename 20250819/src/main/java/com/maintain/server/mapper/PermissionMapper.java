package com.maintain.server.mapper;

import com.maintain.server.vo.PermissionVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-22
 */
@Mapper
public interface PermissionMapper {
    /**
     * 加载所有权限信息
     * @return
     */
    List<PermissionVo> findAllResources();

    /**
     * 加载某个角色的所有权限
     * @param roleId
     * @return
     */
    List<PermissionVo> findByRoleId(int roleId);

    /**
     * 根据权限ID加载权限
     * @param permissionIds
     * @return
     */
    List<PermissionVo> findByIds(List<Integer> permissionIds);

    PermissionVo findById(int id);

    PermissionVo findByName(String name);

    List<PermissionVo> findByParentId(int parentId);
}