package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-11-28
 * 审计管理Vo
 */
@Data
public class OperateRecordVo {

    private Integer id;

    private Integer userId;

    private String user;


    private String operate;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date operateTime;
}