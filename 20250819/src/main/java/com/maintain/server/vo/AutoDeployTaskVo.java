package com.maintain.server.vo;

import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-11-20
 * 自动部署的任务
 */
@Data
public class AutoDeployTaskVo {

    private Integer id;

    private String ip;

    private String product;

    private String name;

    private String oldName;

    /**
     * 程序包所在的目录
     */
    private String sourcePath;
    /**
     * 程序包要上传的目录
     */
    private String targetPath;

    /**
     * 需要删除的文件
     */
    private String needDeleteFiles;

    private String isFullPackage;

    private String ignoreBackup;

    private String remark;

    /**
     * 0未开始 1关闭 2备份 3删除 4上传 5失败 6已完成
     */
    private Integer status;

    private Date createTime;

    private Date modifyTime;



    private Set<String> names;
}