package com.maintain.server.vo.bigdata;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/19
 */
@Data
public class BdHostVo {
    private String clusterName;
    private String node;
    private String diskInfo;
    private String hostName;
    private String hostState;
    private String hostStatus;
    private String ip;
    private String osArch;
    private String hostOsFamily;
    private String osType;
    private String publicHostName;
    private String rackInfo;
    private Integer totalMem;
    private String alertsSummary;
    private String hostComponents;
    private Integer cpuCount;
    private Integer phCpuCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}
