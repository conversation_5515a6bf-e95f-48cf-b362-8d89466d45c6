package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-03-18
 */
@Data
@EqualsAndHashCode(exclude = {"createTime","id","lastUpdateTime"})
public class PluginVo implements Serializable {

    private Integer id;

    private String name;

    private String path;

    // 0关闭 1停用
    private Integer isDelete;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date lastUpdateTime;

}