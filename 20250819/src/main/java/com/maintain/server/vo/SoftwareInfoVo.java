package com.maintain.server.vo;

import lombok.Data;

import java.util.Date;

@Data
public class SoftwareInfoVo {

    private Integer id;

    /** 程序名 */
    private String name;

    /** 进程id */
    private Integer pid;

    /** 状态  开启：1 ，关闭：0 */
    private Integer programStatus;

    /** 重启次数 */
    private Integer restartCount;

    /** 操作类型 手动开启关闭：1，agent守护开启：2*/
    private Integer operateType;

    /** 最近一次重启时间 */
    private Date restartTime;

    /** 进程数 */
    private Integer processCount;

    /** 当天error日志数量 */
    private Integer logCount;
    /** 启动脚本是否存在 */
    private Integer isExists;

    /** 所在目录 */
    private String realDir;

    /** 是否手动配置启动程序 */
    private Boolean config;

    /** 识别程序的唯一key */
    private String keys;

    /** 启动脚本的路径 */
    private String scriptPath;

    /** 脚本启动命令 */
    private String script;

    /** 堆大小 单位为mb */
    private Integer maxHeapSize;

    private Integer minHeapSize;

    /** 关闭命令 */
    private String closeScript;

    private Integer heartMonitor;

    private String logPath;
    private String logSuffix;
    private String configPath;
}
