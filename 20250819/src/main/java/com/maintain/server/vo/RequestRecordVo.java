package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

import static com.maintain.server.Constants.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2020-11-09
 */
@Data
public class RequestRecordVo {
    /**
     * id
     */
    private int id;
    /**
     * 接口路径
     */
    private String uri;
    /**
     * 模块
     */
    private String module;
    /**
     * 接口开始时间
     */
    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date startTime;
    /**
     * 接口结束时间
     */
    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date endTime;
    /**
     * 入库时间
     */
    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;
    /**
     * 接口耗时
     */
    private long timeSpent;
}