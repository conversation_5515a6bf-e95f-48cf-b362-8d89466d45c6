package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import com.maintain.server.type.AlarmStatusType;
import lombok.Data;
import java.util.Date;

@Data
public class AnalysisVo {

    private Integer id;

    private String ip;

    private AlarmStatusType status;

    private Integer process;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date updateTime;
}
