package com.maintain.server.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maintain.server.Constants;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */
@Data
public class MysqlBackupVo {
    private Integer id;

    /**
     * 备份时间
     */
    @JsonFormat(pattern = Constants.YYYY_MM_DD)
    private String backupTime;

    /**
     * 0-按天，1-按周
     */
    private int backupType;

    /**
     * 备份的数据库名称
     */
    private String backupDatabases;

    /**
     * 是否备份成功,1-成功，0-失败
     */
    private int success;
    /**
     * 备份文件所在IP
     */
    private String ip;

    /**
     * 异机备份是否成功，1-成功，0-失败
     */
    private int remoteSuccess;

    /**
     * 计算状态时是否忽略该条记录，1-是，0-否
     */
    private int ignoreStatus;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;


        MysqlBackupVo backupVo = (MysqlBackupVo) o;

        if (backupType != backupVo.backupType) return false;
        if (success != backupVo.success) return false;
        if (backupTime != null ? !backupTime.equals(backupVo.backupTime) : backupVo.backupTime != null) return false;
        return backupDatabases != null ? backupDatabases.equals(backupVo.backupDatabases) : backupVo.backupDatabases == null;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (backupTime != null ? backupTime.hashCode() : 0);
        result = 31 * result + backupType;
        result = 31 * result + (backupDatabases != null ? backupDatabases.hashCode() : 0);
        return result;
    }
}