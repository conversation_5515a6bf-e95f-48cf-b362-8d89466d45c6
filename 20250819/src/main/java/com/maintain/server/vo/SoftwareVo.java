package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.OperateType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.SoftwareOperateType;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-11-08
 */
@Data
public class SoftwareVo implements Cloneable, Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String host;

    private String serverIp;

    private Integer serverId;

    @JSONField(name = "processPid")
    private Integer pid;

    // private Integer watchdogStatus;

    private Integer autoDaemon;

    private Long programSize;

    private String name;

    private String version;

    private String baseDir;

    private String deployDir;

    private String realDir;

    private String usedMemory;

    private String totalMemory;

    private String cpuPercent;

    private String memoryPercent;

    private String role;

    /** 是否手动配置启动程序 */
    private Boolean config;

    /** 识别程序的唯一key */
    private String keys;

    /** 启动脚本的路径 */
    private String scriptPath;

    /** 脚本启动命令 */
    private String script;

    /**
     * TODO 程序占用磁盘空间比例
     */
    private String diskPercent;

    private String totalDisk;

    /**
     * 启动参数
     */
    private String startParam;

    //json
    @JSONField(deserialize = false)
    private String dbLogInfo;
    private Map<String, Object> logInfo;

    @JSONField(deserialize = false)
    private String dbConfInfo;
    private Map<String, Object> confInfo;

    @JSONField(deserialize = false)
    private String dbCommonInfo;
    private Map<String, Object> commonInfo;

    @JSONField(deserialize = false)
    private String dbSelfInfo;
    private Map<String, Object> selfInfo;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date lastUpdateTime;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date lastHeartbeatTime;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date capTime;

    private Object info;

    private AlarmStatusType status;

    private SoftwareOperateType operateType;

    private String description;

    private Long runningTime;

    private Boolean heartMonitor;

    private String logPath;
    private String logSuffix;
    private String configPath;

//    private Map<String, AlarmStatusType> alarmTypeMap;

    /**
     * 操作类型 手动开启关闭：1，agent守护开启：2
     */
    private OperateType startType;

    /**
     * 状态  开启：1 ，关闭：0
     */
    private ProgramStatusType programStatus;

    /**
     * 重启次数
     */
    private Integer restartCount;

    /**
     * 最近一次重启时间
     */
    private Date restartTime;

    /**
     * 当天error日志数量
     */
    private Integer logCount;
    /**
     * 启动脚本是否存在
     */
    private Integer isExists;

    /**
     * 进程数量
     */
    private Integer processCount;

    private String note;

    private Integer minHeapSize;

    private Integer maxHeapSize;

    private String closeScript;

    public static List<SoftwareVo> deepCloneList(List<SoftwareVo> origin) {
        List<SoftwareVo> copy = new ArrayList<>(origin.size());
        try {
            for (SoftwareVo anOrigin : origin) {
                copy.add(anOrigin.clone());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return copy;
    }

    @Override
    public SoftwareVo clone() throws CloneNotSupportedException {
        return (SoftwareVo) super.clone();
    }

}