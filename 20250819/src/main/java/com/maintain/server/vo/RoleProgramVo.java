package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-12-11
 */
@Data
public class RoleProgramVo {

    @JSONField(name = "old_name")
    private String oldName;

    @JSONField(name = "need_delete_files")
    private String needDeleteFiles;

    @JSONField(name = "is_full_package")
    private Boolean isFullPackage;

    @JSONField(name = "ignore_backup")
    private String ignoreBackup;
}