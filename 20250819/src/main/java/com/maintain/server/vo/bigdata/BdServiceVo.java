package com.maintain.server.vo.bigdata;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/19
 */
@Data
public class BdServiceVo {
    private String node;
    private String serviceName;
    private String serviceType;
    private String clusterName;
    private String state;
    private String alertsSummary;
    private String components;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}
