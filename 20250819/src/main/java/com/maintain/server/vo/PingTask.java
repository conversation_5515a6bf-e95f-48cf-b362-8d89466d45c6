package com.maintain.server.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-11-20
 */
public class PingTask {
    /**
     * 调用时间
     */
    private String callTime;
    /**
     * 存活的Agent列表
     */
    private List<String> agentList;
    /**
     * 耗时限制，单位：ms
     */
    private long timeLimit = 10L;

    public String getCallTime() {
        return callTime;
    }

    public void setCallTime(String callTime) {
        this.callTime = callTime;
    }

    public List<String> getAgentList() {
        return agentList;
    }

    public void setAgentList(List<String> agentList) {
        this.agentList = agentList;
    }

    public long getTimeLimit() {
        return timeLimit;
    }

    public void setTimeLimit(long timeLimit) {
        this.timeLimit = timeLimit;
    }

    @Override
    public String toString() {
        return "PingTask{" +
                "callTime='" + callTime + '\'' +
                ", agentList=" + agentList +
                ", timeLimit=" + timeLimit +
                '}';
    }
}