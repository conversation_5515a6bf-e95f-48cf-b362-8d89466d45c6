package com.maintain.server.vo;

import lombok.Data;

import java.util.List;

@Data
public class UserVo {

    private Integer id;

    private String name;

    private String pswd;

    List<RoleVo> roles;

    /**
     * 设计缺陷，一开始用户与角色是一对多，后面改成一对一
     */
    private Integer roleId;

    /**
     * 2018-11-01
     * 关联角色的name属性，目前设计是一个账号一个角色，角色就表示部门
     */
    private String department;

    private String createTime;

    private String lastLoginTime;


    public UserVo() {
    }

    public UserVo(String name, String pswd) {
        this.name = name;
        this.pswd = pswd;
    }
}
