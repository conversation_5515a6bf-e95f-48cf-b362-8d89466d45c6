package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.SoftwareOperateType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-08
 * AgentVo，代表服务器，即作为Agent程序自身的module，也兼顾为其他程序的module，因为在自动部署过程中需要通过SSH建立连接方式
 */
@Data
@Accessors(chain = true)
public class AgentVo implements Constants, Serializable,Cloneable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private AlarmStatusType status;

    private String version;

    private String ip;

    private Integer os;

    private String note;

    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date lastHeartbeatTime;

    private List<Integer> roleIds;

    private String role;

    private String name;

    private String pswd;

    private String rootPswd;

    private Integer port;

    private Integer autoNTP;

    private Integer pid;

    private Boolean connect;

    private String description;

    /**
     * 0：未守护
     * 1：守护
     * 是否开启守护狗.
     */
    private int autoWatchDog;

    private List<String> ips;

    private SoftwareOperateType operateType;

    private ProgramStatusType programStatus;

    private Integer ntpStatus;

    private Integer existsVpn;

    @Override
    public AgentVo clone() throws CloneNotSupportedException {
        return (AgentVo) super.clone();
    }
}