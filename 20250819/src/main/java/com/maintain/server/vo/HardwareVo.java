package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.github.pagehelper.PageInfo;
import com.maintain.server.Constants;
import com.maintain.server.type.AlarmStatusType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-10-24
 */
@Data
public class HardwareVo implements Constants, Serializable {

    private String type;

    private Integer id;

    private Integer agentId;

    private AlarmStatusType status;

    private String note;

 /*   @JSONField(serialize = false)
    private String reason;*/

    private String host;

    private String ip;

    private List<String> ipArray;

    private String ips;

    private Integer ports;

    private String role;

    private Integer firewallStatus;

    private Integer os;

    private String osName;

    private String usedCpu;

    private List<Map<String, String>> diskInfo;

    private List<NicConfig> nicInfo;

    @JSONField(serialize = false)
    private String dbDiskInfo;

    @JSONField(serialize = false)
    private String dbNicInfo;

    private String usedDisk;

    private String totalDisk;

    private String diskUsedPercent;

    private String usedMemory;

    private String totalMemory;

    private String memoryUsedPercent;

    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date lastHeartbeatTime;

    private PageInfo<HardwareHistoryHealth> hardwareHistoryHealthList;

    private Info info;

    @JSONField(deserialize = false)
    private String description;

    private AlarmStatusType cpuStatus;

    private AlarmStatusType memoryStatus;

    private AlarmStatusType diskStatus;

    private Boolean show;

    private Integer receiveBytePerSecond;
    private Integer sendBytePerSecond;

    private List<ProcessInfo> cpuTopProcessInfo;
    private List<ProcessInfo> memTopProcessInfo;
    private String cpuTopProcess;
    private String memTopProcess;

    @Data
    public static class ProcessInfo{
        private double cpuPercent;
        private long memRssSize;
        private long memSize;
        private String capTime;
        private String command;
        private String name;
        private int pid;
    }

    @Data
    public class Info {
        private String capTime;

        private List<Map<String, String>> disk;

        private String os;

        private Map<String, String> mem;

        private String osName;

        private String name;

        private Map<String, String> cpu;

        private List<NicConfig> nic;

        private List<Integer> ports;

        private Integer firewallStatus;

        private Map<String, String> diskInfo;

        private Network network;
    }

    @Data
    public static class NicConfig {
        private Map<String, Object> netInterfaceConfig;

        private String speed;
        // private Long osId;
    }

    @Data
    public static class Network {
        /**
         * 时间戳，秒
         */
        private long timestamp;
        /**
         * 收到的字节
         */
        private long receiveBytes;
        /**
         * 发送的字节
         */
        private long sendBytes;
        /**
         * 网卡名称
         */
        private String name;
        /**
         * 时间间隔，秒
         */
        private long interval;
    }
}