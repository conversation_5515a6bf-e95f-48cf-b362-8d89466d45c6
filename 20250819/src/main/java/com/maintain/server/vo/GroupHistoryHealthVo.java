package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Data
public class GroupHistoryHealthVo implements Serializable, Constants {
     @JSONField(serialize = false)
    private Integer id;

    @JSONField(serialize = false)
    private Integer groupId;

    private String name;

    private Integer status;

    //@JSONField(format = YYYY_MM_DD_HH_MM_SS)
    @JSONField(serialize = false)
    private Date createTime;

    private String description;


    public Integer getId() {
        return id;
    }

}