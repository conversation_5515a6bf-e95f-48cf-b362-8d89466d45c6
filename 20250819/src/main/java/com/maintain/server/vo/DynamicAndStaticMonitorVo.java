package com.maintain.server.vo;

import lombok.Data;


@Data
public class DynamicAndStaticMonitorVo {

    private Long id;

    private String monitorTime;

    private Long hisEmlNotAnalysis;

    private Long emlNotAnalysis;

    private Long hisEmlAnalysis;

    private Long emlAnalysis;

    private Integer analysisType;

    private Long hisSuccessNum;

    private Long successNum;

    private Long hisFailNum;

    private Long failNum;

    private Long hisNotAnalysisNum;

    private Long notAnalysisNum;

    private String failAnalysisThreshold;

    private Long analysisThreshold;

    private Integer status;

    private String desc;

}
