package com.maintain.server.vo;

/**
 * <AUTHOR>
 * @date 2018-11-14
 * 文件的双向链表
 */
public class FileNode {

    private FileNode next;

    private FileNode pre;

    private String path;

    public FileNode(){}

    public FileNode(String path){
        this.path = path;
    }

    public FileNode getNext() {
        return next;
    }

    /**
     * Setter method for property <tt>next</tt>.
     *
     * @param next value to be assigned to property next
     */
    public void setNext(FileNode next) {
        this.next = next;
    }

    public FileNode getPre() {
        return pre;
    }

    /**
     * Setter method for property <tt>pre</tt>.
     *
     * @param pre value to be assigned to property pre
     */
    public void setPre(FileNode pre) {
        this.pre = pre;
    }

    public String getPath() {
        return path;
    }

    /**
     * Setter method for property <tt>path</tt>.
     *
     * @param path value to be assigned to property path
     */
    public void setPath(String path) {
        this.path = path;
    }
}