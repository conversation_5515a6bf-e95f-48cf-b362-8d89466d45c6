package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-29
 * 业务监控
 */
@Data
public class BusinessMonitorVo implements Serializable {

    private Integer id;

    private String ip;

    @J<PERSON>NField(name = "name")
    private String softwareName;

    private List<BusinessData> data;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date startTime;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date endTime;

    private String createTime;

    private String version;

    @Data
    public static class BusinessData implements Serializable{
        private String module;

        private String content;

        private String result;

        private String description;
    }
}