package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-02-25
 */
@Data
public class SoftwareRestartVo implements Serializable {

    private Integer id;

    private Integer softwareId;

    private Integer status;

    private String description;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

}