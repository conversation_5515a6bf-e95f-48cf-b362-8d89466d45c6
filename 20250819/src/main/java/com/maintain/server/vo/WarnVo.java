package com.maintain.server.vo;

import com.maintain.server.type.WarnType;
import lombok.Data;
import java.util.Date;

@Data
public class WarnVo {

    private Integer id;

    private WarnType type;

    private Integer status;

    private String description;

    private Date modifyTime;

    private String param1;

    private String param2;

    private String param3;

    private String param4;

    private String param5;

    private String param6;

    private String param7;

    private String param8;

    private String param9;

    private String param10;
}
