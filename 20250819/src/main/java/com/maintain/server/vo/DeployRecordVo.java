package com.maintain.server.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-11-19
 * 平台版本信息
 */
@Data
public class DeployRecordVo {

    private Integer id;

    private String name;

    private String version;

    private Date createTime;

    private Date modifyTime;

    private Integer status;

    private String remark1;

    private String remark2;

    private String remark3;

    private String remark4;

    private String remark5;

    private String configPath;

    private String product;

    private String programPaths;

    private String fullPackage;

    private Integer isModifyCommonConfig;

}