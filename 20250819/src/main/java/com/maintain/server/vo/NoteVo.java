package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import lombok.Data;

import java.util.Date;

@Data
public class NoteVo {

    private Integer id;

    private String content;

    private String author;

    @JSONField(format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date modifyTime;

    private String title;

    private Boolean remind;
}
