package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import com.maintain.server.type.AlarmStatusType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-10-29
 */
@Data
public class HardwareHistoryHealth implements Serializable,Constants {

    private Integer id;

    private Integer agentId;

    private AlarmStatusType status;

    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    private String description;

    private String cpuPercent;

    private String memoryPercent;

    private String diskPercent;

    private String usedMemory;

    private String usedDisk;

    private Integer receiveBytePerSecond;

    private Integer sendBytePerSecond;
}