package com.maintain.server.vo;

import com.maintain.server.type.ResponseType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018-10-10
 */
@Data
public class ResponseVo {

    private Integer code;

    private String msg;

    private Object data;

    public ResponseVo() {
    }

    public ResponseVo(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResponseVo(ResponseType responseType) {
        this.code = responseType.getCode();
        this.msg = responseType.getMsg();
    }

    public void setResponseType(ResponseType responseType) {
        this.code = responseType.getCode();
        this.msg = responseType.getMsg();
    }

    public static ResponseVo getFailed() {
        return new ResponseVo(ResponseType.OPERATE_ERROR);
    }

    public static ResponseVo getSuccess() {
        return new ResponseVo(ResponseType.SUCCESS);
    }

    public static ResponseVo getSuccess(Object data) {
        ResponseVo responseVo = getSuccess();
        responseVo.setData(data);
        return responseVo;
    }
}