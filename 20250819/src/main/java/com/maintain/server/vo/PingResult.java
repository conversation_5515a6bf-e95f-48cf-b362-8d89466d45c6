package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

import static com.maintain.server.Constants.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2020-11-20
 */
public class PingResult {

    /**
     * callTime : 2020-11-20 10:50:01
     * startTime : 2020-11-20 14:26:52
     * source : 192.168.120.236
     * dest : 192.168.120.237
     * timeSpent : 2
     * connectFlag : true
     */
    private Integer id;
    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date callTime;
    @JSONField(format = YYYY_MM_DD_HH_MM_SS)
    private Date startTime;
    private String source;
    private String dest;
    private int timeSpent;
    private boolean connectFlag;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getDest() {
        return dest;
    }

    public void setDest(String dest) {
        this.dest = dest;
    }

    public int getTimeSpent() {
        return timeSpent;
    }

    public void setTimeSpent(int timeSpent) {
        this.timeSpent = timeSpent;
    }

    public boolean isConnectFlag() {
        return connectFlag;
    }

    public void setConnectFlag(boolean connectFlag) {
        this.connectFlag = connectFlag;
    }

}