package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.maintain.server.type.OsType;
import com.maintain.server.utils.DateUtil;

import static com.maintain.server.Constants.MAINTAIN_LINUX_TEMP;
import static com.maintain.server.Constants.MAINTAIN_WINDOWS_TEMP;
import static com.maintain.server.Constants.MYSQL_BACKUP_DAILY;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */
public class DbBackupManageVo {
    /**
     * id
     */
    private Integer id;
    /**
     * 本机备份路径
     */
    private String localPath;
    /**
     * 本机IP
     */
    private String localIp;
    /**
     * 本机操作系统类型
     */
    private OsType localOsType;
    /**
     * 数据库用户名
     */
    private String dbUser;
    /**
     * 数据库密码
     */
    private String dbPass;
    /**
     * 异机备份路径
     */
    private String remotePath;
    /**
     * 异机IP
     */
    private String remoteIp;
    /**
     * 异机操作系统类型
     */
    private OsType remoteOsType;
    /**
     * 是否备份成功
     */
    @JsonIgnore
    @JSONField(serialize = false)
    private boolean success;



    public String getBackupTempPath(String rootPath, String tempPath, String date, String separator) {
        return (rootPath.endsWith(separator) ? rootPath : (rootPath + separator)) + tempPath + separator +
                date + separator + getLocalIp() + separator;
    }


    public String getRemoteFilePath(boolean flag, String tempPath, String date, String separator) {
        return (flag ? (getLocalPath() + (getLocalPath().endsWith(separator) ? "" : separator)) :
         (getRemotePath() + (getRemotePath().endsWith(separator) ? "" : separator))) +
                tempPath + separator + date + separator + getLocalIp() + separator;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath == null ? null : localPath.trim();
    }

    public String getLocalIp() {
        return localIp;
    }

    public void setLocalIp(String localIp) {
        this.localIp = localIp == null ? null : localIp.trim();
    }

    public String getDbUser() {
        return dbUser;
    }

    public void setDbUser(String dbUser) {
        this.dbUser = dbUser == null ? null : dbUser.trim();
    }

    public String getDbPass() {
        return dbPass;
    }

    public void setDbPass(String dbPass) {
        this.dbPass = dbPass == null ? null : dbPass.trim();
    }

    public String getRemotePath() {
        return remotePath;
    }

    public void setRemotePath(String remotePath) {
        this.remotePath = remotePath == null ? null : remotePath.trim();
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp == null ? null : remoteIp.trim();
    }

    public OsType getLocalOsType() {
        return localOsType;
    }

    public void setLocalOsType(OsType localOsType) {
        this.localOsType = localOsType;
    }

    public OsType getRemoteOsType() {
        return remoteOsType;
    }

    public void setRemoteOsType(OsType remoteOsType) {
        this.remoteOsType = remoteOsType;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}