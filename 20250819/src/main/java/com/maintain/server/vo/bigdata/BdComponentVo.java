package com.maintain.server.vo.bigdata;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/19
 */
@Data
public class BdComponentVo {
    private String node;
    private String category;
    private String clusterName;
    private String componentName;
    private String desiredStack;
    private String desiredVersion;
    private String displayName;
    private String serviceName;
    private Integer startedCount;
    private String state;
    private Integer totalCount;
    private Integer unknownCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;
}
