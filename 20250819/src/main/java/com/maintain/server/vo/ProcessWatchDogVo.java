package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.maintain.server.Constants;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-10-10
 */
@Data
public class ProcessWatchDogVo {

    @J<PERSON><PERSON>ield(name = "ModuleName")
    private String  moduleName;

    @J<PERSON><PERSON>ield(name = "RestarTime")
    private String  restarTime;

    @JSONField(name = "NowStats")
    private String  nowStats;

    @JSONField(name = "UserCmd")
    private String userCmd ;

    @JSONField(name = "ModuleDescrypt")
    private String  moduleDescrypt;

    @J<PERSON><PERSON>ield(name = "ConfigPath")
    private String configPath ;

    @J<PERSON><PERSON>ield(name = "ModeulePath")
    private String modeulePath ;

    @JSONField(name = "StartAction")
    private String startAction ;

    @JSONField(name = "ISConfiged")
    private String  iSConfiged;

    @JSONField(name = "Parameter")
    private String  parameter;

    @J<PERSON>NField(name = "IntervalTime")
    private String intervalTime ;

    @JSONField(name = "MemoryLimit")
    private String  memoryLimit;

    @J<PERSON><PERSON>ield(name = "CPULimit")
    private String cpuLimit ;

    @JSO<PERSON>ield(name = "SessionId")
    private String sessionId ;

    @JSONField(name = "Pid")
    private String pid ;

    @JSONField(name = "StartTime",format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date startTime ;

    @JSONField(name = "EndTime",format = Constants.YYYY_MM_DD_HH_MM_SS)
    private Date endTime;

    @JSONField(name = "CurrentDir")
    private String currentDir;

}