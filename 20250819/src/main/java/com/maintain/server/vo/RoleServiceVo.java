package com.maintain.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-11-19
 * 角色-服务对应关系
 */
@Data
public class RoleServiceVo {

    @JSONField(name = "platform_name")
    private String platformName;

    @JSONField(name = "platform_version")
    private String platformVersion;

    @JSONField(name = "platform_regular")
    private String platformRegular;

    @JSONField(name = "is_full_package")
    private Boolean isFullPackage;

    private Map<String,Map<String,Map<String,RoleProgramVo>>> relations;

}