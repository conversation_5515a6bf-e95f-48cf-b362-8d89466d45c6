package com.maintain.server.aop;

import com.alibaba.fastjson.TypeReference;
import com.common.log.Log;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.mapper.HardwareMapper;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.OperateRecordService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.java_websocket.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2018-11-23
 * 审计管理，切面
 */
@Aspect
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@Component
@Slf4j
public class OperateRecordAspect {

    @Autowired
    private HardwareMapper hardwareMapper;

    @Autowired
    private AgentService agentService;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private OperateRecordService operateRecordService;

    @Autowired
    private SessionRegistry sessionRegistry;

    @Pointcut(value = "@annotation(OperateRecordAnnotation)")
    public void pointcut() {
    }

    @Before(value = "pointcut()")
    public void doBeforeAdvice() {
    }

    @AfterReturning(value = ("pointcut() && @annotation(OperateRecordAnnotation)"), returning = "obj")
    public void doAfterAdvice(JoinPoint joinPoint, OperateRecordAnnotation OperateRecordAnnotation, Object obj) {
        String user = getUserName();
        if (StringUtils.isEmpty(user)) {
            try {
                Object[] args = joinPoint.getArgs();
                if (args != null) {
                    for (Object arg : args) {
                        if (arg instanceof WebSocket) {
                            WebSocket webSocket = (WebSocket) arg;
                            UserVo userVo = webSocket.getAttachment();
                            if (userVo != null) {
                                user = userVo.getName();
                            }
                        }
                    }
                }
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        }
        if (StringUtils.isNotEmpty(user)) {
            OperateRecordVo operateRecordVo = new OperateRecordVo();
            operateRecordVo.setUser(user);
            /*String msg = OperateRecordAnnotation.name() + "，操作成功";*/
            String msg = produceMsg(joinPoint.getArgs(), OperateRecordAnnotation, "，操作成功");
            operateRecordVo.setOperate(msg);
            operateRecordService.addOperateRecord(operateRecordVo);
        }
    }

    private String getUserName() {
        String user = "";
        SecurityContext context = SecurityContextHolder.getContext();
        if (context != null) {
            Authentication authentication = context.getAuthentication();
            if (authentication != null) {
                user = authentication.getName();
            }
        }
        return user;
    }

    private void print(Pointcut pointcut) {
        String names = pointcut.argNames();
        System.out.println(names);
        Field[] f = pointcut.annotationType().getDeclaredFields();
        System.out.println(f);
        String value = pointcut.value();
        System.out.println(value);
    }

    @AfterThrowing(value = ("pointcut() && @annotation(OperateRecordAnnotation)"), throwing = "e")
    public void doExceptionAdvice(JoinPoint joinPoint, OperateRecordAnnotation OperateRecordAnnotation, Exception e) {
        // 记录一波异常
        String method = joinPoint.getSignature().getName();
        joinPoint.getArgs();
        Log.high.error(method + " .... " + e.getMessage(), e);
        String user = getUserName();
        if (StringUtils.isEmpty(user)) {
            try {
                Object[] args = joinPoint.getArgs();
                if (args != null) {
                    for (Object arg : args) {
                        if (arg instanceof WebSocket) {
                            WebSocket webSocket = (WebSocket) arg;
                            UserVo userVo = webSocket.getAttachment();
                            if (userVo != null) {
                                user = userVo.getName();
                            }
                        }
                    }
                }
            } catch (Exception e1) {
                Log.high.error(e1.getMessage(), e1);
            }
        }
        if (StringUtils.isNotEmpty(user)) {
            OperateRecordVo operateRecordVo = new OperateRecordVo();
            operateRecordVo.setUser(user);
            /*operateRecordVo.setOperate(OperateRecordAnnotation.name() + "，操作失败，异常信息：" + e.getMessage());*/
            String msg = produceMsg(joinPoint.getArgs(), OperateRecordAnnotation, "，操作失败，异常信息：" + (e.getMessage() == null ? "" : e.getMessage().replace("操作失败,失败原因：", "")));
            operateRecordVo.setOperate(msg);
            operateRecordService.addOperateRecord(operateRecordVo);
        } else {
            Log.high.info(OperateRecordAnnotation.name() + "，获取用户信息为空");
        }
    }

    private String produceMsg(Object[] args, OperateRecordAnnotation operateRecordAnnotation, String result) {
        if ("agent".equals(operateRecordAnnotation.module())) {
            return produceAgentMsg(args, operateRecordAnnotation, result);
        } else if ("hardware".equals(operateRecordAnnotation.module())) {
            return produceHardwareMsg(args, operateRecordAnnotation, result);
        } else if ("software".equals(operateRecordAnnotation.module())) {
            return produceSoftwareMsg(args, operateRecordAnnotation, result);
        } else if ("softwareWithWebSocket".equals(operateRecordAnnotation.module())) {
            return produceSoftwareWithWebSocket(args, operateRecordAnnotation, result);
        } else if ("logstash".equals(operateRecordAnnotation.module())) {
            return produceLogstashMsg(args, operateRecordAnnotation, result);
        }
        return operateRecordAnnotation.name() + result;
    }


    private String produceLogstashMsg(Object[] args, OperateRecordAnnotation operateRecordAnnotation, String result) {
        try {
            String param = getStringParamInTwoParams(args);
            Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
            });
            String replaced = "所有机器";
            if (map.containsKey("ip")) {
                replaced = map.get("ip").toString();
            }
            return String.format(operateRecordAnnotation.name(), replaced) + result;
        } catch (Exception e) {
            log.error("handle error,param:" + Arrays.toString(args));
        }
        return String.format(operateRecordAnnotation.name(), "") + result;
    }

    private String produceSoftwareWithWebSocket(Object[] args, OperateRecordAnnotation operateRecordAnnotation, String result) {
        try {
            Map<String, Object> map = (Map<String, Object>) args[0];
            String operateType = map.get("operateType").toString();
            String operate = "";
            if ("0".equals(operateType)) {
                operate = "关闭";
            } else if ("1".equals(operateType)) {
                operate = "开启";
            } else if ("2".equals(operateType)) {
                operate = "重启";
            }
            Integer softwareId = Integer.valueOf(map.get("softwareId").toString());
            SoftwareCriteria criteria = new SoftwareCriteria();
            criteria.setId(softwareId);
            SoftwareVo softwareVo = softwareService.getSoftwareInfo(criteria);
            return String.format(operateRecordAnnotation.name(), operate, softwareVo.getHost(), softwareVo.getName()) + result;
        } catch (Exception e) {
            log.error("handle error,param:" + Arrays.toString(args));
        }
        return String.format(operateRecordAnnotation.name(), "", "") + result;
    }

    private String produceSoftwareMsg(Object[] args, OperateRecordAnnotation operateRecordAnnotation, String result) {
        try {
            if (args.length == 1) {
                if (args[0] instanceof SoftwareVo) {
                    SoftwareVo softwareVo = (SoftwareVo) args[0];
                    if (StringUtils.isNotEmpty(softwareVo.getName())) {
                        return String.format(operateRecordAnnotation.name(), softwareVo.getHost(), softwareVo.getName()) + result;
                    } else {
                        SoftwareCriteria criteria = new SoftwareCriteria();
                        criteria.setId(softwareVo.getId());
                        SoftwareVo temp = softwareService.getSoftwareInfo(criteria);
                        return String.format(operateRecordAnnotation.name(), temp.getHost(), temp.getName()) + result;
                    }
                } else if (args[0] instanceof String) {
                    return String.format(operateRecordAnnotation.name(), args[0]) + result;
                }
            }
            if (args.length == 2 && args[0] instanceof SoftwareVo) {
                SoftwareVo softwareVo = (SoftwareVo) args[0];
                if (Boolean.parseBoolean(args[1].toString())) {
                    Map<String, Object> param = new HashMap<>(16);
                    param.put("processCount", softwareVo.getProcessCount());
                    param.put("realDir", softwareVo.getRealDir());
                    param.put("heartMonitor", softwareVo.getHeartMonitor());
                    param.put("config", softwareVo.getConfig());
                    param.put("keys", softwareVo.getKeys());
                    param.put("scriptPath", softwareVo.getScriptPath());
                    param.put("script", softwareVo.getScript());
                    param.put("closeScript", softwareVo.getCloseScript());
                    return String.format(operateRecordAnnotation.name(), "编辑", softwareVo.getHost(), softwareVo.getName(), ",参数如下:", param.toString()) + result;
                } else {
                    SoftwareCriteria criteria = new SoftwareCriteria();
                    criteria.setId(softwareVo.getId());
                    SoftwareVo temp = softwareService.getSoftwareInfo(criteria);
                    return String.format(operateRecordAnnotation.name(), softwareVo.getHeartMonitor() ? "开启" : "关闭", temp.getHost(), temp.getName(), "的心跳监控", "") + result;
                }
            }
            if (args.length > 2) {
                AgentVo agentVo = (AgentVo) args[0];
                return String.format(operateRecordAnnotation.name(), agentVo.getIp(), args[1].toString(), args[2].toString()) + result;
            } else {
                log.error("error params==" + Arrays.toString(args));
            }
        } catch (Exception e) {
            log.error("handle error,param:" + Arrays.toString(args));
        }
        return String.format(operateRecordAnnotation.name(), "", "", "") + result;
    }

    private String produceHardwareMsg(Object[] args, OperateRecordAnnotation operateRecordAnnotation, String result) {
        try {
            if (args.length == 1 && args[0] instanceof HardwareVo) {
                HardwareVo hardwareVo = (HardwareVo) args[0];
                if (StringUtils.isEmpty(hardwareVo.getHost())) {
                    HardwareCriteria criteria = new HardwareCriteria();
                    criteria.setAgentId(hardwareVo.getAgentId());
                    List<HardwareVo> hardwareVos = hardwareMapper.getHardwareInfos(criteria);
                    if (hardwareVos != null && hardwareVos.size() == 1) {
                        hardwareVo.setHost(hardwareVos.get(0).getIp());
                    }
                }
                return String.format(operateRecordAnnotation.name(), hardwareVo.getHost(), hardwareVo.getNote()) + result;
            }
            if (args.length == 8) {
                Map<String, Object> param = new HashMap<>(16);
                param.put("status", args[1]);
                param.put("warn", args[2]);
                param.put("error", args[3]);
                param.put("warnPercent", args[4]);
                param.put("errorPercent", args[5]);
                param.put("disk", args[6]);
                param.put("warnType", args[7]);
                return String.format(operateRecordAnnotation.name(), args[0], param.toString()) + result;
            }
            String param = getStringParamInTwoParams(args);

            Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
            });
            String ip = map.get("ip").toString();
            return String.format(operateRecordAnnotation.name(), ip) + result;
        } catch (Exception e) {
            log.error("error,param:" + Arrays.toString(args), e);
        }
        return String.format(operateRecordAnnotation.name(), "") + result;
    }

    private String produceAgentMsg(Object[] args, OperateRecordAnnotation operateRecordAnnotation, String result) {
        if (args.length == 1) {
            if (args[0] instanceof AgentVo) {
                AgentVo agentVo = (AgentVo) args[0];
                Map<String, Object> param = new HashMap<>(8);
                if (StringUtils.isNotEmpty(agentVo.getNote())) {
                    param.put("note", agentVo.getNote());
                }
                if (StringUtils.isNotEmpty(agentVo.getRole())) {
                    param.put("role", agentVo.getRole());
                }
                if (param.isEmpty()) {
                    return String.format(operateRecordAnnotation.name(), agentVo.getIp()) + "，无有效更新内容" + result;
                }
                return String.format(operateRecordAnnotation.name(), agentVo.getIp()) + ",参数如下：" + param.toString() + result;
            }
            if (args[0] instanceof String) {
                return String.format(operateRecordAnnotation.name(), args[0]) + result;
            }
        }
        String param = getStringParamInTwoParams(args);
        try {
            String replaced = "";
            if (StringUtils.isNotEmpty(param)) {
                if (!operateRecordAnnotation.isJsonParam()) {
                    replaced = param;
                } else {
                    Map<String, Object> map = JsonUtil.parseObject(param, new TypeReference<Map<String, Object>>() {
                    });
                    if (map.containsKey("id")) {
                        Object id = map.get("id");
                        AgentCriteria criteria = new AgentCriteria();
                        criteria.setId(Integer.valueOf(id.toString()));
                        AgentVo agentVo = agentService.getAgentByCriteria(criteria);
                        replaced = agentVo.getIp();
                    } else if (map.containsKey("ip")) {
                        replaced = map.get("ip").toString();
                    } else {
                        log.error("unknown msg:" + param);
                    }
                }
                return String.format(operateRecordAnnotation.name(), replaced) + result;
            }
        } catch (Exception e) {
            log.error("error,param:" + param, e);
        }
        return String.format(operateRecordAnnotation.name(), "所有机器") + result;
    }

    private String getStringParamInTwoParams(Object[] args) {
        if (args == null || args.length == 0) {
            return "";
        }
        for (Object arg : args) {
            if (arg instanceof WebSocket) {
                continue;
            }
            if (arg == null) {
                return "";
            }
            return arg.toString();
        }
        return "";
    }
}