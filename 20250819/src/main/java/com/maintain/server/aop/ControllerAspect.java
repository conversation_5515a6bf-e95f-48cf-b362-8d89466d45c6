package com.maintain.server.aop;

import com.maintain.server.enums.OperateTypeEnum;
import com.maintain.server.mapper.RequestRecordMapper;
import com.maintain.server.type.OperateType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.RequestRecordVo;
import com.maintain.server.vo.WebSocketRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.java_websocket.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-11-09
 */
@Aspect
@Component
@Slf4j
public class ControllerAspect {

    @Autowired
    private RequestRecordMapper requestRecordMapper;

    @Around("execution(public * com.maintain.server.controller.*.*(..))")
    public Object aroundController(ProceedingJoinPoint pip) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        RequestRecordVo vo = new RequestRecordVo();
        vo.setUri(request.getRequestURI());
        long start = System.currentTimeMillis();
        vo.setStartTime(new Date());
        Object o = pip.proceed();
        int moduleEnd = vo.getUri().indexOf("/", "/maintain".length() + 1);
        vo.setModule(moduleEnd == -1 ? "/" : vo.getUri().substring("/maintain".length(), moduleEnd));
        vo.setEndTime(new Date());
        vo.setCreateTime(new Date());
        vo.setTimeSpent(System.currentTimeMillis() - start);
        requestRecordMapper.addRequestRecord(vo);
        return o;
    }


    /*@Around("execution(public * com.maintain.server.websocket.WebSocketController.operate(..))")
    public Object aroundWebSocketController(ProceedingJoinPoint pip) throws Throwable {
        log.info("开始记录WebSocket相关切面数据");
        return pip.proceed();
        *//*String message = pip.getArgs()[1].toString();
        if ("ping".equals(message)) {
            ((WebSocket) pip.getArgs()[0]).send("pong");
            return pip.proceed();
        }
        RequestRecordVo vo = new RequestRecordVo();
        vo.setStartTime(new Date());
        long start = System.currentTimeMillis();
        Object o = pip.proceed();
        vo.setEndTime(new Date());
        vo.setTimeSpent((System.currentTimeMillis() - start));
        vo.setCreateTime(new Date());
        WebSocketRequestVo webSocketRequestVo = JsonUtil.parseObject(message, WebSocketRequestVo.class);
        vo.setModule(webSocketRequestVo.getService() + "WebSocket");
        vo.setUri(OperateTypeEnum.byType(webSocketRequestVo.getOperateType()).getOperate());
        requestRecordMapper.addRequestRecord(vo);
        return o;*//*
    }*/
}