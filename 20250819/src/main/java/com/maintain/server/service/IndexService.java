package com.maintain.server.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.type.ReportType;

import java.util.List;
import java.util.Map;

/**
 * Created by Azurio on 2019/1/31.
 */
public interface IndexService {

    List<Map<String, Object>> getMonitorData()throws ServiceException;

    Map<String,Object> getReport(ReportType type,String startTime, String endTime) throws ServiceException, JsonProcessingException;
}
