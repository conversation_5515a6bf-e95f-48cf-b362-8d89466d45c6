package com.maintain.server.service;

import com.maintain.server.criteria.PluginCriteria;
import com.maintain.server.vo.PluginVo;

import java.util.List;

/**
 * Created by Azurio on 2019/3/18.
 */
public interface PluginService {

    void addPlugin(PluginVo plugin);

    void updatePlugin(PluginVo pluginVo);

    List<PluginVo> getPlugins(PluginCriteria criteria);

    PluginVo getPlugin(PluginCriteria criteria);
}
