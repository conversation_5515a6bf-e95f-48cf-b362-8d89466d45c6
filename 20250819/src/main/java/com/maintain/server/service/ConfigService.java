package com.maintain.server.service;


import com.maintain.server.enums.ConfigModuleEnum;
import com.maintain.server.enums.ConfigTtypeEnum;
import com.maintain.server.vo.ConfigVo;

/**
 * 配置服务
 *
 * <AUTHOR>
 * @date 2025/7/26
 */
public interface ConfigService {
    /**
     * 获取配置
     *
     * @param moduleEnum
     * @param typeEnum
     * @param key
     * @return
     */
    ConfigVo getConfig(ConfigModuleEnum moduleEnum, ConfigTtypeEnum typeEnum, String key);

    /**
     * 保存
     *
     * @param moduleEnum
     * @param typeEnum
     * @param key
     * @param value
     */
    void save(ConfigModuleEnum moduleEnum, ConfigTtypeEnum typeEnum, String key, String value);

    /**
     * 更新
     *
     * @param id
     * @param value
     */
    void update(Integer id, String value);
}
