package com.maintain.server.service.impl;

import com.github.pagehelper.PageInfo;
import com.maintain.server.service.ReportService;
import com.maintain.server.type.ReportType;
import com.maintain.server.vo.ReportVo;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.net.URLEncoder;
import java.util.Date;
import java.util.LinkedList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ReportServiceImpl implements ReportService {

    @Value("${report.day}")
    private String dayPath;

    @Value("${report.week}")
    private String weekPath;

    @Value("${report.month}")
    private String monthPath;

    @Override
    public PageInfo<ReportVo> getReportPage(ReportType type, Integer page, Integer pageSize) {
        String directoryPath;
        if (type.equals(ReportType.DAY)) {
            directoryPath = dayPath;
        } else if (type.equals(ReportType.WEEK)) {
            directoryPath = weekPath;
        } else {
            directoryPath = monthPath;
        }
        final File directory = new File(directoryPath);
        if (!directory.exists()) {
            return null;
        }
        final File[] files = directory.listFiles();
        final LinkedList<ReportVo> list = new LinkedList<>();
        final PageInfo<ReportVo> pageInfo = new PageInfo<>(list);
        if (pageSize >= files.length) {
            for (int i = files.length - 1, j = 0; i >= 0; i--, j++) {
                list.add(new ReportVo(files[i].getName(), new Date(files[i].lastModified()), type, files[i].length(), files[i].getPath().substring(2).replace("\\","/")));
            }
        } else {
            int startIndex = (page - 1) * pageSize;
            int endIndex = startIndex + pageSize - 1;
            for (int i = files.length - 1, j = 0; i >= 0; i--, j++) {
                if (j > endIndex) {
                    break;
                } else if (j >= startIndex && j <= endIndex) {
                    list.add(new ReportVo(files[i].getName(), new Date(files[i].lastModified()), type, files[i].length(), files[i].getPath().substring(2).replace("\\","/")));
                }
            }
            pageInfo.setStartRow(startIndex);
            pageInfo.setEndRow(endIndex);
        }
        pageInfo.setPageNum(page);
        pageInfo.setPages(pageSize);
        pageInfo.setTotal(files.length);
        pageInfo.setPages(Double.valueOf(Math.ceil((files.length * 1.0 / pageSize))).intValue());
        return pageInfo;
    }

    public static void main(String[] args) {
        System.out.println(Double.valueOf(Math.ceil((11 * 1.0 / 5))).intValue());
    }

    @Override
    public ResponseEntity<byte[]> downLoadReport(String path) throws Exception {
        String[] dir = path.split("/");
        if(dir[0] != "report" && dir.length != 3){
            return new ResponseEntity<>(HttpStatus.SERVICE_UNAVAILABLE);
        }
        Pattern dot = Pattern.compile("[\\.]+");
        String dirPath = path.substring(0,path.lastIndexOf("/"));
        Matcher ctx = dot.matcher(dirPath);
        if(ctx.find()){
            return new ResponseEntity<>(HttpStatus.SERVICE_UNAVAILABLE);
        }
        final File file = new File(path);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在!");
        }
        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachment", file.getName());
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<>(FileUtils.readFileToByteArray(file), httpHeaders, HttpStatus.OK);
    }
}
