package com.maintain.server.service.impl;

import com.maintain.server.criteria.PluginCriteria;
import com.maintain.server.mapper.PluginMapper;
import com.maintain.server.service.PluginService;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.PluginVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-18
 */
@Service
public class PluginServiceImpl implements PluginService {

    @Autowired
    private PluginMapper pluginMapper;

    @Override
    @Transactional
    public void addPlugin(PluginVo plugin) {
        pluginMapper.addPlugin(plugin);
    }

    @Override
    @Transactional
    public void updatePlugin(PluginVo pluginVo) {
        pluginMapper.updatePlugin(pluginVo);
    }

    @Override
    public List<PluginVo> getPlugins(PluginCriteria criteria) {
        return pluginMapper.getPlugins(criteria);
    }

    @Override
    public PluginVo getPlugin(PluginCriteria criteria) {
        List<PluginVo> pluginVos = getPlugins(criteria);
        if (ListUtil.isNotEmpty(pluginVos)) {
            return pluginVos.get(0);
        }
        return null;
    }
}