package com.maintain.server.service.impl;

import com.common.log.Log;
import com.maintain.server.Constants;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.service.AgentService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.SoftwareOperateType;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018-10-08
 */
@Service("agentService")
public class AgentServiceImpl implements AgentService, IceFlag, Constants {

    @Value("${server.localHost}")
    private String maintainServerIp;

    @Autowired
    private AgentMapper agentMapper;

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AddResult addAgent(AgentVo agentVo) throws ServiceException {
        int succCount = 0;
        AddResult addResult = new AddResult();
        try {
            AgentCriteria criteria = new AgentCriteria();
            criteria.setIps(agentVo.getIps());
            criteria.setIp(agentVo.getIp());
            List<AgentVo> agent = agentMapper.getAgent(criteria);
            //添加操作
            if (ListUtil.isEmpty(agent)) {
                succCount = agentMapper.addAgent(agentVo);
                addResult.setAddSucc(succCount);
                //已存在，不允许此类更新操作
            } else {
                addResult.setRepeat(agent.size());
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return addResult;
    }

    @Override
    @OperateRecordAnnotation(name = "删除%s上的Agent", module = "agent", isJsonParam = false)
    @Transactional
    public void deleteAgent(String ip) {
        agentMapper.deleteAgent(ip);
    }


    @Override
    public List<AgentVo> getAgentList(AgentCriteria criteria) throws ServiceException {
        // AgentCriteria分页预留
        //PageHelper.startPage(criteria.getPn(),criteria.getPs());
        return  agentMapper.getAgent(criteria);
    }

    @Override
    public List<AgentVo> getAgentListForPlugin(String plugin) throws ServiceException {
        return agentMapper.getAgentForPlugin(plugin);
    }


    @Override
    public AgentVo getAgentByCriteria(AgentCriteria criteria) throws ServiceException {
        List<AgentVo> list = getAgentList(criteria);
        if (ListUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getPlatformRole(List<Integer> ids) {
        return agentMapper.getPlatformRole(ids);
    }

    @Override
    public List<Map<String,Object>> getAllHosts(AgentCriteria criteria) {
        return agentMapper.getAllHosts(criteria);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ImportResult importData(MultipartFile file) {
        int importSucc = 0;
        int importfail = 0;
        int repeat = 0;
        ImportResult importResult = new ImportResult();
        try {
            final InputStream inputStream = file.getInputStream();
            final XSSFWorkbook sheets = new XSSFWorkbook(inputStream);
            final XSSFSheet sheet = sheets.getSheetAt(0);
            final int rows = sheet.getPhysicalNumberOfRows();
            if(rows < 2){
                throw new ServiceException("数据不对");
            }
            for(int i = 1;i<rows;i++){
                final XSSFRow row = sheet.getRow(i);
                if(row == null){
                    continue;
                }
                final XSSFCell ipShell = row.getCell(0);
                final XSSFCell osShell = row.getCell(1);
                final XSSFCell roleShell = row.getCell(2);
                final XSSFCell usernameShell = row.getCell(3);
                final XSSFCell passwordShell = row.getCell(4);
                final XSSFCell rootpswdShell = row.getCell(5);
                final XSSFCell portShell = row.getCell(6);
                final XSSFCell ntpShell = row.getCell(7);
                if(Objects.isNull(ipShell) || Objects.isNull(osShell) || Objects.isNull(roleShell) || Objects.isNull(usernameShell)
                        || Objects.isNull(passwordShell) || Objects.isNull(portShell) || Objects.isNull(ntpShell)){
                    throw new ServiceException("数据有误");
                }
                final AgentVo agentVo = new AgentVo();
                agentVo.setIp(ipShell.getStringCellValue());
                agentVo.setOs(Double.valueOf(osShell.getNumericCellValue()).intValue());
                agentVo.setRole(roleShell.getStringCellValue());
                agentVo.setName(usernameShell.getStringCellValue());
                agentVo.setPswd(passwordShell.getStringCellValue());
                if (rootpswdShell!=null){
                    agentVo.setRootPswd(rootpswdShell.getStringCellValue());
                }
                agentVo.setPort(Double.valueOf(portShell.getNumericCellValue()).intValue());
                agentVo.setAutoNTP(Double.valueOf(ntpShell.getNumericCellValue()).intValue());
                agentVo.setProgramStatus(ProgramStatusType.CLOSE);
                agentVo.setOperateType(SoftwareOperateType.CLOSE);
                agentVo.setStatus(AlarmStatusType.YELLOW);
                agentVo.setVersion("1.1");
                agentVo.setDescription("agent尚未部署更新");
                final AgentCriteria agentCriteria = new AgentCriteria();
                agentCriteria.setIp(agentVo.getIp());
                final List<AgentVo> agent = agentMapper.getAgent(agentCriteria);

                if(ListUtil.isNotEmpty(agent)){
                    repeat++;
                    continue;
                }
                importSucc = agentMapper.addAgent(agentVo);
                importResult.setSuccCount(importSucc);
                importResult.setRepeat(repeat);
                importfail = rows - 1 - importSucc - repeat;
                importResult.setFailCount(importfail);
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(),e);
            throw new ServiceException("导入excel异常");
        }
        return importResult;
    }

    @Override
    @Transactional
    public void updateNtp(String ip, Integer status) {
        final AgentVo agentVo = new AgentVo();
        agentVo.setIp(ip);
        agentVo.setNtpStatus(status);
        agentMapper.updateAgent(agentVo);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    @OperateRecordAnnotation(name = "编辑%s上的Agent", module = "agent")
    public void updateAgent(AgentVo agentVo) throws ServiceException {
        try {
            agentMapper.updateAgent(agentVo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<IpRoleVo> getIpRoleVoList() {
        return agentMapper.getIpRoleVoList();
    }
}