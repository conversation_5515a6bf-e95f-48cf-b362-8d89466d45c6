package com.maintain.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.common.log.Log;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpATTRS;
import com.jcraft.jsch.SftpException;
import com.maintain.server.Constants;
import com.maintain.server.criteria.AutoDeployTaskCriteria;
import com.maintain.server.criteria.ModifyConfigCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.mapper.AutoDeployMapper;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.AutoDeployService;
import com.maintain.server.service.CommonConfigService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.*;
import com.maintain.server.utils.*;
import com.maintain.server.vo.*;
import com.maintain.server.websocket.BaseWebSocket;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.LinkedMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.java_websocket.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-02-28
 */
@Service("autoDeployService")
public class AutoDeployServiceImpl extends BaseWebSocket implements AutoDeployService {

    @Autowired
    private AutoDeployMapper autoDeployMapper;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private CommonConfigService commonConfigService;

    @Value("${kafka.kafkaZookeeperConnStr}")
    private String kafkaZookeeperConnStr;

    @Value("${server.localHost}")
    private String maintainServerIp;

    @Value("${seven-zip-path}")
    private String sevenZipPath;

    @Value("${es.prefix}")
    private String prefix;

    private WebSocket webSocket;

    @Autowired
    private AgentService agentService;

    private String product;

    private DeployRecordVo deployRecordVo;

    public DeployRecordVo getRecordVo(){
        return deployRecordVo;
    }

    private Map<String, List<AutoDeployTaskVo>> prepareVo;

    private File unzipFile;

    private Map<String, Map<String, Map<String, RoleProgramVo>>> deployMap;

    private Map<String, Map<String, Map<String, RoleProgramVo>>> deployTaskMap;

    private RoleServiceVo roleServiceRelationFileStr;

    private StringJoiner programPath;

    private Map<String,List<String>> needManulHandleExceptionInfo = new LinkedHashMap<>();

    public Map<String, List<String>> getNeedManulHandleExceptionInfo(){
        return needManulHandleExceptionInfo;
    }

    public Map<String, List<AutoDeployTaskVo>> getPrepareVo(){
        return prepareVo;
    }

    public DeployRecordVo getDeployRecordVo(DeployRecordVo deployRecordVo) {
        return autoDeployMapper.getDeployRecordInfo(deployRecordVo);
    }

    public Map<String, Map<String, Map<String, RoleProgramVo>>> getDeployTaskMap(){
        return deployTaskMap;
    }

    @Override
    @Transactional
    public void addDeployRecord(DeployRecordVo deployRecordVo) throws ServiceException {
        DeployRecordVo deployRecordInfo = autoDeployMapper.getDeployRecordInfo(new DeployRecordVo());
        if (deployRecordInfo == null) {
            autoDeployMapper.addDeployRecord(deployRecordVo);
        } else {
            autoDeployMapper.lockDeployTask(deployRecordVo);
        }
    }

    @Override
    public void setWebSocket(WebSocket webSocket) {
        this.webSocket = webSocket;
    }

    @Override
    public File checkPackage(File sevenZipFile) {
        String sevenZipFileName = sevenZipFile.getName();
        String[] elements = sevenZipFileName.split("-");
        if (elements.length != 6) {
            throw new ServiceException("部署包命名不符合规范：" + sevenZipFileName);
        }
        String product = elements[3] + "-" + elements[4];
        this.product = product;
        return sevenZipFile;
    }

    @Override
    public Boolean checkUpgradePackage(){
        try {
            File unzipFile = getProgramSevenZFile();
            File file = checkPackage(unzipFile);
            if (file.exists()) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            Log.high.info("在线升级部署包异常",e);
            return false;
        }
    }

    @Override
    public Boolean unzipPackage(){
        File file = getProgramSevenZFile();
        File sevenFile = checkPackage(file);
        String absolutePath = sevenFile.getAbsolutePath();
        String unzipPackage = absolutePath.substring(0, absolutePath.lastIndexOf("."));
        unzipFile = new File(unzipPackage);
        boolean result;
        if (!unzipFile.exists()) {
            if (StringUtils.isNotEmpty(sevenZipPath)) {
                // Windows用7z.exe解压
                result = SevenZipUtil.unZip(sevenZipPath, sevenFile, null);
            } else {
                // Linux用第三方解压
                result = FileUtil.un7z(sevenFile.getAbsolutePath(), sevenFile.getAbsoluteFile().getAbsolutePath(), null);
            }
        } else {
            result = true;
        }
        /*long byteSize = sevenFile.length();
        long unzipByteSize = FileUtil.getTotalSizeOfFilesInDir(unzipFile);
        if ((byteSize <= unzipByteSize) && result) {
            return true;
        } else {
            return false;
        }*/
        return result;
    }

    private File getProgramSevenZFile() {
        final File file = unzipAndGetDownloadSevenZPackage();
        List<File> sevenZipFiles = new ArrayList<>();
        if (file != null) {
            File[] files = file.listFiles();
            if (files == null || files.length == 0) {
                throw new ServiceException("部署路径：" + file.getAbsolutePath() + "文件为空");
            }
            for (File f : files) {
                if (f.getName().contains("壳") && f.getName().contains(".7z")) {
                    sevenZipFiles.add(f);
                }
            }
            if (sevenZipFiles.isEmpty()) {
                throw new ServiceException("未发现任何.7z格式的部署包" + file.getAbsolutePath());
            }
        } else {
            throw new ServiceException("解压失败，请输入正确的解压密码!");
        }
        return sevenZipFiles.get(0);
    }

    private File unzipAndGetDownloadSevenZPackage() {
        if (StringUtils.isEmpty(maintainServerIp) || StringUtils.isEmpty(sevenZipPath)) {
            throw new ServiceException("系统错误，系统配置中运维服务器IP、7z路径有误");
        }
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        String localPath = "";
        if (LINUX.equals(osId)) {
            localPath = MAINTAIN_LINUX_TEMP;
        } else if (WINDOWS.equals(osId)) {
            localPath = MAINTAIN_WINDOWS_TEMP;
        }
        if (StringUtils.isEmpty(localPath)) {
            throw new ServiceException("未知操作系统类型: " + osName);
        }
        File maintainTempFile = new File(localPath);
        if (!maintainTempFile.exists() && !maintainTempFile.mkdirs()) {
            throw new ServiceException("未发现部署路径：" + localPath);
        }
        File[] files = maintainTempFile.listFiles();
        if (files == null || files.length == 0) {
            throw new ServiceException("部署路径：" + localPath + "文件为空");
        }
        File sevenZipFile;
        List<File> sevenZipFiles = new ArrayList<>();
        for (File f : files) {
            if (f.getName().endsWith(".7z")) {
                sevenZipFiles.add(f);
            }
        }
        if (sevenZipFiles.isEmpty()) {
            throw new ServiceException("未发现任何.7z格式的部署包" + localPath);
        }
        sevenZipFiles.sort(new FileCompareDescUtil());
        sevenZipFile = sevenZipFiles.get(0);
        String absolutePath = sevenZipFile.getAbsolutePath();
        String unzipPackage = absolutePath.substring(0, absolutePath.lastIndexOf(".")); //解压包路径
        //部署包真正路径
        File downloadPackage = new File(unzipPackage);
        if (!downloadPackage.exists()) {
            //TODO
            String unzipPwd = softwareService.getUnzipPwd();
            //String unzipPwd = "123456";
            if (!StringUtils.isEmpty(unzipPwd)) {
                try {
                    if (StringUtils.isNotEmpty(sevenZipPath)) {
                        // Windows用7z.exe解压
                        SevenZipUtil.unZip(sevenZipPath, sevenZipFile, unzipPwd);
                    } else {
                        // Linux用第三方解压
                        FileUtil.un7z(sevenZipFile.getAbsolutePath(), sevenZipFile.getAbsoluteFile().getAbsolutePath(), unzipPwd);
                    }
                } catch (Exception e) {
                    Log.high.info(e);
                    return null;
                }
            } else {
                return null;
            }
        }
        return downloadPackage;
    }

    @Override
    public UnzipResult checkUnzipPackageFormat() throws Exception{
        UnzipResult unzipResult = new UnzipResult();
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        String localPath = "";
        if (LINUX.equals(osId)) {
            localPath = MAINTAIN_LINUX_TEMP;
        } else if (WINDOWS.equals(osId)) {
            localPath = MAINTAIN_WINDOWS_TEMP;
        }
        File maintainTempFile = new File(localPath);
        File[] files = maintainTempFile.listFiles();
        if (files == null) {
            unzipResult.setMsg("文件在部署过程中被删除");
            unzipResult.setResult(false);
            return unzipResult;
        }
        String str = "";
        if (LINUX.equals(osId)) {
            str = "/";
        } else if (WINDOWS.equals(osId)) {
            str = "\\";
        }
        File[] unzipFiles = unzipFile.listFiles();
        String conmmonConfigPath = "";
        File deployPackage;
        if (unzipFiles == null || unzipFiles.length == 0) {
            unzipResult.setMsg("部署文件不符合格式");
            unzipResult.setResult(false);
            return unzipResult;
        } else {
            File[] versionDir = unzipFiles[0].listFiles();
            if (versionDir == null || versionDir.length == 0) {
                unzipResult.setMsg("部署文件不符合格式");
                unzipResult.setResult(false);
                return unzipResult;
            } else {
                String deployPath = unzipFiles[0].getAbsolutePath() + str + "deploy";
                deployPackage = new File(deployPath);
                if (!deployPackage.exists()) {
                    unzipResult.setMsg("未发现【deploy文件夹】：" + deployPath);
                    unzipResult.setResult(false);
                    return unzipResult;
                } else {
                    List<String> deployFileNames = Arrays.asList(deployPackage.list());
                    int fileNum = deployFileNames.size();
                    if (fileNum != 1) {
                        unzipResult.setMsg("部署文件不符合格式");
                        unzipResult.setResult(false);
                        return unzipResult;
                    } else {
                        if (!deployFileNames.contains("role_service_relation.json")) {
                            unzipResult.setMsg("部署包配置文件不存在");
                            unzipResult.setResult(false);
                            return unzipResult;
                        }
                    }
                }
                String pro = unzipFiles[0].getAbsolutePath() + str;
                File proFile = new File(pro);
                if (!proFile.exists()) {
                    unzipResult.setMsg("未发现部署文件：" + pro);
                    unzipResult.setResult(false);
                    return unzipResult;
                } else {
                    programPath = new StringJoiner(";","","");
                    File ganzhiDirFile = new File(pro + "感知");
                    if (ganzhiDirFile.exists()) {
                        String ganzhi = pro + "感知" + File.separator + "program";
                        File program = new File(ganzhi);
                        if (!program.exists()) {
                            unzipResult.setMsg("未发现感知【program文件夹】：" + ganzhi);
                            unzipResult.setResult(false);
                            return unzipResult;
                        } else {
                            programPath.add(ganzhi);
                        }
                    }
                    File suyuanDirFile = new File(pro + "溯源");
                    if (suyuanDirFile.exists()) {
                        String suyuan = pro + "溯源" + File.separator + "program";
                        File program2 = new File(suyuan);
                        if (!program2.exists()) {
                            unzipResult.setMsg("未发现溯源【program文件夹】：" + suyuan);
                            unzipResult.setResult(false);
                            return unzipResult;
                        } else {
                            programPath.add(suyuan);
                        }
                    }
                    File zhongshuDirFile = new File(pro + "感知");
                    if (zhongshuDirFile.exists()) {
                        String zhongshu = pro + "中枢" + File.separator + "program";
                        File program3 = new File(zhongshu);
                        if (!program3.exists()) {
                            unzipResult.setMsg("未发现中枢【program文件夹】：" + zhongshu);
                            unzipResult.setResult(false);
                            return unzipResult;
                        } else {
                            programPath.add(zhongshu);
                        }
                        conmmonConfigPath = program3.getAbsolutePath() + File.separator + Constants.COMMON_CONFIG_FILE_PATH;
                    }
                }
            }
        }
        File[] deployFiles = deployPackage.listFiles();
        File roleServiceRelationFile = deployFiles[0];
        if (roleServiceRelationFile == null) {
            unzipResult.setMsg("未发现【role_service_relation.json】：" + deployPackage.getAbsolutePath());
            unzipResult.setResult(false);
            return unzipResult;
        }
        //解析role_service_relation.json文件内容到map
        Map<String, Map<String, Map<String, RoleProgramVo>>> task;
        Map<String,String> regularMap;
        try {
            Map<String, Object> map = parseRoleServiceRelationFileToMap(roleServiceRelationFile);
            task = (Map<String, Map<String, Map<String, RoleProgramVo>>>)map.get("task");
            regularMap = (Map<String,String>)map.get("regularMap");
        } catch (Exception e) {
            unzipResult.setMsg("1.请检查role_service_relation.json文件是否为标准json格式! ; 2.该文件中windows路径应该使用双斜杠");
            unzipResult.setResult(false);
            return unzipResult;
        }
        deployMap = task;
        roleServiceRelationFileStr = parseRoleServiceFileToMap(roleServiceRelationFile);
        if (task.isEmpty()) {
            unzipResult.setMsg("待部署程序列表为空,请检查是否配置role_service_relation.json表中的Agent角色!");
            unzipResult.setResult(false);
            return unzipResult;
        }
        String isFullPackage = regularMap.get("is_full_package");
        if (StringUtils.isEmpty(isFullPackage)) {
            unzipResult.setMsg("请在部署文件role_service_relation.json中填写正确的is_full_package配置项");
            unzipResult.setResult(false);
            return unzipResult;
        }
        List<SoftwareVo> softwareVos = softwareService.listAllSoftwares(null);
        if (ListUtil.isEmpty(softwareVos)) {
            if ("true".equals(isFullPackage)) {
                if (!"*".equals(regularMap.get("platform_regular"))) {
                    return judgeVersion(regularMap);
                }
            } else {
                unzipResult.setMsg("当前平台未部署程序,且第一次部署应该为全量包,请检查role_service_relation.json配置是否正确!");
                unzipResult.setResult(false);
                return unzipResult;
            }
        } else {
            if (!"*".equals(regularMap.get("platform_regular"))) {
                return judgeVersion(regularMap);
            }
        }
        DeployRecordVo deployRecordVo = new DeployRecordVo();
        deployRecordVo.setName("\'" + regularMap.get("platform_name") + "\'");
        deployRecordVo.setVersion("\'" + regularMap.get("platform_version") + "\'");
        deployRecordVo.setStatus(5);

        //TODO  暂时注释
        DeployRecordVo deployRecor = autoDeployMapper.getDeployRecordInfo(deployRecordVo);
        if (deployRecor != null){
            unzipResult.setMsg("程序已部署");
            unzipResult.setResult(false);
            return unzipResult;
        } else {
            //将本次部署包的版本入库
            DeployRecordVo deployRecord = new DeployRecordVo();
            deployRecord.setName(regularMap.get("platform_name"));
            deployRecord.setVersion(regularMap.get("platform_version"));
            deployRecord.setStatus(0);
            deployRecord.setRemark1("");
            deployRecord.setRemark2("");
            deployRecord.setRemark3("");
            deployRecord.setRemark4("");
            deployRecord.setRemark5("");
            deployRecord.setConfigPath(conmmonConfigPath);
            deployRecord.setProduct(product);
            deployRecord.setProgramPaths(programPath.toString());
            deployRecord.setFullPackage(isFullPackage);
            addDeployRecord(deployRecord);
            unzipResult.setMsg("解压包校验通过！");
            unzipResult.setResult(true);
        }
        return unzipResult;
    }

    private UnzipResult judgeVersion( Map<String, String> regularMap){
        UnzipResult unzipResult = new UnzipResult();
        List<DeployRecordVo> platformVersion = autoDeployMapper.getPlatformVersionInfo(regularMap);
        if (ListUtil.isEmpty(platformVersion)) {
            unzipResult.setMsg("当前部署包不满足平台版本要求");
            unzipResult.setResult(false);
        } else {
            unzipResult.setMsg("当前部署包满足平台版本要求");
            unzipResult.setResult(true);
        }
        return unzipResult;
    }

    private RoleServiceVo parseRoleServiceFileToMap(File roleServiceRelationFile) {
        RoleServiceVo roleServiceVo = null;
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(roleServiceRelationFile);
            String content = IOUtils.toString(fileInputStream, UTF_8);
            roleServiceVo = JsonUtil.parseObject(content, RoleServiceVo.class);
            fileInputStream.close();
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            try {
                fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return roleServiceVo;
    }

    @Override
    public CompareResultVo compareDeployTask() {
        CompareResultVo compareResultVo = new CompareResultVo();
        Map<String,String> map = new LinkedHashMap<>();
        //StringJoiner stringJoiner = new StringJoiner("","","目录未部署程序;");
        //StringJoiner stringJoiner2 = new StringJoiner("","","目录未部署");
        //StringJoiner stringJoiner3 = new StringJoiner("","","程序");
        //读取数据库记录的当前平台各个角色机部署程序的记录
        List<SoftwareVo> softwareVos = softwareService.listAllSoftwares(null);
        //List<String> collect1 = softwareVos.stream().filter(f -> f.getDeployDir() == null).map(SoftwareVo::getName).collect(Collectors.toList());
        //stringBuilder.append(collect1 + "部署路径未知;");
        Map<String, Map<String, Set<String>>> collect = softwareVos.stream().filter(f -> f.getServerIp() != null && f.getDeployDir() != null)
                .collect(Collectors.groupingBy(SoftwareVo::getServerIp,
                        Collectors.groupingBy(SoftwareVo::getDeployDir,
                                Collectors.mapping(SoftwareVo::getName, Collectors.toSet()))));
        //比较
        for (Map.Entry roleMap : deployMap.entrySet()) {
            String role = (String)roleMap.getKey();
            Map<String, Set<String>> stringSetMap = collect.get(role);
            if (MapUtils.isEmpty(stringSetMap)) {
                //map.put(role,"没有部署程序记录;");
            } else {
                String string = "";
                Map<String, Map<String, RoleProgramVo>> value = (Map<String, Map<String, RoleProgramVo>>)roleMap.getValue();
                for (Map.Entry deploy : value.entrySet()) {
                    String deployDir = (String)deploy.getKey();
                    Set<String> programs = stringSetMap.get(deployDir);
                    if (programs == null || programs.isEmpty()) {
                        //stringJoiner.add("{" + deployDir + "}");
                    } else {
                        Map<String, RoleProgramVo> value1 = (Map<String, RoleProgramVo>)deploy.getValue();
                        Boolean falg = false;
                        Set<String> set = new HashSet<>();
                        for (Map.Entry program : value1.entrySet()) {
                            String programName = (String)program.getKey();
                            if (!programs.contains(programName)) {
                                set.add(programName);
                                falg = true;
                            }
                        }
                        if (falg) {
                            string = "{" + deployDir + "}目录未部署" + set.toString() + "程序;" + string;
                            //map.put(role,string);
                        }
                    }
                }
                map.put(role,string);
            }
        }
        if (map.size() == 0){
            map.put("比较结果", "无差异项");
        }
        compareResultVo.setDiff(JsonUtil.toJsonString(map));
        compareResultVo.setDeploy(JsonUtil.toJsonString(roleServiceRelationFileStr));
        return compareResultVo;
    }

    @Override
    public Map<String, Object> deployTask(Map<String,Object> deploy) {
        // 获取上次未部署完成的Task
        AutoDeployTaskCriteria deployTaskCriteria = new AutoDeployTaskCriteria();
        deployTaskCriteria.setNotInStatus(AutoDeployType.SUCCESS.getStatus());
        List<AutoDeployTaskVo> autoDeployTaskList = getAutoDeployTasks(deployTaskCriteria);
        Map<String, Object> deployMap = new HashMap<>();
        if (ListUtil.isNotEmpty(autoDeployTaskList)) {
            Map<String, List<AutoDeployTaskVo>> task = new LinkedHashMap<>();
            for (AutoDeployTaskVo deployTaskVo : autoDeployTaskList) {
                List<AutoDeployTaskVo> deployTaskList = task.get(deployTaskVo.getIp());
                if (deployTaskList == null) {
                    deployTaskList = new LinkedList<>();
                }
                deployTaskList.add(deployTaskVo);
                task.put(deployTaskVo.getIp(), deployTaskList);
            }
            deployMap.put("task", task);
        } else {
            Log.low.info("开始获取部署任务");
            Map<String, Map<String, Map<String, RoleProgramVo>>> map = (Map<String, Map<String, Map<String, RoleProgramVo>>>) deploy.get("relations");
            Map<String, Map<String, Map<String, RoleProgramVo>>> task = getTask(map);
            Map<String, List<AutoDeployTaskVo>> prepareVo = prepareDeploy(task);
            int c = 0;
            while (prepareVo == null && c <= 30) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Log.low.info("线程中断异常",e);
                }
                c++;
            }
            if (prepareVo != null) {
                deployMap.put("task", prepareVo);
            }
        }
        String packageVersion = getDeployRecordVo(new DeployRecordVo()).getVersion();
        deployMap.put("version", packageVersion);
        deployTaskMap = (Map<String, Map<String, Map<String, RoleProgramVo>>>)deployMap.get("task");
        return deployMap;
    }

    @Override
    public CommonConfigResultVo getCommonConfig() {
        String commonConfigPath = getDeployRecordVo(new DeployRecordVo()).getConfigPath();
        CommonConfigResultVo commonConfigResultVo = new CommonConfigResultVo();
        if (!StringUtils.isEmpty(commonConfigPath)) {
            File file = new File(commonConfigPath);
            if (file.exists()) {
                String content = commonConfigService.loadCommonConfigInfo(commonConfigPath);
                byte[] bytes = content.getBytes(Charset.forName(Constants.UTF_8));
                String commonConfigStr = Base64.getEncoder().encodeToString(bytes);
                commonConfigResultVo.setCommonConfigStr(commonConfigStr);
                String checkAgentRoleResult = commonConfigService.getCheckAgentRoleResult();
                commonConfigResultVo.setCheckAgentRole(checkAgentRoleResult);
            } else {
                return null;
            }
        } else {
            return null;
        }
        return commonConfigResultVo;
    }

    @Override
    public void recordIsModifyCommonConfig(Integer isModifyCommonConfig) {
        DeployRecordVo deployRecordVo = new DeployRecordVo();
        deployRecordVo.setIsModifyCommonConfig(isModifyCommonConfig);
        this.deployRecordVo = deployRecordVo;
    }

    @Override
    public DeployRecordVo getIsModifyCommonConfig() {
        DeployRecordVo deployRecordVo = new DeployRecordVo();
        DeployRecordVo deployRecordInfo = autoDeployMapper.getDeployRecordInfo(deployRecordVo);
        if (deployRecordInfo == null) {
            DeployRecordVo deployRecordInfos = new DeployRecordVo();
            deployRecordInfos.setIsModifyCommonConfig(Integer.valueOf(0));
            return deployRecordInfos;
        } else {
            return deployRecordInfo;
        }
    }

    @Override
    public Map<String, List<AutoDeployTaskVo>> prepareDeploy(Map<String, Map<String, Map<String, RoleProgramVo>>>  task) throws ServiceException {
        String product = getDeployRecordVo(new DeployRecordVo()).getProduct();
        // 根据program文件夹，排除掉不需要部署的程序
        Map<String, List<AutoDeployTaskVo>>  deployTask = new LinkedHashMap<>();
        for (Map.Entry<String, Map<String, Map<String, RoleProgramVo>>> entry : task.entrySet()) {
            String ip = entry.getKey();
            Map<String, Map<String, RoleProgramVo>> val1 = entry.getValue();
            List<AutoDeployTaskVo> autoDeployTaskVos = new LinkedList<>();
            for (Map.Entry<String, Map<String, RoleProgramVo>> val2 : val1.entrySet()) {
                Set<String> set = new LinkedHashSet<>();
                String destPath = val2.getKey();
                Map<String, RoleProgramVo> val3 = val2.getValue();
                if (val3 == null || val3.isEmpty()) {
                    continue;
                }
                for (Map.Entry<String, RoleProgramVo> entry1 : val3.entrySet()) {
                    String programName = entry1.getKey();
                    String string = JSONObject.toJSONString(entry1.getValue());
                    RoleProgramVo value = JsonUtil.parseObject(string, RoleProgramVo.class);
                    DeployRecordVo deployRecordVo = getDeployRecordVo(new DeployRecordVo());
                    String programPathStr = deployRecordVo.getProgramPaths();
                    final String[] programPathArr = programPathStr.split(";");
                    for (String programPath : programPathArr) {
                        String path = programPath + File.separator + programName;
                        File tempFile = new File(path);
                        if (tempFile.exists() /*&& !"java-libs".equals(programName)&& !"config".equals(programName)*/) {
                            if (!"web".equals(programName)) {
                                set.add(programPath);
                            }
                            AutoDeployTaskVo autoDeployTaskVo = new AutoDeployTaskVo();
                            autoDeployTaskVo.setIp(ip);
                            autoDeployTaskVo.setProduct(product);
                            autoDeployTaskVo.setName(programName);
                            if (StringUtils.isEmpty(value.getOldName())) {
                                autoDeployTaskVo.setOldName(programName);
                            } else {
                                autoDeployTaskVo.setOldName(value.getOldName());
                            }
                            autoDeployTaskVo.setNeedDeleteFiles(value.getNeedDeleteFiles());
                            autoDeployTaskVo.setIgnoreBackup(value.getIgnoreBackup());
                            if (value.getIsFullPackage() == null) {
                                autoDeployTaskVo.setIsFullPackage(getDeployRecordVo(new DeployRecordVo()).getFullPackage());
                            } else {
                                autoDeployTaskVo.setIsFullPackage(value.getIsFullPackage().toString());
                            }
                            autoDeployTaskVo.setSourcePath(path);
                            autoDeployTaskVo.setTargetPath(destPath);
                            autoDeployTaskVos.add(autoDeployTaskVo);
                        }
                    }
                }
               /* if (set != null && !set.isEmpty()) {
                    for (String s : set) {
                        String path = s + File.separator + "java-libs";
                        String path2 = s + File.separator + "config";
                        File tempFile = new File(path);
                        File tempFile2 = new File(path2);
                        if (tempFile.exists() && val3.keySet().contains("java-libs")) {
                            AutoDeployTaskVo autoDeployTaskVo = new AutoDeployTaskVo();
                            autoDeployTaskVo.setIp(ip);
                            autoDeployTaskVo.setProduct(product);
                            autoDeployTaskVo.setName("java-libs");
                            String str = JSON.toJSONString(val3.get("java-libs"));
                            RoleProgramVo javaLibs = JSONObject.parseObject(str, RoleProgramVo.class);
                            autoDeployTaskVo.setOldName(javaLibs.getOldName());
                            autoDeployTaskVo.setNeedDeleteFiles(javaLibs.getNeedDeleteFiles());
                            autoDeployTaskVo.setSourcePath(path);
                            autoDeployTaskVo.setTargetPath(destPath);
                            autoDeployTaskVos.add(autoDeployTaskVo);
                        }
                        if (tempFile2.exists() && val3.keySet().contains("config")) {
                            AutoDeployTaskVo autoDeployTaskVo2 = new AutoDeployTaskVo();
                            autoDeployTaskVo2.setIp(ip);
                            autoDeployTaskVo2.setProduct(product);
                            autoDeployTaskVo2.setName("config");
                            String str = JSON.toJSONString(val3.get("config"));
                            RoleProgramVo config = JSONObject.parseObject(str, RoleProgramVo.class);
                            autoDeployTaskVo2.setOldName(config.getOldName());
                            autoDeployTaskVo2.setNeedDeleteFiles(config.getNeedDeleteFiles());
                            autoDeployTaskVo2.setSourcePath(path2);
                            autoDeployTaskVo2.setTargetPath(destPath);
                            autoDeployTaskVos.add(autoDeployTaskVo2);
                        }
                    }
                }*/
            }
            if (!ListUtil.isEmpty(autoDeployTaskVos)) {
                deployTask.put(ip,autoDeployTaskVos);
            }
        }
        this.prepareVo = deployTask;
        return deployTask;
    }

    @Override
    @Transactional
    public boolean addDeployTask(List<AutoDeployTaskVo> prepareVo) throws ServiceException {
        if (prepareVo == null) {
            return false;
        }
        try {
            autoDeployMapper.addDeployTask(prepareVo);
            return true;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<AutoDeployTaskVo> getAutoDeployTasks(AutoDeployTaskCriteria criteria) throws ServiceException {
        try {
            return autoDeployMapper.getAutoDeployTasks(criteria);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public Boolean autoDeploy(AgentVo agentVo, Map<String, Object> map) throws ServiceException {
        if (map == null || agentVo == null) {
            throw new ServiceException("部署参数不能为空...");
        }
        Object ip = map.get("ip");
        Object deploy = map.get("deploy");
        if (deploy == null || ip == null) {
            throw new ServiceException("deploy、ip参数不完整...");
        }
        List<AutoDeployTaskVo> deployTaskVos = null;
        if (deploy instanceof List) {
            deployTaskVos = (List<AutoDeployTaskVo>) deploy;
            for (AutoDeployTaskVo deployTaskVo : deployTaskVos) {
                String name = deployTaskVo.getName();
                String path = deployTaskVo.getSourcePath();
                File file = new File(path);
                //一般都是会有的
                if (file.exists()) {
                    if (!name.equals("config") && !name.equals("java-libs")){
                        this.updateConfigFile(file, map);
                    }
                }
            }
        }
        Boolean deployResult;
        if (maintainServerIp.equals(agentVo.getIp())) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("本地部署",""));
            deployResult = localDeploy(agentVo, deployTaskVos);
        } else {
            //远程部署
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("远程部署，IP：" + agentVo.getIp(),""));
            deployResult = remoteDeploy(agentVo, deployTaskVos);
        }
        if (deployResult) {
            updateSoftwareInfo(deployTaskVos, agentVo);
        }
        return deployResult;
    }

    @Override
    protected String autoDeployCmdInWindows(String processName) {
        String cmd = " cd /d \"D:\\dist\\" + processName + "\\deploy\" && start.bat>nul";
        Log.low.info("cmdInWindows:" + cmd);
        return cmd;
    }

    @Override
    protected String autoDeployCmdInLinux(String processName) {
        String cmd = "cd /dist/" + processName + "/deploy && chmod 777 start.sh && nohup sh start.sh >/dev/null  2>log &";
        Log.low.info("cmdInLinux:" + cmd);
        return cmd;
    }

    /**
     * 通用版修改icepool.conf
     *
     * @param tempFile tempFile
     * @param map      map
     * @throws ServiceException ServiceException
     */
    @Override
    public void updateConfigFile(File tempFile, Map<String, Object> map) throws ServiceException {
        if (map.get("ip") == null) {
            return;
        }
        String ip = map.get("ip").toString();
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        String icepoolPath;
        if (osId.equals(LINUX)) {
            icepoolPath = tempFile.getPath() + "/deploy/icepool.conf";
        } else {
            icepoolPath = tempFile.getPath() + "\\deploy\\icepool.conf";
        }
        File file = new File(icepoolPath);
        if (!file.exists()) {
            return;
        }
        BufferedReader reader = null;
        try {
            Map<String, String> icepoolMap = new HashMap<>();
            reader = new BufferedReader(new FileReader(file));
            String str;
            while ((str = reader.readLine()) != null) {
                if (str.contains("Endpoints=")) {
                    String[] strs = str.split("-p");
                    if (strs.length > 1) {
                        String port = strs[1].trim();
                        icepoolMap.put("Endpoints", "tcp -h " + ip + " -p " + port);
                        FileUtil.updateKeyValue(icepoolPath, icepoolMap);
                        break;
                    }
                }
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    Log.high.error(e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public void lockTask(AutoDeployTaskVo taskVo) {
        autoDeployMapper.lockTask(taskVo);
    }

    @Override
    public void lockDeployTask(DeployRecordVo deployRecordVo) {
        autoDeployMapper.lockDeployTask(deployRecordVo);
    }

    private Boolean localDeploy(AgentVo agentVo, List<AutoDeployTaskVo> deployTaskVos) {
        Boolean deployResult = true;
        if (ListUtil.isNotEmpty(deployTaskVos)) {
            for (AutoDeployTaskVo taskVo : deployTaskVos) {
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("---开始部署<" + taskVo.getName() + ">程序---",""));
                String uploadPath = taskVo.getSourcePath();
                File uploadFile = new File(uploadPath);
                Boolean result = false;
                if (agentVo.getOs().equals(LINUX)) {
                    String deployPath = LINUX_FILE_SEPARATOR + taskVo.getTargetPath() + LINUX_FILE_SEPARATOR;
                    try {
                        result = backupAndUploadFile(deployPath, uploadFile, agentVo.getIp(), LINUX_FILE_SEPARATOR, taskVo);
                    } catch (Exception e) {
                        Log.high.error(e.getMessage(), e);
                    }
                } else {
                    String deployPath = "D:" + WINDOWS_FILE_SEPARATOR + taskVo.getTargetPath() + WINDOWS_FILE_SEPARATOR;
                    try {
                        result = backupAndUploadFile(deployPath, uploadFile, agentVo.getIp(), WINDOWS_FILE_SEPARATOR, taskVo);
                    } catch (Exception e) {
                        Log.high.error(e.getMessage(), e);
                    }
                }
                if (!result) {
                    deployResult = false;
                    break;
                } else {
                    taskVo.setStatus(AutoDeployType.SUCCESS.getStatus());
                    taskVo.setRemark("部署成功");
                    this.lockTask(taskVo);
                }
            }
            return deployResult;
        } else {
            throw new ServiceException("部署列表为空...");
        }
    }

    /**
     * 备份、传包
     *
     * @param remotePath remotePath
     * @param file       file
     * @throws Exception Exception
     */
    private Boolean backupAndUploadFile(String remotePath, File file, String ip, String separator,AutoDeployTaskVo taskVo) throws Exception {
        ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
        List<ModifyConfigVo> modifyConfigVos = softwareService.queryModifyConfig(modifyConfigCriteria);
        List<String> collect = modifyConfigVos.stream().map(ModifyConfigVo::getProgramName).collect(Collectors.toList());
        // 关闭 备份 删除 上传
        try {
            if ("2".equals(taskVo.getStatus())) {
                return step2(ip, file, remotePath, separator, taskVo, collect);
            } else if ("3".equals(taskVo.getStatus())) {
                return step3(ip, file, remotePath, separator, taskVo, collect);
            } else if ("4".equals(taskVo.getStatus())) {
                return step4(ip, file, remotePath, separator, taskVo, collect);
            } else if ("5".equals(taskVo.getStatus())) {
                return step5(ip, remotePath, separator, taskVo, collect);
            } else {
                return step1(ip, file, remotePath, separator, taskVo, collect);
            }
        } catch(Exception e){
            Log.high.error(e.getMessage(), e);
            //失败
            taskVo.setRemark(e.getMessage());
            taskVo.setStatus(AutoDeployType.FAIL.getStatus());
            this.lockTask(taskVo);
            return false;
        }
    }

    private Boolean step1(String ip, File file, String remotePath, String separator, AutoDeployTaskVo taskVo, List<String> collect){
        String oldFileName = taskVo.getOldName();
        Boolean closeResult = closeOldSoftware(ip, taskVo);
        if (closeResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + "-> 程序:" + oldFileName + "关闭成功！",""));
            Log.low.info(ip + "-> 程序:" + oldFileName + "关闭成功！");
            return step2(ip, file, remotePath, separator, taskVo, collect);
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 程序:" + oldFileName + "关闭失败,请手动关闭后再重试！",""));
            Log.low.info(ip + " -> 程序:" + oldFileName + "关闭失败,请手动关闭后再重试！");
            return false;
        }
    }

    private Boolean step2(String ip, File file, String remotePath, String separator, AutoDeployTaskVo taskVo, List<String> collect){
        String oldFileName = taskVo.getOldName();
        File oldFile = new File(remotePath + oldFileName);
        long start = System.currentTimeMillis();
        Boolean backupResult = backupOldSoftware(remotePath, oldFile, separator, taskVo);
        long end = System.currentTimeMillis();
        Log.low.info("备份程序" + taskVo.getName() + " , 用时为(" + (end - start)/3600000 + "min)");
        if (backupResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 备份: " + oldFileName + "成功！",""));
            Log.low.info(ip + " -> 备份: " + oldFileName + "成功！");
            return step3(ip, file, remotePath, separator, taskVo, collect);
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 备份: " + oldFileName + "失败！",""));
            Log.low.info(ip + " -> 备份: " + oldFileName + "失败！");
            return false;
        }
    }

    private Boolean step3(String ip, File file, String remotePath, String separator, AutoDeployTaskVo taskVo, List<String> collect){
        String oldFileName = taskVo.getOldName();
        File oldFile = new File(remotePath + oldFileName);
        Boolean deleteResult = deleteOldSoftware(remotePath,oldFile, taskVo);
        if (deleteResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 成功删除: " + oldFileName + "冗余文件！",""));
            Log.low.info(ip + " -> 成功删除: " + oldFileName + "冗余文件！");
            return step4(ip, file, remotePath, separator, taskVo, collect);
        }else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 删除: " + oldFileName + "文件失败,请解决文件占用后重试！",""));
            Log.low.info(ip + " -> 删除: " + oldFileName + "文件失败,请解决文件占用后重试！");
            return false;
        }
    }

    private Boolean step4(String ip, File file, String remotePath, String separator, AutoDeployTaskVo taskVo, List<String> collect){
        long start2 = System.currentTimeMillis();
        Boolean uploadResult = uploadSoftware(ip, file, taskVo, remotePath);
        long end2 = System.currentTimeMillis();
        Log.low.info("上传程序" + taskVo.getName() + " , 用时为(" + (end2 - start2)/3600000 + "min)");
        if (uploadResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 上传:" + taskVo.getName() + "成功！",""));
            Log.low.info(ip + " -> 上传:" + taskVo.getName() + "成功！");
            return step5(ip, remotePath, separator, taskVo, collect);
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 上传:" + taskVo.getName() + "失败,请关闭程序，解决占用后重试！",""));
            Log.low.info(ip + " -> 上传:" + taskVo.getName() + "失败,请关闭程序，解决占用后重试！");
            return false;
        }
    }

    private Boolean step5(String ip, String remotePath, String separator, AutoDeployTaskVo taskVo, List<String> collect){
        long start3 = System.currentTimeMillis();
        Boolean replaceResult = replaceConfig(remotePath,separator,taskVo);
        long end3 = System.currentTimeMillis();
        Log.low.info("替换" + taskVo.getName() + "程序配置, 用时为(" + (end3 - start3)/3600000 + "min)");
        if (replaceResult) {
            if (!"java-libs".equals(taskVo.getName()) && collect.contains(taskVo.getName())) {
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 成功替换程序: " + taskVo.getName() + "的配置文件！",""));
                Log.low.info(ip + " -> 成功替换程序: " + taskVo.getName() + "配置文件！");
            }
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 替换程序: " + taskVo.getName() + "配置文件失败,请重试！",""));
            Log.low.info(ip + " -> 替换程序: " + taskVo.getName() + "配置文件失败,请重试！");
            return false;
        }
        return true;
    }

    private Boolean replaceConfig(String remotePath,String separator,AutoDeployTaskVo taskVo) {
        taskVo.setStatus(AutoDeployType.REPLACE.getStatus());
        this.lockTask(taskVo);
        ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
        modifyConfigCriteria.setProgramName(taskVo.getName());
        List<ModifyConfigVo> modifyConfigVos = softwareService.queryModifyConfig(modifyConfigCriteria);
        if (modifyConfigVos.size() > 0) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始替换配置......",""));
            for (ModifyConfigVo modifyConfigVo : modifyConfigVos) {
                try {
                    String packageVersion = getDeployRecordVo(new DeployRecordVo()).getVersion();
                    String backupConfigPath = remotePath + "bak" + separator + packageVersion + separator + taskVo.getOldName() + "_bak" + separator + modifyConfigVo.getConfigPath().replace("/",File.separator);
                    File sourceFile = new File(backupConfigPath);
                    String newUploadConfigPath = remotePath + taskVo.getName() + separator + modifyConfigVo.getConfigPath().replace("/",File.separator);
                    File targetFile = new File(newUploadConfigPath);
                    if (sourceFile.exists() && targetFile.exists()) {
                        if (sourceFile.getName().endsWith(".json") && targetFile.getName().endsWith(".json")) {
                            replaceJsonCommonConfigItem(sourceFile,targetFile);
                        }
                        if (sourceFile.getName().endsWith(".xml") && targetFile.getName().endsWith(".xml")){
                            replaceXmlCommonConfigItem(sourceFile,targetFile);
                        }
                    }
                } catch (Exception e) {
                    Log.high.info(e);
                    return false;
                }
            }
        }
        return true;
    }

    private String replaceStr(Map<String, Object> sourceFileMap,Map<String, Object> targetFileMap,String targetFileStr){
        if (!MapUtils.isEmpty(sourceFileMap) && !MapUtils.isEmpty(targetFileMap)) {
            for (Map.Entry<String, Object> m : targetFileMap.entrySet()) {
                String key = m.getKey();
                Object o = sourceFileMap.get(key);
                if (m.getValue() instanceof Map) {
                    Map<String, Object> value = (Map<String, Object>) m.getValue();
                    if (value.size() >= 1 && sourceFileMap.get(key) instanceof Map) {
                        Map<String, Object> om = (Map<String, Object>) o;
                        replaceStr(om,value,targetFileStr);
                    } else {
                        if (o != null) {
                            String newStr = o.toString();
                            Integer srartIndex = targetFileStr.indexOf(key) + key.length();
                            Integer endIndex = srartIndex + m.getValue().toString().length();
                            String oldStr = m.getValue().toString();
                            Integer oldStrStartIndex = targetFileStr.indexOf(oldStr);
                            if (oldStrStartIndex > srartIndex && oldStrStartIndex < endIndex) {
                                targetFileStr = targetFileStr.replace(oldStr, newStr);
                            }
                        }
                    }
                }
            }
        }
        return targetFileStr;
    }
    private void replaceXmlCommonConfigItem(File sourceFile, File targetFile) throws IOException {
        Map<String, Object> sourceFileMap = new LinkedHashMap();
        Map<String, Object> targetFileMap = new LinkedHashMap();
        String targetFileStr = FileUtils.readFileToString(targetFile,"UTF-8");
        try {
            SAXReader saxreader = new SAXReader();
            SAXReader saxreader2 = new SAXReader();
            Document doc = saxreader.read(sourceFile);
            Document doc2 = saxreader2.read(targetFile);
            Element rootElement = doc.getRootElement();
            Element rootElement2 = doc2.getRootElement();
            sourceFileMap.put(rootElement.getName(), DiGui(rootElement));
            targetFileMap.put(rootElement2.getName(), DiGui(rootElement2));
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        targetFileStr = replaceStr(sourceFileMap, targetFileMap, targetFileStr);
        FileUtils.writeStringToFile(targetFile, targetFileStr, Constants.UTF_8);
    }

    public Map DiGui(Element rootElement) {
        int flag = hasGradeChrid(rootElement);
        Map<String, Object> map_this = new LinkedHashMap<String, Object>();
        Map<String, Object> map_children = new LinkedHashMap<String, Object>();
        Iterator<Element> iterator = rootElement.elementIterator();
        if (flag == 0) {
            int num = 0;
            while (iterator.hasNext()) {
                Element childelement = iterator.next();
                map_children = DiGui(childelement);
                map_this.put(childelement.getName() + "_" + num, map_children);
                num++;
            }
        }
        if (flag == 1) {
            while (iterator.hasNext()) {
                Element childelement = iterator.next();
                map_this.put(childelement.getName(),
                        (String) childelement.getData());
            }
        }
        if (flag == 2) {
            int nodes = rootElement.elements().size();
            while (nodes >= 1) {
                nodes--;
                int num = 0;
                Element element = iterator.next();
                flag = hasGradeChrid(element);
                if (flag == 1) {
                    map_this.put(element.getName(), element.getData());
                }
                else{
                    map_children = DiGui(element);
                    map_this.put(element.getName() + "_" + num, map_children);
                }
            }
        }
        return map_this;
    }

    /**
     * 用于判断该节点的类型
     *
     * @param rootelement
     * @return
     */
    public int hasGradeChrid(Element rootelement) {
        int flag = 1;
        StringBuffer flag_arr = new StringBuffer();
        Iterator<Element> iterator = rootelement.elementIterator();
        while (iterator.hasNext()) {
            Element element = iterator.next();
            if (element.elements().size() > 0) {
                flag_arr.append("0");
            } else {
                flag_arr.append("1");
            }
        }
        if (flag_arr.toString().contains("0")) {
            flag = 0;
        }
        if (flag_arr.toString().contains("1")) {
            flag = 1;
        }
        if (flag_arr.toString().contains("0")
                && flag_arr.toString().contains("1")) {
            flag = 2;
        }
        return flag;
    }

    private Boolean replaceRemoteConfig(AutoDeployTaskVo taskVo) {
        taskVo.setStatus(AutoDeployType.REPLACE.getStatus());
        this.lockTask(taskVo);
        ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
        modifyConfigCriteria.setProgramName(taskVo.getName());
        List<ModifyConfigVo> modifyConfigVos = softwareService.queryModifyConfig(modifyConfigCriteria);
        List<String> configPaths = modifyConfigVos.stream().map(ModifyConfigVo::getConfigPath).collect(Collectors.toList());
        if (modifyConfigVos.size() > 0) {
            try {
                String oldFileName = taskVo.getOldName();
                String remotePath = taskVo.getTargetPath();
                String ip = taskVo.getIp();
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始替换配置......",""));
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("oldFileName", oldFileName);
                String packageVersion = getDeployRecordVo(new DeployRecordVo()).getVersion();
                paramMap.put("productVersion", packageVersion);
                paramMap.put("remotePath", remotePath);
                paramMap.put("configPaths", configPaths);
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("请求Agent替换配置",""));
                String param = JsonUtil.toJsonString(paramMap);
                try {
                    String result = softwareService.iceRequest(ip, IceFlag.REPLACE_CONFIG, param);
                    if (result == null) {
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("调用Agent失败,请检查Agent连接状态", ""));
                        return false;
                    } else {
                        Map<String, Object> map = JsonUtil.parseObject(result, new TypeReference<Map<String, Object>>() {
                        });
                        Boolean data = (Boolean)map.get("data");
                        if (data){
                            return true;
                        }else {
                            return false;
                        }
                    }
                } catch (Exception e) {
                    sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("调用Agent失败,请检查Agent连接状态", ""));
                    return false;
                }
            } catch (Exception e) {
                Log.high.info(e);
                return false;
            }
        }
        return true;
    }

    private void replaceJsonCommonConfigItem(File sourceFile,File targetFile) throws IOException {
        String sourceFileStr = FileUtils.readFileToString(sourceFile, Constants.UTF_8);
        String targetFileStr = FileUtils.readFileToString(targetFile, Constants.UTF_8);
        Map<String, Object> sourceFileStrMap = JsonUtil.parseObject(sourceFileStr, new TypeReference<Map<String, Object>>() {
        });
        Map<String, Object> targetFileStrMap = JsonUtil.parseObject(targetFileStr, new TypeReference<Map<String, Object>>() {
        });
        targetFileStr = replaceStr(sourceFileStrMap,targetFileStrMap,targetFileStr);
        FileUtils.writeStringToFile(targetFile, targetFileStr, Constants.UTF_8);
    }

    private Boolean uploadSoftware(String ip,File file,AutoDeployTaskVo taskVo,String remotePath) {
        taskVo.setStatus(AutoDeployType.UPLOAD.getStatus());
        this.lockTask(taskVo);
        //传包(本地拷贝) , 拷贝新程序过来
        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 开始上传程序:" + taskVo.getName() + "......",""));
        if (file.getName().equals("config")) {
            try {
                commonConfigService.prepareCommonConfigForRemoteServer(ip);
            } catch (Exception e) {
                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent( "准备公共配置异常,请解决问题后重试",""));
                return false;
            }
        }
        try {
            double totalSizeOfFilesInDir = FileUtil.getTotalSizeOfFilesInDir(file) / (1024 * 1024);
            BigDecimal bigDecimal = BigDecimal.valueOf(totalSizeOfFilesInDir).setScale(2, BigDecimal.ROUND_UP);
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("即将上传的" + taskVo.getName() + "部署包大小为" + bigDecimal + "M, 请耐心等待...", ""));
            List<File> files = (List<File>)FileUtils.listFiles(file, null, true);
            List<String> failureFile = new LinkedList<>();
            for (int i = 0; i < files.size(); i++) {
                String absolutePath = files.get(i).getAbsolutePath();
                String path = remotePath + absolutePath.substring(absolutePath.indexOf(taskVo.getName()));
                File file1 = new File(path);
                try {
                    if (files.get(i).isDirectory()) {
                        FileUtils.copyDirectory(files.get(i), file1);
                    } else {
                        FileUtils.copyFile(files.get(i), file1);
                    }
                } catch (Exception e) {
                    Log.high.error(e);
                    if ("java-libs".equals(taskVo.getName()) || "config".equals(taskVo.getName())) {
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent( "！！！*****************************！！！",""));
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent( " -> 文件:" + absolutePath + "上传" + ip + "失败,请在部署结束后解决占用再手动上传！！！",""));
                        failureFile.add(absolutePath);
                    } else {
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 文件:" + absolutePath + "上传失败,请解决文件占用后重试!",""));
                        return false;
                    }
                }
                Boolean uploadResult = checkUploadResult(files.get(i), file1);
                if (!uploadResult) {
                    return false;
                }
            }
            if (ListUtil.isNotEmpty(failureFile)) {
                needManulHandleExceptionInfo.put(ip,failureFile);
            }
        } catch (Exception e) {
            Log.high.error(e);
            return false;
        }
        return true;
    }

    private Boolean deleteOldSoftware(String remotePath, File oldFile,AutoDeployTaskVo taskVo) {
        //删除原文件
        taskVo.setStatus(AutoDeployType.DELETE.getStatus());
        this.lockTask(taskVo);
        if (oldFile.exists()) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始删除配置文件role_service_relation.json中配置的冗余文件......",""));
            String needDeleteFiles = taskVo.getNeedDeleteFiles();
            if (!StringUtils.isEmpty(needDeleteFiles)) {
                needDeleteFiles = needDeleteFiles.replace("\\",File.separator).replace("/",File.separator);
                String[] split = needDeleteFiles.split(";");
                for (int i = 0; i < split.length; i++) {
                    File file = new File(remotePath + taskVo.getOldName() + File.separator + split[i]);
                    if (!file.exists()) {
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("要删除文件的目标路径为 " + remotePath + split[i] + ",请检查role_service_relation.json文件need_delete_files配置是否正确,文件中need_delete_files项的正确配置应该从程序路径开始",""));
                        return false;
                    }
                    FileUtil.deleteDir(file);
                    Boolean deleteResult = checkDeleteResult(file);
                    if (!deleteResult) {
                        return false;
                    }
                }
            }
            if (!"java-libs".equals(taskVo.getOldName())&& !"config".equals(taskVo.getOldName())) {
                if ("true".equals(taskVo.getIsFullPackage())) {
                    sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("全量部署,开始删除" + remotePath + "下的冗余文件......",""));
                    File file2 = new File(remotePath + oldFile.getName());
                    FileUtil.deleteDir(file2);
                    Boolean deleteResult = checkDeleteResult(file2);
                    if (!deleteResult) {
                        return false;
                    }
                } else {
                    sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("增量部署,开始按部署包文件删除要部署目录" + remotePath + "下的冗余文件......",""));
                    File file = new File(taskVo.getSourcePath());
                    List<File> files = (List<File>)FileUtils.listFiles(file, null, true);
                    for (int i = 0; i < files.size(); i++) {
                        String absolutePath = files.get(i).getAbsolutePath();
                        String path = remotePath + absolutePath.substring(absolutePath.indexOf(oldFile.getName()));
                        File file1 = new File(path);
                        FileUtil.deleteDir(file1);
                        Boolean deleteResult = checkDeleteResult(file1);
                        if (!deleteResult) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    private Boolean backupOldSoftware(String remotePath, File oldFile, String separator,AutoDeployTaskVo taskVo) {
        if (oldFile.exists()) {
            String oldFileName = oldFile.getName();
            String bak = remotePath + "bak";
            File bakupFile = new File(bak);
            if (!bakupFile.exists()) {
                bakupFile.mkdirs();
            } else {
                List<File> files = FileUtil.getChildrenDirs(bakupFile);
                if (files.size() >= 3) {
                    files.sort(new FileCompareDescUtil());
                    for (int i = 2; i < files.size(); i++) {
                        FileUtil.deleteDir(files.get(i));
                    }
                }
            }
            String packageVersion = getDeployRecordVo(new DeployRecordVo()).getVersion();
            String bakPath = remotePath + "bak" + separator + packageVersion;
            File bakFile = new File(bakPath);
            String backupFilePath = bakPath + separator +  oldFileName + "_bak";
            File backupFile = new File(backupFilePath);
            if (!bakFile.exists()) {
                bakFile.mkdirs();
            } else {
                if (backupFile.exists()) {
                    FileUtil.deleteDir(backupFile);
                }
            }
            taskVo.setStatus(AutoDeployType.BACKUP.getStatus());
            this.lockTask(taskVo);
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始备份程序：" + oldFile + " 到备份目录：" + backupFilePath + "......",""));
            try {
                double totalSizeOfFilesInDir = FileUtil.getTotalSizeOfFilesInDir(oldFile) / (1024 * 1024);
                BigDecimal bigDecimal = BigDecimal.valueOf(totalSizeOfFilesInDir).setScale(2, BigDecimal.ROUND_UP);
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("即将备份的" + oldFile.getName() + "文件大小为" + bigDecimal + "M, 请耐心等待...", ""));
                String ignoreBackup = taskVo.getIgnoreBackup();
                if (!StringUtils.isEmpty(ignoreBackup)) {
                    ignoreBackup = ignoreBackup.replace("\\", File.separator).replace("/", File.separator);
                    String[] split = ignoreBackup.split(";");
                    for (int i = 0; i < split.length; i++) {
                        File file = new File(remotePath + taskVo.getOldName() + File.separator + split[i]);
                        FileUtils.copyDirectory(oldFile, backupFile, new FileFilter(){
                            @Override
                            public boolean accept(File pathname) {
                                return !pathname.getAbsolutePath().contains(file.getAbsolutePath());
                            }
                        });
                    }
                } else {
                    FileUtils.copyDirectory(oldFile, backupFile);
                }
                Boolean backupResult = checkBackupResult(backupFile);
                if (!backupResult) {
                    return false;
                }
                deleteLogAndSelfPidFile(oldFile);
            } catch (IOException e) {
                return false;
            }
        }
        return true;
    }

    private Boolean closeOldSoftware(String ip,AutoDeployTaskVo taskVo) {
        Boolean result;
        String oldFileName = taskVo.getOldName();
        if (!"java-libs".equals(oldFileName)&& !"web".equals(oldFileName) && !"config".equals(oldFileName)) {
            //关闭原程序
            taskVo.setStatus(AutoDeployType.STOPING.getStatus());
            this.lockTask(taskVo);
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 开始关闭程序......" + oldFileName,""));
            result = closeSoftware(oldFileName, ip);
        }else {
            result = true;
        }
        return result;
    }

    private Boolean checkUploadResult(File file,File oldFile) {
        for (int i = 0; i < 3; i++) {
            long uploadFileSize = FileUtil.getTotalSizeOfFilesInDir(oldFile);
            long fileSize = FileUtil.getTotalSizeOfFilesInDir(file);
            Boolean compare = uploadFileSize == fileSize;
            if (compare) {
                return true;
            } else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    return false;
                }
            }
        }
        return false;
    }

    private Boolean checkRemoteUploadResult(ChannelSftp sftp,File localFile,String filePath,String fileName) {
        try {
            String fileTempPath = filePath + fileName;
            if (!fileTempPath.startsWith("/")) {
                fileTempPath = "/" + fileTempPath;
            }
            SftpATTRS lstat = sftp.lstat(fileTempPath);
            long size = lstat.getSize();
            long uploadFileSize = FileUtil.getTotalSizeOfFilesInDir(localFile);
            if (size == uploadFileSize) {
                return true;
            } else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    return false;
                }
            }
        } catch (SftpException e) {
            e.printStackTrace();
            return false;
        }
        return false;
    }

    private Boolean checkDeleteResult(File oldFile) {
        for (int i = 0; i < 3; i++) {
            if (!oldFile.exists()) {
                return true;
            } else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    return false;
                }
            }
        }
        return false;
    }

    private Boolean checkBackupResult(File backupFile) {
         for (int i = 0; i < 3; i++) {
            long backupFileSize = FileUtil.getTotalSizeOfFilesInDir(backupFile);
            if (backupFileSize > 0) {
                return true;
            } else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    return false;
                }
            }
        }
        return false;
    }

    private Boolean closeSoftware(String oldFileName,String ip){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("operateType", 0);
        paramMap.put("name", oldFileName);
        paramMap.put("pid", -1);
        String request = JsonUtil.toJsonString(paramMap);
        for (int i = 0; i < 3; i++) {
            String result = softwareService.iceRequest(ip, IceFlag.SOFTWARE_OPERATE, request);
            if (StringUtils.isEmpty(result)) {
                Log.high.info(ResponseType.OPERATE_ERROR.getMsg() + "，ICE访问失败 " + ip);
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 连接Agent失败, 正在重试...",""));
                continue;
            }
            ResponseVo agentResponse = JsonUtil.parseObject(result, ResponseVo.class);
            if (agentResponse.getCode() != null && agentResponse.getCode().equals(ResponseType.SUCCESS.getCode())) {
                return true;
            } else {
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 程序" + oldFileName + "关闭失败,正在重试...",""));
            }
        }
        return false;
    }

    private Boolean remoteDeploy(AgentVo agentVo, List<AutoDeployTaskVo> deployTaskVos) {
        Boolean deployResult = true;
        if (ListUtil.isNotEmpty(deployTaskVos)) {
            Session session = null;
            ChannelSftp sftp = null;
            try {
                session = BaseWebSocket.getSession(agentVo);
                sftp = ProcessUtil.openChannelSftp(session);
                ProcessUtil.initChannelShell(session, agentVo, 12000000L);
                for (AutoDeployTaskVo taskVo : deployTaskVos) {
                    sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("---开始部署<" + taskVo.getName() + ">程序---",""));
                    Boolean remoteDeployResult = remoteBackupAndUploadFile(taskVo, sftp, agentVo);
                    if (!remoteDeployResult) {
                        deployResult = false;
                        break;
                    } else {
                        taskVo.setStatus(AutoDeployType.SUCCESS.getStatus());
                        taskVo.setRemark("部署成功");
                        this.lockTask(taskVo);
                    }
                }
            }  catch(Exception e){
                Log.high.error(e.getMessage(), e);
                return false;
            } finally{
                if (sftp != null) {
                    sftp.disconnect();
                }
                if (session != null) {
                    session.disconnect();
                }
                try {
                    ProcessUtil.destroyChannelShell(agentVo);
                } catch (IOException e) {
                    Log.high.error(e.getMessage(), e);
                    return false;
                }
            }
        }
        return deployResult;
    }

    private Boolean remoteBackupAndUploadFile(AutoDeployTaskVo taskVo,ChannelSftp sftp, AgentVo agentVo){
        ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
        List<ModifyConfigVo> modifyConfigVos = softwareService.queryModifyConfig(modifyConfigCriteria);
        List<String> collect = modifyConfigVos.stream().map(ModifyConfigVo::getProgramName).collect(Collectors.toList());
        String oldFileName = taskVo.getOldName();
        String ip = agentVo.getIp();
        try {
            if ("2".equals(taskVo.getStatus())) {
                return remoteStep2(ip, oldFileName, sftp, agentVo, taskVo, collect);
            } else if ("3".equals(taskVo.getStatus())) {
                return remoteStep3(ip, oldFileName, sftp, agentVo, taskVo, collect);
            } else if ("4".equals(taskVo.getStatus())) {
                return remoteStep4(agentVo, ip, sftp, taskVo, collect);
            } else if ("5".equals(taskVo.getStatus())) {
                return remoteStep5(ip, taskVo, collect);
            } else {
                return remoteStep1(ip, oldFileName, sftp, agentVo, taskVo, collect);
            }
        } catch(Exception e){
            Log.high.error(e.getMessage(), e);
            //失败
            taskVo.setRemark(e.getMessage());
            taskVo.setStatus(AutoDeployType.FAIL.getStatus());
            this.lockTask(taskVo);
            return false;
        }
    }

    private Boolean remoteStep1(String ip, String oldFileName, ChannelSftp sftp, AgentVo agentVo, AutoDeployTaskVo taskVo, List<String> collect) throws FileNotFoundException, SftpException {
        Boolean closeResult = closeOldSoftware(ip, taskVo);
        if (closeResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + "-> 程序:" + oldFileName + "关闭成功！",""));
            Log.low.info(ip + "-> 程序:" + oldFileName + "关闭成功！");
            return remoteStep2(ip, oldFileName, sftp, agentVo, taskVo, collect);
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 关闭: " + oldFileName + "程序失败,请解决文件占用后重试！",""));
            return false;
        }
    }

    private Boolean remoteStep2(String ip, String oldFileName, ChannelSftp sftp, AgentVo agentVo, AutoDeployTaskVo taskVo, List<String> collect) throws FileNotFoundException, SftpException {
        Boolean backupResult = remoteBackupOldSoftware(taskVo);
        if (backupResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 备份: " + oldFileName + "成功！",""));
            Log.low.info(ip + " -> 备份: " + oldFileName + "成功！");
            return  remoteStep3(ip, oldFileName, sftp, agentVo, taskVo, collect);
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 备份: " + oldFileName + "失败,请重试！",""));
            return false;
        }
    }

    private Boolean remoteStep3(String ip, String oldFileName, ChannelSftp sftp, AgentVo agentVo, AutoDeployTaskVo taskVo, List<String> collect) throws FileNotFoundException, SftpException {
        Boolean deleteResult = remoteDeleteOldSoftware(taskVo);
        if (deleteResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 删除: " + oldFileName + "冗余文件成功！",""));
            Log.low.info(ip + " -> 删除: " + oldFileName + "冗余文件成功！");
            return remoteStep4(agentVo, ip, sftp, taskVo, collect);
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 删除: " + oldFileName + "文件失败,请解决文件占用后重试！",""));
            return false;
        }
    }

    private Boolean remoteStep4(AgentVo agentVo, String ip, ChannelSftp sftp, AutoDeployTaskVo taskVo, List<String> collect) throws FileNotFoundException, SftpException {
        Boolean uploadResult = remoteUploadSoftware(sftp,agentVo,taskVo);
        if (uploadResult) {
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 上传:" + taskVo.getName() + "成功！",""));
            Log.low.info(ip + " -> 上传:" + taskVo.getName() + "成功！");
            return remoteStep5(ip, taskVo, collect);
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 上传:" + taskVo.getName() + "->失败,请关闭程序，请解决文件占用后重试！",""));
            return false;
        }
    }

    private Boolean remoteStep5(String ip,AutoDeployTaskVo taskVo, List<String> collect){
        Boolean replaceResult = replaceRemoteConfig(taskVo);
        if (replaceResult) {
            if (!"java-libs".equals(taskVo.getName()) && collect.contains(taskVo.getName())) {
                sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " -> 成功替换程序: " + taskVo.getName() + "配置文件！",""));
                Log.low.info(ip + " -> 成功替换程序: " + taskVo.getName() + "配置文件！");
            }
        } else {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 替换程序: " + taskVo.getName() + "配置文件失败,请重试！",""));
            return false;
        }
        return true;
    }


    private Boolean remoteUploadSoftware(ChannelSftp sftp, AgentVo agentVo,AutoDeployTaskVo taskVo) throws FileNotFoundException, SftpException {
        taskVo.setStatus(AutoDeployType.UPLOAD.getStatus());
        this.lockTask(taskVo);
        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始上传" + taskVo.getName() + "程序......", ""));
        String ip = taskVo.getIp();
        String remotePath;
        String remoteUploadPath = null;
        if (agentVo.getOs().equals(LINUX)) {
            remotePath = "/" + taskVo.getTargetPath() + "/";
            if ("root".equals(agentVo.getName())) {
                remoteUploadPath = remotePath;
            } else {
                remoteUploadPath = "/home/" + agentVo.getName() + "/";
            }
        } else if (agentVo.getOs().equals(WINDOWS)) {
            remotePath = "D:\\" + taskVo.getTargetPath() + "\\";
        } else {
            throw new ServiceException("未知操作系统...");
        }
        File localProgramPackage = new File(taskVo.getSourcePath());
        double totalSizeOfFilesInDir1 = FileUtil.getTotalSizeOfFilesInDir(localProgramPackage) / (1024*1024);
        BigDecimal bigDecimal = BigDecimal.valueOf(totalSizeOfFilesInDir1).setScale(2, BigDecimal.ROUND_UP);
        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("即将上传的" + taskVo.getName() + "部署包大小为" + bigDecimal + "M, 请耐心等待...", ""));
        List<File> files = (List<File>) FileUtils.listFiles(localProgramPackage, null, true);
        Boolean result = true;
        List<String> failureFile = new LinkedList<>();
        for (int i = 0; i < files.size(); i++) {
            Boolean uploadResult = false;
            String localFilePath = files.get(i).getAbsolutePath();
            String programFilePath = localFilePath.substring(localFilePath.indexOf(taskVo.getName()));
            String uploadPath;
            String remoteUploadFilePath;
            String targetPath;
            File file = new File(localFilePath);
            if (agentVo.getOs().equals(LINUX)) {
                uploadPath = programFilePath.replace("\\","/").replace(file.getName(),"");
                remoteUploadFilePath = remoteUploadPath + uploadPath;
                targetPath = remotePath + uploadPath;
                if (taskVo.getName().equals("config")) {
                    try {
                        commonConfigService.prepareCommonConfigForRemoteServer(agentVo.getIp());
                    } catch (Exception e) {
                        Log.high.error(e.getMessage(), e);
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent( "准备公共配置异常,请解决问题后重试",""));
                        return false;
                    }
                }
                try {
                    ProcessUtil.updateSingleFileToRemote(sftp, localFilePath, file.getName(),targetPath,remoteUploadFilePath);
                }catch (Exception e) {
                    Log.high.error(e);
                    if ("java-libs".equals(taskVo.getName()) || "config".equals(taskVo.getName())) {
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent( "！！！*****************************！！！",""));
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent( " -> 文件:" + localFilePath + "上传" + ip + "失败,请在部署结束后解决占用再手动上传！！！",""));
                        failureFile.add(localFilePath);
                    } else {
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 文件:" + localFilePath + "上传失败,请解决文件占用后重试!",""));
                        return false;
                    }
                }
                uploadResult = checkRemoteUploadResult(sftp,file,targetPath,file.getName());
            } else {
                String fileName = file.getName();
                targetPath = remotePath + programFilePath.replace(fileName,"");
                if (taskVo.getName().equals("config")) {
                    try {
                        commonConfigService.prepareCommonConfigForRemoteServer(agentVo.getIp());
                    } catch (Exception e) {
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent( "准备公共配置异常,请解决问题后重试",""));
                        return false;
                    }

                }
                try {
                    ProcessUtil.uploadSingleFileToRemoteWindows(sftp,localFilePath,targetPath.replace("\\","/"),fileName);
                }  catch (Exception e) {
                    Log.high.error(e);
                    if ("java-libs".equals(taskVo.getName()) || "config".equals(taskVo.getName())) {
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent( "！！！*****************************！！！",""));
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent( " -> 文件:" + localFilePath + "上传" + ip + "失败,请在部署结束后解决占用再手动上传！！！",""));
                        failureFile.add(localFilePath);
                    } else {
                        sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent(ip + " -> 文件:" + localFilePath + "上传失败,请解决文件占用后重试!",""));
                        return false;
                    }
                }
                uploadResult = checkRemoteUploadResult(sftp,file,targetPath.replace("\\","/"),fileName);
            }
            if (!uploadResult) {
                result = false;
                break;
            }
        }
        if (ListUtil.isNotEmpty(failureFile)) {
            needManulHandleExceptionInfo.put(ip,failureFile);
        }
        return result;
    }

    private Boolean remoteDeleteOldSoftware(AutoDeployTaskVo taskVo) {
        taskVo.setStatus(AutoDeployType.DELETE.getStatus());
        this.lockTask(taskVo);
        String ip = taskVo.getIp();
        DeleteResultVo data;
        if (!"java-libs".equals(taskVo.getOldName())&& !"config".equals(taskVo.getOldName())) {
            String remotePath = taskVo.getTargetPath();
            sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始删除要部署目录" + remotePath + "下的冗余文件......",""));
            String oldFileName = taskVo.getOldName();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("remotePath", remotePath);
            paramMap.put("oldFileName", oldFileName);
            List<String> deleteFiles = new LinkedList<>();
            File file = new File(taskVo.getSourcePath());
            List<File> files = (List<File>)FileUtils.listFiles(file, null, true);
            files.stream().forEach(e -> deleteFiles.add(e.getAbsolutePath()));
            paramMap.put("deleteFiles", deleteFiles);
            paramMap.put("needDeleteFiles", taskVo.getNeedDeleteFiles());
            paramMap.put("fullPackage", taskVo.getIsFullPackage());
            String param = JsonUtil.toJsonString(paramMap);
            try {
                String result = softwareService.iceRequest(ip, IceFlag.DELETE_OLD_SOFTWARE, param);
                if (result == null) {
                    sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("调用Agent失败,请检查Agent连接状态", ""));
                    return false;
                } else {
                    Map<String, Object> map = JsonUtil.parseObject(result, new TypeReference<Map<String, Object>>() {
                    });
                    String str = JSON.toJSONString(map.get("data"));
                    data = JSONObject.parseObject(str, DeleteResultVo.class);
                    if (data.getSucc()){
                        return true;
                    }else {
                        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent(ip + " 机器的 " + data.getContent() + " 文件上传失败,请手动上传", ""));
                        return false;
                    }
                }
            } catch (Exception e) {
                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("调用Agent失败,请检查Agent连接状态", ""));
                return false;
            }
        } else {
            return true;
        }
    }

    private Boolean remoteBackupOldSoftware(AutoDeployTaskVo taskVo) {
        taskVo.setStatus(AutoDeployType.BACKUP.getStatus());
        this.lockTask(taskVo);
        //移动到备份目录,将原程序备份为：程序名_bak;
        String oldFileName = taskVo.getOldName();
        String remotePath = taskVo.getTargetPath();
        String ip = taskVo.getIp();
        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始备份程序：" + oldFileName + "......",""));
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("oldFileName", oldFileName);
        paraMap.put("remotePath", remotePath);
        String para = JsonUtil.toJsonString(paraMap);
        try {
            String totalSizeOfFilesInDir = softwareService.iceRequest(ip, IceFlag.GET_FILE_SIZE, para);
            if (totalSizeOfFilesInDir == null) {
                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("调用Agent失败,请检查Agent连接状态", ""));
                return false;
            } else {
                Map<String, Object> m = JsonUtil.parseObject(totalSizeOfFilesInDir, new TypeReference<Map<String, Object>>() {
                });
                Integer d = (Integer)m.get("data");
                if (d != null) {
                    double size = d / (1024 * 1024);
                    BigDecimal bigDecimal = BigDecimal.valueOf(size).setScale(2, BigDecimal.ROUND_UP);
                    sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("即将备份的" + oldFileName + "文件大小为" + bigDecimal + "M, 请耐心等待...", ""));
                }
            }
        } catch (Exception e) {
            Log.low.info("调用Agent失败， " + e);
            return false;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("oldFileName", oldFileName);
        String packageVersion = getDeployRecordVo(new DeployRecordVo()).getVersion();
        paramMap.put("productVersion",packageVersion);
        paramMap.put("remotePath", remotePath);
        paramMap.put("ignore", taskVo.getIgnoreBackup());
        sentText(webSocket, ReqRespHelper.writeSuccessWithMsgAndContent("开始请求Agent备份程序...",""));
        String param = JsonUtil.toJsonString(paramMap);
        try {
            String result = softwareService.iceRequest(ip, IceFlag.BACKUP_PROGRAM, param);
            if (result == null) {
                sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("调用Agent失败,请检查Agent连接状态", ""));
                return false;
            } else {
                Map<String, Object> map = JsonUtil.parseObject(result, new TypeReference<Map<String, Object>>() {
                });
                Boolean data = (Boolean)map.get("data");
                if (data){
                    return true;
                }else {
                    return false;
                }
            }
        } catch (Exception e) {
            sentText(webSocket, ReqRespHelper.writeResultFailureWithMsgAndContent("调用Agent失败,请检查Agent连接状态", ""));
            return false;
        }
    }

    public Map<String, Object> parseRoleServiceRelationFileToMap(File roleServiceRelationFile) {
        try {
            Map<String, Object> map = new LinkedHashMap<>();
            String content = IOUtils.toString(new FileInputStream(roleServiceRelationFile), UTF_8);
            RoleServiceVo roleServiceVo = JsonUtil.parseObject(content, RoleServiceVo.class);
            Map<String,Map<String,Map<String,RoleProgramVo>>> relations = roleServiceVo.getRelations();
            Map<String, Map<String, Map<String, RoleProgramVo>>> task = getTask(relations);
            map.put("task", task);
            Map<String,String> regularMap = new LinkedMap();
            regularMap.put("platform_name",roleServiceVo.getPlatformName());
            regularMap.put("platform_regular",roleServiceVo.getPlatformRegular());
            regularMap.put("platform_version",roleServiceVo.getPlatformVersion());
            regularMap.put("is_full_package",roleServiceVo.getIsFullPackage().toString());
            map.put("regularMap",regularMap);
            return map;
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    public Map<String, Map<String, Map<String, RoleProgramVo>>> getTask(Map<String,Map<String,Map<String,RoleProgramVo>>> relations){
        Map<String, Map<String, Map<String, RoleProgramVo>>> task = new HashMap<>();
        List<IpRoleVo> ipRoleVoList = agentService.getIpRoleVoList();
        for (IpRoleVo ipRoleVo : ipRoleVoList) {
            String roleName = ipRoleVo.getRole();
            String ip = ipRoleVo.getIp();
            if (StringUtils.isEmpty(ip)) {
                continue;
            }
            Map<String, Map<String, RoleProgramVo>> roleMap = new LinkedHashMap<>();
            if (StringUtils.isNotEmpty(roleName)) {
                if (roleName.contains(",")) {
                    String[] roleNames = roleName.split(",");
                    for (String name : roleNames) {
                        roleMap = relations.get(name);
                    }
                } else {
                    roleMap = relations.get(roleName);
                }
            }
            if (roleMap != null && !roleMap.isEmpty()) {
                task.put(ip, roleMap);
            }
        }
        return task;
    }

    private void updateSoftwareInfo(List<AutoDeployTaskVo> deployTaskVos, AgentVo agentVo) {
        if (agentVo == null || deployTaskVos == null) {
            return;
        }
        for (AutoDeployTaskVo autoDeployTaskVo : deployTaskVos) {
            if ("java-libs".equals(autoDeployTaskVo.getName()) || "config".equals(autoDeployTaskVo.getName())) { //Web的加入，但在管理页面不展示
                continue;
            }
            String remotePath = "";
            if (agentVo.getOs().equals(LINUX)) {
                remotePath = "/" + autoDeployTaskVo.getTargetPath() +"/";
            } else if (agentVo.getOs().equals(WINDOWS)) {
                remotePath = "D:\\" + autoDeployTaskVo.getTargetPath() + "\\";
            }
            String name = autoDeployTaskVo.getName();
            try {
                //部署成功，往数据库更新程序信息.
                //Log.low.info(name + " agentVo信息:" + JsonUtil.toJsonString(agentVo));
                SoftwareCriteria criteria = new SoftwareCriteria();
                criteria.setName(name);
                criteria.setServerId(agentVo.getId());
                SoftwareVo softwareVo = softwareService.getSoftwareInfo(criteria);

                if (softwareVo == null) {
                    softwareVo = new SoftwareVo();
                    softwareVo.setName(name);
                    softwareVo.setHost(agentVo.getIp());
                    softwareVo.setServerIp(agentVo.getIp());
                    softwareVo.setBaseDir(autoDeployTaskVo.getSourcePath());
                    softwareVo.setRealDir(remotePath + name);
                    softwareVo.setVersion(autoDeployTaskVo.getProduct().split("-")[1]);
                    softwareVo.setServerId(agentVo.getId());
                    softwareVo.setHeartMonitor(Boolean.TRUE);
                    softwareVo.setProcessCount(1);
                    softwareVo.setProgramStatus(ProgramStatusType.CLOSE);
                    softwareVo.setDescription("程序未开启");
                    softwareVo.setConfig(Boolean.FALSE);
                    softwareVo.setStatus(AlarmStatusType.YELLOW);
                    softwareVo.setAutoDaemon(1);
                    softwareService.addSoftwareInfo(softwareVo);
                    softwareService.pushSoftwareInfoToAgent(agentVo.getIp());
                } else {
                    //更新版本号等信息.
                    softwareVo.setServerId(agentVo.getId());
                    softwareVo.setLastUpdateTime(new Date());
                    softwareVo.setBaseDir(autoDeployTaskVo.getSourcePath());
                    softwareVo.setRealDir(remotePath + name);
                    softwareVo.setVersion(autoDeployTaskVo.getProduct().split("-")[1]);
                    softwareVo.setProgramStatus(ProgramStatusType.CLOSE);
                    softwareVo.setDescription("程序未开启");
                    softwareVo.setStatus(AlarmStatusType.YELLOW);
                    softwareService.updateSoftwareInfo(softwareVo, true);
                }
            } catch (Exception e) {
                Log.high.error(name + "..." + e.getMessage(), e);
            }
        }
    }
}