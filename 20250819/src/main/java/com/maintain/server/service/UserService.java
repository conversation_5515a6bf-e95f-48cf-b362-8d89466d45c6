package com.maintain.server.service;

import com.maintain.server.criteria.BaseCriteria;
import com.maintain.server.criteria.DynamicAndStaticCriteria;
import com.maintain.server.criteria.UserCriteria;
import com.maintain.server.vo.DynamicAndStaVoticThresholdVo;
import com.maintain.server.vo.PermissionVo;
import com.maintain.server.vo.RoleVo;
import com.maintain.server.vo.UserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-10-24
 */
public interface UserService {

    /**
     * 注册用户
     * @return
     */
    int registry(UserVo user);


    /**
     * 注销用户
     * @return
     */
    boolean cancel(UserVo user);

    /**
     * 初始化用户密码
     * @return
     */
    boolean initPassword(UserVo user);

    /**
     * 用户列表
     * @param criteria
     * @return
     */
    List<UserVo> listUsers(BaseCriteria criteria);

    /**
     * 角色列表
     * @param criteria
     * @return
     */
    List<RoleVo> listRoles(BaseCriteria criteria);

    /**
     * 权限列表
     * @param criteria
     * @return
     */
    List<PermissionVo> listPermissions(BaseCriteria criteria);

    /**
     * 通过角色ID查找角色
     * @param roleIds
     * @return
     */
    List<RoleVo> findRolesByIds(List<Integer> roleIds);

    /**
     * 更新用户拥有的角色
     * @param userId
     * @param roleIds
     */
    boolean updateRolesOfUser(int userId, List<Integer> roleIds);

    /**
     * 更新角色的权限
     * @param roleId
     * @param permission
     * @return
     */
    boolean updatePermissionOfRole(int roleId, List<Integer> permission);

    /**
     * 通过username加载该用户拥有的权限
     * @param username
     * @return
     */
    List<PermissionVo> loadPermissionByUsername(String username);

    /**
     * userId
     * @param userId
     * @return
     */
    List<PermissionVo> loadPermissionByUserId(int userId);

    /**
     * 根据权限ID返回权限
     * @param id
     * @return
     */
    PermissionVo findPermissionById(int id);

    PermissionVo findPermissionByName(String name);

    List<PermissionVo> findChildPermissions(int parentId);

    UserVo getUserByCriteria(UserCriteria criteria);

    boolean addDynamicAndStatic(DynamicAndStaVoticThresholdVo dynamicAndStaVoticThresholdVo );

    boolean updateDynamicAndStatic(DynamicAndStaVoticThresholdVo dynamicAndStaVoticThresholdVo );

    List<DynamicAndStaVoticThresholdVo> findThreshold(DynamicAndStaticCriteria dynamicAndStaticCriteria);

    /**
     * 校验用户和其对应的密码是否匹配
     * @param user
     * @param pass
     * @return
     */
    boolean confirmUserAndPass(String user, String pass);


}