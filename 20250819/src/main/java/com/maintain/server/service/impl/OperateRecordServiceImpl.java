package com.maintain.server.service.impl;

import com.common.log.Log;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.OperateRecordCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.OperateRecordMapper;
import com.maintain.server.service.OperateRecordService;
import com.maintain.server.vo.OperateRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-28
 */
@Service
public class OperateRecordServiceImpl implements OperateRecordService {

    @Autowired
    private OperateRecordMapper operateRecordMapper;

    @Override
    public PageInfo<OperateRecordVo> getOperateRecord(OperateRecordCriteria operateRecordCriteria) {
        try {
            if (operateRecordCriteria != null) {
                Integer pn = operateRecordCriteria.getPn();
                Integer ps = operateRecordCriteria.getPs();
                if (pn != null && ps != null) {
                    PageHelper.startPage(pn, ps);
                }
            }
            List<OperateRecordVo> list = operateRecordMapper.getOperateRecord();
            return new PageInfo<>(list);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
        return new PageInfo<>();
    }

    @Override
    @Transactional
    public void addOperateRecord(OperateRecordVo operateRecordVo) {
        operateRecordMapper.addOperateRecord(operateRecordVo);
    }

    @Override
    @OperateRecordAnnotation(name = "操作审计管理")
    public void operateRecordAOP(Integer tag) {
        if (tag != null) {
            return;
        }
        throw new ServiceException("tag参数为Null");
    }
}