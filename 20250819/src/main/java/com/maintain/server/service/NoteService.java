package com.maintain.server.service;

import com.maintain.server.criteria.NoteCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.vo.NoteVo;

import java.util.List;

public interface NoteService {

    List<NoteVo> queryNotes(NoteCriteria noteCriteria) throws ServiceException;

    void addNote(NoteCriteria noteCriteria) throws ServiceException;

    void deleteNote(NoteCriteria noteCriteria) throws ServiceException;

    void updateNote(NoteCriteria noteCriteria) throws ServiceException;

    Integer count(NoteCriteria noteCriteria)throws ServiceException;

}
