package com.maintain.server.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.maintain.server.Constants;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.BaseCriteria;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.criteria.PingResultCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.HardwareMapper;
import com.maintain.server.mapper.PingResultMapper;
import com.maintain.server.mapper.WarnMapper;
import com.maintain.server.service.HardwareService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.OsType;
import com.maintain.server.type.WarnType;
import com.maintain.server.utils.*;
import com.maintain.server.vo.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-10-24
 */
@Service("hardwareService")
public class HardwareServiceImpl implements /*MonitorService,*/ HardwareService, Constants {

    @Autowired
    private HardwareMapper hardwareMapper;

    @Autowired
    private WarnMapper warnMapper;

    @Autowired
    private PingResultMapper pingResultMapper;

    @Value("${ping-count-monitor.red}")
    private Integer pingCountRed;

    @Value("${ping-count-monitor.yellow}")
    private Integer pingCountYellow;

    private String cpuRange = "80,90";

    private String memoryRange = "80,90";

    private String diskRange = "80,90";

    @Value("${ignore-dir}")
    private String ignoreDir;

    @Value("${port-monitor.yellow}")
    private Integer low;

    @Value("${port-monitor.red}")
    private Integer high;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addHardwareInfo(HardwareVo hardwareVo) throws ServiceException {
        //同一事务
        HardwareCriteria criteria = new HardwareCriteria();
        criteria.setAgentId(hardwareVo.getAgentId());
        if (ListUtil.isNotEmpty(hardwareMapper.getHardwareInfos(criteria))) {
            hardwareMapper.updateHardware(hardwareVo);
        } else {
            hardwareMapper.addHardwareInfo(hardwareVo);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @OperateRecordAnnotation(name = "编辑%s上的备注，备注信息如下:%s", module = "hardware")
    public void updateHardwareInfo(HardwareVo hardwareVo) {
        hardwareMapper.updateHardware(hardwareVo);
    }

    @Override
    public HardwareVo getHardwareInfo(HardwareCriteria criteria) throws ServiceException {
        if (criteria == null) {
            criteria = new HardwareCriteria();
        }
        List<HardwareVo> list = getHardwareInfos(criteria);
        if (ListUtil.isNotEmpty(list)) {
            // setStatus(hardwareVo);
            return list.get(0);
        }
        return null;
    }

    /**
     * 中途return表示该hardwareVo的信息有问题，需要修改Agent的监控回调数据格式
     *
     * @param hardwareVo hardwareVo
     */
    private void setStatus(HardwareVo hardwareVo) {
        Date heartbeatTime = hardwareVo.getLastHeartbeatTime();
        hardwareVo.setShow(Boolean.TRUE);
        //判断windows 2016版本是否是Standard版的，不是则告警
        if (OsType.WINDOWS.getId().equals(hardwareVo.getOs())) {
            if (hardwareVo.getOsName().contains("2016") && !hardwareVo.getOsName().contains("Standard")) {
                hardwareVo.setStatus(AlarmStatusType.RED);
                hardwareVo.setDescription("该系统不是Windows2016标准版");
                hardwareVo.setShow(Boolean.FALSE);
                return;
            }
        }
        if (heartbeatTime == null) {
            hardwareVo.setStatus(AlarmStatusType.RED);
            hardwareVo.setDescription("最近心跳为空");
            hardwareVo.setShow(Boolean.FALSE);
            return;
        } else if ((System.currentTimeMillis() - heartbeatTime.getTime()) >= 1800 * 1000) {
            hardwareVo.setStatus(AlarmStatusType.RED);
            hardwareVo.setDescription("最近一次心跳在30分钟前");
            hardwareVo.setShow(Boolean.FALSE);
            return;
        }
        String diskUsedPercent = hardwareVo.getDiskUsedPercent();
        Map<String, AlarmStatusType> statusMap = new HashMap<>();
        Map<AlarmStatusType, String> descriptionMap = new HashMap<>();
        if (StringUtils.isEmpty(diskUsedPercent)) {
            hardwareVo.setDescription("硬盘信息有误");
            hardwareVo.setDiskStatus(AlarmStatusType.RED);
            return;
        } else {
            final WarnVo warnVo = new WarnVo();
            warnVo.setType(WarnType.HARDWARE_DISK_SPACE);
            warnVo.setParam1(hardwareVo.getIp());
            final List<WarnVo> warnList = warnMapper.getWarnList(warnVo);
            WarnVo warn = null;
            for (WarnVo vo : warnList) {
                if ("DEFAULT".equals(vo.getParam2())) {
                    warn = vo;
                    break;
                }
            }
            Set<WarnVo> warnSet = warnList.stream().filter(w -> !w.getParam2().equals("DEFAULT")).collect(Collectors.toSet());
            HardwareStatusUtil util;
            if (Objects.isNull(warn) || Integer.valueOf(0).equals(warn.getStatus())) {
                util = new HardwareStatusUtil("1T", "500G", "80", "90");
            } else {
                util = new HardwareStatusUtil(warn.getParam3(), warn.getParam4(), "80", "90");
            }
            util.judgeDiskInfo("硬盘", diskUsedPercent, hardwareVo.getTotalDisk(), hardwareVo.getUsedDisk(), statusMap, descriptionMap);
            final LinkedList<Map<String, String>> linkedList = JsonUtil.parseObject(hardwareVo.getDbDiskInfo(), LinkedList.class);
            HashSet<String> set = new HashSet<>();
            if (ignoreDir != null) {
                String[] split = ignoreDir.split(",");
                Collections.addAll(set, split);
            }
            out:
            for (Map<String, String> o : linkedList) {
                String name = o.get("name");
                if (!set.contains(name)) {
                    for (WarnVo vo : warnSet) {
                        if (vo.getParam2().equals(name)) {
                            new HardwareStatusUtil(vo.getParam3(), vo.getParam4(), vo.getParam5(), vo.getParam6()).judgeDiskInfo("目录 " + o.get("name"), o.get("usedPercent"), o.get("total"), o.get("used"), statusMap, descriptionMap);
                            continue out;
                        }
                    }
                    util.judgeDiskInfo("目录 " + o.get("name"), o.get("usedPercent"), o.get("total"), o.get("used"), statusMap, descriptionMap);
                }
            }
        }
        String usedCpu = hardwareVo.getUsedCpu();
        usedCpu = usedCpu.replace("%", "").trim();
        BigDecimal bigDecimal = new BigDecimal(usedCpu);
        if (StringUtils.isEmpty(usedCpu)) {
            hardwareVo.setDescription("CPU信息有误");
            hardwareVo.setCpuStatus(AlarmStatusType.RED);
            hardwareVo.setStatus(AlarmStatusType.RED);
            return;
        } else {
            final WarnVo warnVo = new WarnVo();
            warnVo.setType(WarnType.CPU_SPACE);
            warnVo.setParam1(hardwareVo.getIp());
            final List<WarnVo> warnList = warnMapper.getWarnList(warnVo);
            WarnVo warn = null;
            for (WarnVo vo : warnList) {
                if (!"DEFAULT".equals(vo.getParam2())) {
                    warn = vo;
                    break;
                }
            }
            HardwareStatusUtil util;
            if (Objects.isNull(warn) || Integer.valueOf(0).equals(warn.getStatus())) {
                String low = cpuRange.split(",")[0];
                String high = cpuRange.split(",")[1];
                util = new HardwareStatusUtil(low, high);
            } else {
                util = new HardwareStatusUtil(warn.getParam5(), warn.getParam6());
            }
            util.judgeCpuAndMemInfo("cpu", descriptionMap, statusMap, bigDecimal);
        }
        String usedMemory = hardwareVo.getMemoryUsedPercent();
        if (StringUtils.isEmpty(usedMemory)) {
            hardwareVo.setDescription("内存信息有误");
            hardwareVo.setMemoryStatus(AlarmStatusType.RED);
            hardwareVo.setStatus(AlarmStatusType.RED);
            return;
        } else {
            final WarnVo warnVo = new WarnVo();
            warnVo.setType(WarnType.MEMORY_SPACE);
            warnVo.setParam1(hardwareVo.getIp());
            final List<WarnVo> warnList = warnMapper.getWarnList(warnVo);
            WarnVo warn = null;
            for (WarnVo vo : warnList) {
                if (!"DEFAULT".equals(vo.getParam2())) {
                    warn = vo;
                    break;
                }
            }
            HardwareStatusUtil util;
            if (Objects.isNull(warn) || Integer.valueOf(0).equals(warn.getStatus())) {
                String low = memoryRange.split(",")[0];
                String high = memoryRange.split(",")[1];
                util = new HardwareStatusUtil(low, high);
            } else {
                util = new HardwareStatusUtil(warn.getParam5(), warn.getParam6());
            }
            util.judgeCpuAndMemInfo("内存", descriptionMap, statusMap, bigDecimal);
        }
        Set<AlarmStatusType> set = new HashSet<>();
        for (Map.Entry<String, AlarmStatusType> entry : statusMap.entrySet()) {
            String key = entry.getKey();
            AlarmStatusType type = entry.getValue();
            set.add(type);
            if ("硬盘".equals(key)) {
                hardwareVo.setDiskStatus(type);
            } else if ("cpu".equals(key)) {
                hardwareVo.setCpuStatus(type);
            } else if ("内存".equals(key)) {
                hardwareVo.setMemoryStatus(type);
            } else if (key.contains("目录")) {
                if (type.equals(AlarmStatusType.YELLOW) || type.equals(AlarmStatusType.RED)) {
                    final AlarmStatusType diskType = statusMap.get("硬盘");
                    if (diskType.compareTo(type) < 0) {
                        statusMap.put("硬盘", type);
                    }
                }
            }
        }
        Integer firewallStatus = hardwareVo.getFirewallStatus();
        if (firewallStatus == null || firewallStatus == 0) {
            AlarmStatusType red = AlarmStatusType.RED;
            set.add(red);
            statusMap.put("防火墙", red);
            String description = descriptionMap.get(red);
            String newDescription = "未开启防火墙";
            if (description != null) {
                description = description + "," + newDescription;
            } else {
                description = newDescription;
            }
            descriptionMap.put(red, description);
        }
        List<AlarmStatusType> typeList = new ArrayList<>(set);
        if (ListUtil.isEmpty(typeList)) {
            return;
        }
        typeList.sort(new AlarmTypeCompareDescUtil());
        AlarmStatusType status = typeList.get(0);
        String description = descriptionMap.get(status);
        final Integer ports = hardwareVo.getPorts();
        AlarmStatusType stat = AlarmStatusType.GREEN;
        String des = null;
        if (ports != null && ports >= low) {
            if (ports >= high) {
                stat = AlarmStatusType.RED;
            } else {
                stat = AlarmStatusType.YELLOW;
            }
            des = "占用端口数:" + ports;
        }
        if (stat.compareTo(status) < 0) {
            stat = status;
        }
        final StringJoiner stringJoiner = new StringJoiner(",", "", "");
        if (description != null) {
            stringJoiner.add(description);
        }
        if (des != null) {
            stringJoiner.add(des);
        }
        /*AlarmStatusType networkStatus = judgeNetwork(hardwareVo.getIp());
        if (networkStatus != AlarmStatusType.GREEN) {
            hardwareVo.setStatus(networkStatus);
            hardwareVo.setDescription("网络有异常情况");
            return;
        }*/

        hardwareVo.setStatus(stat);
        hardwareVo.setDescription(stringJoiner.toString());
    }

    @Override
    public List<HardwareVo> getHardwareInfos(HardwareCriteria criteria) throws ServiceException {
        List<HardwareVo> list = hardwareMapper.getHardwareInfos(criteria);
            if (ListUtil.isNotEmpty(list)) {
            for (HardwareVo hardwareVo : list) {
                setStatus(hardwareVo);
            }
            if (criteria != null && criteria.getStatusType() != null) {
                list = list.stream().filter(li -> li.getStatus().equals(criteria.getStatusType())).collect(Collectors.toList());
            }
            list.sort((o1, o2) -> {
                try {
                    AlarmStatusType type1 = o1.getStatus();
                    AlarmStatusType type2 = o2.getStatus();
                    if (type1.getId() > type2.getId()) {
                        return -1;
                    }
                    if (type1.getId() < type2.getId()) {
                        return 1;
                    }
                } catch (Exception e) {
                    return 0;
                }
                return 0;
            });
        }
        return list;
    }

    @Override
    public Map<String, Object> getHardwareInfosAndStatus(HardwareCriteria criteria) throws ServiceException {
        AlarmStatusType alarmStatusType = criteria.getStatusType();
        criteria.setStatusType(null);
        List<HardwareVo> hardwareVos = getHardwareInfos(criteria);
        Map<String, Long> statusMap = hardwareVos.stream().collect(Collectors.groupingBy(v -> v.getStatus().getValue(), Collectors.counting()));
        hardwareVos = hardwareVos.stream()
                .filter(v -> alarmStatusType == null || alarmStatusType.equals(v.getStatus()))
                .collect(Collectors.toList());
        Map<String, Object> resultMap = new HashMap<>(4);
        resultMap.put("statusMap", statusMap);
        resultMap.put("hardwares", hardwareVos);
        return resultMap;
    }

    @Override
    public void addHardwareHistoryHealth(HardwareHistoryHealth historyHealthVo) throws ServiceException {
        try {
            List<HardwareHistoryHealth> list = new ArrayList<>();
            list.add(historyHealthVo);
            hardwareMapper.addHardwareHistoryHealth(list);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void addHardwareHistoryHealth(List<HardwareHistoryHealth> hardwareList) throws ServiceException {
        try {
            hardwareMapper.addHardwareHistoryHealth(hardwareList);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public PageInfo<HardwareHistoryHealth> getHardwareHistoryHealth(HardwareCriteria criteria) {
        if (criteria == null) {
            criteria = new HardwareCriteria();
        }
        if (criteria.getPn() != null && criteria.getPs() != null) {
            PageHelper.startPage(criteria.getPn(), criteria.getPs());
        }
        List<HardwareHistoryHealth> historyHealthList = hardwareMapper.getHardwareHistoryHealth(criteria);
        return new PageInfo<>(historyHealthList);
    }

    @Override
    public Map<String, Object> getHistoryResource(HardwareCriteria hardwareCriteria, Boolean picture) {
        final List<HardwareHistoryHealth> historyResource = hardwareMapper.getHardwareHistoryHealth(hardwareCriteria);
        Map<String, Object> historyResourceResult = null;
        if (historyResource != null && !historyResource.isEmpty()) {
            historyResourceResult = new HashMap<>();
            Map<String, Object> memoryMap = new HashMap<>();
            Map<String, Object> cpuMap = new HashMap<>();
            List<BigDecimal> diskYList = new ArrayList<>(historyResource.size());
            List<BigDecimal> cpuYList = new ArrayList<>(historyResource.size());
            List<BigDecimal> memoryYList = new ArrayList<>(historyResource.size());
            List<Integer> sendYList = new ArrayList<>(historyResource.size());
            List<Integer> receivceYList = new ArrayList<>(historyResource.size());
            List<String> timeXList = new ArrayList<>(historyResource.size());
            String unit = "%";
            Map<String, Object> diskMap = new HashMap<>();
            int cpuCount = 0;
            int memoryCount = 0;
            int diskCount = 0;
            final BigDecimal val = BigDecimal.valueOf(90);
            for (int i = 0; i < historyResource.size(); i++) {
                if (StringUtils.isEmpty(historyResource.get(i).getCpuPercent()) ||
                        StringUtils.isEmpty(historyResource.get(i).getMemoryPercent()) ||
                        StringUtils.isEmpty(historyResource.get(i).getCpuPercent())) {
                    continue;
                }
                final BigDecimal cpu = new BigDecimal(historyResource.get(i).getCpuPercent().replace(unit, ""));
                cpuYList.add(cpu);
                if (cpu.compareTo(val) >= 0) {
                    cpuCount++;
                }
                final BigDecimal memory = new BigDecimal((historyResource.get(i).getMemoryPercent().replace(unit, "")));
                memoryYList.add(memory);
                if (memory.compareTo(val) >= 0) {
                    memoryCount++;
                }
                timeXList.add(DateUtil.format(historyResource.get(i).getCreateTime(), Constants.YYYY_MM_DD_HH_MM_SS));
                final BigDecimal disk = new BigDecimal(historyResource.get(i).getDiskPercent().replace(unit, ""));
                diskYList.add(disk);
                if (disk.compareTo(val) >= 0) {
                    diskCount++;
                }
                sendYList.add(historyResource.get(i).getSendBytePerSecond() == null ? 0 : historyResource.get(i).getSendBytePerSecond());
                receivceYList.add(historyResource.get(i).getReceiveBytePerSecond() == null ? 0 : historyResource.get(i).getReceiveBytePerSecond());
            }
            if (picture) {
                historyResourceResult.put("cpuCount", cpuCount);
                historyResourceResult.put("memoryCount", memoryCount);
                historyResourceResult.put("diskCount", diskCount);
            }
            cpuMap.put("x", timeXList);
            cpuMap.put("y", cpuYList);
            memoryMap.put("x", timeXList);
            memoryMap.put("y", memoryYList);
            diskMap.put("x", timeXList);
            diskMap.put("y", diskYList);
            cpuMap.put("content", "cpu使用率");
            memoryMap.put("content", "内存使用率");
            diskMap.put("content", "磁盘使用率");
            Map<String, Object> sendMap = new HashMap<>(4);
            sendMap.put("x", timeXList);
            sendMap.put("y", sendYList);
            sendMap.put("content", "发送(字节/秒)");

            Map<String, Object> receiveMap = new HashMap<>(4);
            receiveMap.put("x", timeXList);
            receiveMap.put("y", receivceYList);
            receiveMap.put("content", "接收(字节/秒)");

            historyResourceResult.put("networkReceiveResource", receiveMap);
            historyResourceResult.put("networkSendResource", sendMap);
            historyResourceResult.put("cpuResource", cpuMap);
            historyResourceResult.put("memoryResource", memoryMap);
            historyResourceResult.put("diskResource", diskMap);
        }
        return historyResourceResult;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @OperateRecordAnnotation(name = "修改%s上的告警阈值，参数如下：%s", module = "hardware")
    public void updateWarning(String ip, Integer status, String warn, String error
            , String warnPercent, String errorPercent, String disk, WarnType warnType) {
        synchronized (HardwareServiceImpl.class) {
            final WarnVo warnVo = new WarnVo();
            warnVo.setParam1(ip);
            warnVo.setParam2(disk);
            warnVo.setType(warnType);
            final WarnVo vo = warnMapper.getWarn(warnVo);
            warnVo.setParam3(warn);
            warnVo.setParam4(error);
            warnVo.setParam5(warnPercent);
            warnVo.setParam6(errorPercent);
            warnVo.setStatus(status);
            if (vo != null) {
                warnMapper.updateWarnRule(warnVo);
            } else {
                warnMapper.addWarnRule(warnVo);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @OperateRecordAnnotation(name = "修改%s上的告警阈值，参数如下：%s", module = "hardware")
    public void updateAllIpWarning(String ips, Integer status, String warn, String error
            , String warnPercent, String errorPercent, String disk, WarnType warnType) {
        String[] split = ips.split(",");
        for (String ip : split) {
            updateWarning(ip, status, warn, error, warnPercent, errorPercent, disk, warnType);
        }
    }

    @Override
    public Object getWarning(String ip) {
        final WarnVo warnVo = new WarnVo();
        warnVo.setParam1(ip);
        List<WarnVo> warnList = warnMapper.getWarnList(warnVo);
        List<Map<String, Object>> result = new ArrayList<>();
        final HashMap<String, Object> main1 = new HashMap<>();
        final HashMap<String, Object> map1 = new HashMap<>();
        map1.put("warnSize", "1");
        map1.put("errorSize", "500");
        map1.put("status", 1);
        map1.put("default", false);
        map1.put("warnUnit", "T");
        map1.put("errorUnit", "G");
        map1.put("warnType", WarnType.HARDWARE_DISK_SPACE);

        final HashMap<String, Object> main2 = new HashMap<>();
        final HashMap<String, Object> map2 = new HashMap<>();
        map2.put("warnPercent", "80");
        map2.put("errorPercent", "90");
        map2.put("status", 1);
        map2.put("default", false);
        map2.put("warnType", WarnType.CPU_SPACE);

        final HashMap<String, Object> main3 = new HashMap<>();
        final HashMap<String, Object> map3 = new HashMap<>();
        map3.put("warnPercent", "80");
        map3.put("errorPercent", "90");
        map3.put("status", 1);
        map3.put("default", false);
        map3.put("warnType", WarnType.MEMORY_SPACE);

        if (warnList != null) {
            for (WarnVo vo : warnList) {
                if ("DEFAULT".equals(vo.getParam2()) || "CPU".equals(vo.getParam2()) || "内存".equals(vo.getParam2())) {
                    if (vo.getType().equals(WarnType.HARDWARE_DISK_SPACE)) {
                        map1.put("ip", vo.getParam1());
                        if (vo.getParam3() != null) {
                            map1.put("warnSize", vo.getParam3().substring(0, vo.getParam3().length() - 1));
                            map1.put("warnUnit", vo.getParam3().substring(vo.getParam3().length() - 1));
                        }
                        map1.put("warnPercent", vo.getParam5());
                        if (vo.getParam4() != null) {
                            map1.put("errorSize", vo.getParam4().substring(0, vo.getParam4().length() - 1));
                            map1.put("errorUnit", vo.getParam4().substring(vo.getParam4().length() - 1));
                        }
                        map1.put("errorPercent", vo.getParam6());
                        map1.put("status", vo.getStatus());
                        map1.put("warnType", vo.getType());
                    }
                    if (vo.getType().equals(WarnType.CPU_SPACE)) {
                        map2.put("ip", vo.getParam1());
                        map2.put("warnPercent", vo.getParam5());
                        map2.put("errorPercent", vo.getParam6());
                        map2.put("status", vo.getStatus());
                        map2.put("warnType", vo.getType());
                    }
                    if (vo.getType().equals(WarnType.MEMORY_SPACE)) {
                        map3.put("ip", vo.getParam1());
                        map3.put("warnPercent", vo.getParam5());
                        map3.put("errorPercent", vo.getParam6());
                        map3.put("status", vo.getStatus());
                        map3.put("warnType", vo.getType());
                    }
                } else {
                    final HashMap<String, Object> main = new HashMap<>();
                    final HashMap<String, Object> data = new HashMap<>();
                    data.put("ip", vo.getParam1());
                    if (vo.getParam3() != null) {
                        data.put("warnSize", vo.getParam3().substring(0, vo.getParam3().length() - 1));
                        data.put("warnUnit", vo.getParam3().substring(vo.getParam3().length() - 1));
                    }
                    data.put("warnPercent", vo.getParam5());
                    if (vo.getParam4() != null) {
                        data.put("errorSize", vo.getParam4().substring(0, vo.getParam4().length() - 1));
                        data.put("errorUnit", vo.getParam4().substring(vo.getParam4().length() - 1));
                    }
                    data.put("errorPercent", vo.getParam6());
                    data.put("status", vo.getStatus());
                    data.put("warnType", vo.getType());
                    main.put("data", data);
                    main.put("name", vo.getParam2());
                    result.add(main);
                }
            }
        }
        main1.put("data", map1);
        main1.put("name", "全部磁盘");
        result.add(main1);
        main2.put("data", map2);
        main2.put("name", "CPU");
        result.add(main2);
        main3.put("data", map3);
        main3.put("name", "内存");
        result.add(main3);

        return result;
    }

    @Override
    public PageInfo<OsOperateLogVo> getOsOperateLog(Integer pn, Integer ps, String ip) {
        final OsOperateLogVo osOperateLogVo = new OsOperateLogVo();
        osOperateLogVo.setIp(ip);
        PageHelper.startPage(pn, ps);
        return new PageInfo<>(hardwareMapper.getOsOperateLog(osOperateLogVo));
    }

    @Override
    public PageInfo<PingResult> exceptionNetworkInfo(Integer pn, Integer ps, String ip) {
        PingResultCriteria criteria = new PingResultCriteria();
        criteria.setIp(ip);
        if (pn != null && ps != null) {
            PageHelper.startPage(pn, ps);
        }
        return new PageInfo<>(pingResultMapper.list(criteria));
    }

    @Override
    public List<String> getDisks(String ip) {
        HardwareVo hardwareInfo = hardwareMapper.getHardwareInfo(ip);
        String dbDiskInfo = hardwareInfo.getDbDiskInfo();
        final LinkedList<Map<String, String>> linkedList = JsonUtil.parseObject(dbDiskInfo, new TypeReference<LinkedList<Map<String, String>>>() {
        });
        final LinkedList<String> strings = new LinkedList<>();
        for (Map<String, String> o : linkedList) {
            strings.add(o.get("name"));
        }
        return strings;
    }

    public static void main(String[] args) {
        System.out.println("100g".substring("100g".length() - 1));
    }

    @Override
    public void clearHistoryData(BaseCriteria criteria) {
        hardwareMapper.clearHistoryData(criteria);
    }


    private AlarmStatusType judgeNetwork(String serverIp) {
        PingResultCriteria criteria = new PingResultCriteria();
        criteria.setIp(serverIp);
        PageHelper.startPage(1, 1);
        long times = new PageInfo<>(pingResultMapper.list(criteria)).getTotal();
        if (times >= pingCountYellow && times < pingCountRed) {
            return AlarmStatusType.YELLOW;
        } else if (times >= pingCountRed) {
            return AlarmStatusType.RED;
        }
        return AlarmStatusType.GREEN;
    }
}