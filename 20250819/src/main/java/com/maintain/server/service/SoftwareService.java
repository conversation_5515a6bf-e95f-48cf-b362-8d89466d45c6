package com.maintain.server.service;

import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.BusinessMonitorCriteria;
import com.maintain.server.criteria.ModifyConfigCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.vo.*;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-11-07
 */
public interface SoftwareService extends ClearHistoryDataService {

    void addSoftwareInfo(SoftwareVo softwareVo) throws ServiceException;

    void pushSoftwareInfoToAgent(String ip) throws ServiceException;

    void updateSoftwareInfo(SoftwareVo softwareVo, Boolean push) throws ServiceException;

    void updateSoftware(SoftwareVo softwareVo, Boolean push);

    Map<String, Object> getHistoryResource(String startTime, String endTime, Integer softwareId, String software);

    void updateSoftwareMonitorInfo(SoftwareInfoVo softwareInfoVo) throws ServiceException;

    PageInfo<SoftwareVo> getSoftwareInfos(SoftwareCriteria criteria) throws ServiceException;

    SoftwareVo getSoftwareInfo(SoftwareCriteria criteria) throws ServiceException;

    SoftwareVo getSoftware(Integer id) throws ServiceException;

    List<SoftwareVo> listAllSoftwares(SoftwareCriteria softwareCriteria);

    boolean setSoftwareInfo(SoftwareVo softwareVo, boolean heartVali) throws ServiceException;

    void clearRestartCount(SoftwareVo softwareVo) throws ServiceException;

    void clearRestartCount(String host) throws ServiceException;

    void deleteSoftware(SoftwareVo softwareVo) throws ServiceException;

    List<SoftwareInfoVo> getSoftwareInfoList(String ip) throws ServiceException;

    /**
     * 获取日志文件的的文件流
     *
     * @param agentVo 获取session的信息
     * @param process 程序名称
     * @param log     日志名称
     * @return
     */
    InputStream getLogFile(AgentVo agentVo, String process, String log);

    /**
     * 获取某个机器下某个程序的日志列表
     *
     * @param agentVo    获取session的信息
     * @param softwareVo 程序名称
     * @return
     */
    PageInfo<Map<String, Object>> listAllLogs(AgentVo agentVo, SoftwareVo softwareVo, int pn, int ps);

    /**
     * 获取某个机器下某个程序的配置文件列表
     *
     * @param agentVo
     * @param process
     * @return
     */
    List<Map<String, Object>> listAllConfigs(AgentVo agentVo, SoftwareVo process);

    /**
     * 读取某个机器上摸个程序的某个文件
     *
     * @param agentVo
     * @param process
     * @return
     */
    String readFileContent(AgentVo agentVo, String process ,String path, String config, String isBackupFile, String version);

    /**
     * 读取本地公共配置文件
     *
     * @return
     */
    String readCommonConfigFileContent(String commonConfigPath) throws IOException;

    /**
     * 保存配置文件
     *
     * @param agentVo agent信息
     * @param process 程序名称
     * @param name    配置文件
     * @param content 配置文件内容，用base64编码
     * @return
     */
    boolean saveConfigFile(AgentVo agentVo, String process, String name, String content,String path, String backupFile, String version);

    /**
     * 列出所有的程序
     *
     * @return
     */
    Map<String, Map<String, Object>> listAllSoftwares(SoftwareCriteria softwareCriteria, boolean isExcludeSoftWare);

    /**
     * 将查询后的程序列表按照角色和状态进行聚合
     *
     * @param softwareCriteria
     * @param isExcludeSoftWare
     * @return
     */
    Map<String, Object> listAllSoftwaresAndStatus(SoftwareCriteria softwareCriteria, boolean isExcludeSoftWare);

    /**
     * 查询程序心跳时间在一段时间内的资源使用情况
     *
     * @param softwareCriteria
     * @return
     */
    List<SoftwareVo> listSoftwaresResource(SoftwareCriteria softwareCriteria);

    String iceRequest(String agentIp, int flag, String param) throws ServiceException;

    void addBusinessMonitor(BusinessMonitorVo businessMonitorVo);

    PageInfo<BusinessMonitorVo> getBusinessMonitor(BusinessMonitorCriteria criteria);

    /**
     * 校验文件格式
     *
     * @param content
     * @param suffix
     * @return
     */
    boolean verifyFileFormat(String content, String suffix);

    void addRestartHistory(List<SoftwareRestartVo> list);


    void setHeapSize(Integer id, String ip, String name, Integer minSize, Integer maxSize);

    Object getSoftwareFromRemoteServer(String ip, String directory);

    List<ModifyConfigVo> queryModifyConfig(ModifyConfigCriteria modifyConfigCriteria);

    int addModifyConfig(ModifyConfigCriteria modifyConfigCriteria);

    void deleteModifyConfig(ModifyConfigCriteria modifyConfigCriteria);

    int updateModifyConfig(ModifyConfigCriteria modifyConfigCriteria);

    void setUnzipPwd(String pwd);

    Boolean isContinueUpgrade();

    List<String> getIps(Integer targetPath);

    List<ExecuteResultVo> executeTask(String filePath, Integer os, String ips, String targetPath,String command);

    CheckResultVo checkPath(String targetPath,String ips);

    ExecuteResultVo tryAgain(String ip,String targetPath, String command);

    String getUnzipPwd();

}