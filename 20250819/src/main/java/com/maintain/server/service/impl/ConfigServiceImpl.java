package com.maintain.server.service.impl;

import cn.hutool.core.util.StrUtil;

import com.maintain.server.enums.ConfigModuleEnum;
import com.maintain.server.enums.ConfigTtypeEnum;
import com.maintain.server.mapper.ConfigMapper;
import com.maintain.server.service.ConfigService;
import com.maintain.server.vo.ConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/28
 */
@Service
public class ConfigServiceImpl implements ConfigService {
    @Autowired
    private ConfigMapper configMapper;

    @Override
    public ConfigVo getConfig(ConfigModuleEnum moduleEnum, ConfigTtypeEnum typeEnum, String key) {
        if (StrUtil.isBlank(key)) {
            return null;
        }
        return configMapper.getConfig(moduleEnum.getCode(), typeEnum.getCode(), key);
    }

    @Override
    public void save(ConfigModuleEnum moduleEnum, ConfigTtypeEnum typeEnum, String key, String value) {
          configMapper.save(moduleEnum.getCode(), typeEnum.getCode(), key, value);
    }

    @Override
    public void update(Integer id, String value) {
        configMapper.updateValue(id, value);
    }
}
