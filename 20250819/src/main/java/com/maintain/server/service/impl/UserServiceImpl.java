package com.maintain.server.service.impl;

import com.maintain.server.criteria.BaseCriteria;
import com.maintain.server.criteria.DynamicAndStaticCriteria;
import com.maintain.server.criteria.UserCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.PermissionMapper;
import com.maintain.server.mapper.UserMapper;
import com.maintain.server.service.UserService;
import com.maintain.server.vo.DynamicAndStaVoticThresholdVo;
import com.maintain.server.vo.PermissionVo;
import com.maintain.server.vo.RoleVo;
import com.maintain.server.vo.UserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-10-24
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public int registry(UserVo user) {
        //此用户已存在
        if (userMapper.userExistByName(user.getName()) == 1) {
            return -1;
        }
        encryptPassword(user);
        int result = userMapper.insertUser(user);
        if (result > 0) {
            //写入角色信息
            if (user.getRoles() != null && !user.getRoles().isEmpty()) {
                result = userMapper.insertRolesOfUser(user);
            }
        }
        return result;
    }


    @Override
    public boolean cancel(UserVo user) {
        int result = userMapper.deleteUser(user.getId());
        if (result == 1) {
            result = userMapper.deleteRolesOfUser(user.getId());
        }
        return result > 0;
    }

    @Override
    public boolean initPassword(UserVo user) {
        encryptPassword(user);
        return userMapper.updateUser(user) == 1;
    }

    @Override
    public List<UserVo> listUsers(BaseCriteria criteria) {
        return userMapper.listUsers();
    }

    @Override
    public List<RoleVo> listRoles(BaseCriteria criteria) {
        return userMapper.listRoles();
    }

    @Override
    public List<PermissionVo> listPermissions(BaseCriteria criteria) {
        List<PermissionVo> rootPermissions = userMapper.getRootPermission();
        rootPermissions.forEach(this::appendChildPermission);
        return rootPermissions;
    }

    /**
     * 递归追加子权限
     *
     * @param parent
     */
    private void appendChildPermission(PermissionVo parent) {
        List<PermissionVo> permissionVos = userMapper.getChildPermission(parent.getId());
        if (permissionVos == null || permissionVos.isEmpty()) {
            return;
        }
        parent.setChildren(permissionVos);
        permissionVos.forEach(this::appendChildPermission);
    }

    @Override
    public List<RoleVo> findRolesByIds(List<Integer> roleIds) {
        if (roleIds.isEmpty()) {
            return new ArrayList<>();
        }
        return userMapper.findRoleByIds(roleIds);
    }

    @Override
    public boolean updateRolesOfUser(int userId, List<Integer> roleIds) {
        UserVo user = userMapper.findUserById(userId);
        if (user == null) {
            throw new RuntimeException("指定用户不存在");
        }
        if ("admin".equals(user.getName())) {
            throw new RuntimeException("不能为管理员赋予新的角色");
        }
        userMapper.deleteRolesOfUser(userId);
        List<RoleVo> roles = findRolesByIds(roleIds);
        user.setRoles(roles);
        int result = 0;
        if (roles != null && !roles.isEmpty()) {
            result = userMapper.insertRolesOfUser(user);
        }

        return result > 0;
    }

    @Override
    public boolean updatePermissionOfRole(int roleId, List<Integer> permissionIds) {
        List<Integer> roleIdList = new ArrayList<>();
        roleIdList.add(roleId);
        List<RoleVo> roles = userMapper.findRoleByIds(roleIdList);
        if (roles.isEmpty()) {
            throw new ServiceException("指定角色不存在");
        }
        if ("ROLE_ADMIN".equals(roles.get(0).getName())) {
            throw new ServiceException("不能为管理员角色赋予权限");
        }

        List<PermissionVo> permissions = permissionMapper.findByIds(permissionIds);

        /**
         * 用户管理模块相关权限只能由admin角色拥有
         */
        boolean r = permissions.stream().anyMatch(p -> p.getId() == 1 || p.getParentId() == 1);
        if (r) {
            throw new ServiceException("不能给其它角色赋予用户管理模块的权限");
        }

        userMapper.deletePermissionOfRole(roleId);
        RoleVo role = new RoleVo();
        role.setId(roleId);

        role.setPermissions(permissions);
        if (permissions != null && !permissions.isEmpty()) {
            return userMapper.insertPermissionOfRole(role) > 0;
        }
        return true;
    }

    @Override
    public List<PermissionVo> loadPermissionByUsername(String username) {
        return userMapper.getAllPermissionByUsername(username);
    }

    @Override
    public List<PermissionVo> loadPermissionByUserId(int userId) {
        return userMapper.getAllPermissionByUserId(userId);
    }

    @Override
    public PermissionVo findPermissionById(int id) {
        PermissionVo permissionVo = permissionMapper.findById(id);
        if (permissionVo != null) {
            permissionVo.setChildren(findChildPermissions(permissionVo.getId()));
        }
        return permissionVo;
    }

    @Override
    public PermissionVo findPermissionByName(String name) {
        PermissionVo permissionVo = permissionMapper.findByName(name);
        if (permissionVo != null) {
            permissionVo.setChildren(findChildPermissions(permissionVo.getId()));
        }
        return permissionVo;
    }

    @Override
    public List<PermissionVo> findChildPermissions(int parentId) {
        return permissionMapper.findByParentId(parentId);
    }

    @Override
    public UserVo getUserByCriteria(UserCriteria criteria) {
        return userMapper.findByUsername(criteria);
    }

    @Override
    public boolean addDynamicAndStatic(DynamicAndStaVoticThresholdVo dynamicAndStaVoticThresholdVo) {
        if(userMapper.addDynamicAndStatic(dynamicAndStaVoticThresholdVo) > 0){
            return true;
        }
        return false;
    }

    @Override
    public boolean updateDynamicAndStatic(DynamicAndStaVoticThresholdVo dynamicAndStaVoticThresholdVo) {
        if(userMapper.updateDynamicAndStatic(dynamicAndStaVoticThresholdVo) > 0){
            return true;
        }
        return false;
    }

    @Override
    public List<DynamicAndStaVoticThresholdVo> findThreshold(DynamicAndStaticCriteria dynamicAndStaticCriteria) {
        return userMapper.findThreshold(dynamicAndStaticCriteria);
    }

    @Override
    public boolean confirmUserAndPass(String user, String pass) {
        UserCriteria userCriteria = new UserCriteria();
        userCriteria.setName(user);
        userCriteria.setPass(encryptPassword(pass));
        UserVo userVo = userMapper.getByName(userCriteria);
        if (userVo == null) {
            return false;
        }
        return new BCryptPasswordEncoder().matches(pass, userVo.getPswd());
    }

    /**
     * 对密码字符串进行加密
     *
     * @param user
     */
    private void encryptPassword(UserVo user) {
        user.setPswd(encryptPassword(user.getPswd()));
    }

    private String encryptPassword(String pass) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        return encoder.encode(pass);
    }

    /**
     * //TODO 无必要
     */
    private void reloadSecurityInfo() {
        //重新加载用户权限信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        auth.getAuthorities().clear();
        UserCriteria criteria = new UserCriteria();
        criteria.setName(auth.getName());
        UserVo user = userMapper.findByUsername(criteria);
        List<PermissionVo> permissionVos = new ArrayList<>();
        for (RoleVo role : user.getRoles()) {
            permissionVos.addAll(permissionMapper.findByRoleId(role.getId()));
        }

        List<GrantedAuthority> grantedAuthorities = permissionVos.stream()
                .filter(permission -> permission != null && permission.getRoleName() != null)
                .map(PermissionVo::getRoleName).map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
        UsernamePasswordAuthenticationToken token =
                new UsernamePasswordAuthenticationToken(auth.getPrincipal(),
                        auth.getCredentials(), grantedAuthorities);

    }



}