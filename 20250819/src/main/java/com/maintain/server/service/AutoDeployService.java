package com.maintain.server.service;

import com.maintain.server.criteria.AutoDeployTaskCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.vo.*;
import org.java_websocket.WebSocket;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * Created by Azurio on 2018/10/8.
 */
public interface AutoDeployService {

    Map<String, List<AutoDeployTaskVo>> getPrepareVo();

    Map<String, List<String>> getNeedManulHandleExceptionInfo();

    DeployRecordVo getDeployRecordVo(DeployRecordVo deployRecordVo);

    DeployRecordVo getRecordVo();

    Map<String, Map<String, Map<String, RoleProgramVo>>> getDeployTaskMap();

    Map<String, List<AutoDeployTaskVo>> prepareDeploy(Map<String, Map<String, Map<String, RoleProgramVo>>>  task) throws ServiceException;

    boolean addDeployTask(List<AutoDeployTaskVo> prepareVo) throws ServiceException;

    List<AutoDeployTaskVo> getAutoDeployTasks(AutoDeployTaskCriteria criteria) throws ServiceException;

    Boolean autoDeploy(AgentVo agentVo, Map<String, Object> map) throws ServiceException;

    void lockTask(AutoDeployTaskVo taskVo);

    void lockDeployTask(DeployRecordVo deployRecordVo);

    void addDeployRecord(DeployRecordVo deployRecordVo);

    void setWebSocket(WebSocket webSocket);

    File checkPackage(File file);

    Boolean checkUpgradePackage();

    Boolean unzipPackage();

    UnzipResult checkUnzipPackageFormat() throws Exception;

    CompareResultVo compareDeployTask();

    Map<String,Object> deployTask(Map<String,Object> deploy);

    CommonConfigResultVo getCommonConfig();

    void recordIsModifyCommonConfig(Integer isModifyCommonConfig);

    DeployRecordVo getIsModifyCommonConfig();

}
