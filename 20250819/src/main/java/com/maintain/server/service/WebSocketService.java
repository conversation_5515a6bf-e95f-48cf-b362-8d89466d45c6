package com.maintain.server.service;

import com.maintain.server.exception.ServiceException;
import org.java_websocket.WebSocket;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/1/25.
 */
public interface WebSocketService {

    String openSoftware(String param, WebSocket webSocket) throws ServiceException;

    String closeSoftware(String param, WebSocket webSocket) throws ServiceException;

    String reloadSoftware(String param, WebSocket webSocket) throws ServiceException;

    void updateSoftware(String param,WebSocket webSocket)throws ServiceException;

    Boolean autoDeploy(String param, WebSocket webSocket) throws ServiceException;

    void setSyschroTime(String param, WebSocket webSocket) throws ServiceException;

    boolean tryLock()throws ServiceException;

    boolean unLock() throws ServiceException;

    boolean executeUpgrade(String param, WebSocket conn);

    default String operateSoftware(Map<String, Object> map, WebSocket webSocket) throws ServiceException {
        return "";
    }
}
