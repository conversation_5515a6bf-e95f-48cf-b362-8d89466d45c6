package com.maintain.server.service.impl;

import com.common.log.Log;
import com.maintain.server.Constants;
import com.maintain.server.business.DbBackupInLinux;
import com.maintain.server.business.DbBackupInWindows;
import com.maintain.server.config.DbBackupConfig;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.DbBackupManageCriteria;
import com.maintain.server.criteria.MysqlBackupCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.mapper.DbBackupManageMapper;
import com.maintain.server.schedule.MysqlBackupMonitorSchedule;
import com.maintain.server.service.DbBackupManageService;
import com.maintain.server.service.MysqlBackupService;
import com.maintain.server.type.OsType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.DbBackupManageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-22
 */
@Service
@Slf4j
public class DbBackupManageServiceImpl implements DbBackupManageService {
    @Autowired
    private DbBackupManageMapper dbBackupManageMapper;
    @Autowired
    private AgentMapper agentMapper;
    @Autowired
    private MysqlBackupService mysqlBackupService;
    @Autowired
    private MysqlBackupMonitorSchedule mysqlBackupMonitorSchedule;
    @Autowired
    private DbBackupConfig backupConfig;

    @Value("${server.localHost}")
    private String maintainServerIp;

    @Override
    public List<DbBackupManageVo> list(DbBackupManageCriteria criteria) {
        return dbBackupManageMapper.selectList(criteria);
    }

    @Override
    public boolean editRecord(DbBackupManageVo vo) {
        DbBackupManageVo updatedVo = dbBackupManageMapper.selectByPrimaryKey(vo.getId());
        if (updatedVo == null) {
            throw new ServiceException("传入的ID对应的记录不存在");
        }
        if (!vo.getLocalIp().equals(updatedVo.getLocalIp()) || !vo.getRemoteIp().equals(updatedVo.getRemoteIp())) {
            setBackupPath(vo);

        }
        return dbBackupManageMapper.updateByPrimaryKeySelective(vo) != 0;
    }

    @Override
    public boolean deleteRecord(Integer id) {
        if (dbBackupManageMapper.selectByPrimaryKey(id) == null) {
            throw new ServiceException("传入的ID对应的记录不存在");
        }
        return dbBackupManageMapper.deleteByPrimaryKey(id) == 1;
    }

    @Override
    public DbBackupManageVo addRecord(DbBackupManageVo vo) {
        setBackupPath(vo);

        int count = dbBackupManageMapper.insertSelective(vo);
        if (count != 1) {
            throw new ServiceException("新增记录失败!");
        }
        return vo;
    }

    @Override
    public boolean dbBackup(int backupType) {
        String osName = ProcessUtil.getOsName();
        Integer localOsId = OsType.getId(osName);
        List<DbBackupManageVo> voList = dbBackupManageMapper.selectList(null);
        if (voList == null || voList.isEmpty()) {
            Log.low.info("没有配置需要备份的数据库");
            return true;
        }
        List<AgentVo> agentVos = agentMapper.getAgent(new AgentCriteria());
        Log.low.info("需要备份的数据库有:" + voList.stream().map(DbBackupManageVo::getLocalIp).collect(Collectors.joining(",")));
        if (OsType.WINDOWS.getId().equals(localOsId)) {
            new DbBackupInWindows(voList, agentVos, backupType, mysqlBackupService, backupConfig).backup(maintainServerIp);
        } else {
            new DbBackupInLinux(voList, agentVos, backupType, mysqlBackupService, backupConfig).backup(maintainServerIp);
        }
        return true;
    }

    @Override
    public boolean reBackup(int backupType) {
        //1. 删除已有的备份失败的记录
        MysqlBackupCriteria criteria = new MysqlBackupCriteria();
        criteria.setBackupType(backupType);
        criteria.setBackupTime(DateUtil.currentDay());
        mysqlBackupService.clearRecord(criteria);

        //2. 重新备份
        boolean flag = dbBackup(backupType);

        //3. 重新检测备份结果
        if (backupType == 0) {
            mysqlBackupMonitorSchedule.monitorMysqlDailyBackup();
        } else {
            mysqlBackupMonitorSchedule.monitorMysqlWeeklyBackup();
        }
        return false;
    }

    @Override
    public boolean testConnect(DbBackupManageVo vo) {
        String driver = "com.mysql.jdbc.Driver";
        String url = "jdbc:mysql://" + vo.getLocalIp() +":3306/mysql?connectTimeout=1000";
        Connection conn = null;
        try {
            Class.forName(driver);
            conn = DriverManager.getConnection(url, vo.getDbUser(), vo.getDbPass());
        } catch (Exception e) {
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {

                }
            }
        }
        return true;
    }

    private String chooseBackupRootPath(Integer osType) {
        if (OsType.WINDOWS.getId().equals(osType)) {
            return Constants.DB_BACKUP_PATH_IN_WINDOWS;
        }
        return Constants.DB_BACKUP_PATH_IN_LINUX;
    }


    /**
     * 设置备份路径
     * @param vo
     */
    private void setBackupPath(DbBackupManageVo vo) {
        AgentCriteria criteria = new AgentCriteria();
        criteria.setIps(Arrays.asList(vo.getLocalIp(), vo.getRemoteIp()));
        List<AgentVo> agentVos = agentMapper.getAgent(criteria);
        agentVos.stream().filter(a -> a.getIp().equals(vo.getLocalIp()))
                .findAny().ifPresent(a -> vo.setLocalPath(chooseBackupRootPath(a.getOs())));

        agentVos.stream().filter(a -> a.getIp().equals(vo.getRemoteIp()))
                .findAny().ifPresent(a -> vo.setRemotePath(chooseBackupRootPath(a.getOs())));

        if (StringUtils.isEmpty(vo.getLocalPath()) || StringUtils.isEmpty(vo.getRemotePath())) {
            throw new ServiceException("存在无效的AgentIP");
        }
    }
}