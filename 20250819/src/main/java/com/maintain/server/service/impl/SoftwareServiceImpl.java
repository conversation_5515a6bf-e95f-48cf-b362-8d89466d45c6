package com.maintain.server.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.common.log.Log;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.maintain.server.Constants;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.*;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.mapper.*;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.SoftwareOperateType;
import com.maintain.server.utils.*;
import com.maintain.server.vo.*;
import com.maintain.server.websocket.BaseWebSocket;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.DocumentException;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-11-07
 */
@Service
public class SoftwareServiceImpl extends BaseWebSocket implements SoftwareService, DisposableBean, Constants {


    @Autowired
    private SoftwareMapper softwareMapper;

    @Autowired
    private AutoDeployMapper autoDeployMapper;

    @Autowired
    private ModifyConfigMapper modifyConfigMapper;

    @Autowired
    private SoftwareHistoryMapper softwareHistoryMapper;

    @Autowired
    private AgentMapper agentMapper;

    @Value("${server.localHost}")
    private String maintainServerIp;

    @Value("${validateHeart}")
    private boolean valisoftware;

    @Value("${logstash-exclude-software}")
    private String excludeSoftware;

    private String unzipPwd;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperateRecordAnnotation(name = "在%s上添加%s程序", module = "software")
    public void addSoftwareInfo(SoftwareVo softwareVo) throws ServiceException {
        try {
            softwareMapper.addSoftwareInfo(softwareVo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void pushSoftwareInfoToAgent(String ip) throws ServiceException {
        final String result = this.iceRequest(ip, IceFlag.UPDATE_SOFTWARE_INFO, JsonUtil.toJsonString(getSoftwareInfoList(ip)));
        if (Objects.isNull(result)) {
            throw new ServiceException("请求agent更新程序信息失败,请检查MaintainAgent运行状态");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateSoftwareInfo(SoftwareVo softwareVo, Boolean push) throws ServiceException {
        softwareMapper.updateSoftwareInfo(softwareVo);
        if (push) {
            String agentIp = softwareVo.getHost();
            String param = JsonUtil.toJsonString(getSoftwareInfoList(softwareVo.getHost()));
            final String result = this.iceRequest(agentIp, IceFlag.UPDATE_SOFTWARE_INFO, param);
            //Log.low.info("请求20043号接口-更新程序信息, 请求参数: {agentIp: " + agentIp + " ; param: " + param + "} 请求结果: {" + result + "}");
            if (Objects.isNull(result)) {
                throw new ServiceException("ice请求agent更新程序失败,请检查MaintainAgent运行状态");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @OperateRecordAnnotation(name = "%s%s上的%s程序%s%s", module = "software")
    public void updateSoftware(SoftwareVo softwareVo, Boolean push) {
        softwareMapper.updateSoftware(softwareVo);
        if (push) {
            final String result = this.iceRequest(softwareVo.getHost(), IceFlag.UPDATE_SOFTWARE_INFO, JsonUtil.toJsonString(getSoftwareInfoList(softwareVo.getServerIp())));
            if (Objects.isNull(result)) {
                throw new ServiceException("ice请求agent更新程序失败,请检查MaintainAgent运行状态");
            }
        }
    }

    @Override
    public Map<String, Object> getHistoryResource(String startTime, String endTime, Integer softwareId, String software) {
//        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, Object> data = new LinkedHashMap<>();
        try {
//            final Date start = simpleDateFormat.parse(startTime);
//            final Date end = simpleDateFormat.parse(endTime);
//            final long stime = start.getTime();
//            final long etime = end.getTime();
//            int recordTime = 10 * 60 * 1000;
            int redCount = new Random().nextInt(100);
//            int total = Long.valueOf((stime - etime)/recordTime).intValue();
            data.put("redCount", redCount);
            //data.put("total",total);
            //data.put("normalCount",total - redCount);
            data.put("name", software);
        } catch (Exception e) {
            Log.high.error(e);
        }
        return data;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateSoftwareMonitorInfo(SoftwareInfoVo softwareInfoVo) throws ServiceException {
        softwareMapper.updateSoftwareMonitorInfo(softwareInfoVo);
    }

    @Override
    public PageInfo<SoftwareVo> getSoftwareInfos(SoftwareCriteria criteria) throws ServiceException {
        try {
            if (criteria != null) {
                Integer pn = criteria.getPn();
                Integer ps = criteria.getPs();
                if (pn != null && ps != null) {
                    PageHelper.startPage(pn, ps);
                }
            }
            List<SoftwareVo> softwareVoList = softwareMapper.getSoftwareInfos(criteria);
            if (ListUtil.isNotEmpty(softwareVoList)) {
                return new PageInfo<>(softwareVoList);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return new PageInfo<>();
    }

    @Override
    public SoftwareVo getSoftwareInfo(SoftwareCriteria criteria) throws ServiceException {
        try {
            PageInfo<SoftwareVo> infos = this.getSoftwareInfos(criteria);
            if (infos != null && ListUtil.isNotEmpty(infos.getList())) {
                SoftwareVo vo = infos.getList().get(0);
                Long runningTime = null;
                // 最后一次重启时间
                final Date restartTime = vo.getRestartTime();
                //最后一次心跳时间
                final Date lastHeartbeatTime = vo.getLastHeartbeatTime();
                //守护状态
                final ProgramStatusType programStatus = vo.getProgramStatus();
                final Date createTime = vo.getCreateTime();
                final long currentTimeMillis = System.currentTimeMillis();
                if (ProgramStatusType.OPEN.equals(programStatus)) {
                    //开启了守护
                    //如果心跳时间为空，运行时间=当前时间-最后重启时间
                    //如果心跳时间不为空，但心跳时间比上次重启时间更早，运行时间 = 心跳时间 - 当前时间
                    //如果心跳时间不为空，且心跳时间在重启时间和当前时间之内，则运行时间 = 当前时间 - 最后重启时间
                    if (lastHeartbeatTime == null) {
                        if (restartTime != null) {
                            runningTime = currentTimeMillis - restartTime.getTime();
                        } else if (createTime != null) {
                            runningTime = currentTimeMillis - createTime.getTime();
                        }
                    } else {
                        if (restartTime != null) {
                            final long time1 = lastHeartbeatTime.getTime();
                            final long time = restartTime.getTime();
                            if (time1 > time) {
                                //正常
                                runningTime = currentTimeMillis - time;
                            } else {
                                runningTime = time1 - currentTimeMillis;
                            }
                        }
                    }
                } else {
                    //关闭了守护
                    if (lastHeartbeatTime != null) {
                        runningTime = lastHeartbeatTime.getTime() - currentTimeMillis;
                    } else if (restartTime != null) {
                        runningTime = restartTime.getTime() - currentTimeMillis;
                    } else if (createTime != null) {
                        runningTime = createTime.getTime() - currentTimeMillis;
                    }
                }
                vo.setRunningTime(runningTime);
                return vo;
            }
            return null;
        } catch (ServiceException e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public SoftwareVo getSoftware(Integer id) throws ServiceException {
        return softwareMapper.getSoftware(id);
    }

    @Override
    public List<SoftwareVo> listAllSoftwares(SoftwareCriteria softwareCriteria) {
        List<SoftwareVo> softwareVos = softwareMapper.listAllSoftwares(softwareCriteria);
        softwareVos.stream().forEach(e ->
                {
                    if (e.getBaseDir() != null) {
                        e.setDeployDir(e.getRealDir().toString().replace("/", "").replace("\\", "").replace("D:", "").replace(e.getName(), "").trim());
                    }
                }
        );
        return softwareVos;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean setSoftwareInfo(SoftwareVo softwareVo, boolean heartVali) {
        if (softwareVo == null) {
            return false;
        }
        SoftwareCriteria criteria = new SoftwareCriteria();
        String name = softwareVo.getName();
        Integer serverId = softwareVo.getServerId();
        criteria.setName(name);
        criteria.setServerId(serverId);

        if (this.getSoftwareInfo(criteria) == null) {
            if (heartVali && valisoftware) {
                return true;
            }
            if (heartVali) {
                softwareVo.setStatus(AlarmStatusType.GREEN);
            }
            softwareVo.setConfig(Boolean.FALSE);
            softwareMapper.addSoftwareInfo(softwareVo);
            this.pushSoftwareInfoToAgent(softwareVo.getHost());
        } else {
            softwareMapper.updateSoftwareInfo(softwareVo);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @OperateRecordAnnotation(name = "清空%s上%s程序的重启次数", module = "software")
    public void clearRestartCount(SoftwareVo softwareVo) throws ServiceException {
        if (softwareVo == null) {
            return;
        }
        String response = this.iceRequest(softwareVo.getHost(), IceFlag.CLEAR_RESTART_COUNT, softwareVo.getName());
        if (Objects.isNull(response)) {
            throw new ServiceException("请求agent清空重启次数失败");
        }
        if ("0".equals(response)) {
            this.iceRequest(softwareVo.getHost(), IceFlag.UPDATE_SOFTWARE_INFO, JsonUtil.toJsonString(getSoftwareInfoList(softwareVo.getHost())));
            response = this.iceRequest(softwareVo.getHost(), IceFlag.CLEAR_RESTART_COUNT, softwareVo.getName());
        }
        if ("1".equals(response)) {
            SoftwareInfoVo softwareInfoVo = new SoftwareInfoVo();
            softwareInfoVo.setId(softwareVo.getId());
            softwareInfoVo.setRestartCount(0);
            softwareMapper.updateSoftwareMonitorInfo(softwareInfoVo);
        } else {
            throw new ServiceException("清空程序重启次数失败");
        }

    }

    @Override
    @OperateRecordAnnotation(name = "清空%s上所有程序的重启次数", module = "software")
    public void clearRestartCount(String host) throws ServiceException {
        final List<SoftwareInfoVo> softwareInfoList = getSoftwareInfoList(host);
        for (SoftwareInfoVo softwareInfoVo : softwareInfoList) {
            softwareInfoVo.setRestartCount(0);
        }
        final String result = this.iceRequest(host, IceFlag.UPDATE_SOFTWARE_INFO, JsonUtil.toJsonString(softwareInfoList));
        if (Objects.isNull(result)) {
            throw new ServiceException("请求agent清空重启次数失败");
        }
        for (SoftwareInfoVo softwareInfoVo : softwareInfoList) {
            softwareMapper.updateSoftwareMonitorInfo(softwareInfoVo);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @OperateRecordAnnotation(name = "删除%s上的%s程序", module = "software")
    public void deleteSoftware(SoftwareVo softwareVo) throws ServiceException {
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setId(softwareVo.getId());
        SoftwareVo temp = getSoftwareInfo(criteria);
        if (temp != null) {
            softwareVo.setName(temp.getName());
        }
        softwareMapper.deleteSoftware(softwareVo);
        final String result = this.iceRequest(softwareVo.getServerIp(), IceFlag.UPDATE_SOFTWARE_INFO, JsonUtil.toJsonString(getSoftwareInfoList(softwareVo.getServerIp())));
        if (Objects.isNull(result)) {
            throw new ServiceException("ice请求agent删除程序失败,请检查MaintainAgent运行状态");
        }
    }

    @Override
    public List<SoftwareInfoVo> getSoftwareInfoList(String ip) throws ServiceException {
        return softwareMapper.getSoftwareInfoList(ip);
    }

    @Override
    @OperateRecordAnnotation(name = "下载%s上%s程序的日志文件:%s", module = "software")
    public InputStream getLogFile(AgentVo agentVo, String process, String log) {
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setName(process);
        criteria.setServerId(agentVo.getId());
        SoftwareVo softwareVo = getSoftwareInfo(criteria);
        if (softwareVo == null) {
            throw new ServiceException("找不到相关程序");
        }

        if (maintainServerIp.equals(agentVo.getIp())) {
            String logPath;
            if (process.equals("logstash")) {
                logPath = getLogsPath(agentVo.getOs(), softwareVo, log);
            } else {
                logPath = getLogPath(agentVo.getOs(), softwareVo, log);
            }
            try {
                return new FileInputStream(new File(logPath));
            } catch (FileNotFoundException e) {
                throw new ServiceException("日志文件不存在，请检查是否被删除!");
            }
        } else {
            return getRemoteLogFile(agentVo, softwareVo, log);
        }
    }


    @Override
    public PageInfo<Map<String, Object>> listAllLogs(AgentVo agentVo, SoftwareVo softwareVo, int pn, int ps) {
        String path = softwareVo.getRealDir();
        if (StringUtils.isEmpty(path)) {
            path = softwareVo.getBaseDir();
        }
        if (StringUtils.isEmpty(path)) {
            throw new ServiceException("程序部署路径为空，请检查common-project版本是否为最新版");
        }
        List<Map<String, Object>> result = null;
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("host", agentVo.getIp());
        paramMap.put("path", path);
        paramMap.put("name", softwareVo.getName());
        paramMap.put("logPath", softwareVo.getLogPath());
        paramMap.put("logSuffix", softwareVo.getLogSuffix());
        String param = JsonUtil.toJsonString(paramMap);
        String response = this.iceRequest(agentVo.getIp(), IceFlag.GET_HOST_PROC_LOGS, param);
        if (StringUtils.isEmpty(response)) {
            throw new ServiceException("获取" + path + "日志目录失败");
        }
        Map<String, Object> map = (Map<String, Object>) JSONObject.parse(response);
        if (map.get("data") instanceof List) {
            result = (List<Map<String, Object>>) map.get("data");
        }
        if (result == null) {
            throw new ServiceException("获取" + path + "日志目录失败");
        }
        int totalPage = (result.size() - 1) / ps + 1;
        int start = (pn - 1) * ps;
        int end = Integer.min(pn * ps, result.size());
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        List<Map<String, Object>> temp = result.subList(start, end);
        pageInfo.setList(temp);
        pageInfo.setPageSize(ps);
        pageInfo.setPageNum(pn);
        pageInfo.setSize(temp.size());
        pageInfo.setStartRow(start);
        pageInfo.setEndRow(end);
        pageInfo.setTotal(result.size());
        pageInfo.setPages(totalPage);
        return pageInfo;
    }

    @Override
    public List<Map<String, Object>> listAllConfigs(AgentVo agentVo, SoftwareVo softwareVo) {
        String path = softwareVo.getRealDir();
        if (StringUtils.isEmpty(path)) {
            path = softwareVo.getBaseDir();
        }
        if (StringUtils.isEmpty(path)) {
            throw new ServiceException("程序部署路径为空，请检查common-project版本是否为最新版");
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("host", agentVo.getIp());
        paramMap.put("path", path);
        paramMap.put("name", softwareVo.getName());
        paramMap.put("configPath", softwareVo.getConfigPath());
        String param = JsonUtil.toJsonString(paramMap);
        String response = this.iceRequest(agentVo.getIp(), IceFlag.GET_HOST_PROC_CONFIGS, param);
        if (Objects.isNull(response)) {
            throw new ServiceException("获取" + softwareVo.getName() + "配置文件目录失败");
        }
        List<Map<String, Object>> result = null;
        Map<String, Object> map = (Map<String, Object>) JSONObject.parse(response);
        if (map.get("data") instanceof List) {
            result = (List<Map<String, Object>>) map.get("data");
        }
        if (result == null) {
            throw new ServiceException("获取" + softwareVo.getName() + "配置文件目录失败");
        }
        return result;
    }

    @Override
    public String readFileContent(AgentVo agentVo, String process, String path, String config, String isBackupFile, String version) {
        //使用agent的接口完成读取文件
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setName(process);
        SoftwareVo softwareVo = getSoftwareInfo(criteria);
        if (softwareVo == null) {
            throw new ServiceException("程序已被删除");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("path", path);
        jsonObject.put("proc", process);
        jsonObject.put("file", config);
        jsonObject.put("backupFile", isBackupFile);
        jsonObject.put("version", version);
        jsonObject.put("configPath", softwareVo.getConfigPath());
        String params = jsonObject.toString();
        try {
            String result = this.iceRequest(agentVo.getIp(), IceFlag.READ_HOST_FILE, params);
            if (result == null) {
                return null;
            } else {
                Map<String, Object> map = (Map<String, Object>) JSONObject.parse(result);
                if ("0".equals(map.get("code").toString())) {
                    if (map.get("data") != null) {
                        return map.get("data").toString();
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            Log.low.info("Agent连接失败");
            return null;
        }
    }

    @Override
    public String readCommonConfigFileContent(String commonConfigPath) throws IOException {
        final String content = FileUtil.readFile(commonConfigPath);
        return content;
    }

    @Override
    @OperateRecordAnnotation(name = "修改%s上%s程序的配置文件:%s", module = "software")
    public boolean saveConfigFile(AgentVo agentVo, String process, String name, String content, String path, String backupFile, String version) {
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setName(process);
        SoftwareVo softwareVo = getSoftwareInfo(criteria);
        if (softwareVo == null) {
            throw new ServiceException("程序已被删除");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("path", path);
        jsonObject.put("proc", process);
        jsonObject.put("file", name);
        jsonObject.put("content", content);
        jsonObject.put("backupFile", backupFile);
        jsonObject.put("version", version);
        jsonObject.put("configPath", softwareVo.getConfigPath());
        String params = jsonObject.toString();
        String result = this.iceRequest(agentVo.getIp(), IceFlag.WRITE_HOST_FILE, params);
        return result.contains("操作成功");
    }

    @Override
    public Map<String, Map<String, Object>> listAllSoftwares(SoftwareCriteria criteria, boolean isExcludeSoftWare) {
        List<SoftwareVo> softwareInfoVos = this.getSoftwareInfos(criteria).getList();
        Map<String, Map<String, Object>> result = new HashMap<>(16);
        if (softwareInfoVos == null) {
            return result;
        }
        if (isExcludeSoftWare) {
            //排除掉一些程序
            Iterator<SoftwareVo> iter = softwareInfoVos.iterator();
            if (StringUtils.isNotBlank(excludeSoftware)) {
                String[] exSoftWareArr = excludeSoftware.split(",");
                List<String> exSoftWareList = new ArrayList<>(Arrays.asList(exSoftWareArr));
                while (iter.hasNext()) {
                    String name = iter.next().getName();
                    if (exSoftWareList.contains(name)) {
                        iter.remove();
                    }
                }
            }
        }

        Map<String, List<SoftwareVo>> map = softwareInfoVos.stream()
                .filter(s -> !StringUtils.isEmpty(s.getRole()))
                .collect(Collectors.groupingBy(SoftwareVo::getRole, ConcurrentHashMap::new, Collectors.toList()));

        //有些role使用逗号分隔，此处合并各个role上的所有程序
        List<String> needRemove = new ArrayList<>();
        Set<String> needExclude = new LinkedHashSet<>();
        for (Map.Entry<String, List<SoftwareVo>> entry : map.entrySet()) {
            String[] roles = entry.getKey().split(",");
            if (roles.length > 1) {
                for (String role : roles) {
                    softwareInfoVos.forEach(s -> {
                        s.setRole(role);
                        if (s.getStatus() == null) {
                            s.setStatus(AlarmStatusType.GREEN);
                        }
                        if (s.getOperateType() == null) {
                            s.setOperateType(SoftwareOperateType.CLOSE);
                        }
                    });
                    if (map.containsKey(role)) {
                        map.get(role).addAll(entry.getValue());
                    } else {
                        for (SoftwareVo softwareVo : entry.getValue()) {
                            if (!needExclude.contains(softwareVo.getName())) {
                                map.put(role, entry.getValue());
                            }
                        }
                    }
                    for (SoftwareVo softwareVo : entry.getValue()) {
                        needExclude.add(softwareVo.getName());
                    }
                }
                needRemove.add(entry.getKey());
            }
        }
        needRemove.forEach(map::remove);
        for (Map.Entry<String, List<SoftwareVo>> entry : map.entrySet()) {
            result.put(entry.getKey(), convertSoftwareInfoVos(entry.getValue(), entry.getKey()));
        }
        return result;
    }

    @Override
    public Map<String, Object> listAllSoftwaresAndStatus(SoftwareCriteria criteria, boolean isExcludeSoftWare) {
        AlarmStatusType statusType = AlarmStatusType.getType(criteria.getStatus());
        criteria.setStatus(null);
        List<SoftwareVo> softwareInfoVos = this.getSoftwareInfos(criteria).getList();
        Map<String, Object> result = new HashMap<>(16);
        if (softwareInfoVos == null) {
            return result;
        }
        if (isExcludeSoftWare) {
            //排除掉一些程序
            Iterator<SoftwareVo> iter = softwareInfoVos.iterator();
            if (StringUtils.isNotBlank(excludeSoftware)) {
                String[] exSoftWareArr = excludeSoftware.split(",");
                List<String> exSoftWareList = new ArrayList<>(Arrays.asList(exSoftWareArr));
                while (iter.hasNext()) {
                    String name = iter.next().getName();
                    if (exSoftWareList.contains(name)) {
                        iter.remove();
                    }
                }
            }
        }

        Map<String, Long> statusMap = softwareInfoVos.stream()
                .filter(s -> !StringUtils.isEmpty(s.getRole()))
                .collect(Collectors.groupingBy(s -> s.getStatus().getValue(), Collectors.counting()));

        Map<String, List<SoftwareVo>> map = softwareInfoVos.stream()
                .filter(s -> statusType == null || statusType.equals(s.getStatus()))
                .filter(s -> !StringUtils.isEmpty(s.getRole()))
                .collect(Collectors.groupingBy(SoftwareVo::getRole, ConcurrentHashMap::new, Collectors.toList()));

        //有些role使用逗号分隔，此处合并各个role上的所有程序
        List<String> needRemove = new ArrayList<>();
        Set<String> needExclude = new LinkedHashSet<>();
        for (Map.Entry<String, List<SoftwareVo>> entry : map.entrySet()) {
            String[] roles = entry.getKey().split(",");
            if (roles.length > 1) {
                for (String role : roles) {
                    softwareInfoVos.forEach(s -> {
                        s.setRole(role);
                        if (s.getStatus() == null) {
                            s.setStatus(AlarmStatusType.GREEN);
                        }
                        if (s.getOperateType() == null) {
                            s.setOperateType(SoftwareOperateType.CLOSE);
                        }
                    });
                    if (map.containsKey(role)) {
                        map.get(role).addAll(entry.getValue());
                    } else {
                        for (SoftwareVo softwareVo : entry.getValue()) {
                            if (!needExclude.contains(softwareVo.getName())) {
                                map.put(role, entry.getValue());
                            }
                        }
                    }
                    for (SoftwareVo softwareVo : entry.getValue()) {
                        needExclude.add(softwareVo.getName());
                    }
                }
                needRemove.add(entry.getKey());
            }
        }
        needRemove.forEach(map::remove);
        Map<String, Map<String, Object>> roleMap = new HashMap<>(map.size());
        for (Map.Entry<String, List<SoftwareVo>> entry : map.entrySet()) {
            roleMap.put(entry.getKey(), convertSoftwareInfoVos(entry.getValue(), entry.getKey()));
        }
        result.put("softwares", roleMap);
        result.put("statusMap", statusMap);
        return result;
    }

    /**
     * 把程序列表转换为WEB需要的格式
     *
     * @param softwareInfoVos
     * @param role
     * @return
     */
    private static Map<String, Object> convertSoftwareInfoVos(List<SoftwareVo> softwareInfoVos, String role) {
        if (softwareInfoVos == null || softwareInfoVos.isEmpty()) {
            return null;
        }
        Map<String, Object> map = new HashMap<>();
        //机器的状态，由程序列表中最严重的状态决定
        Optional<Integer> id = softwareInfoVos.stream().filter(vo -> vo.getStatus() != null).map(vo -> vo.getStatus().getId()).max(Integer::compare);
        AlarmStatusType totalStatus = AlarmStatusType.getType(id.orElse(0));
        map.put("totalStatus", totalStatus);
        map.put("dataList", softwareInfoVos);
        return map;
    }

    @Override
    public List<SoftwareVo> listSoftwaresResource(SoftwareCriteria softwareCriteria) {
        List<SoftwareVo> softwareInfoVos =
                softwareMapper.listSoftwaresResource(softwareCriteria);
        if (softwareInfoVos == null) {
            return null;
        }
        softwareInfoVos.forEach(s -> {
            if (s != null && s.getProgramSize() != null && StringUtils.isEmpty(s.getDiskPercent())) {
                Long size = s.getProgramSize();
                if (size == null) {
                    size = 0L;
                }
                String total = s.getTotalDisk();
                double totalSize = convertStorageSize(total);
                DecimalFormat df = new DecimalFormat("0.00");
                String diskPercent = df.format(size / totalSize);
                s.setDiskPercent(diskPercent + "%");
            }
        });
        return softwareInfoVos;
    }


    @Override
    public String iceRequest(String agentIp, int flag, String param) throws ServiceException {
        try {
            return ServerRequestUtil.sendRequest(agentIp, flag, param);
        } catch (Exception e) {
            String msg;
            if (StringUtils.isNotEmpty(e.getMessage())) {
                msg = e.getMessage() + " " + agentIp;
            } else {
                msg = agentIp;
            }
            Log.high.error(agentIp + msg + " Agent连接失败", e);
            return null;
        }
    }

    @Override
    @Transactional
    public void addBusinessMonitor(BusinessMonitorVo businessMonitorVo) {
        softwareMapper.addBusinessMonitor(businessMonitorVo);
    }

    @Override
    public PageInfo<BusinessMonitorVo> getBusinessMonitor(BusinessMonitorCriteria criteria) {
        if (criteria == null) {
            return null;
        }
        Integer pn = criteria.getPn();
        Integer ps = criteria.getPs();
        if (pn != null && ps != null) {
            PageHelper.startPage(pn, ps);
        }
        try {
            List<BusinessMonitorVo> list = softwareMapper.getBusinessMonitor(criteria);
            return new PageInfo<>(list);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void destroy() throws Exception {

    }

    /**
     * 根据程序端口、ERROR日志文件错误大小判断程序状态
     *
     * @param mapList mapList
     */
    private void judgeStatusByPortsAndErrorLog(List<Map<String, Object>> mapList, List<SoftwareVo> softwareInfoVos) {
        for (int i = 0; i < mapList.size(); i++) {
            Map<String, Object> map = mapList.get(i);
            int portCount = Integer.parseInt(map.get("portCount").toString());
            int errorFileSize = Integer.parseInt(map.get("errorFileSize").toString());
            if (portCount >= 100 || errorFileSize >= TEN_MEGABYTES) {
                softwareInfoVos.get(i).setStatus(AlarmStatusType.RED);
            }
        }
    }


    @Override
    public boolean verifyFileFormat(String content, String suffix) {
        byte[] bytes = Base64.getDecoder().decode(content);
        content = new String(bytes, Charset.forName(UTF_8)).trim();
        if (suffix.endsWith(JSON_SUFFIX)) {
            return verifyJson(content);
        } else if (suffix.endsWith(XML_SUFFIX)) {
            return verifyXml(content);
        }
        throw new ServiceException("不支持的校验格式");
    }

    @Override
    @Transactional
    public void addRestartHistory(List<SoftwareRestartVo> list) {
        if (list != null && !list.isEmpty()) {
            softwareHistoryMapper.addRestartHistory(list);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void setHeapSize(Integer id, String ip, String name, Integer minSize, Integer maxSize) {
        final HashMap<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("minSize", minSize);
        map.put("maxSize", maxSize);
        final SoftwareVo softwareVo = new SoftwareVo();
        softwareVo.setId(id);
        softwareVo.setMinHeapSize(minSize);
        softwareVo.setMaxHeapSize(maxSize);
        softwareMapper.updateSoftware(softwareVo);
        final String result = iceRequest(ip, IceFlag.SET_HEAP_SIZE, JsonUtil.toJsonString(map));
        if (result == null) {
            throw new ServiceException("请求agent修改堆大小失败");
        }
        final HashMap hashMap = JsonUtil.parseObject(result, HashMap.class);
        final Object code = hashMap.get("code");
        if (code != null && !"0".equals(code.toString())) {
            String msg = hashMap.get("msg") == null ? "agent修改堆大小失败" : hashMap.get("msg").toString();
            throw new ServiceException(msg);
        }
    }

    @Override
    public Object getSoftwareFromRemoteServer(String ip, String directory) {
        final String result = iceRequest(ip, IceFlag.GET_SOFTWARE_LIST, directory);
        if (result == null) {
            throw new ServiceException("请求获取远程服务器程序目录失败");
        }
        final HashMap hashMap = JsonUtil.parseObject(result, HashMap.class);
        final Integer code = (Integer) hashMap.get("code");
        if (Integer.valueOf(500).equals(code)) {
            throw new ServiceException(Optional.ofNullable(hashMap.get("msg")).orElse("远程服务器发生错误").toString());
        }
        final LinkedList<String> data = JsonUtil.parseObject(hashMap.get("data").toString(), LinkedList.class);
        final List<SoftwareInfoVo> softwareInfoList = softwareMapper.getSoftwareInfoList(ip);
        final LinkedList<String> resultList = new LinkedList<>();
        out:
        for (String datum : data) {
            if (datum.equals("MAINTAIN_TEMP") || datum.equals("MaintainAgent") || datum.equals("MaintainServer")) {
                continue;
            }
            for (SoftwareInfoVo softwareInfoVo : softwareInfoList) {
                if (softwareInfoVo.getName().equals(datum)) {
                    continue out;
                }
            }
            resultList.add(datum);
        }
        return resultList;

    }

    /**
     * 校验json文件格式
     *
     * @param content
     * @return
     */
    private boolean verifyJson(String content) {
        try {
            if (content.startsWith("[")) {
                JsonUtil.parseArray(content, Map.class);
            } else if (content.startsWith("{")) {
                JsonUtil.parseObject(content, Map.class);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 校验XML文件格式
     *
     * @param content
     * @return
     */
    public boolean verifyXml(String content) {
        SAXReader reader = new SAXReader();
        StringReader stringReader = new StringReader(content);
        try {
            reader.read(stringReader);
        } catch (DocumentException e) {
            return false;
        }
        return true;
    }


    /**
     * 把带单位的存储大小转换为字节数量
     *
     * @param storage storage
     * @return double
     */
    private double convertStorageSize(String storage) {
        String unit = storage.substring(storage.length() - 1, storage.length());
        double size;
        if (isDigit(unit)) {
            size = Double.parseDouble(storage);
        } else {
            size = Double.parseDouble(storage.substring(0, storage.length() - 1));
        }
        if ("G".equalsIgnoreCase(unit)) {
            return size * 1024 * 1024 * 1024;
        } else if ("M".equalsIgnoreCase(unit)) {
            return size * 1024 * 1024 * 1024;
        } else if ("KB".equalsIgnoreCase(unit)) {
            return size * 1024;
        }
        return size;
    }

    /**
     * 是否是一个数值字符
     *
     * @param c c
     * @return boolean
     */
    private boolean isDigit(char c) {
        if (c >= '0' && c <= '9') {
            return true;
        }
        return false;
    }

    /**
     * 判断一个字符串是否是一个数值
     *
     * @param s s
     * @return boolean
     */
    private boolean isDigit(String s) {
        for (char c : s.toCharArray()) {
            if (!isDigit(c) || c != '.') {
                return false;
            }
        }
        return true;
    }


    private InputStream getRemoteLogFile(AgentVo agentVo, SoftwareVo softwareVo, String log) {
        Session session = null;
        ChannelSftp sftp = null;
        InputStream is = null;
        try {
            session = getSession(agentVo);
            sftp = ProcessUtil.openChannelSftp(session);
            String logPath;
            if (LINUX.equals(agentVo.getOs())) {
                if ("logstash".equals(softwareVo.getName())) {
                    logPath = getLogsPath(agentVo.getOs(), softwareVo, log);
                } else {
                    logPath = getLogPath(agentVo.getOs(), softwareVo, log);
                }
                logPath = logPath.replace(log, "");
                sftp.cd(logPath);
                is = sftp.get(log);
            } else {
                if ("logstash".equals(softwareVo.getName())) {
                    logPath = getLogsPath(agentVo.getOs(), softwareVo, log);
                } else {
                    logPath = getLogPath(agentVo.getOs(), softwareVo, log);
                }
                String tempPath = MAINTAIN_TEMP + "\\" + softwareVo.getName();
                StringBuilder command = new StringBuilder();
                command.append("md ").append(tempPath).append(" & ")
                        .append("xcopy ").append(logPath).append(" ").append(tempPath)
                        .append(" /Y");
                String batName = ProcessUtil.uploadCmdBatFileToRemoteWindows(sftp, command.toString());
                ProcessUtil.exeCommand(session, "cmd /c " + batName);
                sftp.rm(batName);
                sftp.cd(tempPath.replace("\\", "/"));
                is = sftp.get(log);
            }
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buff = new byte[8096];
            int length;
            while ((length = is.read(buff)) != -1) {
                baos.write(buff, 0, length);
            }
            sftp.cd("..");
            // 删除临时文件,因为LINUX上是直接读取的日志文件，故不需要进行删除
            if (!LINUX.equals(agentVo.getOs())) {
                ProcessUtil.rmDir(sftp, softwareVo.getName());
            }
            return new ByteArrayInputStream(baos.toByteArray());
        } catch (JSchException e) {
            throw new ServiceException("获取远程Session出错，请检查ssh连接是否正常");
        } catch (SftpException e) {
            Log.high.error("进入日志路径失败", e);
            throw new ServiceException("日志文件不存在，请检查是否被删除!");
        } catch (Exception e) {
            Log.high.error("interrupt", e);
            throw new ServiceException("下载日志失败，未知情况");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    Log.high.error("关闭流出错", e);
                }
            }
            if (sftp != null) {

                sftp.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }

    /**
     * 获取日志文件路径
     *
     * @param osId       操作系统标识
     * @param softwareVo 程序名称
     * @return
     */
    private String getLogPath(Integer osId, SoftwareVo softwareVo) {
        String logPath = "";
        String process = softwareVo.getName();
        if (StringUtils.isEmpty(softwareVo.getLogPath())) {
            if (LINUX.equals(osId)) {
                logPath = MessageFormat.format(LINUX_LOG_PATH, process);
            } else if (WINDOWS.equals(osId)) {
                logPath = MessageFormat.format(WINDOWS_LOG_PATH, process);
            }
        } else {
            if (softwareVo.getLogPath().startsWith("/")) {
                logPath = softwareVo.getLogPath();
            } else {
                logPath = softwareVo.getBaseDir() + File.separator + softwareVo.getLogPath();
            }
        }
        if ("".equals(logPath)) {
            throw new ServiceException("操作系统解析失败" + osId);
        }
        return logPath;
    }

    private String getLogsPath(Integer osId, SoftwareVo softwareVo) {
        String logPath = "";
        String process = softwareVo.getName();
        if (StringUtils.isEmpty(softwareVo.getLogPath())) {
            if (LINUX.equals(osId)) {
                logPath = MessageFormat.format(LINUX_LOGS_PATH, process);
            } else if (WINDOWS.equals(osId)) {
                logPath = MessageFormat.format(WINDOWS_LOGS_PATH, process);
            }
        } else {
            if (softwareVo.getLogPath().startsWith("/")) {
                logPath = softwareVo.getLogPath();
            } else {
                logPath = softwareVo.getBaseDir() + File.separator + softwareVo.getLogPath();
            }
        }
        if ("".equals(logPath)) {
            throw new ServiceException("操作系统解析失败" + osId);
        }
        return logPath;
    }


    /**
     * 获取日志文件路径
     *
     * @param osId       操作系统标识
     * @param softwareVo 程序名称
     * @return return
     */
    private String getLogPath(Integer osId, SoftwareVo softwareVo, String log) {
        String logPath = getLogPath(osId, softwareVo);
        if (LINUX.equals(osId)) {
            return logPath + "/" + log;
        } else {
            return logPath + "\\" + log;
        }
    }

    private String getLogsPath(Integer osId, SoftwareVo softwareVo, String log) {
        String logPath = getLogsPath(osId, softwareVo);
        if (LINUX.equals(osId)) {
            return logPath + "/" + log;
        } else {
            return logPath + "\\" + log;
        }
    }

    @Override
    @Transactional
    public void clearHistoryData(BaseCriteria criteria) {
        softwareMapper.clearHistoryData(criteria);
    }


    @Override
    public List<ModifyConfigVo> queryModifyConfig(ModifyConfigCriteria modifyConfigCriteria) {
        return modifyConfigMapper.queryModifyConfig(modifyConfigCriteria);
    }

    @Override
    @Transactional
    public int addModifyConfig(ModifyConfigCriteria modifyConfigCriteria) {
        if (!modifyConfigCriteria.getConfigPath().endsWith(".json") || !modifyConfigCriteria.getConfigPath().endsWith(".xml")) {
            new ServiceException("仅支持json和xml配置文件!");
        }
        return modifyConfigMapper.addModifyConfig(modifyConfigCriteria);
    }

    @Override
    @Transactional
    public void deleteModifyConfig(ModifyConfigCriteria modifyConfigCriteria) {
        modifyConfigMapper.deleteModifyConfig(modifyConfigCriteria);
    }

    @Override
    @Transactional
    public int updateModifyConfig(ModifyConfigCriteria modifyConfigCriteria) {
        if (!modifyConfigCriteria.getConfigPath().endsWith(".json") || !modifyConfigCriteria.getConfigPath().endsWith(".xml")) {
            new ServiceException("仅支持json和xml配置文件!");
        }
        return modifyConfigMapper.updateModifyConfig(modifyConfigCriteria);
    }

    @Override
    public void setUnzipPwd(String pwd) {
        unzipPwd = pwd;
    }

    @Override
    public Boolean isContinueUpgrade() {
        DeployRecordVo deployRecordVo = autoDeployMapper.getDeployRecordInfo(new DeployRecordVo());
        if (deployRecordVo == null || deployRecordVo.getStatus() == null || deployRecordVo.getStatus() == 5 || deployRecordVo.getStatus() == 0) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public List<String> getIps(Integer os) {
        List<AgentVo> agents = agentMapper.getAgent(new AgentCriteria());
        if (ListUtil.isEmpty(agents)) {
            throw new ServiceException("暂未添加agent");
        }
        return agents.stream().filter(f -> f.getOs() == os).map(AgentVo::getIp).collect(Collectors.toList());
    }

    @Override
    public List<ExecuteResultVo> executeTask(String filePath, Integer os, String ips, String targetPath, String command) {
        List<ExecuteResultVo> executeResultVoList = new LinkedList<>();
        String[] split = ips.split(";");
        for (String ip : split) {
            //上传文件
            AgentCriteria agentCriteria = new AgentCriteria();
            agentCriteria.setIp(ip);
            List<AgentVo> agent = agentMapper.getAgent(agentCriteria);
            if (agent == null) {
                throw new ServiceException("请先添加Agent!");
            }
            AgentVo agentVo = agent.get(0);
            if (maintainServerIp.equals(ip)) {
                if (!filePath.contains(targetPath)) {
                    try {
                        File orignalFile = new File(filePath);
                        String targetFilePath;
                        if (targetPath.endsWith("/") || targetPath.endsWith("\\")) {
                            targetFilePath = targetPath + orignalFile.getName();
                        } else {
                            targetFilePath = targetPath + File.separator + orignalFile.getName();
                        }
                        File targetFile = new File(targetFilePath);
                        if (orignalFile.isFile() && orignalFile.exists()) {
                            if (targetFile.isFile() && targetFile.exists()) {
                                targetFile.delete();
                            }
                            FileUtils.copyFile(orignalFile, targetFile);
                        }
                        if (orignalFile.isDirectory() && orignalFile.exists()) {
                            if (targetFile.isDirectory() && targetFile.exists()) {
                                FileUtils.deleteDirectory(targetFile);
                            }
                            FileUtils.copyDirectory(orignalFile, targetFile);
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                SFTPUtils sftp = null;
                try {
                    sftp = new SFTPUtils(ip, agentVo.getName(), agentVo.getPswd(), agentVo.getPort());
                    if (sftp != null) {
                        sftp.connect();
                        File orignalFile = new File(filePath);
                        String fileName = orignalFile.getName();
                        String targetFilePath;
                        String seperator;
                        if (LINUX.equals(agentVo.getOs())) {
                            seperator = Constants.LINUX_FILE_SEPARATOR;
                        } else {
                            seperator = Constants.WINDOWS_FILE_SEPARATOR;
                        }
                        if (targetPath.endsWith("/") || targetPath.endsWith("\\")) {
                            targetFilePath = targetPath + orignalFile.getName();
                        } else {
                            targetFilePath = targetPath + seperator + orignalFile.getName();
                        }
                        if (orignalFile.isFile() && orignalFile.exists()) {
                            if (sftp.isFileExist(targetPath, fileName)) {
                                sftp.rmFile(targetPath, fileName);
                            }
                            sftp.uploadFile(targetPath, fileName, filePath.replace(fileName, ""), fileName);
                        }
                        if (orignalFile.isDirectory() && orignalFile.exists()) {
                            if (sftp.isDirExist(targetFilePath)) {
                                sftp.rmDir(targetFilePath);
                            }
                            sftp.uploadDir(filePath, targetPath);
                        }
                    }
                } catch (Exception e) {
                    Log.high.info("远程文件上传异常", e);
                } finally {
                    sftp.disconnect();
                }
            }
            ExecuteResultVo executeResultVo1 = executeScripts(agentVo, targetPath, command);
            executeResultVoList.add(executeResultVo1);
        }
        return executeResultVoList;
    }

    public ExecuteResultVo executeScripts(AgentVo agentVo, String targetPath, String command) {
        //执行脚本
        ExecuteResultVo executeResultVo = new ExecuteResultVo();
        executeResultVo.setIp(agentVo.getIp());
        String resultString = null;
        try {
            if (LINUX.equals(agentVo.getOs())) {
                ProcessUtil.destroy();
                Session session = BaseWebSocket.getSession(agentVo);
                ProcessUtil.initChannelShell(session, agentVo, 3600000L);
                ChannelSftp channelSftp = ProcessUtil.openChannelSftp(session);
                ProcessUtil.cdDir(channelSftp, targetPath);
                if (agentVo.getIp() != null && agentVo.getIp().equals(this.maintainServerIp)) {
                    resultString = ProcessUtil.execLocalCommand(command, agentVo.getOs());
                } else {
                    resultString = ProcessUtil.execShellGetResult(command);
                }
            } else {
                if (agentVo.getIp() != null && agentVo.getIp().equals(this.maintainServerIp)) {
                    resultString = ProcessUtil.execLocalCommand(command, agentVo.getOs());
                } else {
                    resultString = ProcessUtil.execShellGetResult(command);
                }
            }
            executeResultVo.setExecuteResult(true);
            executeResultVo.setResultStr(resultString);
        } catch (Exception e) {
            executeResultVo.setExecuteResult(false);
            executeResultVo.setResultStr("脚本执行异常: " + e);
        }
        return executeResultVo;
    }

    @Override
    public CheckResultVo checkPath(String targetPath, String ips) {
        String[] split = ips.split(";");
        StringJoiner stringJoiner = new StringJoiner(",", "机器", "的" + targetPath + "目录不存在且创建失败,请手动创建该目录!");
        Boolean result = true;
        for (String ip : split) {
            AgentCriteria agentCriteria = new AgentCriteria();
            agentCriteria.setIp(ip);
            List<AgentVo> agent = agentMapper.getAgent(agentCriteria);
            if (agent == null) {
                throw new ServiceException("请先添加Agent!");
            }
            AgentVo agentVo = agent.get(0);
            SFTPUtils sftpUtils = null;
            boolean dirExist = false;
            try {
                sftpUtils = new SFTPUtils(ip, agentVo.getName(), agentVo.getPswd(), agentVo.getPort());
                if (sftpUtils != null) {
                    sftpUtils.connect();
                    dirExist = sftpUtils.isDirExist(targetPath);
                }
            } catch (Exception e) {
                Log.high.info("检查目标目录异常", e);
            } finally {
                sftpUtils.disconnect();
            }
            if (!dirExist) {
                stringJoiner.add(ip);
            }
            result = dirExist && result;
        }
        String string = stringJoiner.toString();
        CheckResultVo resultVo = new CheckResultVo();
        if (!result) {
            resultVo.setResult(false);
            resultVo.setMsg(string);
        } else {
            resultVo.setResult(true);
            resultVo.setMsg("所有机器均已存在目标路径!");
        }
        return resultVo;
    }

    @Override
    public ExecuteResultVo tryAgain(String ip, String targetPath, String command) {
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(ip);
        List<AgentVo> agent = agentMapper.getAgent(agentCriteria);
        if (agent == null) {
            throw new ServiceException("请先添加Agent!");
        }
        AgentVo agentVo = agent.get(0);
        ExecuteResultVo executeResultVo = executeScripts(agentVo, targetPath, command);
        return executeResultVo;
    }

    @Override
    public String getUnzipPwd() {
        return unzipPwd;
    }
}