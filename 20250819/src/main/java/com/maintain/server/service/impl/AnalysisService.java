package com.maintain.server.service.impl;


import com.maintain.server.Constants;
import com.maintain.server.mapper.AnalysisMapper;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.AnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class AnalysisService {

    @Autowired
    private AnalysisMapper mapper;

    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.YYYY_MM_DD_HH_MM_SS);

    public Map<String,Object> getAnalysisMap(String startTime, String endTime){
        //如果某ip中间节点没有数据，那么会造成数据与时间对应不上。后面再解决
        List<AnalysisVo> analysisList = mapper.getAnalysisList(startTime, endTime);
        List<String> dates = DateUtil.getDates(startTime, endTime, Constants.YYYY_MM_DD_HH_MM_SS);
        if (ListUtil.isEmpty(analysisList)) {
            for (String d : dates) {
                AnalysisVo analysisVo = new AnalysisVo();
                analysisVo.setIp("");
                analysisVo.setProcess(0);
                analysisVo.setStatus(AlarmStatusType.RED);
                try {
                    analysisVo.setUpdateTime(simpleDateFormat.parse(d));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                analysisList.add(analysisVo);
            }
        }
        ConcurrentHashMap<String, List<AnalysisVo>> ipMap = analysisList.stream().collect(Collectors.groupingBy(AnalysisVo::getIp, ConcurrentHashMap::new, Collectors.toList()));
        HashMap<String, Object> resultMap = new HashMap<>();
        Set<Map.Entry<String, List<AnalysisVo>>> entries = ipMap.entrySet();
        Iterator<Map.Entry<String, List<AnalysisVo>>> iterator = entries.iterator();
        if(!iterator.hasNext()){
            return null;
        }
        Map.Entry<String, List<AnalysisVo>> next = iterator.next();
        List<AnalysisVo> value = next.getValue();
        String ip = next.getKey();
        resultMap.put("x",analysisList.stream().map(l->DateUtil.format(l.getUpdateTime(),Constants.YYYY_MM_DD_HH_MM_SS)).sorted().collect(Collectors.toCollection(LinkedHashSet::new)));
        ArrayList<Object> series = new ArrayList<>();
        resultMap.put("series",series);
        List<Integer> collect = value.stream().map(AnalysisVo::getProcess).collect(Collectors.toList());
        HashMap<String, Object> one = new HashMap<>();
        one.put("name",ip);
        one.put("data",collect);
        series.add(one);
        while(iterator.hasNext()){
            next = iterator.next();
            value = next.getValue();
            ip = next.getKey();
            collect = value.stream().map(AnalysisVo::getProcess).collect(Collectors.toList());
            HashMap<String, Object> map = new HashMap<>();
            map.put("name",ip);
            map.put("data",collect);
            series.add(map);
        }
        return resultMap;
    }
}
