package com.maintain.server.service;

import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.vo.SoftwareRestartVo;
import com.maintain.server.vo.SoftwareVo;

import java.util.List;

/**
 * Created by Azurio on 2019/3/5.
 */
public interface SoftwareHistoryService extends ClearHistoryDataService {

    void addRestartHistory(List<SoftwareRestartVo> list);

    PageInfo<SoftwareRestartVo> getRestartHistory(SoftwareCriteria criteria);

    /**
     * 把程序资源的使用情况插入数据库
     *
     * @param softwareInfoVos
     */
    void addSoftwareResource(List<SoftwareVo> softwareInfoVos);

    PageInfo<SoftwareVo> getSoftwareHistoryInfo(SoftwareCriteria softwareCriteria);
}
