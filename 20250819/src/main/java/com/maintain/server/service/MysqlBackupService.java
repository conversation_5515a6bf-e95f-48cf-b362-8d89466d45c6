package com.maintain.server.service;

import com.maintain.server.criteria.MysqlBackupCriteria;
import com.maintain.server.vo.MysqlBackupVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */
public interface MysqlBackupService {

    List<MysqlBackupVo> findMysqlBackup(MysqlBackupCriteria mysqlBackupCriteria);

    boolean insertMysqlBackup(List<MysqlBackupVo> mysqlBackupVoList);

    boolean findByBackupTime(String backupTime, int backupType, String backupDatabases);

    /**
     * 标记忽略状态
     *
     * @param idList
     * @return
     */
    boolean markIgnoreStatus(List<Integer> idList);

    /**
     * 清理mysql备份记录
     *
     * @param criteria
     */
    void clearRecord(MysqlBackupCriteria criteria);
}