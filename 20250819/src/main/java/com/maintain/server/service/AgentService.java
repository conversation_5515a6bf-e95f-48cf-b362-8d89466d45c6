package com.maintain.server.service;

import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Created by Azurio on 2018/10/8.
 */
public interface AgentService {

    AddResult addAgent(AgentVo agentVo);

    void deleteAgent(String ip);

    void updateAgent(AgentVo agentVo) throws ServiceException;

    List<AgentVo> getAgentList(AgentCriteria criteria) throws ServiceException;

    List<AgentVo> getAgentListForPlugin(String plugin) throws ServiceException;

    AgentVo getAgentByCriteria(AgentCriteria criteria) throws ServiceException;

    List<Map<String, Object>> getPlatformRole(List<Integer> ids);

    List<Map<String,Object>> getAllHosts(AgentCriteria criteria);

    ImportResult importData(MultipartFile file);

    void updateNtp(String ip,Integer status);

    List<IpRoleVo> getIpRoleVoList();
}
