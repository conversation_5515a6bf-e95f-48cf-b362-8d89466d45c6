package com.maintain.server.service.impl;

import com.maintain.server.criteria.MysqlBackupCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.MysqlBackupMapper;
import com.maintain.server.service.MysqlBackupService;
import com.maintain.server.vo.MysqlBackupVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */
@Service("MysqlBackupService")
public class MysqlBackupServiceImpl implements MysqlBackupService {

    @Autowired
    private MysqlBackupMapper mysqlBackupMapper;
    @Override
    public List<MysqlBackupVo> findMysqlBackup(MysqlBackupCriteria mysqlBackupCriteria) {
        if(StringUtils.isEmpty(mysqlBackupCriteria.getStartTime()) || StringUtils.isEmpty(mysqlBackupCriteria.getEndTime()) ||
                StringUtils.isEmpty(mysqlBackupCriteria.getBackupType())){
            throw new ServiceException("查询参数为空");
        }
        return mysqlBackupMapper.findMysqlBackup(mysqlBackupCriteria);
    }

    @Override
    @Transactional
    public boolean insertMysqlBackup(List<MysqlBackupVo> mysqlBackupVoList) {
        boolean result = false;
        if(!mysqlBackupVoList.isEmpty()){
            result = mysqlBackupMapper.insertMysqlBackup(mysqlBackupVoList);
        }
        return result;
    }

    @Override
    public boolean findByBackupTime(String backupTime,int backupType, String backupDatabases) {
        if(StringUtils.isEmpty(backupTime) || StringUtils.isEmpty(backupType) ||
                StringUtils.isEmpty(backupDatabases)){
            throw new ServiceException("查询参数为空");
        }
        List<MysqlBackupVo> byBackupTime = mysqlBackupMapper.findByBackupTime(backupTime, backupType, backupDatabases);
        boolean result = false;
        if (byBackupTime != null && byBackupTime.size() > 0){
            result = true;
        }
        return result;
    }

    @Override
    @Transactional
    public boolean markIgnoreStatus(List<Integer> idList) {
        mysqlBackupMapper.updateIgnoreStatusByIdList(idList, 1);
        return true;
    }

    @Override
    @Transactional
    public void clearRecord(MysqlBackupCriteria criteria) {
        mysqlBackupMapper.deleteRecords(criteria);
    }
}