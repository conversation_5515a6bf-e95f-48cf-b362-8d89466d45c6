package com.maintain.server.service.impl;

import com.maintain.server.criteria.RequestRecordCriteria;
import com.maintain.server.enums.OperateTypeEnum;
import com.maintain.server.mapper.RequestRecordMapper;
import com.maintain.server.service.RequestRecordService;
import com.maintain.server.vo.RequestRecordStatVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-11-09
 */
@Service
public class RequestRecordServiceImpl implements RequestRecordService {

    @Autowired
    private WebApplicationContext applicationContext;

    @Autowired
    private RequestRecordMapper requestRecordMapper;

    private Set<String> urls;

    @Override
    public List<RequestRecordStatVo> statisRequestRecord(String startTime, String endTime) {
        RequestRecordCriteria criteria = new RequestRecordCriteria();
        criteria.setStartTime(startTime);
        criteria.setEndTime(endTime);
        if (urls == null) {
            loadUrls();
        }
        List<RequestRecordStatVo> recordStatVos = requestRecordMapper.selectStatic(criteria);
        List<String> calledUrls = recordStatVos.stream().map(RequestRecordStatVo::getUri).collect(Collectors.toList());
        recordStatVos.addAll(urls.stream().filter(url -> !calledUrls.contains(url))
                .map(url -> {
                    RequestRecordStatVo vo = new RequestRecordStatVo();
                    vo.setUri(url);
                    int moduleEnd = url.indexOf("/", 1);
                    vo.setModule(moduleEnd == -1 ? "/" : url.substring(0, moduleEnd));
                    return vo;
                }).collect(Collectors.toList()));

        return recordStatVos;
    }

    private void loadUrls() {
        RequestMappingHandlerMapping mapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();
        Set<String> urls = new HashSet<>();
        for (RequestMappingInfo info : map.keySet()) {
            urls.addAll(info.getPatternsCondition().getPatterns());
        }
        urls.addAll(OperateTypeEnum.getAllOperate());
        this.urls = urls;
    }
}