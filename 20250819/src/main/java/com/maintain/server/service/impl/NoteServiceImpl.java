package com.maintain.server.service.impl;

import com.maintain.server.criteria.NoteCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.mapper.NoteMapper;
import com.maintain.server.service.NoteService;
import com.maintain.server.vo.NoteVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class NoteServiceImpl implements NoteService {

    @Autowired
    private NoteMapper mapper;

    @Override
    public List<NoteVo> queryNotes(NoteCriteria noteCriteria) throws ServiceException {
        return mapper.queryNotes(noteCriteria);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addNote(NoteCriteria noteCriteria) throws ServiceException {
        if(Objects.isNull(noteCriteria.getAuthor()) || Objects.isNull(noteCriteria.getContent()) || Objects.isNull(noteCriteria.getTitle())){
            throw new ServiceException("参数不完整");
        }
        mapper.addNote(noteCriteria);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteNote(NoteCriteria noteCriteria) throws ServiceException {
        if(Objects.isNull(noteCriteria.getId()) && Objects.isNull(noteCriteria.getAuthor()) && Objects.isNull(noteCriteria.getTitle())){
            throw new ServiceException("参数不完整");
        }
        mapper.deleteNote(noteCriteria);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateNote(NoteCriteria noteCriteria) throws ServiceException {
        if(Objects.isNull(noteCriteria.getId())){
            throw new ServiceException("参数不完整");
        }
        mapper.updateNote(noteCriteria);
    }

    @Override
    public Integer count(NoteCriteria noteCriteria) throws ServiceException {
        return mapper.count(noteCriteria);
    }
}
