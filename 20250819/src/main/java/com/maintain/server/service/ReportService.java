package com.maintain.server.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.type.ReportType;
import com.maintain.server.vo.ReportVo;
import org.springframework.http.ResponseEntity;

public interface ReportService {

    PageInfo<ReportVo> getReportPage(ReportType type, Integer page, Integer pageSize);

    ResponseEntity<byte[]> downLoadReport(String path) throws Exception;
}
