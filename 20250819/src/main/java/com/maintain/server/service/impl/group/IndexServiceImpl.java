package com.maintain.server.service.impl.group;

import com.common.log.Log;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageInfo;
import com.maintain.server.Constants;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.GroupCriteria;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.schedule.GroupHistoryHealthSchedule;
import com.maintain.server.service.*;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.GroupType;
import com.maintain.server.type.ReportType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.utils.WordUtils;
import com.maintain.server.vo.*;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-01-31
 */
@Service("groupMonitorService")
public class IndexServiceImpl implements IndexService, Constants {

    @Value("${report.day}")
    private String reportDayPath;

    @Value("${report.week}")
    private String reportWeekPath;

    @Value("${report.month}")
    private String reportMonthPath;

    @Autowired
    private AgentService agentService;

    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private GroupHistoryService groupHistoryService;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private GroupHistoryHealthSchedule groupHistoryHealthSchedule;

    @Override
    public List<Map<String, Object>> getMonitorData() throws ServiceException {
        List<Map<String, Object>> data = new ArrayList<>();

        data.add(getGroupMonitorData());
        data.add(getAgentMonitorData());
        data.add(getHardwareMonitorData());
        data.add(getSoftwareMonitorData());
        return data;
    }

    private Map<String, Object> getSoftwareMonitorData() {
        Map<String, Object> softwareMap = new HashMap<>();
        Map<String, AlarmStatusType> softwareStatus = new HashMap<>();
        softwareMap.put("moduleName", "程序管理");

        Map<String, Map<String, Object>> softwareInfoVos = softwareService.listAllSoftwares(null, false);
        if (softwareInfoVos != null && !softwareInfoVos.isEmpty()) {
            softwareInfoVos.entrySet().forEach(e -> softwareStatus.put(e.getKey() + e.getValue().get("name"), (AlarmStatusType) e.getValue().getOrDefault("totalStatus", AlarmStatusType.GREEN)));
            softwareMap.put("status", softwareStatus);
        } else {
            softwareStatus.put("/", AlarmStatusType.GREEN);
            softwareMap.put("status", softwareStatus);
        }
        return softwareMap;
    }

    private Map<String, Object> getHardwareMonitorData() {
        Map<String, Object> hardwareMap = new HashMap<>();
        Map<String, AlarmStatusType> hardwareStatus = new HashMap<>();
        hardwareMap.put("moduleName", "服务器管理");

        List<HardwareVo> hardwareList = hardwareService.getHardwareInfos(null);
        if (ListUtil.isNotEmpty(hardwareList)) {
            Iterator<HardwareVo> iterator = hardwareList.iterator();
            while (iterator.hasNext()) {
                HardwareVo hardwareVo = iterator.next();
                if (StringUtils.isEmpty(hardwareVo.getIp())) {
                    continue;
                }
                hardwareStatus.put(hardwareVo.getIp(), hardwareVo.getStatus());
            }
            hardwareMap.put("status", hardwareStatus);
        } else {
            hardwareStatus.put("/", AlarmStatusType.GREEN);
            hardwareMap.put("status", hardwareStatus);
        }
        return hardwareMap;
    }

    private Map<String, Object> getAgentMonitorData() {
        Map<String, Object> agentMap = new HashMap<>();
        Map<String, AlarmStatusType> agentStatus = new HashMap<>();
        agentMap.put("moduleName", "Agent管理");
        List<AgentVo> agentList = agentService.getAgentList(new AgentCriteria());
        if (ListUtil.isNotEmpty(agentList)) {
            for (AgentVo agentVo : agentList) {
                agentStatus.put(agentVo.getIp(), agentVo.getStatus());
            }
            agentMap.put("status", agentStatus);
        } else {
            agentStatus.put("/", AlarmStatusType.GREEN);
            agentMap.put("status", agentStatus);

        }
        return agentMap;
    }

    private Map<String, Object> getGroupMonitorData() {
        Map<String, Object> groupMap = new HashMap<>();

        List<Map<String, Object>> groupList = new ArrayList<>();
        // 界面导航栏的状态
        Map<String, AlarmStatusType> groupStatus = new HashMap<>();

        Map<String, Object> warnMap = new LinkedHashMap<>();

        Map<AlarmStatusType, String> esType = null;


        //集群告警
        warnMap.put("groupId", 3);
        warnMap.put("name", "集群告警");
        warnMap.put("url", "");
        warnMap.put("status", "");
        groupList.add(warnMap);

        List<GroupHistoryHealthVo> serviceHealth = groupHistoryHealthSchedule.queryServiceHealth();
        for (GroupHistoryHealthVo service : serviceHealth) {

            AlarmStatusType status = AlarmStatusType.getType(service.getStatus());

            groupStatus.put(service.getName(), status);
        }

        groupMap.put("url", groupList);
        groupMap.put("moduleName", "集群管理");
        groupMap.put("status", groupStatus);
        return groupMap;
    }

    @Override
    public Map<String, Object> getReport(ReportType type, String startTime, String endTime) throws ServiceException, JsonProcessingException {
        //生成报告的数据
        //所有agent都会生成一份数据
        Log.low.info("开始生成 " + startTime + " - " + endTime + " 的" + type.getTitle());
        Map<String, Object> data = new HashedMap();
        String pdfPath;
        String docFileName = System.currentTimeMillis() + ".doc";
        if (type.equals(ReportType.DAY)) {
            final File file = new File(reportDayPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            pdfPath = reportDayPath + File.separator + startTime.substring(0, 10) + ".pdf";

        } else if (type.equals(ReportType.WEEK)) {
            final File file = new File(reportWeekPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            pdfPath = reportWeekPath + File.separator + startTime.substring(0, 10) + "-" + endTime.substring(0, 10) + ".pdf";
        } else {
            final File file = new File(reportMonthPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            pdfPath = reportMonthPath + File.separator + startTime.substring(0, 10) + "-" + endTime.substring(0, 10) + ".pdf";
        }
        final List<AgentVo> agentList = agentService.getAgentList(new AgentCriteria());
        final HardwareCriteria hardwareCriteria = new HardwareCriteria();
        hardwareCriteria.setStartTime(startTime);
        hardwareCriteria.setEndTime(endTime);
        final LinkedList<Object> agentInfos = new LinkedList<>();
        List<AgentInfosVo> agentInfosVoList = new ArrayList<>();
        Integer softwareNormalCount = 0;
        Integer softwareWrongCount = 0;

        Integer hardwareCpuWrongCount = 0;
        Integer hardwareDiskWrongCount = 0;
        Integer hardwareMemoryWrongCount = 0;
        Integer hardwareCpuNormalCount = 0;
        Integer hardwareDiskNormalCount = 0;
        Integer hardwareMemoryNormalCount = 0;
        final LinkedList<Object> softwareInfos = new LinkedList<>();

        for (AgentVo agentVo : agentList) {

            AgentInfosVo agentInfosVo = new AgentInfosVo();
            //服务器管理信息
            hardwareCriteria.setAgentId(agentVo.getId());
            final HashMap<String, Object> map = new HashMap<>();
            map.put("ip", agentVo.getIp());
            map.put("hardwareResource", hardwareService.getHistoryResource(hardwareCriteria, Boolean.TRUE));
            agentInfosVo.setIp(agentVo.getIp());
            //生成cpu图片
            if (map.get("hardwareResource") == null) continue;

            Map hardwareResource = (Map) map.get("hardwareResource");
            Integer cpuCount = (Integer) hardwareResource.get("cpuCount");
            if (cpuCount != null && cpuCount > 0) {
                Map cpu = (Map) hardwareResource.get("cpuResource");
                String cpuPic = WordUtils.derverGenerateImag(cpu, "./config/zhexian.html");
                agentInfosVo.setCpu(cpuPic);
                hardwareCpuWrongCount++;
            } else {
                hardwareCpuNormalCount++;
            }
            Integer diskCount = (Integer) hardwareResource.get("diskCount");
            if (diskCount != null && diskCount > 0) {
                //生成磁盘图片
                Map disk = (Map) hardwareResource.get("diskResource");
                String diskPic = WordUtils.derverGenerateImag(disk, "./config/zhexian.html");
                agentInfosVo.setDisk(diskPic);
                hardwareDiskWrongCount++;
            } else {
                hardwareDiskNormalCount++;
            }
            Integer memoryCount = (Integer) hardwareResource.get("memoryCount");
            if (memoryCount != null && memoryCount > 0) {
                //生成内存图片
                Map memory = (Map) hardwareResource.get("memoryResource");
                String memoryPic = WordUtils.derverGenerateImag(memory, "./config/zhexian.html");
                agentInfosVo.setMemory(memoryPic);
                hardwareMemoryWrongCount++;
            } else {
                hardwareMemoryNormalCount++;
            }
            //程序管理信息
            final List<SoftwareInfoVo> softwareInfoList = softwareService.getSoftwareInfoList(agentVo.getIp());
            List<ReportSoftInfoVo> reportSoftInfoVoList = new ArrayList<>();

            for (SoftwareInfoVo softwareInfoVo : softwareInfoList) {
                final Map<String, Object> historyResource = softwareService.getHistoryResource(startTime, endTime, softwareInfoVo.getId(), softwareInfoVo.getName());
                historyResource.put("ip", agentVo.getIp());
                Integer redCount = (Integer) historyResource.get("redCount");
                if (redCount > 0 && redCount < 5) {
                    //生成软件信息图片
                    //String softPic = WordUtils.derverGenerateImag(historyResource,"./config/jiqun.html");
                    ReportSoftInfoVo reportSoftInfoVo = new ReportSoftInfoVo();
                    //reportSoftInfoVo.setInfo(softPic);
                    reportSoftInfoVo.setName(softwareInfoVo.getName());
                    reportSoftInfoVoList.add(reportSoftInfoVo);
                    softwareInfos.add(historyResource);
                    softwareWrongCount++;
                } else {
                    softwareNormalCount++;
                }

            }
            if (cpuCount > 0 || diskCount > 0 || memoryCount > 0) {
                //agentInfosVo.setSoftwareList(reportSoftInfoVoList);
                //map.put("softwareResource",softwareInfos);
                //agentInfos.add(map);
                agentInfosVoList.add(agentInfosVo);
            }
        }

        data.put("softwareResource", softwareInfos);
        // 饼图的数据
        final HashMap<String, Object> softwareNormalMap = new HashMap<>();
        final HashMap<String, Object> softwareWrongMap = new HashMap<>();
        final HashMap<String, Object> hardwareCpuNormalMap = new HashMap<>();
        final HashMap<String, Object> hardwareCpuWrongMap = new HashMap<>();
        final HashMap<String, Object> hardwareDiskNormalMap = new HashMap<>();
        final HashMap<String, Object> hardwareDiskWrongMap = new HashMap<>();
        final HashMap<String, Object> hardwareMemoryNormalMap = new HashMap<>();
        final HashMap<String, Object> hardwareMemoryWrongMap = new HashMap<>();
        final ArrayList<Object> software = new ArrayList<>();
        final ArrayList<Object> cpu = new ArrayList<>();
        final ArrayList<Object> disk = new ArrayList<>();
        final ArrayList<Object> memory = new ArrayList<>();
        //程序状况图
        softwareNormalMap.put("name", "正常");
        softwareNormalMap.put("value", softwareNormalCount);
        softwareWrongMap.put("name", "异常");
        softwareWrongMap.put("value", softwareWrongCount);
        software.add(softwareNormalMap);
        software.add(softwareWrongMap);
        Map<String, Object> softPicMap = new HashedMap();
        softPicMap.put("data", software);
        softPicMap.put("name", "程序异常占比");
        data.put("software", WordUtils.derverGenerateImag(softPicMap, "./config/bintu.html"));
        //cpu状况图
        hardwareCpuNormalMap.put("name", "正常");
        hardwareCpuNormalMap.put("value", hardwareCpuNormalCount);
        hardwareCpuWrongMap.put("name", "异常");
        hardwareCpuWrongMap.put("value", hardwareCpuWrongCount);
        cpu.add(hardwareCpuNormalMap);
        cpu.add(hardwareCpuWrongMap);
        Map<String, Object> cpuPicMap = new HashedMap();
        cpuPicMap.put("data", cpu);
        cpuPicMap.put("name", "CPU异常主机占比");
        data.put("cpu", WordUtils.derverGenerateImag(cpuPicMap, "./config/bintu.html"));
        //硬盘状况图
        hardwareDiskNormalMap.put("name", "正常");
        hardwareDiskNormalMap.put("value", hardwareDiskNormalCount);
        hardwareDiskWrongMap.put("name", "异常");
        hardwareDiskWrongMap.put("value", hardwareDiskWrongCount);
        disk.add(hardwareDiskNormalMap);
        disk.add(hardwareDiskWrongMap);
        Map<String, Object> diskPicMap = new HashedMap();
        diskPicMap.put("data", disk);
        diskPicMap.put("name", "硬盘异常主机占比");
        data.put("disk", WordUtils.derverGenerateImag(diskPicMap, "./config/bintu.html"));
        //内存状况图
        hardwareMemoryNormalMap.put("name", "正常");
        hardwareMemoryNormalMap.put("value", hardwareMemoryNormalCount);
        hardwareMemoryWrongMap.put("name", "异常");
        hardwareMemoryWrongMap.put("value", hardwareMemoryWrongCount);
        memory.add(hardwareMemoryNormalMap);
        memory.add(hardwareMemoryWrongMap);
        Map<String, Object> memoryPicMap = new HashedMap();
        memoryPicMap.put("data", memory);
        memoryPicMap.put("name", "内存异常主机占比");
        data.put("memory", WordUtils.derverGenerateImag(memoryPicMap, "./config/bintu.html"));
        data.put("title", type.getTitle());

        data.put("agentInfos", agentInfosVoList);
        //集群管理信息
        GroupCriteria criteria = new GroupCriteria();
        criteria.setGroupId(GroupType.CODIS.getId()).setStartTime(startTime).setEndTime(endTime);
        PageInfo<GroupHistoryHealthVo> codisPage = groupHistoryService.getGroupHistoryHealth(criteria);
        final HashMap<String, Object> groupMap = new HashMap<>();
        groupMap.put("codis", parsePageInfo(codisPage.getList()));
        //生成codis监控图
        Map codis = (Map) groupMap.get("codis");
        String codisPic = WordUtils.derverGenerateImag(codis, "./config/jiqun.html");
        data.put("codis", codisPic);

        criteria.setGroupId(GroupType.CDH.getId());
        PageInfo<GroupHistoryHealthVo> cdhPage = groupHistoryService.getGroupHistoryHealth(criteria);
        groupMap.put("cdh", parsePageInfo(cdhPage.getList()));
        //生成cdh监控图
        Map cdh = (Map) groupMap.get("cdh");
        String cdhPic = WordUtils.derverGenerateImag(cdh, "./config/jiqun.html");
        data.put("cdh", cdhPic);
        criteria.setGroupId(GroupType.ES.getId());
        PageInfo<GroupHistoryHealthVo> esPage = groupHistoryService.getGroupHistoryHealth(criteria);
        groupMap.put("es", parsePageInfo(esPage.getList()));
        //生成es监控图
        Map es = (Map) groupMap.get("es");
        String esPic = WordUtils.derverGenerateImag(es, "./config/jiqun.html");
        data.put("es", esPic);

        try {
            docFileName = WordUtils.exportMillCertificateWord(data, docFileName);
            WordUtils.wordTopdf(docFileName, pdfPath);
        } catch (IOException e) {
            Log.high.error(e);
        }
        return data;
    }

    private Map<String, Object> parsePageInfo(List<GroupHistoryHealthVo> list) {
        Map<String, Object> data = new LinkedHashMap<>();
        Set<String> xTime = new LinkedHashSet<>();
        //List<GroupHistoryHealthVo> tempList;
        Map<String, Object> greenMap = new HashMap<>();
        List<Integer> greenList = new ArrayList<>();
        int green = 0;
        Map<String, Object> yellowMap = new HashMap<>();
        List<Integer> yellowList = new ArrayList<>();
        int yellow = 0;
        Map<String, Object> redMap = new HashMap<>();
        List<Integer> redList = new ArrayList<>();
        int red = 0;
        List<String> descriptionList = new ArrayList<>();
        for (GroupHistoryHealthVo vo : list) {
            Integer status = vo.getStatus();
            if (status == null) {
                continue;
            }
            Date date = vo.getCreateTime();
            xTime.add(DateUtil.format(date, Constants.YYYY_MM_DD_HH_MM_SS));
            if (AlarmStatusType.GREEN.getId().equals(status)) {
                greenList.add(status);
                ++green;
                yellowList.add(null);
                redList.add(null);
            } else if (AlarmStatusType.YELLOW.getId().equals(status)) {
                yellowList.add(status);
                ++yellow;
                greenList.add(null);
                redList.add(null);
            } else if (AlarmStatusType.RED.getId().equals(status)) {
                redList.add(status);
                ++red;
                greenList.add(null);
                yellowList.add(null);
            }
            descriptionList.add(vo.getDescription());
        }

        greenMap.put("num", green);
        greenMap.put("chart", greenList);
        yellowMap.put("num", yellow);
        yellowMap.put("chart", yellowList);
        redMap.put("num", red);
        redMap.put("chart", redList);
        data.put(AlarmStatusType.RED.name(), redMap);
        data.put(AlarmStatusType.YELLOW.name(), yellowMap);
        data.put(AlarmStatusType.GREEN.name(), greenMap);
        data.put("x", xTime);
        data.put("description", descriptionList);
        data.put("content", list.get(0).getName());
        return data;
    }

    private AlarmStatusType changeAlarmStatusType(Map<AlarmStatusType, String> type, String module) {
        if (type == null || type.isEmpty()) {
            return AlarmStatusType.RED;
        }
        if (type.get(AlarmStatusType.RED) != null) {
            return AlarmStatusType.RED;
        }
        if (type.get(AlarmStatusType.YELLOW) != null) {
            return AlarmStatusType.YELLOW;
        }
        return AlarmStatusType.GREEN;
    }
}