package com.maintain.server.service;

import org.java_websocket.WebSocket;

import java.util.Map;

/**
 * Created by 巨建章In on 2020/8/21.
 */
public interface CommonConfigService {
    String getCheckAgentRoleResult();

    String uploadCommonConfig(String param, WebSocket conn);

    String loadCommonConfigInfo(String commonConfigPath);

    String prepareCommonConfig(String commonConfigPath);

    String getCommonConfigStr(String ip, Map<String, Map<String, Object>> applicationYamlMap);

    void prepareCommonConfigForRemoteServer(String ip);

    /**
     * 一键关闭平台
     *
     * @return
     */
    String closePlatform(WebSocket conn);

    /**
     * 一键打开平台
     *
     * @return
     */
    String openPlatform(WebSocket conn);

}
