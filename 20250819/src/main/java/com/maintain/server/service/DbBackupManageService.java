package com.maintain.server.service;

import com.maintain.server.criteria.DbBackupManageCriteria;
import com.maintain.server.vo.DbBackupManageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-22
 */
public interface DbBackupManageService {
    /**
     * 列出数据库备份管理记录
     *
     * @param criteria
     * @return
     */
    List<DbBackupManageVo> list(DbBackupManageCriteria criteria);

    /**
     * 编辑一条数据库备份管理记录
     *
     * @param vo
     * @return
     */
    boolean editRecord(DbBackupManageVo vo);

    /**
     * 删除一条数据库备份管理记录
     *
     * @param id
     */
    boolean deleteRecord(Integer id);

    /**
     * 新增一条数据库备份管理记录
     *
     * @param vo
     * @return
     */
    DbBackupManageVo addRecord(DbBackupManageVo vo);

    /**
     * 备份数据库
     *
     * @param backupType
     * @return
     */
    boolean dbBackup(int backupType);

    /**
     * 重新备份
     *
     * @param backupType
     * @return
     */
    boolean reBackup(int backupType);

    /**
     * 测试数据库连通性
     *
     * @param vo
     * @return
     */
    boolean testConnect(DbBackupManageVo vo);
}