package com.maintain.server.service;

import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.type.WarnType;
import com.maintain.server.vo.HardwareHistoryHealth;
import com.maintain.server.vo.HardwareVo;
import com.maintain.server.vo.OsOperateLogVo;
import com.maintain.server.vo.PingResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-10-24
 */
public interface HardwareService extends ClearHistoryDataService {

    void addHardwareInfo(HardwareVo hardwareVo) throws ServiceException;

    void updateHardwareInfo(HardwareVo hardwareVo);

    HardwareVo getHardwareInfo(HardwareCriteria criteria) throws ServiceException;

    List<HardwareVo> getHardwareInfos(HardwareCriteria criteria) throws ServiceException;

    Map<String, Object> getHardwareInfosAndStatus(HardwareCriteria criteria) throws ServiceException;

    void addHardwareHistoryHealth(HardwareHistoryHealth hardwareVo) throws ServiceException;

    void addHardwareHistoryHealth(List<HardwareHistoryHealth> hardwareList) throws ServiceException;

    PageInfo<HardwareHistoryHealth> getHardwareHistoryHealth(HardwareCriteria criteria);

    Map<String, Object> getHistoryResource(HardwareCriteria hardwareCriteria, Boolean picture);

    void updateWarning(String ip, Integer status, String warn, String error
            , String warnPercent, String errorPercent, String disk, WarnType warnType);

    void updateAllIpWarning(String ips, Integer status, String warn, String error
            , String warnPercent, String errorPercent, String disk, WarnType warnType);

    Object getWarning(String ip);

    List<String> getDisks(String ip);

    PageInfo<OsOperateLogVo> getOsOperateLog(Integer pn, Integer ps, String ip);

    /**
     * 异常网络数据
     *
     * @param pn
     * @param ps
     * @param ip
     * @return
     */
    PageInfo<PingResult> exceptionNetworkInfo(Integer pn, Integer ps, String ip);
}