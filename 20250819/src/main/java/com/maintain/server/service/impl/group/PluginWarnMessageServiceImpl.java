package com.maintain.server.service.impl.group;

import com.maintain.server.mapper.PluginWarnMessageMapper;
import com.maintain.server.service.PluginWarnMessageService;
import com.maintain.server.vo.PluginWarnMessageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Service
public class PluginWarnMessageServiceImpl implements PluginWarnMessageService {

    @Autowired
    private PluginWarnMessageMapper pluginWarnMessageMapper;

    @Override
    public List<PluginWarnMessageVo> getAllGroupWarnInfo() {
        return pluginWarnMessageMapper.getAllGroupWarnInfo();
    }


}