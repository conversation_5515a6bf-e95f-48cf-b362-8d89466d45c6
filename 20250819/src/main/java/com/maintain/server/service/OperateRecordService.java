package com.maintain.server.service;

import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.OperateRecordCriteria;
import com.maintain.server.vo.OperateRecordVo;

/**
 * Created by Azurio on 2018/11/28.
 */
public interface OperateRecordService {

    PageInfo<OperateRecordVo> getOperateRecord(OperateRecordCriteria operateRecordCriteria);

    void addOperateRecord(OperateRecordVo operateRecordVo);

    void operateRecordAOP(Integer tag);
}
