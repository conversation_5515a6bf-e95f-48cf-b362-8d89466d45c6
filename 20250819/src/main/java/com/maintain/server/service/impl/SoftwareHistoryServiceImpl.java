package com.maintain.server.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.BaseCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.mapper.SoftwareHistoryMapper;
import com.maintain.server.service.SoftwareHistoryService;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.SoftwareRestartVo;
import com.maintain.server.vo.SoftwareVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-05
 */
@Service
public class SoftwareHistoryServiceImpl implements SoftwareHistoryService {


    @Autowired
    private SoftwareHistoryMapper softwareHistoryMapper;


    @Override
    @Transactional
    public void clearHistoryData(BaseCriteria criteria) {
        softwareHistoryMapper.clearRestartHistoryData(criteria);
        softwareHistoryMapper.clearHistoryData(criteria);
    }

    @Override
    @Transactional
    public void addRestartHistory(List<SoftwareRestartVo> list) {
        softwareHistoryMapper.addRestartHistory(list);
    }

    @Override
    public PageInfo<SoftwareRestartVo> getRestartHistory(SoftwareCriteria criteria) {
        if (criteria != null && criteria.getPn() != null && criteria.getPs() != null) {
            PageHelper.startPage(criteria.getPn(), criteria.getPs());
        }
        List<SoftwareRestartVo> list = softwareHistoryMapper.getRestartHistory(criteria);
        if (ListUtil.isEmpty(list)) {
            return null;
        }
        return new PageInfo<>(list);
    }


    @Override
    @Transactional
    public void addSoftwareResource(List<SoftwareVo> softwareInfoVos) {
        if (ListUtil.isEmpty(softwareInfoVos)) {
            return;
        }
        softwareHistoryMapper.addSoftwareHistoryInfo(softwareInfoVos);
    }

    @Override
    public PageInfo<SoftwareVo> getSoftwareHistoryInfo(SoftwareCriteria softwareCriteria) {
        if (softwareCriteria == null) {
            return null;
        }
        Integer pn = softwareCriteria.getPn();
        Integer ps = softwareCriteria.getPs();
        if (pn != null && ps != null) {
            PageHelper.startPage(pn, ps);
        }
        List<SoftwareVo> list = softwareHistoryMapper.getSoftwareHistoryInfo(softwareCriteria);
        return new PageInfo<>(list);
    }
}