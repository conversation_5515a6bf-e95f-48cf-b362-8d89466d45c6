package com.maintain.server.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.common.log.Log;
import com.jcraft.jsch.ChannelShell;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.maintain.server.aop.OperateRecordAnnotation;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.enums.OperateTypeEnum;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.mapper.AgentMapper;
import com.maintain.server.mapper.SoftwareMapper;
import com.maintain.server.service.*;
import com.maintain.server.type.OsType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.utils.BaseConfigUtil;
import com.maintain.server.utils.FileUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.*;
import com.maintain.server.websocket.BaseWebSocket;
import org.java_websocket.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.ConnectException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


@Service("commonconfigService")
public class CommonConfigServiceImpl extends BaseWebSocket implements IceFlag, CommonConfigService {

    @Autowired
    private AgentService agentService;

    @Value("${server.localHost}")
    private String maintainServerIp;

    @Autowired
    private SoftwareMapper softwareMapper;

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private AutoDeployService autoDeployService;

    @Resource(name = "softwareWebSocketService")
    private WebSocketService softwareWebSocketService;

    @Resource(name = "agentWebSocketService")
    private WebSocketService agentWebSocketService;

    private List<AgentVo> getAgentList() {
        AgentCriteria agentCriteria = new AgentCriteria();
        List<AgentVo> list = agentService.getAgentList(agentCriteria);
        return list;
    }

    @Override
    public String getCheckAgentRoleResult() {
        List<AgentVo> agentList = getAgentList();
        List<String> roleList = agentList.stream().map(AgentVo::getRole).collect(Collectors.toList());
        Set<String> set = new LinkedHashSet<>();
        set.add("WEB");
        set.add("数据分析");
        set.add("动态分析");
        String result = "";
        StringBuffer stringBuffer = new StringBuffer();
        for (String s : set) {
            if (!roleList.contains(s)) {
                stringBuffer.append(s).append(",");
            }
        }
        result = stringBuffer.toString();
        if (!StringUtils.isEmpty(result)) {
            result = "角色机[" + result.substring(0, result.length() - 1) + "]Agent未添加,请手动修改文件相应配置!";
        }
        return result;
    }

    @Override
    @OperateRecordAnnotation(name = "分发公共配置文件")
    public String uploadCommonConfig(String param, WebSocket conn) {
        String result = CLOSE;
        try {
            uploadCommonConfigToServer(conn, param);
            result = SUCCESS;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public String loadCommonConfigInfo(String commonConfigPath) {
        String content = prepareCommonConfig(commonConfigPath);
        return content;
    }

    @Override
    public String prepareCommonConfig(String commonConfigPath) {
        Map<String, Map<String, Object>> applicationYamlMap = BaseConfigUtil.applicationYamlMap;

        CommonConfigVo commonConfig = readLocalCommonConfig(commonConfigPath, applicationYamlMap);

        String configStr = commonConfig.getJsonStr();

        Map<String, LinkedHashMap<String, Object>> stringLinkedHashMapMap = replaceMapValue(commonConfig.getCommonConfigMap(), applicationYamlMap);

        Map<String, LinkedHashMap<String, Object>> stringLinkedHashMapMap1 = replaceRoleIp(stringLinkedHashMapMap);

        String newJsonStr = useNewMapCreateNewJsonStr("", stringLinkedHashMapMap1, configStr);
        try {
            FileUtil.writeStringToFile(new File(commonConfigPath), newJsonStr, "UTF-8", false);
        } catch (IOException e) {
            Log.high.error("本地公共配置文件修改异常", e);
        }
        return newJsonStr;
    }

    public CommonConfigVo readLocalCommonConfig(String commonConfigPath, Map<String, Map<String, Object>> applicationYamlMap) {
        File localCommonConfigfile = new File(commonConfigPath);
        if (!localCommonConfigfile.exists()) {
            throw new ServiceException("找不到" + localCommonConfigfile + ",请确保待上传common-config.json文件已经存放于此。");
        }
        Map<String, LinkedHashMap<String, Object>> commonConfigMap = null;
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder configStr = new StringBuilder();
        try (FileInputStream fileInputStream = new FileInputStream(localCommonConfigfile);
             InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "UTF-8");
             BufferedReader reader = new BufferedReader(inputStreamReader)) {
            String line = "";
            int i = 0;
            while ((line = reader.readLine()) != null) {
                if (i == 0) {
                    byte[] bytes = line.getBytes("UTF-8");
                    if (bytes.length > 3) {
                        line = new String(bytes, 3, bytes.length - 3);
                    }
                }
                i++;
                if (!line.startsWith("\t//") && line.contains(":") || line.contains("\t},") || line.contains("{") || line.contains("}") || line.contains("\t}")) {
                    if (line.contains(":{") || line.startsWith("\t\t") || line.startsWith("\t  ") || line.contains("\t},") || line.contains("{") || line.contains("}") || line.contains("\t}")) {
                        String replace = line.replace("\t", "").replace("\t\t", "").trim();
                        stringBuilder.append(replace);
                    } else {
                        String key = line.substring(0, line.indexOf(":")).trim();
                        if (applicationYamlMap != null) {
                            if (key.equals("\"prefix\"")) {
                                String newValue = applicationYamlMap.get("es").get("prefix").toString();
                                line = replaceValue(line, newValue);
                            }
                        }
                        if (key.equals("\"localAddress\"")) {
                            line = replaceValue(line, "***各角色机的本机IP***");
                        }
                    }
                }
                configStr.append(line + "\r\n");
            }
            commonConfigMap = JSONObject.parseObject(stringBuilder.toString(), new TypeReference<LinkedHashMap<String, LinkedHashMap<String, Object>>>() {
            });
        } catch (IOException e) {
            Log.high.error("公共配置文件读取异常", e);
        }
        CommonConfigVo commonConfigVo = new CommonConfigVo();
        commonConfigVo.setCommonConfigMap(commonConfigMap);
        commonConfigVo.setJsonStr(configStr.toString());
        return commonConfigVo;
    }

    /**
     * 用运维配置修改部分公共配置Map
     *
     * @param applicationYamlMap
     */
    private Map<String, LinkedHashMap<String, Object>> replaceMapValue(Map<String, LinkedHashMap<String, Object>> commonConfigMap, Map<String, Map<String, Object>> applicationYamlMap) {
        Map<String, Object> datasource = (Map<String, Object>) applicationYamlMap.get("spring").get("datasource");
        Map<String, Object> druid = (Map<String, Object>) datasource.get("druid");
        Map<String, Object> guard = (Map<String, Object>) druid.get("guard");
        Object url = guard.get("url");
        Object pswd = applicationYamlMap.get("codis").get("pswd");
        Object kafkaZookeeperConnStr = applicationYamlMap.get("kafka").get("kafkaZookeeperConnStr");
        Object monitor = applicationYamlMap.get("cm").get("monitor");
        Object userName = applicationYamlMap.get("cm").get("userName");
        Object pswd1 = applicationYamlMap.get("cm").get("pswd");
        Object kafkaBrokerConnStr = applicationYamlMap.get("kafka").get("kafkaBrokerConnStr");
        Map<String, Object> firstes = (Map<String, Object>) applicationYamlMap.get("es").get("firstes");
        Map<String, Object> secondes = (Map<String, Object>) applicationYamlMap.get("es").get("secondes");
        if (commonConfigMap.containsKey("suyuanConnection")) {
            LinkedHashMap<String, Object> suyuanConnection = commonConfigMap.get("suyuanConnection");
            String[] split = suyuanConnection.get("restServiceUrl").toString().split(":");
            String newValue = split[0] + "://" + "***WEB角色机IP***" + ":" + split[2];
            suyuanConnection.put("restServiceUrl", newValue);
            commonConfigMap.put("suyuanConnection", suyuanConnection);
        }
        if (url != null) {
            String[] mysqlIpArr = url.toString().split("/");
            if (mysqlIpArr.length > 2) {
                String mysqlIp = mysqlIpArr[2];
                if (commonConfigMap.containsKey("mysqlDbConnection")) {
                    LinkedHashMap<String, Object> mysqlDbConnection = commonConfigMap.get("mysqlDbConnection");
                    if (mysqlDbConnection.containsKey("jdbcUrl")) {
                        Object jdbcUrl = mysqlDbConnection.get("jdbcUrl");
                        if (jdbcUrl != null) {
                            String[] jdbcUrlArr = jdbcUrl.toString().split("/");
                            if (jdbcUrlArr.length > 3) {
                                String newJdbcUrl = jdbcUrlArr[0] + "//" + mysqlIp + "/" + jdbcUrlArr[3];
                                mysqlDbConnection.put("jdbcUrl", newJdbcUrl);
                            }
                        }
                    }
                    if (mysqlDbConnection.containsKey("userName")) {
                        mysqlDbConnection.put("userName", guard.get("username"));
                    }
                    if (mysqlDbConnection.containsKey("password")) {
                        mysqlDbConnection.put("password", guard.get("password"));
                    }
                    commonConfigMap.put("mysqlDbConnection", mysqlDbConnection);
                }
                if (commonConfigMap.containsKey("permissionDbConnection")) {
                    LinkedHashMap<String, Object> permissionDbConnection = commonConfigMap.get("permissionDbConnection");
                    if (permissionDbConnection.containsKey("jdbcUrl")) {
                        Object jdbcUrl1 = permissionDbConnection.get("jdbcUrl");
                        if (jdbcUrl1 != null) {
                            String[] jdbcUrlArr1 = jdbcUrl1.toString().split("/");
                            if (jdbcUrlArr1.length > 3) {
                                String newJdbcUrl1 = jdbcUrlArr1[0] + "//" + mysqlIp + "/" + jdbcUrlArr1[3];
                                permissionDbConnection.put("jdbcUrl", newJdbcUrl1);
                            }
                        }
                    }
                    if (permissionDbConnection.containsKey("userName")) {
                        permissionDbConnection.put("userName", guard.get("username"));
                    }
                    if (permissionDbConnection.containsKey("password")) {
                        permissionDbConnection.put("password", guard.get("password"));
                    }
                    commonConfigMap.put("permissionDbConnection", permissionDbConnection);
                }
            }
        }
        if (commonConfigMap.containsKey("hdfsConnection")) {
            LinkedHashMap<String, Object> hdfsConnection = commonConfigMap.get("hdfsConnection");
            if (kafkaZookeeperConnStr != null) {
                String[] split = kafkaZookeeperConnStr.toString().split(",");
                if (split.length > 1) {
                    if (hdfsConnection.containsKey("dfs.namenode.rpc-address.hadoop-cluster.nn1")) {
                        hdfsConnection.put("dfs.namenode.rpc-address.hadoop-cluster.nn1", split[0]);
                    }
                    if (hdfsConnection.containsKey("dfs.namenode.rpc-address.hadoop-cluster.nn2")) {
                        hdfsConnection.put("dfs.namenode.rpc-address.hadoop-cluster.nn2", split[1]);
                    }
                }
            }
            commonConfigMap.put("hdfsConnection", hdfsConnection);
        }
        if (commonConfigMap.containsKey("proConnection")) {
            LinkedHashMap<String, Object> proConnection = commonConfigMap.get("proConnection");
            String[] strArr = new String[]{"restServiceUrl", "fileUploadConnect", "mailUploadServiceConnect", "esSearchServiceConnect", "esServiceConnect",
                    "hbaseServiceConnect", "kafkaServiceConnect", "redisServiceConnect", "dynamicDispatchServiceConnect", "dynamicScanAnalysisConnect",
                    "resetAnalysisConnect"};
            String[] itemArr = new String[]{"WEB角色机IP", "WEB角色机IP", "WEB角色机IP", "WEB角色机IP", "各角色机的本机IP", "各角色机的本机IP", "各角色机的本机IP", "数据分析角色机IP", "动态分析机IP", "动态分析机IP", "动态分析机IP"};
            for (int i = 0; i < strArr.length; i++) {
                if (proConnection.containsKey(strArr[i])) {
                    Object fileUploadConnect = proConnection.get(strArr[i]);
                    if (fileUploadConnect != null) {
                        String[] split = fileUploadConnect.toString().split(" ");
                        split[2] = "***" + itemArr[i] + "***";
                        StringJoiner stringJoiner = new StringJoiner(" ", "", "");
                        for (String string : split) {
                            stringJoiner.add(string);
                        }
                        proConnection.put(strArr[i], stringJoiner.toString());
                    }
                }
            }
            commonConfigMap.put("proConnection", proConnection);
        }

        if (commonConfigMap.containsKey("otherConnection")) {
            LinkedHashMap<String, Object> otherConnection = commonConfigMap.get("otherConnection");
            if (pswd != null) {
                if (otherConnection.containsKey("codisPwd")) {
                    otherConnection.put("codisPwd", pswd.toString());
                }
            }
            if (otherConnection.containsKey("analysisIp")) {
                otherConnection.put("analysisIp", "***动态分析机IP***");
            }
            if (kafkaZookeeperConnStr != null) {
                String[] splitArr = kafkaZookeeperConnStr.toString().split(",");
                int port = 0;
                StringJoiner stringJoiner = new StringJoiner(",", "", "");
                if (splitArr.length > 1) {
                    for (String s : splitArr) {
                        String[] splitArr2 = s.split(":");
                        stringJoiner.add(splitArr2[0]);
                    }
                    port = Integer.valueOf(splitArr[0].split(":")[1]);
                }
                if (otherConnection.containsKey("zookeeper")) {
                    otherConnection.put("zookeeper", stringJoiner.toString());
                }
                if (otherConnection.containsKey("zkPort")) {
                    otherConnection.put("zkPort", port);
                }
            }
            if (monitor != null) {
                if (otherConnection.containsKey("hdfsHostsUrl")) {
                    String str = monitor.toString() + "/api/v13/hosts";
                    otherConnection.put("hdfsHostsUrl", str);
                }
            }
            if (userName != null) {
                if (otherConnection.containsKey("hdfsHostsUser")) {
                    otherConnection.put("hdfsHostsUser", userName.toString());
                }
            }
            if (pswd1 != null) {
                if (otherConnection.containsKey("hdfsHostsPassword")) {
                    otherConnection.put("hdfsHostsPassword", pswd1.toString());
                }
            }
            if (kafkaBrokerConnStr != null) {
                if (otherConnection.containsKey("kafkaBrokerConnStr")) {
                    otherConnection.put("kafkaBrokerConnStr", kafkaBrokerConnStr.toString());
                }
            }
            if (kafkaZookeeperConnStr != null) {
                if (otherConnection.containsKey("kafkaZookeeperConnStr")) {
                    otherConnection.put("kafkaZookeeperConnStr", kafkaZookeeperConnStr.toString());
                }
            }
            commonConfigMap.put("otherConnection", otherConnection);
        }
        if (commonConfigMap.containsKey("esConnection")) {
            LinkedHashMap<String, Object> esConnection = commonConfigMap.get("esConnection");
            if (esConnection.containsKey("indexClusterName")) {
                esConnection.put("indexClusterName", firstes.get("clusterName"));
            }
            if (esConnection.containsKey("indexClusterServer")) {
                esConnection.put("indexClusterServer", firstes.get("url"));
            }
            if (esConnection.containsKey("indexClusterServerHttp")) {
                esConnection.put("indexClusterServerHttp", firstes.get("httpUrl"));
            }
            Object security = firstes.get("security");
            if (security != null) {
                String[] securities = security.toString().split(":");
                if (securities.length > 1) {
                    if (esConnection.containsKey("indexUserName")) {
                        esConnection.put("indexUserName", securities[0]);
                    }
                    if (esConnection.containsKey("indexPassword")) {
                        esConnection.put("indexPassword", securities[1]);
                    }
                }
            }
            if (esConnection.containsKey("ipdnsClusterName")) {
                esConnection.put("ipdnsClusterName", secondes.get("clusterName"));
            }
            if (esConnection.containsKey("ipdnsClusterServer")) {
                esConnection.put("ipdnsClusterServer", secondes.get("url"));
            }
            if (esConnection.containsKey("ipdnsClusterServerHttp")) {
                esConnection.put("ipdnsClusterServerHttp", secondes.get("httpUrl"));
            }
            Object security1 = secondes.get("security");
            if (security1 != null) {
                String[] securities1 = secondes.get("security").toString().split(":");
                if (securities1.length > 1) {
                    if (esConnection.containsKey("ipdnsUserName")) {
                        esConnection.put("ipdnsUserName", securities1[0]);
                    }
                    if (esConnection.containsKey("ipdnsPassword")) {
                        esConnection.put("ipdnsPassword", securities1[1]);
                    }
                }
            }
            commonConfigMap.put("esConnection", esConnection);
        }
        return commonConfigMap;
    }

    public String useNewMapCreateNewJsonStr(String ip, Map<String, LinkedHashMap<String, Object>> commonConfigMap, String configStr) {
        String[] splitLine = configStr.split("\\r\\n");
        StringBuffer stringBuffer = new StringBuffer();
        LinkedHashMap<String, Object> stringObjectLinkedHashMap = null;
        for (String line : splitLine) {
            if (line.contains("localAddress") && !StringUtils.isEmpty(ip)) {
                AgentCriteria agentCriteria = new AgentCriteria();
                agentCriteria.setIp(ip);
                agentCriteria.setExistsVnp(1);
                List<AgentVo> agentVos = agentMapper.getAgent(agentCriteria);
                String replace;
                if (ListUtil.isNotEmpty(agentVos)) {
                    replace = line.replace(line.substring(line.indexOf(":") + 1), "\"0.0.0.0\",");
                } else {
                    replace = line.replace(line.substring(line.indexOf(":") + 1), "\"" + ip + "\",");
                }
                stringBuffer.append(replace + "\r\n");
            } else {
                if (line.contains(":{")) {
                    String key = line.replace("\t\"", "").replace("\"", "").split(":")[0].trim();
                    stringObjectLinkedHashMap = commonConfigMap.get(key);
                    stringBuffer.append(line + "\r\n");
                    continue;
                }
                if (stringObjectLinkedHashMap != null && line.contains(":")) {
                    String part1 = line.split(String.valueOf(line.charAt(line.indexOf(":"))))[0];
                    String key = part1.replace("\t\t\"", "").replace("\"", "").trim();
                    Object value = stringObjectLinkedHashMap.get(key);
                    String newLine = "";
                    if (value != null) {
                        newLine = replaceValue(line, value.toString());
                    }
                    stringBuffer.append(newLine + "\r\n");
                    stringObjectLinkedHashMap.remove(key);
                } else {
                    stringBuffer.append(line + "\r\n");
                }
            }
        }
        return stringBuffer.toString();
    }

    public String replaceValue(String lineStr, Object value) {
        String newLineStr = "";
        String newValue = "";
        if (lineStr.contains(":") && !StringUtils.isEmpty(lineStr)) {
            String oldValue = lineStr.substring(lineStr.indexOf(":") + 1).trim();
            if (oldValue.startsWith("\"") && oldValue.endsWith("\",")) {
                newValue = "\"" + value.toString() + "\",";
            } else if (oldValue.startsWith("\"") && oldValue.endsWith("\"")) {
                newValue = "\"" + value.toString() + "\"";
            } else if (!oldValue.startsWith("\"") && oldValue.endsWith(",")) {
                newValue = value.toString() + ",";
            } else {
                newValue = value.toString();
            }
            if (newValue instanceof String) {
                if (!StringUtils.isEmpty(newValue.toString())) {
                    newLineStr = lineStr.replace(oldValue, newValue);
                }
            }
        }
        return newLineStr;
    }

    public boolean uploadCommonConfigToServer(WebSocket conn, String param) {
        Map<String, Object> linkedHashMap = new LinkedHashMap<>();
        Set<String> set = new LinkedHashSet<>();
        set.add("config");
        linkedHashMap.put("name", set);
        String commonConfigPath = autoDeployService.getDeployRecordVo(new DeployRecordVo()).getConfigPath();
        String[] arr = commonConfigPath.split("\\\\");
        String dir = arr[0] + "\\" + arr[1] + "\\" + arr[2] + "\\" + arr[3] + "\\";
        linkedHashMap.put("dir", dir);
        linkedHashMap.put("product", arr[2]);

        List<AgentVo> agentList = getAgentList();
        for (AgentVo agentVo : agentList) {
            String ip = agentVo.getIp();
            sentText(conn, "------------------------------------");
            if (StringUtils.isEmpty(ip)) {
                continue;
            }
            linkedHashMap.put("ip", agentVo.getIp());
            try {
                sentText(conn, "开始向服务器：" + ip + "上传config");
                autoDeployService.autoDeploy(agentVo, linkedHashMap);
                sentText(conn, "服务器:" + ip + "上传完毕");
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
                sentText(conn, e.getMessage());
            }
            sentText(conn, "------------------------------------");
        }
        return true;
    }

    private Map<String, LinkedHashMap<String, Object>> updateServerIp(String ip, Map<String, LinkedHashMap<String, Object>> commonConfigMap) {
        if (!StringUtils.isEmpty(ip)) {
            LinkedHashMap<String, Object> proConnection = commonConfigMap.get("proConnection");
            if (commonConfigMap.containsKey("proConnection")) {
                if (proConnection.containsKey("esServiceConnect")) {
                    getNewValue("esServiceConnect", ip, proConnection);
                }
                if (proConnection.containsKey("hbaseServiceConnect")) {
                    getNewValue("hbaseServiceConnect", ip, proConnection);
                }
                if (proConnection.containsKey("kafkaServiceConnect")) {
                    getNewValue("kafkaServiceConnect", ip, proConnection);
                }
            }
        }
        return commonConfigMap;
    }

    public Map<String, LinkedHashMap<String, Object>> replaceRoleIp(Map<String, LinkedHashMap<String, Object>> commonConfigMap) {
        LinkedHashMap<String, Object> proConnection = commonConfigMap.get("proConnection");
        LinkedHashMap<String, Object> otherConnection = commonConfigMap.get("otherConnection");
        LinkedHashMap<String, Object> suyuanConnection = commonConfigMap.get("suyuanConnection");

        List<AgentVo> agentList = getAgentList();
        for (AgentVo agentVo : agentList) {
            String ip = agentVo.getIp();
            String role = agentVo.getRole();
            if (commonConfigMap.containsKey("suyuanConnection")) {
                if (suyuanConnection.containsKey("restServiceUrl") && role.equals("WEB")) {
                    String[] split = suyuanConnection.get("restServiceUrl").toString().split(":");
                    String newValue = split[0] + "://" + ip + ":" + split[2];
                    suyuanConnection.put("restServiceUrl", newValue);
                    commonConfigMap.put("suyuanConnection", suyuanConnection);
                }
            }
            if (commonConfigMap.containsKey("proConnection")) {
                if (proConnection.containsKey("fileUploadConnect") && role.equals("WEB")) {
                    getNewValue("fileUploadConnect", ip, proConnection);
                }
                if (proConnection.containsKey("mailUploadServiceConnect") && role.equals("WEB")) {
                    getNewValue("mailUploadServiceConnect", ip, proConnection);
                }
                if (proConnection.containsKey("esSearchServiceConnect") && role.equals("WEB")) {
                    getNewValue("esSearchServiceConnect", ip, proConnection);
                }
                if (proConnection.containsKey("redisServiceConnect") && role.equals("数据分析")) {
                    getNewValue("redisServiceConnect", ip, proConnection);
                }
                if (proConnection.containsKey("dynamicScanAnalysisConnect") && role.equals("动态分析")) {
                    getNewValue("dynamicScanAnalysisConnect", ip, proConnection);
                }
                if (proConnection.containsKey("dynamicDispatchServiceConnect") && role.equals("动态分析")) {
                    getNewValue("dynamicDispatchServiceConnect", ip, proConnection);
                }
            }
            if (commonConfigMap.containsKey("otherConnection")) {
                if (otherConnection.containsKey("analysisIp") && role.equals("动态分析")) {
                    getNewValue("analysisIp", ip, proConnection);
                }
            }
        }
        return commonConfigMap;
    }

    public void getNewValue(String str, String ip, LinkedHashMap<String, Object> proConnection) {
        Object fileUploadConnect = proConnection.get(str);
        if (fileUploadConnect != null) {
            String[] split = fileUploadConnect.toString().split(" ");
            split[2] = ip;
            StringJoiner stringJoiner = new StringJoiner(" ", "", "");
            for (String string : split) {
                stringJoiner.add(string);
            }
            proConnection.put(str, stringJoiner.toString());
        }
    }

    @Override
    public String getCommonConfigStr(String ip, Map<String, Map<String, Object>> applicationYamlMap) {
        String commonConfigPath = autoDeployService.getDeployRecordVo(new DeployRecordVo()).getConfigPath();
        CommonConfigVo newCommonConfigStr = readLocalCommonConfig(commonConfigPath, applicationYamlMap);
        String jsonStr = newCommonConfigStr.getJsonStr();
        Map<String, LinkedHashMap<String, Object>> commonConfigMap1 = newCommonConfigStr.getCommonConfigMap();
        // Map<String,LinkedHashMap<String,Object>> commonConfigMap = replaceRoleIp(commonConfigMap1);
        Map<String, LinkedHashMap<String, Object>> stringLinkedHashMapMap = updateServerIp(ip, commonConfigMap1);
        String commonConfigStr = useNewMapCreateNewJsonStr(ip, stringLinkedHashMapMap, jsonStr);
        return commonConfigStr;
    }

    @Override
    public void prepareCommonConfigForRemoteServer(String ip) {
        String commonConfigPath = autoDeployService.getDeployRecordVo(new DeployRecordVo()).getConfigPath();
        String commonConfigStr = getCommonConfigStr(ip, null);
        try {
            FileUtil.writeStringToFile(new File(commonConfigPath), commonConfigStr, "UTF-8", false);
        } catch (IOException e) {
            Log.high.error("准备公共配置文件异常", e);
        }
    }

    @Override
    public String closePlatform(WebSocket conn) {
        StringBuilder errorBuilder = new StringBuilder();
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setProgramStatus(ProgramStatusType.OPEN.getValue());
        List<SoftwareVo> softwareVoList = softwareService.getSoftwareInfos(criteria).getList();
        if (softwareVoList.isEmpty()) {
            sentText(conn, "所有程序均已关闭");
        } else {
            Map<String, List<SoftwareVo>> softwareMap = softwareVoList.stream()
                    .filter(s -> !StringUtils.isEmpty(s.getHost()))
                    .collect(Collectors.groupingBy(SoftwareVo::getHost));
            softwareMapper.batchUpdatePlatformFlag(softwareVoList.stream()
                    .map(SoftwareVo::getId).collect(Collectors.toList()), 1, ProgramStatusType.CLOSE.getValue());

            closeSoftware(conn, softwareMap, errorBuilder);
        }

        //开始关闭ES
        closeEs(conn, errorBuilder);

        List<AgentVo> allAgentList = agentMapper.getAgent(new AgentCriteria());
        //先关Agent
        closeAgent(conn, allAgentList, errorBuilder);
        //关机
        closeHardware(conn, allAgentList, errorBuilder);
        int i = 1;
        boolean stopped = false;
        while (!stopped) {
            sentText(conn, "休眠30S后检测服务器是否关闭成功");
            try {
                TimeUnit.SECONDS.sleep(30);
            } catch (InterruptedException e) {

            }
            //检查是否完成关机
            stopped = checkHardware(conn, allAgentList);
            i++;
            if (i > 6) {
                break;
            }
        }
        List<AgentVo> agentVos = allAgentList.stream()
                .filter(a -> a.getRole().contains("WEB") || a.getRole().contains("MySQL"))
                .collect(Collectors.toList());
        if (!agentVos.isEmpty()) {
            if (agentVos.size() == 1) {
                sentText(conn, "请手动关闭" + agentVos.get(0).getIp() + "上的MySQL服务，以及该机器");
            } else {
                agentVos.stream().filter(a -> a.getRole().contains("MySQL")).findAny().ifPresent(a -> sentText(conn, "请手动关闭" + a.getIp() + "上的MySQL服务，以及该机器"));
                agentVos.stream().filter(a -> a.getRole().contains("WEB")).findAny().ifPresent(a -> sentText(conn, "请手动关闭" + a.getIp() + "该机器"));
            }
        }
        if (errorBuilder.length() != 0) {
            sentText(conn, "关闭过程中出现了一些异常，请手动检查，异常信息如下：" + NEXT_LINE_SEPARATOR + errorBuilder.toString());
        }
        return "一键关闭完成";
    }

    private boolean checkHardware(WebSocket conn, List<AgentVo> allAgentList) {
        boolean stopped = true;
        for (AgentVo agent : allAgentList) {
            if (agent.getRole().toLowerCase().contains("web") || agent.getRole().toLowerCase().contains("mysql")) {
                continue;
            }
            try {
                BaseWebSocket.getSession(agent);
                sentText(conn, "服务器:" + agent.getIp() + "还未关闭完成");
                stopped = false;
            } catch (ServiceException e) {
                if (e.getMessage().contains("socket is not established")) {
                    sentText(conn, "服务器:" + agent.getIp() + "关闭完成");
                } else {
                    Log.high.error("服务器:" + agent.getIp() + "连接失败", e);
                    sentText(conn, "服务器:" + agent.getIp() + "连接失败，请检查");
                }
            }
        }
        return stopped;
    }

    public boolean closeProcess(WebSocket conn, String agentIp, String process) {
        try {
            if (checkProcessExist(conn, agentIp, process)) {
                int i = 1;
                sentText(conn, "开始关闭" + agentIp + "上" + process + "相关进程");
                while (!killProcess(conn, agentIp, process)) {
                    sentText(conn, "关闭" + agentIp + "上的" + process + "失败，重试第" + i + "次");
                    i++;
                    if (i > 3) {
                        sentText(conn, "关闭" + agentIp + "上的" + process + "失败,已重试3次，请手动关闭");
                        break;
                    }
                }
            } else {
                sentText(conn, agentIp + "上" + process + "相关进程已关闭!");
            }
        } catch (RuntimeException e) {
            return false;
        }
        return true;
    }

    private boolean killProcess(WebSocket conn, String agentIp, String process) {
        String processIds = execCommandInLinux(agentIp, "ps -ef | grep " + process + "|grep -v grep|awk '{print $2}'");
        if (processIds == null) {
            sentText(conn, "在" + agentIp + "上调用命令失败，请手动检查该机器是否正常!");
            throw new RuntimeException();
        }
        for (String processId : processIds.split("\r\n")) {
            if (StringUtils.isEmpty(processId)) {
                continue;
            }
            execCommandInLinux(agentIp, "kill -9 " + processId.trim());
        }
        return !checkProcessExist(conn, agentIp, process);
    }

    private boolean checkProcessExist(WebSocket conn, String agentIp, String process) {
        String processCountStr = execCommandInLinux(agentIp, "ps -ef | grep " + process + "|grep -v grep|wc -l");
        if (StringUtils.isEmpty(processCountStr)) {
            sentText(conn, "在" + agentIp + "上调用命令失败，请手动检查该机器是否正常!");
            throw new RuntimeException();
        }
        return Integer.parseInt(processCountStr.trim()) != 0;
    }

    private void closeSoftware(WebSocket conn, Map<String, List<SoftwareVo>> softwareMap, StringBuilder errorBuilder) {
        for (Map.Entry<String, List<SoftwareVo>> entry : softwareMap.entrySet()) {
            sentText(conn, "开始关闭" + entry.getKey() + "上的程序");
            for (SoftwareVo softwareVo : entry.getValue()) {
                Map<String, Object> param = new HashMap<>(8);
                param.put("operateType", OperateTypeEnum.CLOSE.getType());
                param.put("softwareId", softwareVo.getId());
                try {
                    softwareWebSocketService.operateSoftware(param, conn);
                } catch (ServiceException e) {
                    Log.high.error("关闭程序时异常", e);
                    //conn.send("关闭程序发生异常，异常信息：" +  e.getMessage());
                    errorBuilder.append("关闭程序发生异常，异常信息：").append(e.getMessage()).append(NEXT_LINE_SEPARATOR);
                }
            }
        }
    }

    public void closeEs(WebSocket conn, StringBuilder errorBuilder) {
        SoftwareCriteria softwareCriteria = new SoftwareCriteria();
        softwareCriteria.setFuzzyName("elasticsearch");
        List<SoftwareVo> softwareVos = softwareMapper.getSoftwareInfos(softwareCriteria);
        //添加了守护，直接由Agent进行关闭
        if (!softwareVos.isEmpty()) {
            return;
        }
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setRoleFuzzy("ES");
        List<AgentVo> esAgentList = agentMapper.getAgent(agentCriteria);
        if (esAgentList.isEmpty()) {
            sentText(conn, "当前平台无ES角色，请确认ES是否正常关闭");
            return;
        }
        sentText(conn, "开始关闭ES，共有" + esAgentList.size() + "台服务器");
        Log.low.info("开始关闭" + esAgentList.stream().map(AgentVo::getIp).collect(Collectors.joining(",")) + "上的ES程序");
        for (AgentVo esAgent : esAgentList) {
            try {
                closeProcess(conn, esAgent.getIp(), "org.elasticsearch.bootstrap.Elasticsearch");
            } catch (Exception e) {
                Log.high.error("关闭ES异常", e);
                //sentText(conn, "关闭" + esAgent.getIp() + "上的ES时出现异常,异常信息:" + e.getMessage());
                errorBuilder.append("关闭").append(esAgent.getIp()).append("上的ES时出现异常,异常信息:").append(e.getMessage()).append(NEXT_LINE_SEPARATOR);
            }
        }
        sentText(conn, "关闭ES完成");
    }

    private void closeAgent(WebSocket conn, List<AgentVo> allAgentList, StringBuilder errorBuilder) {
        sentText(conn, "开始关闭Agent程序");
        for (AgentVo agent : allAgentList) {
            Map<String, Object> param = new HashMap<>(8);
            param.put("id", agent.getId());
            try {
                agentWebSocketService.closeSoftware(JSON.toJSONString(param), conn);
                //Agent不存活时，需要建立SSH连接，需要做一个清除，不然此处一个线程循环多个机器，会导致所有的SSH连接都是第一个服务器
                ProcessUtil.destroy();
            } catch (Exception e) {
                Log.high.error("关闭" + agent.getIp() + "上的Agent出错", e);
                errorBuilder.append("关闭").append(agent.getIp()).append("上的Agent发生异常，异常信息：").append(e.getMessage()).append(NEXT_LINE_SEPARATOR);
            }
        }
    }

    public void closeHardware(WebSocket conn, List<AgentVo> allAgentList, StringBuilder errorBuilder) {
        sentText(conn, "开始关闭除WEB、MySQL之外的服务器");
        for (AgentVo agent : allAgentList) {
            if (agent.getRole().toLowerCase().contains("web") || agent.getRole().toLowerCase().contains("mysql")) {
                continue;
            }
            sentText(conn, "通过SSH连接" + agent.getIp() + "服务器");
            Session session = BaseWebSocket.getSession(agent);
            sentText(conn, "连接成功");
            try {
                ProcessUtil.initChannelShell(session, agent, 10000L);
                ProcessUtil.closeHardware(agent);
                ProcessUtil.destroy();
                //sendCloseCommand(session, agent, 10000L);
            } catch (Exception e) {
                Log.high.error("关闭服务器：" + agent.getIp() + "发生异常", e);
                errorBuilder.append("关闭服务器：").append(agent.getIp()).append("发生异常，异常信息:").append(e.getMessage()).append(NEXT_LINE_SEPARATOR);
            }
        }
    }

    private void sendCloseCommand(final Session session, AgentVo agentVo, Long delayTime) throws JSchException, IOException, InterruptedException {
        final ChannelShell shell = (ChannelShell) session.openChannel("shell");
        OutputStream outputStream = shell.getOutputStream();
        String charset = agentVo.getOs().equals(OsType.WINDOWS.getId()) ? "gbk" : "utf8";
        final AtomicBoolean atomicBoolean = new AtomicBoolean(false);
        shell.connect();
        final Thread thread = Thread.currentThread();

        if (!"root".equals(agentVo.getName()) && agentVo.getOs().equals(OsType.LINUX.getId())) {
            //切换root
            StringBuilder sb = new StringBuilder();
            sb.append("cd /home/").append(agentVo.getName()).append(" && echo -e '#!/usr/bin/expect").append("\\n")
                    .append("spawn su").append("\\n")
                    .append("expect {").append("\\n")
                    .append("\"密码\" {")
                    .append("send \"").append(agentVo.getRootPswd()).append("\\\\r\"}").append("\\n")
                    .append("\"Password\" {")
                    .append("send \"").append(agentVo.getRootPswd()).append("\\\\r\"}").append("\\n")
                    .append("\"password\" {")
                    .append("send \"").append(agentVo.getRootPswd()).append("\\\\r\"}").append("\\n")
                    .append("}").append("\\n")
                    .append("interact").append("' > change.sh && chmod 777 change.sh && ./change.sh \n");
            outputStream.write(sb.toString().getBytes(charset));
            outputStream.flush();
            Thread.sleep(500);
        }

        if (agentVo.getOs().equals(OsType.LINUX.getId())) {
            try {
                outputStream.write("shutdown -h now\n".getBytes(charset));
                Log.low.info("开始在" + agentVo.getIp() + "上执行关机命令:shutdown -h now");
                outputStream.flush();
                Thread.sleep(500);
            } catch (Exception e) {
                Log.high.error("在" + agentVo.getIp() + "上执行关机命令异常", e);
            }
        } else {
            try {
                outputStream.write("shutdown -s -t 2\n".getBytes(charset));
                Log.low.info("开始在" + agentVo.getIp() + "上执行关机命令:shutdown -s -t 2");
                outputStream.flush();
                Thread.sleep(500);
            } catch (Exception e) {
                Log.high.error("在" + agentVo.getIp() + "上执行关机命令异常", e);
            }
        }

    }

    @Override
    public String openPlatform(WebSocket conn) {
        //TODO 检测Agent是否启动成功，未启动成功，则先启动Agent或者通过SSH的方式调用命令
        StringBuilder errorBuilder = new StringBuilder();
        openAgent(conn, errorBuilder);
        //开启Cdh上的MySql
        openCdhMySql(conn, errorBuilder);
        //开启scm-server和scm-agent
        openServerAndAgent(conn, errorBuilder);
        //开启CmService
        //开启Es
        openEs(conn, errorBuilder);


        //将各个程序的守护开启
        openSoftware(conn, errorBuilder);
        if (errorBuilder.length() != 0) {
            sentText(conn, "开启过程中出现一些异常，需要手动检查，异常信息如下：" + NEXT_LINE_SEPARATOR + errorBuilder.toString());
        }
        return "一键开启完成";
    }

    private void openSoftware(WebSocket conn, StringBuilder errorBuilder) {
        try {
            Log.low.info("开始开启程序");
            SoftwareCriteria criteria = new SoftwareCriteria();
            criteria.setPlatformFlag(1);
            List<SoftwareVo> softwareVoList = softwareService.getSoftwareInfos(criteria).getList();
            if (softwareVoList != null && !softwareVoList.isEmpty()) {
                softwareMapper.batchUpdatePlatformFlag(softwareVoList.stream()
                                .map(SoftwareVo::getId).collect(Collectors.toList()), 0,
                        ProgramStatusType.OPEN.getValue());
                Log.low.info("程序状态更新完成");
                Map<String, List<SoftwareVo>> softwareMap = softwareVoList.stream()
                        .filter(s -> !StringUtils.isEmpty(s.getHost()))
                        .collect(Collectors.groupingBy(SoftwareVo::getHost));
                //通过一个一个地开启程序进行刷新守护状态
                openSoftware(conn, softwareMap);
            }
        } catch (Exception e) {
            Log.high.error("开启程序异常", e);
            errorBuilder.append("开启程序异常,异常信息：").append(e.getMessage()).append(NEXT_LINE_SEPARATOR);
        }
    }

    public void openServerAndAgent(WebSocket conn, StringBuilder errorBuilder) {
        try {
            AgentCriteria agentCriteria = new AgentCriteria();
            agentCriteria.setRoleFuzzy("CDH");
            List<AgentVo> agentVos = agentMapper.getAgent(agentCriteria);
            Optional<AgentVo> optional = agentVos.stream().filter(a -> a.getRole().contains("CDH-NN")).findAny();
            if (optional.isPresent()) {
                AgentVo agent = optional.get();
                if (checkFileExist(agent.getIp(), "/opt/cm-5.8.3/etc/init.d/cloudera-scm-server")) {
                    String status = execCommandInLinux(agent.getIp(), "/opt/cm-5.8.3/etc/init.d/cloudera-scm-server status");
                    if (StringUtils.isEmpty(status) || !status.contains("running")) {
                        execCommandInLinux(agent.getIp(), "/opt/cm-5.8.3/etc/init.d/cloudera-scm-server start");
                    }
                } else {
                    //sentText(conn, agent.getIp() + "上不存在/opt/cm-5.8.3/etc/init.d/cloudera-scm-server文件，请手动开启scm-server");
                    errorBuilder.append(agent.getIp()).append("上不存在/opt/cm-5.8.3/etc/init.d/cloudera-scm-server文件，请手动开启scm-server").append(NEXT_LINE_SEPARATOR);
                }
            }
            for (AgentVo agent : agentVos) {
                if (checkFileExist(agent.getIp(), "/opt/cm-5.8.3/etc/init.d/cloudera-scm-agent")) {
                    String status = execCommandInLinux(agent.getIp(), "/opt/cm-5.8.3/etc/init.d/cloudera-scm-agent status");
                    if (StringUtils.isEmpty(status) || !status.contains("running")) {
                        execCommandInLinux(agent.getIp(), "/opt/cm-5.8.3/etc/init.d/cloudera-scm-agent start");
                    }
                } else {
                    //sentText(conn, agent.getIp() + "上不存在/opt/cm-5.8.3/etc/init.d/cloudera-scm-agent文件，请手动开启scm-agent");
                    errorBuilder.append(agent.getIp()).append("上不存在/opt/cm-5.8.3/etc/init.d/cloudera-scm-agent文件，请手动开启scm-agent").append(NEXT_LINE_SEPARATOR);
                }
            }
        } catch (Exception e) {
            Log.high.error("开启scm-server和agent异常", e);
            //sentText(conn, "开启scm-server和agent异常，异常信息: " + e.getMessage());
            errorBuilder.append("开启scm-server和agent异常，异常信息: ").append(e.getMessage()).append(NEXT_LINE_SEPARATOR);
        }
    }

    public void openCdhMySql(WebSocket conn, StringBuilder errorBuilder) {
        try {
            AgentCriteria agentCriteria = new AgentCriteria();
            agentCriteria.setRoleFuzzy("CDH-NN");
            List<AgentVo> agentVos = agentMapper.getAgent(agentCriteria);
            if (agentVos.isEmpty()) {
                //throw new RuntimeException("不存在CDH-NN角色，CDH相关服务无法正常启动");
                errorBuilder.append("不存在CDH-NN角色，CDH相关服务无法正常启动")
                        .append(NEXT_LINE_SEPARATOR);
                return;
            }

            if (agentVos.size() != 1) {
                //throw new RuntimeException("存在多个CDH-NN角色，CDH相关服务无法正常启动");
                errorBuilder.append("存在多个CDH-NN角色，CDH相关服务无法正常启动")
                        .append(NEXT_LINE_SEPARATOR);
                return;
            }
            String result = execCommandInLinux(agentVos.get(0).getIp(), "service mysqld status");
            if (result.contains("running")) {
                sentText(conn, "MySQL正在运行");
                return;
            }
            sentText(conn, "MySQL当前状态为:" + result);
            result = execCommandInLinux(agentVos.get(0).getIp(), "service mysqld start");
            Log.low.info("启动MySQL的结果为：" + result);
            while (!result.contains("running")) {
                result = execCommandInLinux(agentVos.get(0).getIp(), "service mysqld status");
                try {
                    TimeUnit.SECONDS.sleep(5);
                } catch (InterruptedException e) {

                }
            }
        } catch (Exception e) {
            Log.high.error("启动CDH上的MySQL异常", e);
            //sentText(conn, "启动CDH上的MySQL异常，异常信息：" + e.getMessage());
            errorBuilder.append("启动CDH上的MySQL异常，异常信息：").append(e.getMessage()).append(NEXT_LINE_SEPARATOR);
        }
    }

    private void openAgent(WebSocket conn, StringBuilder errorBuilder) {
        try {
            List<AgentVo> allAgentList = agentMapper.getAgent(new AgentCriteria());
            sentText(conn, "开始开启Agent程序");
            for (AgentVo agent : allAgentList) {
                Map<String, Object> param = new HashMap<>(8);
                param.put("id", agent.getId());
                agentWebSocketService.openSoftware(JSON.toJSONString(param), conn);
                //TODO 此处的销毁不应该放到此处
                ProcessUtil.destroy();
            }
        } catch (Exception e) {
            Log.high.error("开启Agent异常", e);
            //sentText(conn, "开启Agent异常,异常信息:" + e.getMessage());
            errorBuilder.append("开启Agent异常,异常信息:").append(e.getMessage())
                    .append(NEXT_LINE_SEPARATOR);
        }
    }


    public void openEs(WebSocket conn, StringBuilder errorBuilder) {
        try {
            SoftwareCriteria softwareCriteria = new SoftwareCriteria();
            softwareCriteria.setFuzzyName("elasticsearch");
            List<SoftwareVo> softwareVos = softwareMapper.getSoftwareInfos(softwareCriteria);
            //添加了守护，直接由Agent进行关闭
            if (!softwareVos.isEmpty()) {
                return;
            }
            AgentCriteria agentCriteria = new AgentCriteria();
            sentText(conn, "开始开启ES");
            agentCriteria.setRoleFuzzy("ES");
            List<AgentVo> esAgentList = agentMapper.getAgent(agentCriteria);
            for (AgentVo esAgent : esAgentList) {
                if (!checkProcessExist(conn, esAgent.getIp(), "org.elasticsearch.bootstrap.Elasticsearch")) {
                    execCommandInLinux(esAgent.getIp(), "nohup sh /dist/elasticsearch-cloud-shield/deploy/start.sh >> /dev/null 2>&1 &");
                    TimeUnit.SECONDS.sleep(1);
                    execCommandInLinux(esAgent.getIp(), "nohup sh /dist/elasticsearch-cloud-shield-data/deploy/start.sh >> /dev/null 2>&1 &");
                    TimeUnit.SECONDS.sleep(1);
                    execCommandInLinux(esAgent.getIp(), "nohup sh /dist/elasticsearch-cloud-shield-data1/deploy/start.sh >> /dev/null 2>&1 &");
                    TimeUnit.SECONDS.sleep(1);
                    execCommandInLinux(esAgent.getIp(), "nohup sh /dist/elasticsearch-cloud-shield-data2/deploy/start.sh >> /dev/null 2>&1 &");
                    TimeUnit.SECONDS.sleep(1);
                }
            }
            sentText(conn, "开启ES完成");
        } catch (Exception e) {
            Log.high.error("开启ES失败", e);
            //sentText(conn, "开启ES失败，请手动开启");
            errorBuilder.append("开启ES失败，请手动开启");
        }
    }



    private void openSoftware(WebSocket conn, Map<String, List<SoftwareVo>> softwareMap) {
        for (Map.Entry<String, List<SoftwareVo>> entry : softwareMap.entrySet()) {
            sentText(conn, "开始开启" + entry.getKey() + "上的程序");
            for (SoftwareVo softwareVo : entry.getValue()) {
                Map<String, Object> param = new HashMap<>(8);
                param.put("operateType", OperateTypeEnum.OPEN.getType());
                param.put("softwareId", softwareVo.getId());
                try {
                    softwareWebSocketService.operateSoftware(param, conn);
                } catch (ServiceException e) {
                    Log.high.error("开启程序" + softwareVo.getName() + "时异常", e);
                    conn.send(e.getMessage());
                }
            }
        }
    }

    private String execCommandInLinux(String ip, String command) {
        String result = ProcessUtil.iceRequest(ip, IceFlag.EXEC_COMMAND_IN_LINUX, command);
        System.out.println("执行命令{" + command + "}，结果为：" + result);
        if (StringUtils.isEmpty(result)) {
            throw new ServiceException("在" + ip + "上执行命令:{" + command + "}异常");
        }
        LinuxCommandResponse resp = JSON.parseObject(result, LinuxCommandResponse.class);
        if (resp.getCode() != 0) {
            throw new ServiceException("在" + ip + "上执行命令:{" + command + "}失败，失败信息:" + result);
        }
        if (resp.getData() == null || resp.getData().isEmpty()) {
            return "";
        }
        return resp.getData().get(0);
    }

    private boolean checkFileExist(String ip, String file) {
        String result = execCommandInLinux(ip, "ls " + file + " | wc -l");
        if (StringUtils.isEmpty(result)) {
            throw new RuntimeException("在" + ip + "上检查文件是否存在报错");
        }
        return Integer.parseInt(result.trim()) != 0;
    }

}
