package com.maintain.server.service.impl;

import com.maintain.server.mapper.PlatformRoleMapper;
import com.maintain.server.service.PlatformRoleService;
import com.maintain.server.utils.ListUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-02
 */
@Service
@Transactional
public class PlatformRoleServiceImpl implements PlatformRoleService {

    @Autowired
    private PlatformRoleMapper platformRoleMapper;

    @Override
    public void addPlatformRole(List<String> names) {
        if (ListUtil.isEmpty(names)) {
            return;
        }
        platformRoleMapper.addPlatformRole(names);
    }

    @Override
    public void addPlatformRole(String name) {
        if (StringUtils.isEmpty(name)) {
            return;
        }
        List<String> names = new ArrayList<>();
        names.add(name);
        this.addPlatformRole(names);
    }

    @Override
    public List<String> getPlatformRoles() {
        return platformRoleMapper.getPlatformRoles();
    }
}