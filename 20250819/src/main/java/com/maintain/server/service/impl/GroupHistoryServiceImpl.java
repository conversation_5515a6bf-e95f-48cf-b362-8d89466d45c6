package com.maintain.server.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.BaseCriteria;
import com.maintain.server.criteria.GroupCriteria;
import com.maintain.server.mapper.GroupMapper;
import com.maintain.server.service.GroupHistoryService;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.GroupHistoryHealthVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Service
public class GroupHistoryServiceImpl implements GroupHistoryService {

    @Autowired
    private GroupMapper groupMapper;

    @Override
    @Transactional
    public void addGroupHistoryHealth(List<GroupHistoryHealthVo> list) {
        groupMapper.addGroupHistoryHealth(list);
    }

    @Override
    public PageInfo<GroupHistoryHealthVo> getGroupHistoryHealth(GroupCriteria criteria) {
        if (criteria != null) {
            if (criteria.getPn() != null && criteria.getPs() != null) {
                PageHelper.startPage(criteria.getPn(), criteria.getPs());
            }
        }
        List<GroupHistoryHealthVo> list = groupMapper.getGroupHistoryHealth(criteria);
        if (ListUtil.isNotEmpty(list)) {
            return new PageInfo<>(list);
        }
        return null;
    }

    @Override
    @Transactional
    public void clearHistoryData(BaseCriteria criteria) {
        groupMapper.clearHistoryData(criteria);
    }
}