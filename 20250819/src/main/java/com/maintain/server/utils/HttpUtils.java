package com.maintain.server.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.common.log.Log;
import com.maintain.server.Constants;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public final class HttpUtils {



    private HttpUtils() {
    }

    public static void main(String[] args) throws IOException {
    }

    public static String getCmHttpSession(String urlStr,String username,String password) throws IOException {
        final CloseableHttpClient client = HttpClients.custom().setConnectionTimeToLive(60000, TimeUnit.MILLISECONDS).build();
        final HttpPost httpPost = new HttpPost(urlStr);
        List<BasicNameValuePair> list = new ArrayList<>();
        list.add(new BasicNameValuePair("j_username",username));
        list.add(new BasicNameValuePair("j_password",password));
        httpPost.setEntity(new UrlEncodedFormEntity(list));
        final CloseableHttpResponse response = client.execute(httpPost);
        final Header[] headers = response.getHeaders("Set-Cookie");
        for (Header header : headers) {
            return header.getValue().split(";")[0];
        }
        return null;
    }

    public static String httpRequest(String urlStr, Map<String, String> header) {
        HttpURLConnection httpConn = null;
        String res = null;
        try {
            URL url = new URL(urlStr);
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod(Constants.HTTP_GET);
            if (header != null) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpConn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            httpConn.setDoInput(true);
            httpConn.setDoOutput(true);
            httpConn.setConnectTimeout(Constants.CONNECT_TIMEOUT);
            httpConn.setReadTimeout(Constants.CONNECT_TIMEOUT);
            // 获得响应状态
            int responseCode = httpConn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode) {
                res = readData(httpConn);
            } else {
                Log.low.info(urlStr + " Response Code:" + responseCode);
            }
        } catch (Exception ex) {
            Log.low.warn(urlStr, ex);
        } finally {
            if (null != httpConn) {
                httpConn.disconnect();
            }
        }
        return res;
    }

    public static String httpRequestUsePost(String urlStr, Map<String, String> header) {
        HttpURLConnection httpConn = null;
        String res = null;
        try {
            URL url = new URL(urlStr);
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod(Constants.HTTP_POST);
            if (header != null) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpConn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            httpConn.setDoInput(true);
            httpConn.setDoOutput(true);
            httpConn.setConnectTimeout(Constants.CONNECT_TIMEOUT);
            httpConn.setReadTimeout(Constants.CONNECT_TIMEOUT);
            // 获得响应状态
            int responseCode = httpConn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode) {
                res = readData(httpConn);
            } else {
                Log.low.info(urlStr + " Response Code:" + responseCode);
            }
        } catch (Exception ex) {
            Log.low.warn(urlStr, ex);
        } finally {
            if (null != httpConn) {
                httpConn.disconnect();
            }
        }
        return res;
    }

    public static String httpRequest(String urlStr, Map<String, String> params, String charSet) {
        HttpURLConnection httpConn = null;
        String res = null;
        try {
            URL url = new URL(urlStr);
            httpConn = (HttpURLConnection) url.openConnection();
            if (null == params) {
                httpConn.setRequestMethod(Constants.HTTP_GET);
            } else {
                httpConn.setRequestMethod(Constants.HTTP_POST);
            }
            httpConn.setRequestProperty("Connection", "keep-alive");
            httpConn.setRequestProperty("ContentType",
                    "application/x-www-form-urlencoded");
            httpConn.setDoInput(true);
            httpConn.setDoOutput(true);
            httpConn.setConnectTimeout(Constants.CONNECT_TIMEOUT);// jdk 1.5换成这个,连接超时
            httpConn.setReadTimeout(Constants.CONNECT_TIMEOUT);// jdk 1.5换成这个,读操作超时
            httpConn.connect();
            // writeData(params, charSet, httpConn);
            // 获得响应状态
            int responseCode = httpConn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode) {
                res = readData(httpConn);
            } else {
                Log.low.info(urlStr + " Response Code:" + responseCode);
            }
        } catch (Exception ex) {
            Log.high.error(urlStr, ex);
        } finally {
            if (null != httpConn) {
                httpConn.disconnect();
            }
        }
        return res;
    }

    /**
     *
     * @param urlStr
     * @param header  请求头
     * @param params  请求参数
     * @return
     */
    public static String httpRequest(String urlStr, Map<String, String> header, JSONObject params) {
        HttpURLConnection httpConn = null;
        String res = null;
        try {
            URL url = new URL(urlStr);
            httpConn = (HttpURLConnection) url.openConnection();
            if (null == params) {
                httpConn.setRequestMethod(Constants.HTTP_GET);
            } else {
                httpConn.setRequestMethod(Constants.HTTP_POST);
            }
            if (header != null) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpConn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            httpConn.setDoInput(true);
            httpConn.setDoOutput(true);
            httpConn.setConnectTimeout(Constants.CONNECT_TIMEOUT);// jdk 1.5换成这个,连接超时
            httpConn.setReadTimeout(Constants.CONNECT_TIMEOUT);// jdk 1.5换成这个,读操作超时
            httpConn.connect();
            //得到请求的输入流对象
            /*OutputStreamWriter writer = new OutputStreamWriter(httpConn.getOutputStream(), "UTF-8");
            writer.write(params.toJSONString());
            writer.flush();*/
            // 获得响应状态
            int responseCode = httpConn.getResponseCode();
            if (HttpURLConnection.HTTP_OK == responseCode) {
                res = readData(httpConn);
            } else {
                Log.low.info(urlStr + " Response Code:" + responseCode);
            }
        } catch (Exception ex) {
            Log.high.error(urlStr, ex);
        } finally {
            if (null != httpConn) {
                httpConn.disconnect();
            }
        }
        return res;
    }

    private static String readData(HttpURLConnection httpConn) throws IOException {
        InputStream is = httpConn.getInputStream();
        //指定编码TODO
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
        StringBuilder sb = new StringBuilder();
        String str;
        while ((str = reader.readLine()) != null) {
            sb.append(str).append("\n");
        }
        return sb.toString();
    }

    private static void writeData(Map<String, String> params, String charSet, HttpURLConnection httpConn) throws IOException {
        if (null != params) {
            byte[] data = null;
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            String param = sb.deleteCharAt(sb.lastIndexOf("&")).toString();
            if (!StringUtils.isEmpty(charSet)) {
                data = param.getBytes(charSet);
            } else {
                data = param.getBytes();
            }
            OutputStream os1 = httpConn.getOutputStream();
            os1.write(data);
            os1.flush();
            os1.close();
        }
    }

    //获取token
    public static String getToken(String systemUrl, Map<String, String> up) throws JSONException {
        String tpStr = JSON.toJSONString(up);
        String tokenStr = HttpUtil.post(systemUrl + "/login/",tpStr);
        JSONObject jsonTokenStr = JSON.parseObject(tokenStr);
        String accessToken = jsonTokenStr.getString("token");
        return accessToken;
    }

    public static String sendFile(String url, /*JSONObject jsonObject,*/ String token, File file) {
        MultipartEntityBuilder reqEntity = MultipartEntityBuilder.create();

        try {
            reqEntity.addBinaryBody("file", new FileInputStream(file), ContentType.DEFAULT_BINARY, file.getName());
            /*Iterator iter = jsonObject.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry entry = (Map.Entry) iter.next();
                StringBody value = new StringBody(entry.getValue().toString(), ContentType.create("text/plain", Consts.UTF_8));
                reqEntity.addPart(entry.getKey().toString(),value);
            }*/
            org.apache.http.HttpEntity httpEntity = reqEntity.build();
            HttpPost httppost = new HttpPost(url);
            httppost.setEntity(httpEntity);
            setHttpHeader(httppost, token);

            RequestConfig config = RequestConfig.custom()
                    .setConnectTimeout(1000)
                    .setConnectionRequestTimeout(1000)
                    .setSocketTimeout(10 *1000)
                    .build();
            //数据传输的超时时间
            httppost.setConfig(config);
            String result = getPostResult(httppost);
            Log.low.info(result);
            return result;
        } catch (Exception e) {
            Log.low.error(e);
            return "";
        }
    }

    private static void setHttpHeader(HttpPost httppost, String token) {
        httppost.setHeader("token", token);
    }
    /**
     * 获取post请求返回结果
     *
     * @param httppost
     * @return
     */
    private static String getPostResult(HttpPost httppost) {
        String result = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try (CloseableHttpResponse response = httpClient.execute(httppost);) {
            org.apache.http.HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity);
            if (result.contains("Invalid token")) {
                result = "Token 过期,请重新登录";
            }
        } catch (Exception e) {
            Log.low.error("HTTP请求异常,请重试",e);
            result = "HTTP请求异常,请重试";
        }
        return result;
    }

    public static String getCodeHttpRequest(String urlStr, Map<String, String> header) {
        HttpURLConnection httpConn = null;
        String responseCode = null;
        try {
            URL url = new URL(urlStr);
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod(Constants.HTTP_GET);
            if (header != null) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpConn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            httpConn.setDoInput(true);
            httpConn.setDoOutput(true);
            httpConn.setConnectTimeout(Constants.CONNECT_TIMEOUT);
            httpConn.setReadTimeout(Constants.CONNECT_TIMEOUT);
            // 获得响应状态
            responseCode = String.valueOf(httpConn.getResponseCode());
            return responseCode;
        } catch (Exception ex) {
            Log.low.warn(urlStr, ex);
        } finally {
            if (null != httpConn) {
                httpConn.disconnect();
            }
        }
        return responseCode;
    }
}
