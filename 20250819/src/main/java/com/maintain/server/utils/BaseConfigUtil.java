package com.maintain.server.utils;

import com.common.log.Log;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maintain.server.Constants;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.type.OsType;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-12-05
 */
public class BaseConfigUtil {

    public static HashMap<String,String> moduleMap;

    public static HashMap<String,String> platformMap;

    public static HashMap<String,HashMap<String,String>> softwareMap;

    //public static HashMap<String,Object> commonConfigMap;

    public static HashMap<String,String> tokenMap;

    public static Map<String,Map<String,Object>> applicationYamlMap;

    private BaseConfigUtil() {
    }

    public static void init() {
        //创建部署包目录
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        String packagePath = "";
        if (Constants.LINUX.equals(osId)) {
            packagePath = Constants.MAINTAIN_LINUX_TEMP + "FrontUpgradePackage/";
        } else if (Constants.WINDOWS.equals(osId)) {
            packagePath = Constants.MAINTAIN_WINDOWS_TEMP + "FrontUpgradePackage\\";
        }
        File file = new File(packagePath);
        if (!file.exists()){
            file.mkdirs();
        }
        try {
            platformMap = parserPlatformInfo();
            moduleMap = parserModuleInfo();
            softwareMap = parserSoftwareInfo();
            //commonConfigMap = parserCommonConfigInfo();
            tokenMap = new LinkedHashMap<>();
            applicationYamlMap = new LinkedHashMap<>();
        }catch (Exception e){
            Log.low.error("初始化文件解析失败： " + e);
            System.exit(-1);
        }
        try {
            //在这修改yml的配置文件
            modifyConfigFile();
        } catch (IOException e) {
            Log.high.error("modify yml file has error", e);
            System.exit(-1);
        }
    }

    /**
     * 根据执行的xlsx文件修改本地yml配置文件
     *
     * @throws JsonProcessingException
     */
    private static void modifyConfigFile() throws IOException,ServiceException {
        String filepath = "./config/application.yml";
        Yaml yaml = new Yaml();
        Map map = yaml.loadAs(new FileInputStream(new File(filepath)), Map.class);
        boolean autoConfig = Boolean.parseBoolean(map.get("autoConfig").toString());
        Map<String,Object> es = (Map<String, Object>) map.get("es");
        Map<String,Object> spring = (Map<String, Object>) map.get("spring");
        Map<String,Object> kafka = (Map<String, Object>) map.get("kafka");
        Map<String,Object> cm = (Map<String, Object>) map.get("cm");
        Map<String,Object> codis = (Map<String, Object>) map.get("codis");
        applicationYamlMap.put("es",es);
        applicationYamlMap.put("spring",spring);
        applicationYamlMap.put("kafka",kafka);
        applicationYamlMap.put("cm",cm);
        applicationYamlMap.put("codis",codis);
        if (!autoConfig) {
            return;
        }
        /*String osName = ProcessUtil.getOsName();
        if (StringUtils.isEmpty(osName)) {
            throw new ServiceException("未知操作系统");
        }
        Integer osId = OsType.getId(osName);
        String localPath;
        if (LINUX.equals(osId)) {
            localPath = "/dist/MAINTAIN_TEMP/platform_role_relation.xlsx";
        } else if (WINDOWS.equals(osId)) {
            localPath = "D:\\MAINTAIN_TEMP\\platform_role_relation.xlsx";
        } else {
            Log.high.error("未知操作系统:" + osName);
            return;
        }
        File file = new File(localPath);
        if (!file.exists()) {
            Log.high.error(file.getAbsolutePath() + " 不存在");
            return;
        }
        Map<Integer, List<String>> excelData = ExcelUtil.parseExcel(file);
        Map<String, String> secondColumnMap = parsePlatformRoleRelationXlsx(excelData);
        for (List<String> list : excelData.values()) {
            if (list.size() <= 2) {
                continue;
            }
            String tool = list.get(2).toLowerCase();
            if (tool.contains("es")) {
                if (list.size() != 5) {
                    continue;
                }
                String keyString;
                if ("es1".equals(tool)) {
                    keyString = "firstes";
                } else {
                    keyString = "secondes";
                }
                Map<String, Object> esInfoMap = (Map<String, Object>) map.get("es");
                Map<String, Object> esMap = (Map<String, Object>) esInfoMap.get(keyString);
                String clusterInfo = list.get(3);
                String monitor = list.get(4);
                if (StringUtils.isEmpty(clusterInfo) || clusterInfo.split("/").length != 2) {
                    esMap.put("clusterName", null);
                    esMap.put("security", null);
                } else {
                    esMap.put("clusterName", clusterInfo.split("/")[0]);
                    esMap.put("security", clusterInfo.split("/")[1]);
                }
                if (StringUtils.isEmpty(monitor) || monitor.length() == 1) {
                    esMap.put("monitor", null);
                } else {
                    esMap.put("monitor", monitor);
                }
                StringBuilder tempUrl = new StringBuilder();
                List<String> esKeyList = secondColumnMap.keySet().stream()
                        .filter(k -> k.contains(tool)).collect(Collectors.toList());
                for (String key : esKeyList) {
                    String ip = secondColumnMap.get(key);
                    tempUrl.append(ip).append(":9300").append(",");
                }
                if (tempUrl.length() > 1) {
                    tempUrl.deleteCharAt(tempUrl.length() - 1);
                }
                esMap.put("url", tempUrl.toString());
                if (StringUtils.isEmpty(esMap.get("url").toString())) {
                    esMap.clear();
                }
            } else if ("codis".equals(tool)) {
                if (list.size() != 5) {
                    continue;
                }
                String pswd = list.get(3);
                String monitor = list.get(4);
                Map<String, Object> codisMap = (Map<String, Object>) map.get("codis");
                if (pswd.startsWith("/")) {
                    pswd = pswd.substring(1);
                }
                codisMap.put("pswd", pswd);
                codisMap.put("monitor", monitor);
                StringBuilder zkUrl = new StringBuilder();
                StringBuilder proxyUrl = new StringBuilder();
                List<String> keyList = secondColumnMap.keySet().stream()
                        .filter(k -> k.contains("zk")).collect(Collectors.toList());
                for (String key : keyList) {
                    String ip = secondColumnMap.get(key);
                    zkUrl.append(ip)
                            .append(":2181,");
                    proxyUrl.append(ip).append(":11080,");
                }
                String dashBoardUrl = null;
                if (zkUrl.length() > 1) {
                    zkUrl.deleteCharAt(zkUrl.length() - 1);
                    proxyUrl.deleteCharAt(proxyUrl.length() - 1);
                    dashBoardUrl = proxyUrl.toString().split(",")[0].split(":")[0] + ":18080";
                }
                codisMap.put("zkUrl", zkUrl.toString());
                codisMap.put("proxyAdminUrl", proxyUrl.toString());
                codisMap.put("dashboardUrl", dashBoardUrl);
            } else if ("cm".equals(tool)) {
                if (list.size() != 5) {
                    continue;
                }
                String security = list.get(3);
                String monitor = list.get(4);
                Map<String, String> cmMap = (Map<String, String>) map.get("cm");
                if (StringUtils.isNotEmpty(security)) {
                    cmMap.put("userName", security.split("/")[0]);
                    cmMap.put("pswd", security.split("/")[1]);
                }

                cmMap.put("monitor", monitor);
            } else if (tool.equals("mysql2")) {
                String security = list.get(3);
                Optional<String> ip = secondColumnMap.keySet().stream().map(String::toLowerCase)
                        .filter(k -> k.contains(tool)).findFirst();
                Map<String, Object> springMap = (Map<String, Object>) map.get("spring");
                Map<String, Object> datasourceMap = (Map<String, Object>) springMap.get("datasource");
                Map<String, Object> druidMap = (Map<String, Object>) datasourceMap.get("druid");
                if (StringUtils.isNotEmpty(security) && security.length() > 1) {
                    druidMap.put("username", security.split("/")[0]);
                    druidMap.put("password", security.split("/")[1]);
                }
                String url = ((Map)druidMap.get("maintain")).get("url").toString();
                if (ip.isPresent()) {
                    String start = "jdbc:mysql://";
                    int index = url.indexOf("/", start.length());
                    url = start + secondColumnMap.get(ip.get()) + ":3306" + url.subSequence(index, url.length());
                }
                druidMap.put("url", url);
            }
        }*/
        /*if (secondColumnMap.containsKey("web")) {
            String webIp = secondColumnMap.get("web");
            Map<String, Object> serverMap = (Map<String, Object>) map.get("server");
            serverMap.put("localHost", webIp);
            serverMap.put("port", 8085);
            serverMap.put("connection-timeout", 300000);
            Map<String, Object> servletMap = new HashMap<>();
            Map<String, Object> sessionMap = new HashMap<>();
            sessionMap.put("timeout", 86400); // 一天
            servletMap.put("session", sessionMap);
            servletMap.put("context-path", "/maintain");
            serverMap.put("servlet", servletMap);
            modifyIcepoolFile(webIp);
        }*/

        //用运维系统配置，初始化公共配置文件
 /*       Map<String,Object> es = (Map<String, Object>) map.get("es");
        if (commonConfigMap.containsKey("prefix")){
            commonConfigMap.put("prefix",es.get("prefix"));
        }
        if (commonConfigMap.containsKey("localAddress")){
            commonConfigMap.put("localAddress","***各角色机的本机IP***");
        }
        Map<String,Object> spring = (Map<String, Object>) map.get("spring");
        Map<String,Object> datasource = (Map<String, Object>) spring.get("datasource");
        Map<String, Object> druid = (Map<String, Object>) datasource.get("druid");
        Map<String, Object> guard = (Map<String, Object>) druid.get("guard");
        Object url = guard.get("url");
        if (url != null){
            String[] mysqlIpArr = url.toString().split("/");
            if (mysqlIpArr.length > 2){
                String mysqlIp = mysqlIpArr[2];
                if (commonConfigMap.containsKey("mysqlDbConnection")){
                    Map<String,Object> mysqlDbConnection = (Map<String, Object>) commonConfigMap.get("mysqlDbConnection");
                    if (mysqlDbConnection.containsKey("jdbcUrl")){
                        Object jdbcUrl = mysqlDbConnection.get("jdbcUrl");
                        if (jdbcUrl != null){
                            String[] jdbcUrlArr = jdbcUrl.toString().split("/");
                            if (jdbcUrlArr.length > 3){
                                String newJdbcUrl = jdbcUrlArr[0] + "//" + mysqlIp + "/" + jdbcUrlArr[3];
                                mysqlDbConnection.put("jdbcUrl",newJdbcUrl);
                            }
                        }
                    }
                    if (mysqlDbConnection.containsKey("userName")){
                        mysqlDbConnection.put("userName",guard.get("username"));
                    }
                    if (mysqlDbConnection.containsKey("password")){
                        mysqlDbConnection.put("password",guard.get("password"));
                    }
                    commonConfigMap.put("mysqlDbConnection",mysqlDbConnection);
                }
                if (commonConfigMap.containsKey("permissionDbConnection")){
                    Map<String,Object> permissionDbConnection = (Map<String, Object>) commonConfigMap.get("permissionDbConnection");
                    if (permissionDbConnection.containsKey("jdbcUrl")){
                        Object jdbcUrl1 = permissionDbConnection.get("jdbcUrl");
                        if (jdbcUrl1 != null){
                            String[] jdbcUrlArr1 = jdbcUrl1.toString().split("/");
                            if (jdbcUrlArr1.length > 3){
                                String newJdbcUrl1 = jdbcUrlArr1[0] + "//" + mysqlIp + "/" + jdbcUrlArr1[3];
                                permissionDbConnection.put("jdbcUrl",newJdbcUrl1);
                            }
                        }
                    }
                    if (permissionDbConnection.containsKey("userName")){
                        permissionDbConnection.put("userName",guard.get("username"));
                    }
                    if (permissionDbConnection.containsKey("password")){
                        permissionDbConnection.put("password",guard.get("password"));
                    }
                    commonConfigMap.put("permissionDbConnection",permissionDbConnection);
                }
            }
        }
        Map<String,Object> kafka = (Map<String, Object>) map.get("kafka");
        Object kafkaZookeeperConnStr = kafka.get("kafkaZookeeperConnStr");
        if (commonConfigMap.containsKey("hdfsConnection")){
            Map<String,Object> hdfsConnection = (Map<String, Object>) commonConfigMap.get("hdfsConnection");
            if (kafkaZookeeperConnStr != null){
                String[] split = kafkaZookeeperConnStr.toString().split(",");
                if (split.length > 1){
                    if (hdfsConnection.containsKey("dfs.namenode.rpc-address.hadoop-cluster.nn1")){
                        hdfsConnection.put("dfs.namenode.rpc-address.hadoop-cluster.nn1",split[0]);
                    }
                    if (hdfsConnection.containsKey("dfs.namenode.rpc-address.hadoop-cluster.nn2")){
                        hdfsConnection.put("dfs.namenode.rpc-address.hadoop-cluster.nn2",split[1]);
                    }
                }
            }
            commonConfigMap.put("hdfsConnection",hdfsConnection);
        }
        if (commonConfigMap.containsKey("proConnection")){
            Map<String,Object> proConnection = (Map<String, Object>) commonConfigMap.get("proConnection");
            String[] strArr = new String[]{"fileUploadConnect","mailUploadServiceConnect","esServiceConnect","hbaseServiceConnect",
                    "kafkaServiceConnect","redisServiceConnect","dynamicDispatchServiceConnect","dynamicScanAnalysisConnect"};
            String[] replaceStr = new String[]{"WEB角色机IP","WEB角色机IP","各角色机的本机IP","各角色机的本机IP",
                    "各角色机的本机IP","数据分析角色机IP","动态分析机IP","动态分析机IP"};
            for (int i = 0; i < strArr.length; i++) {
                if (proConnection.containsKey(strArr[i])){
                    Object fileUploadConnect = proConnection.get(strArr[i]);
                    if (fileUploadConnect != null){
                        String[] split = fileUploadConnect.toString().split(" ");
                        split[2] = "***" + replaceStr[i] + "***";
                        StringJoiner stringJoiner = new StringJoiner(" ","","");
                        for (String string:split){
                            stringJoiner.add(string);
                        }
                        proConnection.put(strArr[i],stringJoiner.toString());
                    }
                }
            }
            commonConfigMap.put("proConnection",proConnection);
        }
        if (commonConfigMap.containsKey("otherConnection")){
            Map<String,Object> otherConnection = (Map<String, Object>) commonConfigMap.get("otherConnection");
            Map<String,Object> codis = (Map<String, Object>) map.get("codis");
            Object pswd = codis.get("pswd");
            if (pswd != null){
                if (otherConnection.containsKey("codisPwd")){
                    otherConnection.put("codisPwd",pswd.toString());
                }
            }
            if (otherConnection.containsKey("analysisIp")){
                otherConnection.put("analysisIp","***动态分析机IP***");
            }
            if (kafkaZookeeperConnStr != null){
                String[] splitArr = kafkaZookeeperConnStr.toString().split(",");
                int port = 0;
                StringJoiner stringJoiner = new StringJoiner(",","","");
                if (splitArr.length > 1){
                    for (String s : splitArr){
                        String[] splitArr2 = s.split(":");
                        stringJoiner.add(splitArr2[0]);
                    }
                    port = Integer.valueOf(splitArr[0].split(":")[1]);
                }
                if (otherConnection.containsKey("zookeeper")){
                    otherConnection.put("zookeeper",stringJoiner.toString());
                }
                if (otherConnection.containsKey("zkPort")){
                    otherConnection.put("zkPort",port);
                }
            }
            Map<String,Object> cm = (Map<String, Object>) map.get("cm");
            Object monitor = cm.get("monitor");
            if (monitor != null){
                if (otherConnection.containsKey("hdfsHostsUrl")){
                    String str = "http://" + monitor.toString() + "/api/v13/hosts";
                    otherConnection.put("hdfsHostsUrl",str);
                }
            }
            Object userName = cm.get("userName");
            if (userName != null){
                if (otherConnection.containsKey("hdfsHostsUser")){
                    otherConnection.put("hdfsHostsUser",userName.toString());
                }
            }
            Object pswd1 = cm.get("pswd");
            if (pswd1 != null){
                if (otherConnection.containsKey("hdfsHostsPassword")){
                    otherConnection.put("hdfsHostsPassword",pswd1.toString());
                }
            }
            Object kafkaBrokerConnStr = kafka.get("kafkaBrokerConnStr");
            if (kafkaBrokerConnStr != null){
                if (otherConnection.containsKey("kafkaBrokerConnStr")){
                    otherConnection.put("kafkaBrokerConnStr",kafkaBrokerConnStr.toString());
                }
            }
            if (kafkaZookeeperConnStr != null){
                if (otherConnection.containsKey("kafkaZookeeperConnStr")){
                    otherConnection.put("kafkaZookeeperConnStr",kafkaZookeeperConnStr.toString());
                }
            }
            commonConfigMap.put("otherConnection",otherConnection);
        }
        if (commonConfigMap.containsKey("esConnection")){
            Map<String,Object> esConnection = (Map<String, Object>) commonConfigMap.get("esConnection");
            Map<String,Object> firstes = (Map<String, Object>) es.get("firstes");
            if (esConnection.containsKey("indexClusterName")){
                esConnection.put("indexClusterName",firstes.get("clusterName"));
            }
            if (esConnection.containsKey("indexClusterServer")){
                esConnection.put("indexClusterServer",firstes.get("url"));
            }
            if (esConnection.containsKey("indexClusterServerHttp")){
                esConnection.put("indexClusterServerHttp",firstes.get("httpurl"));
            }
            Object security = firstes.get("security");
            if (security != null){
                String[] securities = security.toString().split(":");
                if (securities.length > 1){
                    if (esConnection.containsKey("indexUserName")){
                        esConnection.put("indexUserName",securities[0]);
                    }
                    if (esConnection.containsKey("indexPassword")){
                        esConnection.put("indexPassword",securities[1]);
                    }
                }
            }
            Map<String,Object> secondes = (Map<String, Object>) es.get("secondes");
            if (esConnection.containsKey("ipdnsClusterName")){
                esConnection.put("ipdnsClusterName",secondes.get("clusterName"));
            }
            if (esConnection.containsKey("ipdnsClusterServer")){
                esConnection.put("ipdnsClusterServer",secondes.get("url"));
            }
            if (esConnection.containsKey("ipdnsClusterServerHttp")){
                esConnection.put("ipdnsClusterServerHttp",secondes.get("httpurl"));
            }
            Object security1 = secondes.get("security");
            if (security1 != null){
                String[] securities1 = secondes.get("security").toString().split(":");
                if (securities1.length > 1){
                    if (esConnection.containsKey("ipdnsUserName")){
                        esConnection.put("ipdnsUserName",securities1[0]);
                    }
                    if (esConnection.containsKey("ipdnsPassword")){
                        esConnection.put("ipdnsPassword",securities1[1]);
                    }
                }
            }
            commonConfigMap.put("esConnection",esConnection);
        }
        String jsonStr = JSONObject.toJSONString(commonConfigMap);
        File file = new File(Constants.COMMON_CONFIG_PATH);
        FileUtil.writeStringToFile(file,jsonStr,"UTF-8",false);*/


        //修改运维系统icepool.conf文件ip
        Map<String,Object> server = (Map<String, Object>) map.get("server");
        if (server != null){
            String localHost = server.get("localHost").toString();
            modifyIcepoolFile(localHost);
        }
        map.put("autoConfig", false);
        yaml.dump(map, new FileWriter(new File(filepath)));
    }

    /**
     * 修改icepool文件的IP
     *
     * @param ip ip
     */
    private static void modifyIcepoolFile(String ip) {
        File file = new File("./deploy/icepool.conf");
        if (!file.exists()) {
            return;
        }
        StringBuilder content = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            String s;
            while ((s = br.readLine()) != null) {
                content.append(s).append("\r\n");
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(),e);
        }
        String start = "Endpoints=tcp -h";
        String end = "-p 60000";
        int startIndex = content.indexOf(start);
        int endIndex = content.indexOf(end);
        content.replace(startIndex + start.length() + 1, endIndex - 1, ip);
    }

    private static Map<String, String> parsePlatformRoleRelationXlsx(Map<Integer, List<String>> excelData) {
        //删除第0行
        if (excelData == null) {
            throw new ServiceException("平台角色关系表解析异常");
        } else {
            excelData.remove(0);
        }
        Map<String, String> ipRoleMap = new HashMap<>();
        for (Map.Entry<Integer, List<String>> entry : excelData.entrySet()) {
            List<String> list = entry.getValue();
            if (ListUtil.isEmpty(list)) {
                continue;
            }
            ipRoleMap.put(list.get(1).toLowerCase(), list.get(0));
        }
        return ipRoleMap;
    }

   /* public static HashMap<String,Object> parserCommonConfigInfo(){
        try {
            //String jsonStr = JsonAndYamlUtils.readJson(Constants.COMMON_CONFIG_PATH);
            //commonConfigMap = JSONObject.parseObject(jsonStr,LinkedHashMap.class);
            commonConfigMap = new ObjectMapper().readValue(new File(Constants.COMMON_CONFIG_PATH), HashMap.class);
        } catch (IOException e) {
            Log.high.error("公共配置文件解析失败： " + e);
        }
        return commonConfigMap;
    }*/

    public static HashMap<String,String> parserPlatformInfo(){
        try {
            platformMap = new ObjectMapper().readValue(new File("./config/platform.json"), new TypeReference<HashMap<String, String>>(){});
        } catch (IOException e) {
            Log.high.info("平台名称编码文件解析失败： " + e);
        }
        return platformMap;
    }

    public static HashMap<String,String> parserModuleInfo(){
        try {
            moduleMap = new ObjectMapper().readValue(new File("./config/module.json"), new TypeReference<HashMap<String, String>>(){});
        } catch (IOException e) {
            Log.high.info("运维系统模块名称编码文件解析失败： " + e);
        }
        return moduleMap;
    }

    public static HashMap<String,HashMap<String,String>> parserSoftwareInfo(){
        try {
            softwareMap = new ObjectMapper().readValue(new File("./config/software.json"), new TypeReference<HashMap<String, HashMap>>() {});
        } catch (IOException e) {
            Log.high.info("程序监控信息文件解析失败： " + e);
        }
        return softwareMap;
    }

}