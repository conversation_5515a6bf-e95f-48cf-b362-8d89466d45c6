package com.maintain.server.utils;

import org.apache.commons.codec.binary.Hex;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

/**
 * <AUTHOR>
 * @date 2020-07-29
 */
public class CryptUtil {

    public final static String SEPERATOR = "\t";

    private static Key convertSecretKey;
    private static Cipher cipher;

    static {
       try{
/*           // 生成key//返回生成指定算法密钥的KeyGenerator对象
           KeyGenerator keyGenerator = KeyGenerator.getInstance("DES");
           keyGenerator.init(56);//初始化此密钥生成器,使其具有确定的密钥大小
           SecretKey secretKey = keyGenerator.generateKey();//生成一个密钥
           byte[] bs = secretKey.getEncoded();
           // key转换
           DESKeySpec desKeySpec = new DESKeySpec(bs); //实例化DES密钥规则*/

           SecretKeySpec desKeySpec = new SecretKeySpec("QxTT!@#$".getBytes("GBK"), "DES");
           SecretKeyFactory factory = SecretKeyFactory.getInstance("DES"); //实例化密钥工厂
           convertSecretKey = factory.generateSecret(desKeySpec); //生成密钥
           cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
       }catch (NoSuchAlgorithmException e){

       } catch (InvalidKeySpecException e) {
           e.printStackTrace();
       } catch (NoSuchPaddingException e) {
           e.printStackTrace();
       } catch (UnsupportedEncodingException e) {
           e.printStackTrace();
       }
    }
    public static void main(String[] args) {
        String username = "test";
        String password = "security_!@#$%^&*()";
        String strs = username + SEPERATOR + password;
        String encrypt = encrypt(strs);
        System.out.println(encrypt);

        String decrypt = decrypt(encrypt);
        System.out.println(decrypt);

    }

    public static String encrypt(String str) {

        try {
            // 加密
            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, convertSecretKey);
            byte[] result = cipher.doFinal(str.getBytes());
            return Hex.encodeHexString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String decrypt(String str) {

        try {
            // 解密
            cipher.init(Cipher.DECRYPT_MODE, convertSecretKey);
            byte[] result = cipher.doFinal(Hex.decodeHex(str));
            return new String(result);
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
        return null;
    }

}