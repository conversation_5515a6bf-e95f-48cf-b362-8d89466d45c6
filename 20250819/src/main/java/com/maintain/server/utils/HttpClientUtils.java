package com.maintain.server.utils;

import com.common.log.Log;
import com.maintain.server.Constants;
import org.apache.http.*;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.AllowAllHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020-07-13
 */
public class HttpClientUtils {
   /* private static CloseableHttpClient httpClient;
    private static PoolingHttpClientConnectionManager manager; // 连接池管理类
    private static ScheduledExecutorService monitorExecutor; // 监控
    private final static Object syncLock = new Object(); // 相当于线程锁,用于线程安全*/

    /**
     * 对http请求进行基本设置
     *
     * @param httpRequestBase
     *            http请求
     */
    private static void setRequestConfig(HttpRequestBase httpRequestBase) {
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(Constants.HTTP_CONNECT_TIMEOUT)
                .setConnectTimeout(Constants.HTTP_CONNECT_TIMEOUT).setSocketTimeout(Constants.HTTP_SOCKET_TIMEOUT).build();
        httpRequestBase.setConfig(requestConfig);
    }

    private static void setHttpHeader(HttpPost httpPost, String token) {
        //httpPost.setHeader("Accept", "application/json, text/plain, */*");
        //httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.106 Safari/537.36");
        httpPost.setHeader("token", token);
       /* httpPost.setHeader("Content-Type", "multipart/form-data");
        httpPost.setHeader("Accept-Encoding", "gzip, deflate");
        httpPost.setHeader("Accept-Language", "zh-CN,zh;q=0.9");
        httpPost.setHeader("Cookie", "TokenKey=" + token);*/

    }

    public static String uploadFile(String url, File localFile, String token) {
        HttpPost httpPost = new HttpPost(url);
        setRequestConfig(httpPost);
        setHttpHeader(httpPost, token);
        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(Charset.forName("UTF-8"));
            builder.addBinaryBody("upf",localFile,ContentType.MULTIPART_FORM_DATA,localFile.getName());
            HttpEntity reqEntity = builder.build();
            httpPost.setEntity(reqEntity);
            //获取httpclient对象
            CloseableHttpClient httpClient = HttpClients.custom().setSSLHostnameVerifier(new AllowAllHostnameVerifier())
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                        @Override
                        public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                            return true;
                        }
                    }).build()).build();

            // 发起请求 并返回请求的响应
            response = httpClient.execute(httpPost, HttpClientContext.create());
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");

        } catch (IOException e) {
            Log.high.error("httpClient 请求执行IO异常: " + e);
        } catch (Exception e) {
            Log.high.error("异常: " + e);
        } finally {
            try {
                if (response != null)
                    response.close();
            } catch (IOException e) {
                Log.high.error("IO异常: " + e);
            }
        }
        return resultString;
    }



      /*public static CloseableHttpClient getHttpClient(String url) throws Exception {
        CertificateValidationIgnored.trustAllHttpsCertificates();
        String hostName = url.split("/")[2];
        // System.out.println(hostName);
        int port = 80;
        if (hostName.contains(":")) {
            String[] args = hostName.split(":");
            hostName = args[0];
            port = Integer.parseInt(args[1]);
        }
        if (httpClient == null) {
            // 多线程下多个线程同时调用getHttpClient容易导致重复创建httpClient对象的问题,所以加上了同步锁
            synchronized (syncLock) {
                if (httpClient == null) {
                    httpClient = createHttpClient(hostName, port);
                    // 开启监控线程,对异常和空闲线程进行关闭
                    monitorExecutor = Executors.newScheduledThreadPool(1);
                    monitorExecutor.scheduleAtFixedRate(new TimerTask() {
                                                            @Override
                                                            public void run() {
                                                                // 关闭异常连接
                                                                manager.closeExpiredConnections();
                                                                // 关闭5s空闲的连接
                                                                manager.closeIdleConnections(Constants.MAX_ROUTE, TimeUnit.MILLISECONDS);
                                                                Log.low.info("close expired and idle for over 5s connection");
                                                            }
                                                        }, Constants.MAX_ROUTE, Constants.MAX_PRE_ROUTE,
                            TimeUnit.MILLISECONDS);
                }
            }
        }
        return httpClient;
    }
    */
    /**
     * 根据host和port构建httpclient实例
     *
     * @param host
     *            要访问的域名
     * @param port
     *            要访问的端口
     * @return
     */
  /*  public static CloseableHttpClient createHttpClient(String host, int port) {
        ConnectionSocketFactory plainSocketFactory = PlainConnectionSocketFactory.getSocketFactory();
        LayeredConnectionSocketFactory sslSocketFactory = SSLConnectionSocketFactory.getSocketFactory();
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory> create()
                .register("http", plainSocketFactory).register("https", sslSocketFactory).build();
        manager = new PoolingHttpClientConnectionManager(registry);
        // 设置连接参数
        manager.setMaxTotal(Constants.MAX_CONN); // 最大连接数
        manager.setDefaultMaxPerRoute(Constants.MAX_PRE_ROUTE); // 路由最大连接数


        HttpHost httpHost = new HttpHost(host, port);
        manager.setMaxPerRoute(new HttpRoute(httpHost), Constants.MAX_CONN);
        // 请求失败时,进行请求重试
        HttpRequestRetryHandler handler = new HttpRequestRetryHandler() {
            @Override
            public boolean retryRequest(IOException e, int i, HttpContext httpContext) {
                if (i > 3) {
                    // 重试超过3次,放弃请求
                    Log.low.info("retry has more than 3 time, give up request");
                    return false;
                }
                if (e instanceof NoHttpResponseException) {
                    // 服务器没有响应,可能是服务器断开了连接,应该重试
                    Log.low.info("receive no response from server, retry");
                    return true;
                }
                if (e instanceof SSLHandshakeException) {
                    // SSL握手异常
                    Log.low.info("SSL hand shake exception");
                    return false;
                }
                if (e instanceof InterruptedIOException) {
                    // 超时
                    Log.low.info("InterruptedIOException");
                    return false;
                }
                if (e instanceof UnknownHostException) {
                    // 服务器不可达
                    Log.low.error("server host unknown");
                    return false;
                }
                if (e instanceof ConnectTimeoutException) {
                    // 连接超时
                    Log.low.error("Connection Time out");
                    return false;
                }
                if (e instanceof SSLException) {
                    Log.low.error("SSLException");
                    return false;
                }
                HttpClientContext context = HttpClientContext.adapt(httpContext);
                HttpRequest request = context.getRequest();
                if (!(request instanceof HttpEntityEnclosingRequest)) {
                    // 如果请求不是关闭连接的请求
                    return true;
                }
                return false;
            }
        };
        CloseableHttpClient client = HttpClients.custom().setConnectionManager(manager).setRetryHandler(handler)
                .build();
        return client;
    }

    *//**
     * 关闭连接池
     *//*
    public static void closeConnectionPool() {
        try {
            httpClient.close();
            manager.close();
            monitorExecutor.shutdown();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }*/
}