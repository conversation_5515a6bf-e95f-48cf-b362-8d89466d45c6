package com.maintain.server.utils;

import java.math.BigDecimal;

public class CapacityCalcUtil {
    static BigDecimal T = BigDecimal.valueOf(1024 * 1024 * 1024 * 1024L);
    static BigDecimal G = BigDecimal.valueOf(1024 * 1024 * 1024L);
    static BigDecimal M = BigDecimal.valueOf(1024 * 1024L);
    static BigDecimal K = BigDecimal.valueOf(1024 * 1024L);

    public static String byteToUnitString(long size) {
        BigDecimal totalSize = new BigDecimal(size);
        if (totalSize.compareTo(T) >= 0) {
            return totalSize.divide(T, 2, BigDecimal.ROUND_HALF_UP).toString() + "T";
        } else if (totalSize.compareTo(G) >= 0) {
            return totalSize.divide(G, 2, BigDecimal.ROUND_HALF_UP).toString() + "G";
        } else if (totalSize.compareTo(M) >= 0) {
            return totalSize.divide(M, 2, BigDecimal.ROUND_HALF_UP).toString() + "M";
        } else if (totalSize.compareTo(K) >= 0) {
            return totalSize.divide(K, 2, BigDecimal.ROUND_HALF_UP).toString() + "KB";
        }
        return size + "B";
    }
}
