package com.maintain.server.utils;

import com.common.log.Log;
import com.jcraft.jsch.*;
import com.maintain.server.Constants;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.type.OsType;
import com.maintain.server.vo.AgentVo;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018-10-08
 */
public class ProcessUtil implements Constants {

    private static ThreadLocal<ChannelShell> channelShell = new ThreadLocal<>();

    private static ThreadLocal<String> charset = new ThreadLocal<>();

    private static ThreadLocal<Session> localSession = new ThreadLocal<>();

    private static ThreadLocal<AtomicBoolean> mark = new ThreadLocal<>();

    private static ThreadPoolExecutor service = new ThreadPoolExecutor(3, 10, 2, TimeUnit.MINUTES, new ArrayBlockingQueue<>(5), r -> {
        final Thread thread = new Thread(r);
        thread.setName("ProcessThread");
        return thread;
    }, (task, ser) -> new Thread(task).start());

    private static final Pattern PID_REG = Pattern.compile("\\d+");

    private static final Pattern WIN_PID_REG = Pattern.compile("[0-9]+(?=[^0-9]*$)");

    public static String getOsName() {
        return System.getProperty("os.name").toUpperCase();
    }

    public static boolean isWindows() {
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        return WINDOWS.equals(osId);
    }

    public static void destroy() {
        channelShell.remove();
        charset.remove();
        localSession.remove();
    }

    //==========================================ChannelShell==============================

    /**
     * 1.初始化ChannelShell（localSession，channelShell，charset，mark）
     *
     * @param session
     * @param agentVo
     * @param delayTime
     * @throws Exception
     */
    public static void initChannelShell(final Session session, AgentVo agentVo, Long delayTime) throws Exception {
        if (channelShell.get() != null) {
            return;
        }
        localSession.set(session);
        final ChannelShell shell = (ChannelShell) session.openChannel("shell");
//        shell.connect(3000);
        channelShell.set(shell);
        OutputStream outputStream = shell.getOutputStream();
        String s = agentVo.getOs().equals(OsType.WINDOWS.getId()) ? "gbk" : "utf8";
        charset.set(s);
        final InputStream inputStream = shell.getInputStream();
        final AtomicBoolean atomicBoolean = new AtomicBoolean(false);
        mark.set(atomicBoolean);
        shell.connect();
        final Thread thread = Thread.currentThread();
        if (delayTime != null) {
            service.submit(() -> {
                try {
                    Thread.sleep(delayTime);
                } catch (Exception e) {
                    Log.high.error(e.getMessage(), e);
                }
                if (atomicBoolean.get()) {
                    return;
                }
                if (thread.isAlive()) {
                    thread.interrupt();
                }
            });
        }
        if (!"root".equals(agentVo.getName()) && agentVo.getOs().equals(OsType.LINUX.getId())) {
            //切换root
            StringBuilder sb = new StringBuilder();
            sb.append("cd /home/").append(agentVo.getName()).append(" && echo -e '#!/usr/bin/expect").append("\\n")
                    .append("spawn su").append("\\n")
                    .append("expect {").append("\\n")
                    .append("\"密码\" {")
                    .append("send \"").append(agentVo.getRootPswd()).append("\\\\r\"}").append("\\n")
                    .append("\"Password\" {")
                    .append("send \"").append(agentVo.getRootPswd()).append("\\\\r\"}").append("\\n")
                    .append("\"password\" {")
                    .append("send \"").append(agentVo.getRootPswd()).append("\\\\r\"}").append("\\n")
                    .append("}").append("\\n")
                    .append("interact").append("' > change.sh && chmod 777 change.sh && ./change.sh \n");
            outputStream.write(sb.toString().getBytes(s));
            outputStream.flush();
            Thread.sleep(500);
        }
    }

    public static void destroyChannelShell(AgentVo agent) throws IOException {
        ChannelShell shell = channelShell.get();
        if (shell == null) {
            final Session session = localSession.get();
            if (session != null) {
                session.disconnect();
            }
            return;
        }
        final OutputStream outputStream = channelShell.get().getOutputStream();
        final InputStream inputStream = channelShell.get().getInputStream();
        try {
            if (!"root".equals(agent.getName()) && agent.getOs().equals(OsType.LINUX.getId())) {
                outputStream.write(("cd /home/" + agent.getName() + " && rm -rf change.sh\n").getBytes(charset.get()));
                outputStream.flush();
            }
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Log.high.error(e.getMessage(), e);
            }
            outputStream.write("exit\n".getBytes(charset.get()));
            outputStream.flush();
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Log.high.error(e.getMessage(), e);
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        } finally {
            final AtomicBoolean atomicBoolean = mark.get();
            final boolean result = atomicBoolean.compareAndSet(false, true);
            if (result) {
                try {
                    outputStream.close();
                } catch (Exception e) {
                    Log.high.error(e.getMessage());
                }
                try {
                    inputStream.close();
                } catch (Exception e) {
                    Log.high.error(e.getMessage());
                }
                shell.disconnect();
                channelShell.remove();
                localSession.get().disconnect();
            }
        }
    }


    /**
     * ChannelShell使用流执行命令
     *
     * @param command
     * @throws IOException
     */
    public static void execShell(String command) throws IOException {
        ChannelShell shell = ProcessUtil.channelShell.get();
        if (shell == null) {
            throw new ServiceException("channelshell does not exist");
        }
        final OutputStream outputStream = channelShell.get().getOutputStream();
        if (outputStream != null) {
            outputStream.write((command + "\n").getBytes(charset.get()));
            outputStream.flush();
        }
        Log.low.info("执行shell 命令： " + command);
        try {
            //使用流执行命令是异步的，所以休眠一段时间保证命令已经正确执行
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    /**
     * ChannelShell使用流执行命令,并获取返回结果
     *
     * @param command
     * @return
     * @throws IOException
     */
    public static String execShellGetResult(String command) throws IOException {
        ChannelShell shell = ProcessUtil.channelShell.get();
        if (shell == null) {
            throw new ServiceException("channelshell does not exist");
        }
        final OutputStream outputStream = shell.getOutputStream();
        final InputStream inputStream = shell.getInputStream();
        String start = System.currentTimeMillis() + "";
        try {
            outputStream.write(("echo " + start + " && (" + command + ") && echo command_end\n").getBytes(charset.get()));
            outputStream.flush();
            Thread.sleep(1000);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
//        String result;
        if (inputStream.available() > 0) {
//            byte[] data = new byte[inputStream.available()];
//            final int length = inputStream.read(data);
//            result = new String(data, 0, length, charset.get());
            final BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String msg;
            boolean boo = false;
            final StringBuilder stringBuilder = new StringBuilder();
            while ((msg = bufferedReader.readLine()) != null) {
                if (!boo && msg.contains(start) && !msg.contains("echo")) {
                    boo = true;
                } else if (msg.contains("command_end") && !msg.contains("echo")) {
                    break;
                } else if (boo) {
                    stringBuilder.append(msg).append("\n");
                }
            }
            final String str = stringBuilder.toString();
//            Log.low.info("执行远程命令：" + command + "， 返回结果：" + str);
            return str;
        } else {
            return null;
        }
    }


    //------------------------------ChannelShell - windows-------------------------------

    /**
     * 在windows上使用流执行命令
     *
     * @param command
     * @return
     * @throws IOException
     */
    public static boolean execShellOnRemoteWindows(String command) throws IOException {
        final OutputStream outputStream = channelShell.get().getOutputStream();
//        final InputStream inputStream = channelShell.get().getInputStream();
        String now = System.currentTimeMillis() + "";
        try {
            outputStream.write(("echo " + now + " && @echo off\n" + command + "\nif %errorlevel% equ 0 (echo execCmdOnRemoteWindows success) else (echo execCmdOnRemoteWindows fail)\n echo command_end\n").getBytes(charset.get()));
//            outputStream.write((command + "\n").getBytes(charset.get()));
//            outputStream.write("\n".getBytes(charset.get()));
//            outputStream.write("if %errorlevel% equ 0 (echo execCmdOnRemoteWindows success) else (echo execCmdOnRemoteWindows fail)\n echo command_end\n".getBytes(charset.get()));
            outputStream.flush();
            Thread.sleep(500);
        } catch (Exception e) {
            Log.high.error("exec command:{" + command + "} in remote windows failed", e);
        }
        return true;
    }


    //==========================================ChannelSftp==============================

    /**
     * 打开ChannelSftp
     *
     * @param session
     * @return
     * @throws JSchException
     */
    public static ChannelSftp openChannelSftp(Session session) throws JSchException {
        if (session == null || !session.isConnected()) {
            throw new JSchException("session断开");
        }
        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect(session.getTimeout());
        return sftp;
    }

    /**
     * 删除文件夹
     *
     * @param sftp    连接的sftp，参数二在此连接下
     * @param dirName 文件夹的名字
     * @return 删除成功返回true，否则返回false
     * @throws FileNotFoundException 抛出异常
     */
    public static boolean rmDir(ChannelSftp sftp, String dirName) throws FileNotFoundException {
        if (sftp == null) {
            return false;
        }
        try {
            if (isDir(sftp, dirName)) {
                List<ChannelSftp.LsEntry> list;
                try {
                    list = sftp.ls(dirName);
                } catch (SftpException e) {
                    throw new FileNotFoundException();
                }
                sftp.cd(dirName);
                for (ChannelSftp.LsEntry entry : list) {
                    if (".".equals(entry.getFilename()) || "..".equals(entry.getFilename())) {
                        continue;
                    }
                    if (isDir(sftp, entry.getFilename())) {
                        rmDir(sftp, entry.getFilename());
                    } else {
                        sftp.rm(entry.getFilename());
                    }
                }
                sftp.cd("..");
                sftp.rmdir(dirName);
                return true;
            } else {
                sftp.rm(dirName);
                return true;
            }
        } catch (SftpException e) {
            Log.high.error("rmDir " + dirName + " error", e);
            return false;
        }
    }

    /**
     * 判断某一个文件是不是文件夹
     *
     * @param sftp     连接的sftp，参数二在此连接下
     * @param fileName 文件名
     * @return 是文件夹返回true，否则返回false
     */
    public static boolean isDir(ChannelSftp sftp, String fileName) throws FileNotFoundException {
        try {
            boolean result = false;
            List<ChannelSftp.LsEntry> list = sftp.ls(fileName);
            for (ChannelSftp.LsEntry entry : list) {
                if (!fileName.equals(entry.getFilename())) {
                    result = true;
                    break;
                }
            }
            return result;

        } catch (SftpException e) {
            throw new FileNotFoundException();
            /*Log.high.error("no file " + fileName, e);
            return false;*/
        }
    }

    /**
     * 上传文件夹
     *
     * @param path 源文件夹路径
     * @param sftp sftp连接，目标文件夹上传到此连接所在的目录下
     * @return 上传成功返回true，否则返回false
     */
    public static boolean uploadDir(ChannelSftp sftp, String path) {
        try {
            File source = new File(path);
            //删除老的目标文件夹
            try {
                rmDir(sftp, source.getName());
            } catch (FileNotFoundException e) {
                Log.low.info("there is no old dir " + path);
            }
            sftp.mkdir(source.getName());
            sftp.cd(source.getName());
            File[] files = source.listFiles();
            if (files == null) {
                return false;
            }
            for (File file : files) {
                if (file == null) {
                    continue;
                }
                if (!sftp.isConnected()) {
                    sftp.connect();
                }
                if (file.isDirectory()) {
                    if (!uploadDir(sftp, file.getAbsolutePath())) {
                        sftp.cd("..");
                        return false;
                    }
                } else {
                    sftp.put(file.getAbsolutePath(), file.getName(), ChannelSftp.OVERWRITE);
                }
            }
            sftp.cd("..");
        } catch (JSchException | SftpException e) {
            Log.high.error(e.getMessage(), e);
        }
        return true;
    }

    /**
     * 上传文件
     *
     * @param path 源文件路径
     * @param sftp sftp连接，目标文件夹上传到此连接所在的目录下
     * @return 上传成功返回true，否则返回false
     */
    public static boolean uploadFile(ChannelSftp sftp, String path, String programName) {
        try {
            File source = new File(path);
            //删除老的目标文件夹
            try {
                rmDir(sftp, programName);
            } catch (FileNotFoundException e) {
                Log.low.info("there is no old dir " + path);
            }
            sftp.mkdir(programName);
            sftp.cd(programName);
            if (source == null) {
                return false;
            }
            if (!sftp.isConnected()) {
                sftp.connect();
            }
            if (source.isDirectory()) {
                if (!uploadDir(sftp, source.getAbsolutePath())) {
                    sftp.cd("..");
                    return false;
                }
            } else {
                sftp.put(source.getAbsolutePath(), source.getName(), ChannelSftp.OVERWRITE);
            }
            sftp.cd("..");
        } catch (JSchException | SftpException e) {
            Log.high.error(e.getMessage(), e);
        }
        return true;
    }


    //-----------------------------------ChannelSftp - windows--------------------------------------------------

    /**
     * 上传bat文件到远程windows服务器
     *
     * @param sftp
     * @param command
     * @return
     * @throws IOException
     * @throws SftpException
     * @throws JSchException
     * @throws InterruptedException
     */
    public static String uploadCmdBatFileToRemoteWindows(ChannelSftp sftp, String command) throws IOException, SftpException, JSchException, InterruptedException {
        if (sftp == null) {
            throw new ServiceException("channelSftp is null");
        }
        String batName = UUID.randomUUID().toString() + ".bat";
        command = "@echo off\r\n" + command + ">nul\r\nif %errorlevel% equ 0 (echo success) "
                + "else (echo fail) ";
        String path = "D:\\" + Constants.MAINTAIN_TEMP + "\\" + batName;
        FileUtil.writeStringToFile(path, command, "UTF-8", false);
        //上传bat文件
        sftp.put(path, batName, ChannelSftp.OVERWRITE);
        Thread.sleep(Constants.SLEEP_TIME);
        FileUtil.deleteDir(new File(path));
        return batName;
    }

    /**
     * 把程序的新包传到远程机器上
     *
     * @param map     map
     * @param session session
     * @return boolean
     * @throws JSchException         JSchException
     * @throws SftpException         SftpException
     * @throws FileNotFoundException FileNotFoundException
     * @throws InterruptedException  InterruptedException
     */
    public static synchronized boolean uploadFileToRemoteWindows(Map<String, Object> map, Session session) throws SftpException, IOException {
        String sourcePath = map.get("tempPath").toString();
        String destPath = map.get("remoteTempPath").toString();
        String maintainTemp = Constants.MAINTAIN_TEMP;
        ChannelSftp sftp = null;
        try {
            destPath = destPath.replace("/", "\\");
            if (!destPath.endsWith("\\")) {
                destPath = destPath + "\\";
            }
            sftp = ProcessUtil.openChannelSftp(session);
            File sourceFile = new File(sourcePath);
            //进入临时文件夹
            try {
                sftp.cd(maintainTemp);
            } catch (SftpException e) {
                sftp.mkdir(maintainTemp);
                sftp.cd(maintainTemp);
            }
            //拷贝到临时文件夹
            try {
                Log.low.info("start copy windows temp dir");
                if (!ProcessUtil.uploadDir(sftp, sourcePath)) {
                    Log.high.error("copy to temp dir error");
                    return false;
                }
                Log.low.info("copy to temp dir success");
            } catch (Exception e) {
                sftp.disconnect();
                return false;
            }
            StringBuilder sb = new StringBuilder();
            //删除老的目录
            sb.append("rmdir /q /s ").append(destPath).append("\r\n");
            //创建新的目标目录
            sb.append("mkdir ").append(destPath).append("\r\n");
            //拷贝文件到目标目录
            sb.append("robocopy ").append(maintainTemp).append("\\").append(sourceFile.getName()).append(" ").append(destPath).append("  /E /NS /NC /NFL /NDL /NP");
            //在远端主机上执行bat脚本
            String command = sb.toString();
            Log.low.info("exe remote .bat" + command);
            //ExecUtil.execCmdOnRemoteWindows(session, command)
//            ProcessUtil.execShell(command);
            if (!ProcessUtil.execShellOnRemoteWindows(command)) {
                Log.high.error("copy to dest dir error");
                return false;
            }
            ProcessUtil.execShell("rd " + maintainTemp + "\\" + sourceFile.getName() + " /S /Q");
        } catch (JSchException e1) {
            Log.high.error("windowsCopyDir error " + e1.getMessage(), e1);
            return false;
        } finally {
            if (sftp != null)
                sftp.disconnect();
        }
        return true;
    }

    public static void uploadSingleFileToRemoteWindows(ChannelSftp sftp,String localFilePath, String remoteFileDirPath, String fileName) throws SftpException, IOException, NoSuchFieldException {
        try {
            String pwd = sftp.pwd();
            cdBack(sftp, pwd);
            cdDir(sftp, remoteFileDirPath);
            try {
                rmDir(sftp, fileName);
            } catch (FileNotFoundException e) {
                Log.low.info("there is no old dir ");
            }
            File localFile = null;
            try {
                localFile = new File(localFilePath);
                sftp.put(localFilePath, localFile.getName(), ChannelSftp.OVERWRITE);
            } catch (Exception e) {
                throw new ServiceException("上传文件: " + localFile.getName() + "到: " + remoteFileDirPath + "失败! 请检查是否有上传权限,如果没有请使用管理工具,为目录赋予上传权限!");
            }
            cdBack(sftp, remoteFileDirPath);
            cdDir(sftp, pwd);
        }  catch (Exception e1) {
            Log.high.error("windowsCopyDir error " + e1.getMessage(), e1);
            sftp.disconnect();
        }
    }

    //----------------------------------------ChannelSftp - Linux-----------------------------------------------
    public static synchronized void uploadFileToRemoteLinuxServer(ChannelSftp sftp, String src, String dst) throws SftpException, FileNotFoundException {
        String remoteDir = dst.substring(0, dst.lastIndexOf("/"));
        try {
            sftp.cd(remoteDir);
        } catch (Exception e) {
            mkdirs(remoteDir);
            sftp.cd(remoteDir);
        }
        sftp.put(src, dst, ChannelSftp.OVERWRITE);
    }

    public static void mkdirs(String path)
    {
        File f = new File(path);

        String fs = f.getParent();

        f = new File(fs);

        if (!f.exists())
        {
            f.mkdirs();
        }
    }

    /**
     * 移动目录到远程服务器
     *
     * @param localDir  本地路径
     * @param remoteDir 远程路径
     * @throws SftpException         抛出SftpException
     * @throws FileNotFoundException 抛出FileNotFoundException
     */
    public static synchronized void uploadFileToRemoteLinux(ChannelSftp sftp, String localDir, String remoteDir) throws SftpException, FileNotFoundException {
        Log.low.trace("local dir: " + localDir + ", remote dir: " + remoteDir);
        File localFile = new File(localDir);
        try {
            sftp.cd(remoteDir);
        } catch (Exception e) {
            sftp.mkdir(remoteDir);
            sftp.cd(remoteDir);
        }
        for (File localChildFile : localFile.listFiles()) {
            if (localChildFile.isFile()) {
                Log.low.trace("file : " + localChildFile.getName());
                transferFileToRemote(sftp, localChildFile.getAbsolutePath(), remoteDir);
            } else if (localChildFile.isDirectory()) {
                Log.low.trace("dir: " + localChildFile.getName());
                // repeat (recursive)
                uploadFileToRemoteLinux(sftp, localChildFile.getAbsolutePath(), remoteDir + "/" + localChildFile.getName());
                sftp.cd("..");
            }
        }
    }

    /**
     * 移动目录到远程服务器
     *
     * @param localFile 本地文件
     * @param remoteDir 远程路径
     * @throws SftpException         抛出SftpException
     * @throws FileNotFoundException 抛出FileNotFoundException
     */
    private static void transferFileToRemote(ChannelSftp channel, String localFile, String remoteDir) throws SftpException {
//        channel.cd(remoteDir);
        final File file = new File(localFile);
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            channel.put(fileInputStream, file.getName(), ChannelSftp.OVERWRITE);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }


    //----------------------------------------Linux && windows-----------------------------------------------

    /**
     * 上传单个文件到远程Linux机器上     安装7z等到远程windows有问题
     *
     * @param sftp
     * @param localFile
     * @param remoteDir
     * @throws SftpException
     * @throws FileNotFoundException
     */
    public static void updateSingleFileToRemote(ChannelSftp sftp, String localFile, String fileName, String remoteDir, String remoteUploadDir) throws SftpException, FileNotFoundException {
        //Log.low.info("上传文件:" + fileName + "  上传目录：" + remoteUploadDir + "  目标目录:" + remoteDir);
        cdDir(sftp, remoteUploadDir);
        try {
            sftp.put(new FileInputStream(new File(localFile)),
                    new File(localFile).getName(), ChannelSftp.OVERWRITE);
        } catch (Exception e) {
            throw new ServiceException("上传文件: " + fileName + "到: " + remoteUploadDir + "失败! 请检查是否有上传权限,如果没有请使用管理工具,为目录赋予上传权限!");
        }
        cdBack(sftp, remoteUploadDir);
        try {
            sftp.cd(MAINTAIN_LINUX_TEMP);
        } catch (Exception e) {
            try {
                ProcessUtil.execShell("mkdir " + MAINTAIN_LINUX_TEMP);
            } catch (IOException ex) {
                Log.high.error(e.getMessage(), e);
            }
            Log.low.info("创建" + MAINTAIN_LINUX_TEMP + "文件夹");
        }
        try {
            sftp.cd(remoteDir);
            cdBack(sftp, remoteUploadDir);
        } catch (Exception e) {
            try {
                ProcessUtil.execShell("mkdir -p " + remoteDir);
            } catch (IOException ex) {
                Log.high.error(e.getMessage(), e);
            }
            Log.low.info("创建" + remoteDir + "文件夹");
        }
        if (!remoteDir.equals(remoteUploadDir)) {
            try {
                ProcessUtil.execShell("mv -f " + remoteUploadDir + "/" + fileName + " " + remoteDir);
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 切换文件夹
     *
     * @param sftp
     * @param path
     */
    public static void cdDir(ChannelSftp sftp, String path) {
        try {
            String[] paths = path.split("/");
            for (int i = 0; i < paths.length; i++) {
                String tempPath = paths[i];
                if (StringUtils.isEmpty(paths[i])) {
                    tempPath = "/" + paths[++i];
                }
                try {
                    sftp.cd(tempPath);
                } catch (Exception e) {
                    sftp.mkdir(tempPath);
                    sftp.cd(tempPath);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("进入" + path + "出错");
        }
    }

    /**
     * 回退到父目录
     *
     * @param sftp
     * @param path
     */
    public static void cdBack(ChannelSftp sftp, String path) {
        try {
            String[] paths = path.split("/");
            for (int i = 0; i < paths.length; i++) {
                if (StringUtils.isEmpty(paths[i])) {
                    i++;
                }
                sftp.cd("..");
            }
        } catch (Exception e) {
            throw new ServiceException("回退到父目录出错");
        }
    }


    //---------------------------------ChannelExec------------------------------------------
    public static void exeCommand(Session session, String command) throws JSchException {
        ChannelExec channelExec = openChannelExec(session);
        if (channelExec == null) {
            return;
        }
        channelExec.setCommand(command);
        channelExec.connect();
        //这里要睡眠SLEEP_TIME是因为 channelExec.connect()是异步的，不能马上执行disconnect()不然setCommand可能不生效;
        try {
            Thread.sleep(Constants.SLEEP_TIME * 2);
        } catch (InterruptedException e) {
            Log.high.error(e.getMessage(), e);
        }
        try {
            channelExec.getOutputStream().close();
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
        try {
            channelExec.getInputStream().close();
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
        try {
            channelExec.getErrStream().close();
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
        channelExec.disconnect();
    }

    public static ChannelExec openChannelExec(Session session) throws JSchException {
        if (session == null || !session.isConnected()) {
            return null;
        }
        return (ChannelExec) session.openChannel("exec");
    }


    //------------------------------------------在本机执行命令-------------------------------------------------

    /**
     * 在本机上执行命令
     *
     * @param command
     * @param osId
     * @return
     * @throws IOException
     */
    public static String execLocalCommand(String command, int osId) throws IOException {
        Runtime runtime = Runtime.getRuntime();
        Process process;
        if (LINUX.equals(osId)) {
            process = runtime.exec(new String[]{"/bin/bash", "-c", command});
        } else {
            process = runtime.exec(new String[]{"cmd", "/c", command});
        }
        try (BufferedReader out = new BufferedReader(new InputStreamReader(process.getInputStream(), GBK));
             BufferedReader error = new BufferedReader(new InputStreamReader(process.getErrorStream(), GBK))) {
            String outResult = readStreamToString(out);
            String errorResult = readStreamToString(error);
            if (StringUtils.isNotEmpty(errorResult)) {
                if (!command.contains("mysql") && !errorResult.contains("Warning")) {
                    Log.high.error("exec " + command + " has error:" + errorResult);
                }
                throw new ServiceException(errorResult);
            }
            return outResult;
        }
    }

    /**
     * 把一个Stream的内容读出来并拼接为一个字符串
     *
     * @param br
     * @return
     * @throws IOException
     */
    private static String readStreamToString(BufferedReader br) throws IOException {
        String s = null;
        StringBuilder result = new StringBuilder();
        while ((s = br.readLine()) != null) {
            result.append(s);
        }
        return result.toString();
    }

    public static String execLocalCommand(String command) {
        Runtime runtime = Runtime.getRuntime();
        Process process = null;
        try {
            String osName = ProcessUtil.getOsName();
            Integer osId = OsType.getId(osName);
            if (OsType.LINUX.getId().equals(osId)) {
                process = runtime.exec(new String[]{"/bin/bash", "-c", command});
            } else {
                process = runtime.exec(new String[]{"cmd", "/c", command});
            }
            String is = FileUtil.readInputStream(process.getInputStream(), GBK);
            if (StringUtils.isNotEmpty(is)) {
                return is;
            }
            return FileUtil.readInputStream(process.getErrorStream(), GBK);
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
        return null;
    }


    //---------------------------agent相关--------------------------------------
    public static void closeAgent(AgentVo agentVo) throws IOException {
        final OutputStream outputStream = channelShell.get().getOutputStream();
        if (agentVo.getOs().equals(OsType.LINUX.getId())) {
            try {
                outputStream.write("jps | grep MaintainAgent | awk '{print $1}' | xargs kill -9\n".getBytes());
                outputStream.flush();
                Thread.sleep(500);
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        } else {
            try {
                outputStream.write("net stop MaintainAgent\n".getBytes(charset.get()));
                outputStream.flush();
                Thread.sleep(500);
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        }
    }

    public static void agentLive(AgentVo agentVo) throws Exception {
        String ping = iceRequest(agentVo.getIp(), IceFlag.PONG, null);
        if (ping != null) {
            return;
        }
        OutputStream outputStream = channelShell.get().getOutputStream();
        InputStream inputStream = channelShell.get().getInputStream();
        String msg;
        if (agentVo.getOs().equals(OsType.WINDOWS.getId())) {
            outputStream.write("net start\n".getBytes(charset.get()));
            outputStream.flush();
            Thread.sleep(500);
            if (inputStream.available() > 0) {
                byte[] data = new byte[inputStream.available()];
                final int length = inputStream.read(data);
                if (length < 0) {
                    throw new RuntimeException("错误！");
                }
                final String result = new String(data, charset.get());
                if (!result.contains("Windows")) {
                    //确认命令是执行了；没有真正执行就跳过
                    return;
                }
                if (result.contains("MaintainAgent")) {
                    outputStream.write("netstat -ano | findstr 60001\n".getBytes(charset.get()));
                    outputStream.flush();
                    Thread.sleep(500);
                    if (inputStream.available() > 0) {
                        final BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                        while ((msg = bufferedReader.readLine()) != null) {
                            if (msg.contains("LISTENING")) {
                                final Matcher matcher = WIN_PID_REG.matcher(msg);
                                if (matcher.find()) {
                                    agentVo.setPid(Integer.valueOf(matcher.group(0)));
                                    return;
                                }
                            }
                        }
                    }
                    return;
                }
            }

            outputStream.write("net start MaintainAgent \n".getBytes(charset.get()));
            outputStream.flush();
            Thread.sleep(500);
            Log.low.info(agentVo.getIp() + " agent未启动，准备启动agent...");
        } else {
            outputStream.write("jps\n".getBytes(charset.get()));
            outputStream.flush();
            Thread.sleep(500);
            if (inputStream.available() > 0) {
                byte[] data = new byte[inputStream.available()];
                final int length = inputStream.read(data);
                if (length < 0) {
                    throw new RuntimeException("错误！");
                }
                final String s = new String(data, charset.get());
                if (!s.contains("jps")) {
                    //确认命令是执行了；没有真正执行就跳过
                    return;
                }
                if (s.contains("MaintainAgent")) {
                    final String[] split = s.split("\n");
                    for (String s1 : split) {
                        if (s1.contains("MaintainAgent")) {
                            final Matcher matcher = PID_REG.matcher(s1);
                            if (matcher.find()) {
                                String pidStr = matcher.group(0);
                                agentVo.setPid(Integer.valueOf(pidStr));
                                return;
                            }
                        }
                    }
                    return;
                }
            } else {
                outputStream.write("jps\n".getBytes(charset.get()));
                outputStream.flush();
                if (inputStream.available() > 0) {
                    byte[] data = new byte[inputStream.available()];
                    final int length = inputStream.read(data);
                    if (length < 0) {
                        throw new RuntimeException("错误！");
                    }
                    final String s = new String(data, charset.get());
                    if (!s.contains("jps")) {
                        //确认命令是执行了；没有真正执行就跳过
                        return;
                    }
                    if (s.contains("MaintainAgent")) {
                        final String[] split = s.split("\n");
                        for (String s1 : split) {
                            if (s1.contains("MaintainAgent")) {
                                final Matcher matcher = PID_REG.matcher(s1);
                                if (matcher.find()) {
                                    String pidStr = matcher.group(0);
                                    agentVo.setPid(Integer.valueOf(pidStr));
                                    return;
                                }
                            }
                        }
                        return;
                    }
                }
            }
            Log.low.info(agentVo.getIp() + " agent未启动，准备启动agent...");
            outputStream.write("cd /dist/MaintainAgent/deploy && ./start_background.sh \n".getBytes(charset.get()));
            outputStream.flush();
            Log.low.info("在 " + agentVo.getIp() + " 上执行 shell : cd /dist/MaintainAgent/deploy && ./start_background.sh");
            Thread.sleep(500);
            outputStream.write("jps\n".getBytes(charset.get()));
            outputStream.flush();
            Thread.sleep(500);
            if (inputStream.available() > 0) {
                byte[] data = new byte[inputStream.available()];
                final int length = inputStream.read(data);
                if (length < 0) {
                    throw new RuntimeException("错误！");
                }
                final String s = new String(data, 0, length, charset.get());
                if (s.contains("MaintainAgent")) {
                    final String[] split = s.split("\n");
                    for (String s1 : split) {
                        if (s1.contains("MaintainAgent")) {
                            final Matcher matcher = PID_REG.matcher(s1);
                            if (matcher.find()) {
                                String pidStr = matcher.group(0);
                                agentVo.setPid(Integer.valueOf(pidStr));
                                return;
                            }
                        }
                    }
                }
            }
        }
    }


    //---------------------------ice-------------------------------
    public static String iceRequest(String agentIp, int flag, String param) throws ServiceException {
        try {
            return ServerRequestUtil.sendRequest(agentIp, flag, param);
        } catch (Exception e) {
            Log.high.error("call agent " + agentIp + " exception", e);
            return null;
        }
    }


    //-------------------校验-----------------------------------
    public static boolean valiExpect() throws Exception {
        final ChannelShell channelShell = ProcessUtil.channelShell.get();
        final OutputStream outputStream = channelShell.getOutputStream();
        final InputStream inputStream = channelShell.getInputStream();
        outputStream.write("expect\nexit\n".getBytes(charset.get()));
        outputStream.flush();
        Thread.sleep(1000);
        if (inputStream.available() > 0) {
            byte[] data = new byte[inputStream.available()];
            final int length1 = inputStream.read(data);
            final String s = new String(data, 0, length1, charset.get());
            if (s.contains("not") && s.contains("found")) {
                return false;
            } else {
                Thread.sleep(1000);
                return true;
            }
        }
        return false;
    }

    public static boolean valiYml() throws Exception {
        final ChannelShell channelShell = ProcessUtil.channelShell.get();
        final OutputStream outputStream = channelShell.getOutputStream();
        final InputStream inputStream = channelShell.getInputStream();
        outputStream.write("find /etc/yum.repos.d/localyum.repo || find /etc/yum.repos.d/local.repo\nexit\n".getBytes(charset.get()));
        outputStream.flush();
        Thread.sleep(1000);
        if (inputStream.available() > 0) {
            byte[] data = new byte[inputStream.available()];
            final int length1 = inputStream.read(data);
            final String s = new String(data, 0, length1, charset.get());
            if (s.contains("find: ‘/etc/yum.repos.d/local.repo’: 没有那个文件或目录") && s.contains("find: ‘/etc/yum.repos.d/localyum.repo’: 没有那个文件或目录") ||
                    s.contains("find: ‘/etc/yum.repos.d/local.repo’: No such file or directory") && s.contains("find: ‘/etc/yum.repos.d/localyum.repo’: No such file or directory")) {
                return false;
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 关闭服务器
     *
     * @param agentVo
     * @throws IOException
     */
    public static void closeHardware(AgentVo agentVo) throws IOException {
        final OutputStream outputStream = channelShell.get().getOutputStream();
        if (agentVo.getOs().equals(OsType.LINUX.getId())) {
            try {
                outputStream.write("shutdown -h now\n".getBytes(charset.get()));
                Log.low.info("开始在" + agentVo.getIp() + "上执行关机命令:shutdown -h now");
                outputStream.flush();
                Thread.sleep(500);
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        } else {
            try {
                outputStream.write("shutdown -s -t 2\n".getBytes(charset.get()));
                Log.low.info("开始在" + agentVo.getIp() + "上执行关机命令:shutdown -s -t 2");
                outputStream.flush();
                Thread.sleep(500);
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        }
    }

}