package com.maintain.server.utils;

import com.maintain.server.vo.SoftwareVo;

import java.util.Comparator;

/**
 * <AUTHOR>
 * @date 2018-12-11
 */
public class SoftwareVoCompareDescUtil implements Comparator<SoftwareVo> {

    @Override
    public int compare(SoftwareVo o1, SoftwareVo o2) {
        Integer t1 = o1.getStatus().getId();
        Integer t2 = o2.getStatus().getId();
        if (t2 > t1) {
            return 1;
        } else if (t2 < t1) {
            return -1;
        } else {
            return 0;
        }
    }
}