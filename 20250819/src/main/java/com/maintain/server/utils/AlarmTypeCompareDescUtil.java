package com.maintain.server.utils;

import com.maintain.server.type.AlarmStatusType;

import java.io.File;
import java.util.Comparator;

/**
 * <AUTHOR>
 * @date 2018-12-11
 */
public class AlarmTypeCompareDescUtil implements Comparator<AlarmStatusType> {

    @Override
    public int compare(AlarmStatusType o1, AlarmStatusType o2) {
        Integer type1 = o1.getId();
        Integer type2 = o2.getId();
        if (type2 > type1) {
            return 1;
        } else if (type2 <type1) {
            return -1;
        } else {
            return 0;
        }
    }
}