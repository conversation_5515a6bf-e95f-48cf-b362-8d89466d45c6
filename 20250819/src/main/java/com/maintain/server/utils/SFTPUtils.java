package com.maintain.server.utils;

import com.common.log.Log;
import com.jcraft.jsch.*;
import com.maintain.server.exception.ServiceException;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-11-21
 */
public class SFTPUtils {

    private String host;//服务器连接ip
    private String username;//用户名
    private String password;//密码
    private int port;//端口号
    private ChannelSftp sftp = null;
    private Session sshSession = null;

    public SFTPUtils(){}

    public SFTPUtils(String host, String username, String password, int port)
    {
        this.host = host;
        this.username = username;
        this.password = password;
        this.port = port;
    }

    /**
     * 通过SFTP连接服务器
     */
    public void connect()
    {
        try
        {
            JSch jsch = new JSch();
            jsch.getSession(username, host, port);
            sshSession = jsch.getSession(username, host, port);
            sshSession.setPassword(password);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            sshSession.setConfig(sshConfig);
            sshSession.connect();
            Channel channel = sshSession.openChannel("sftp");
            channel.connect();
            sftp = (ChannelSftp) channel;
        }
        catch (Exception e)
        {
            Log.high.info("连接失败！");
        }
    }

    /**
     * 关闭连接
     */
    public void disconnect()
    {
        if (this.sftp != null)
        {
            if (this.sftp.isConnected())
            {
                this.sftp.disconnect();
            }
        }
        if (this.sshSession != null)
        {
            if (this.sshSession.isConnected())
            {
                this.sshSession.disconnect();
            }
        }
    }

    /**
     * 批量下载文件
     * @param localPath：本地保存目录(以路径符号结束,D:\MAINTAIN_TEMP\sftp\)
     * @param fileFormat：下载文件格式(以特定字符开头,为空不做检验)
     * @param fileEndFormat：下载文件格式(文件格式)
     * @param del：下载后是否删除sftp文件
     * @return
     */
    public List<String> batchDownLoadFile(String remotePath, String localPath,
                                          String fileFormat, String fileEndFormat, boolean del)
    {
        List<String> filenames = new ArrayList<>();
        try
        {
            Vector v = listFiles(remotePath);
            if (v.size() > 0)
            {
                Iterator it = v.iterator();
                while (it.hasNext())
                {
                    ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) it.next();
                    String filename = entry.getFilename();
                    SftpATTRS attrs = entry.getAttrs();
                    if (!attrs.isDir())
                    {
                        boolean flag;
                        String localFileName = localPath + filename;
                        fileFormat = fileFormat == null ? "" : fileFormat
                                .trim();
                        fileEndFormat = fileEndFormat == null ? ""
                                : fileEndFormat.trim();
                        // 三种情况
                        if (fileFormat.length() > 0 && fileEndFormat.length() > 0)
                        {
                            if (filename.startsWith(fileFormat) && filename.endsWith(fileEndFormat))
                            {
                                flag = downloadFile(remotePath, filename,localPath, filename);
                                if (flag)
                                {
                                    filenames.add(localFileName);
                                    if (flag && del)
                                    {
                                        deleteSFTP(remotePath, filename);
                                    }
                                }
                            }
                        }
                        else if (fileFormat.length() > 0 && "".equals(fileEndFormat))
                        {
                            if (filename.startsWith(fileFormat))
                            {
                                flag = downloadFile(remotePath, filename, localPath, filename);
                                if (flag)
                                {
                                    filenames.add(localFileName);
                                    if (flag && del)
                                    {
                                        deleteSFTP(remotePath, filename);
                                    }
                                }
                            }
                        }
                        else if (fileEndFormat.length() > 0 && "".equals(fileFormat))
                        {
                            if (filename.endsWith(fileEndFormat))
                            {
                                flag = downloadFile(remotePath, filename,localPath, filename);
                                if (flag)
                                {
                                    filenames.add(localFileName);
                                    if (flag && del)
                                    {
                                        deleteSFTP(remotePath, filename);
                                    }
                                }
                            }
                        }
                        else
                        {
                            flag = downloadFile(remotePath, filename,localPath, filename);
                            if (flag)
                            {
                                filenames.add(localFileName);
                                if (flag && del)
                                {
                                    deleteSFTP(remotePath, filename);
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (SftpException e)
        {
            Log.high.info("sftp下载异常");
        }
        finally
        {
            // this.disconnect();
        }
        return filenames;
    }

    /**
     * 从ftp服务器下载程序安装包
     */
    public List<String> downloadUpgradePackage(String remotePath, String localPath, boolean del)
    {
        List<String> filenames = new ArrayList<>();
        try
        {
            Vector v = listFiles(remotePath);
            if (v.size() > 0)
            {
                Iterator it = v.iterator();
                while (it.hasNext())
                {
                    ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) it.next();
                    String filename = entry.getFilename();
                    SftpATTRS attrs = entry.getAttrs();
                    if (!attrs.isDir())
                    {
                        boolean flag;
                        String localFileName = localPath + filename;
                        //判断本地是否存在该文件
                        File file = new File(localFileName);
                        if (!file.exists()) {
                            flag = downloadFile(remotePath, filename,localPath, filename);
                            if (flag)
                            {
                                filenames.add(localFileName);
                                if (flag && del)
                                {
                                    deleteSFTP(remotePath, filename);
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (SftpException e)
        {
            Log.high.info("sftp下载异常");
        }
        return filenames;
    }


    /**
     * 下载单个文件
     * @param remoteFileName：下载文件名
     * @param localPath：本地保存目录(以路径符号结束)
     * @param localFileName：保存文件名
     * @return
     */
    public boolean downloadFile(String remotePath, String remoteFileName,String localPath, String localFileName)
    {
        FileOutputStream fieloutput = null;
        try
        {
            File file = new File(localPath + localFileName);
            fieloutput = new FileOutputStream(file);
            sftp.get(remotePath + remoteFileName, fieloutput);
            return true;
        }
        catch (FileNotFoundException e)
        {
            e.printStackTrace();
        }
        catch (SftpException e)
        {
            e.printStackTrace();
        }
        finally
        {
            if (null != fieloutput)
            {
                try
                {
                    fieloutput.close();
                }
                catch (IOException e)
                {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    /**
     * 上传单个文件
     * @param remotePath：远程保存目录
     * @param remoteFileName：保存文件名
     * @param localPath：本地上传目录(以路径符号结束)
     * @param localFileName：上传的文件名
     * @return
     */
    public boolean uploadFile(String remotePath, String remoteFileName,String localPath, String localFileName)
    {
        FileInputStream in = null;
        try
        {
            if (remotePath.contains("\\")) {
                remotePath = cdWindowsPath(remotePath);
            }
            createDir(remotePath);
            File file = new File(localPath + localFileName);
            in = new FileInputStream(file);
            sftp.put(in, remoteFileName);
            return true;
        }
        catch (FileNotFoundException e)
        {
            e.printStackTrace();
        }
        catch (SftpException e)
        {
            e.printStackTrace();
        }
        finally
        {
            if (in != null)
            {
                try
                {
                    in.close();
                }
                catch (IOException e)
                {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    /**
     * 上传文件夹
     *
     */
    public boolean uploadDir(String orignalPath,String targetPath) {
        try {
            if (targetPath.contains("\\")) {
                targetPath = cdWindowsPath(targetPath);
            }
            cdDir(sftp, targetPath);
            File source = new File(orignalPath);
            String dirName = source.getName();
            File[] files = source.listFiles();
            if (files == null) {
                return false;
            }
            for (File file : files) {
                if (file == null) {
                    continue;
                }
                if (!sftp.isConnected()) {
                    sftp.connect();
                }
                String absolutePath = file.getAbsolutePath();
                if (file.isDirectory()) {
                    String tpath1 = targetPath + "/" +  dirName;
                    if (!uploadDir(absolutePath,tpath1)) {
                        return false;
                    }
                } else {
                    String name = file.getName();
                    String tempPath2 = absolutePath.replace(orignalPath.replace(dirName,""),"").replace(name,"").replace("\\","/");
                    String tpath2 = targetPath + "/" + tempPath2;
                    cdDir(sftp, tpath2);
                    sftp.put(absolutePath, file.getName() , ChannelSftp.OVERWRITE);
                }
            }
            sftp.cd("..");
        } catch (JSchException | SftpException e) {
            Log.high.error(e.getMessage(), e);
        }
        return true;
    }

    /**
     * 批量上传文件
     * @param remotePath：远程保存目录
     * @param localPath：本地上传目录(以路径符号结束)
     * @param del：上传后是否删除本地文件
     * @return
     */
    public boolean bacthUploadFile(String remotePath, String localPath,
                                   boolean del)
    {
        try
        {
            connect();
            File file = new File(localPath);
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++)
            {
                if (files[i].isFile())
                {
                    if (this.uploadFile(remotePath, files[i].getName(),
                            localPath, files[i].getName())
                            && del)
                    {
                        deleteFile(localPath + files[i].getName());
                    }
                }
            }
            return true;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        finally
        {
            this.disconnect();
        }
        return false;

    }

    /**
     * 删除本地文件
     * @param filePath
     * @return
     */
    public boolean deleteFile(String filePath)
    {
        File file = new File(filePath);
        if (!file.exists())
        {
            return false;
        }

        if (!file.isFile())
        {
            return false;
        }
        boolean rs = file.delete();
        return rs;
    }

    /**
     * 创建目录
     * @param createpath
     * @return
     */
    public boolean createDir(String createpath)
    {
        try
        {
            if (createpath.contains("\\")) {
                createpath = cdWindowsPath(createpath);
            }
            if (isDirExist(createpath))
            {
                this.sftp.cd(createpath);
                return true;
            }
            String[] pathArry = createpath.split("/");
            StringBuffer filePath = new StringBuffer("/");
            for (String path : pathArry)
            {
                if (path.equals(""))
                {
                    continue;
                }
                filePath.append(path + "/");
                if (isDirExist(filePath.toString()))
                {
                    sftp.cd(filePath.toString());
                }
                else
                {
                    // 建立目录
                    sftp.mkdir(filePath.toString());
                    // 进入并设置为当前目录
                    sftp.cd(filePath.toString());
                }

            }
            this.sftp.cd(createpath);
            return true;
        }
        catch (SftpException e)
        {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 判断目录是否存在
     * @param directory
     * @return
     */
    public boolean isDirExist(String directory)
    {
        boolean isDirExistFlag = false;
        try
        {
            SftpATTRS sftpATTRS;
            if (directory.contains("\\")) {
                directory = cdWindowsPath(directory);
                sftpATTRS = sftp.lstat(sftp.pwd());
            } else {
                cdDir(sftp, directory);
                sftpATTRS = sftp.lstat(directory);
            }
            isDirExistFlag = true;
            return sftpATTRS.isDir();
        }
        catch (Exception e)
        {
            if (e.getMessage().toLowerCase().equals("no such file"))
            {
                isDirExistFlag = false;
            }
        }
        return isDirExistFlag;
    }

    private String cdWindowsPath(String path) throws SftpException {
        path = path.replace("\\","/");
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        String pwd = sftp.pwd();
        cdBack(sftp, pwd);
        cdDir(sftp, path);
        return path;
    }

    /**
     * 删除文件夹
     *
     */
    public boolean rmDir(String dirName) throws FileNotFoundException {
        if (sftp == null) {
            return false;
        }
        try {
            if (isDirExist(dirName)) {
                if (dirName.contains("\\")) {
                    dirName = cdWindowsPath(dirName);
                } else {
                    cdDir(sftp, dirName);
                }
                List<ChannelSftp.LsEntry> list;
                try {
                    list = sftp.ls(dirName);
                } catch (SftpException e) {
                    throw new FileNotFoundException();
                }
                sftp.cd(dirName);
                for (ChannelSftp.LsEntry entry : list) {
                    if (".".equals(entry.getFilename()) || "..".equals(entry.getFilename())) {
                        continue;
                    }
                    if (isDirExist(entry.getFilename())) {
                        rmDir(entry.getFilename());
                    } else {
                        sftp.rm(entry.getFilename());
                    }
                }
                sftp.cd("..");
                sftp.rmdir(dirName);
                return true;
            } else {
                sftp.rm(dirName);
                return true;
            }
        } catch (SftpException e) {
            Log.high.error("rmDir " + dirName + " error", e);
            return false;
        }
    }

    /**
     * 删除文件
     *
     */
    public boolean rmFile(String filePath, String fileName) throws FileNotFoundException {
        if (sftp == null) {
            return false;
        }
        try {
            if (filePath.contains("\\")) {
                filePath = cdWindowsPath(filePath);
            }
            if (isDirExist(filePath)) {
                List<ChannelSftp.LsEntry> list;
                try {
                    list = sftp.ls(fileName);
                } catch (SftpException e) {
                    throw new FileNotFoundException();
                }
                String pwd = sftp.pwd();
                cdBack(sftp, pwd);
                cdDir(sftp, filePath);
                for (ChannelSftp.LsEntry entry : list) {
                    if (".".equals(entry.getFilename()) || "..".equals(entry.getFilename())) {
                        continue;
                    }
                    sftp.rm(entry.getFilename());
                }
            }
            return true;
        } catch (SftpException e) {
            Log.high.error("rmDir " + filePath + fileName + " error", e);
            return false;
        }
    }

    /**
     * 判断文件是否存在
     * @param filePath
     * @return
     */
    public boolean isFileExist(String filePath, String fileName) throws FileNotFoundException
    {
        boolean isFileExist = false;
        try
        {
            if (filePath.contains("\\")) {
                cdWindowsPath(filePath);
            } else {
                cdDir(sftp, filePath);
            }
            List<ChannelSftp.LsEntry> list = sftp.ls(fileName);
            for (ChannelSftp.LsEntry entry : list) {
                if (fileName.equals(entry.getFilename())) {
                    isFileExist = true;
                    break;
                }
            }
        }
        catch (Exception e)
        {
            throw new FileNotFoundException();
        }
        return isFileExist;
    }


    /**
     * 切换文件夹
     *
     * @param sftp
     * @param path
     */
    private static void cdDir(ChannelSftp sftp, String path) {
        try {
            String[] paths = path.split("/");
            for (int i = 0; i < paths.length; i++) {
                String tempPath = paths[i];
                if (StringUtils.isEmpty(paths[i])) {
                    tempPath = "/" + paths[++i];
                }
                try {
                    sftp.cd(tempPath);
                } catch (Exception e) {
                    sftp.mkdir(tempPath);
                    sftp.cd(tempPath);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("进入" + path + "出错");
        }
    }

    /**
     * 回退到父目录
     *
     * @param sftp
     * @param path
     */
    public static void cdBack(ChannelSftp sftp, String path) {
        try {
            String[] paths = path.split("/");
            for (int i = 0; i < paths.length; i++) {
                if (StringUtils.isEmpty(paths[i])) {
                    i++;
                }
                sftp.cd("..");
            }
        } catch (Exception e) {
            throw new ServiceException("回退到父目录出错");
        }
    }

    /**
     * 删除stfp文件
     * @param directory：要删除文件所在目录
     * @param deleteFile：要删除的文件
     */
    public void deleteSFTP(String directory, String deleteFile)
    {
        try
        {
            sftp.rm(directory + deleteFile);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    /**
     * 列出目录下的文件
     *
     * @param directory：要列出的目录
     * @return
     * @throws SftpException
     */
    public Vector listFiles(String directory) throws SftpException
    {
        return sftp.ls(directory);
    }

    public String getHost()
    {
        return host;
    }

    public void setHost(String host)
    {
        this.host = host;
    }

    public String getUsername()
    {
        return username;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public int getPort()
    {
        return port;
    }

    public void setPort(int port)
    {
        this.port = port;
    }

    public ChannelSftp getSftp()
    {
        return sftp;
    }

    public void setSftp(ChannelSftp sftp)
    {
        this.sftp = sftp;
    }

    /**测试*/
    public static void main(String[] args)
    {
        SFTPUtils sftp = null;
        // 本地存放地址
        String localPath = "D:\\MAINTAIN_TEMP\\";
        // Sftp下载路径
        String sftpPath = "/data/ywxt_upgrade_package/";
        try
        {
            sftp = new SFTPUtils("***************", "root", "0okm9ijn*UHB",22);
            sftp.connect();
            // 下载
            sftp.batchDownLoadFile(sftpPath, localPath, null, ".7z", false);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        finally
        {
            sftp.disconnect();
        }
    }
}