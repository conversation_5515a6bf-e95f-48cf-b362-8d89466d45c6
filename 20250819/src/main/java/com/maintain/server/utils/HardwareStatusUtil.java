package com.maintain.server.utils;

import com.common.log.Log;
import com.maintain.server.type.AlarmStatusType;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

public class HardwareStatusUtil {

    private String warnSize;

    private String errorSize;

    private String warnPercent;

    private String errorPercent;

    public HardwareStatusUtil(String warnPercent, String errorPercent) {
        this.warnPercent = warnPercent;
        this.errorPercent = errorPercent;
    }

    public HardwareStatusUtil(String warnSize, String errorSize, String warnPercent, String errorPercent) {
        this.warnSize = warnSize;
        this.errorSize = errorSize;
        this.warnPercent = warnPercent;
        this.errorPercent = errorPercent;
    }

    public void judgeDiskInfo(String module, String diskUsedPercent, String total, String used, Map<String, AlarmStatusType> statusMap, Map<AlarmStatusType, String> descriptionMap) {
        diskUsedPercent = diskUsedPercent.replace("%", "").trim();
        BigDecimal bigDecimal = new BigDecimal(diskUsedPercent);
        //计算磁盘容量应该用字节！
        BigDecimal T = BigDecimal.valueOf(1024 * 1024 * 1024 * 1024L);
        BigDecimal G = BigDecimal.valueOf(1024 * 1024 * 1024L);
        BigDecimal M = BigDecimal.valueOf(1024 * 1024L);
        BigDecimal totalDisk = new BigDecimal(0);
        BigDecimal usedDisk = new BigDecimal(0);
        if (StringUtils.isNotEmpty(total)) {
            boolean containG = total.contains("g") || total.contains("G");
            boolean containM = total.contains("m") || total.contains("M");
            boolean containT = total.contains("t") || total.contains("T");
            if (containG) {
                total = total.replace("g", "").replace("G", "");
                totalDisk = totalDisk.add(new BigDecimal(total).multiply(G));
            } else if (containM) {
                total = total.replace("m", "").replace("M", "");
                Log.low.debug("");
                totalDisk = totalDisk.add(new BigDecimal(total).multiply(M));
            } else if (containT) {
                total = total.replace("t", "").replace("T", "");
                Log.low.debug("");
                totalDisk = totalDisk.add(new BigDecimal(total).multiply(T));
            }
        }
        if (StringUtils.isNotEmpty(used) && used.contains("g") || used.contains("G") || used.contains("t") || used.contains("T")) {
            boolean containG = used.contains("g") || used.contains("G");
            boolean containM = used.contains("m") || used.contains("M");
            boolean containT = used.contains("t") || used.contains("T");
            if (containG) {
                used = used.replace("g", "").replace("G", "");
                usedDisk = usedDisk.add(new BigDecimal(used).multiply(G));
            } else if (containM) {
                used = used.replace("m", "").replace("M", "");
                usedDisk = usedDisk.add(new BigDecimal(used).multiply(M));
            } else if (containT) {
                used = used.replace("t", "").replace("T", "");
                usedDisk = usedDisk.add(new BigDecimal(used).multiply(T));
            }
        }
        //可用磁盘容量
        BigDecimal availableDisk = totalDisk.subtract(usedDisk);
        AlarmStatusType availableDiskStatus = AlarmStatusType.GREEN;
        if (availableDisk.compareTo(getDecimal(warnSize)) >= 0) {
            availableDiskStatus = AlarmStatusType.GREEN;
        } else if (availableDisk.compareTo(getDecimal(errorSize)) < 0) {
            availableDiskStatus = AlarmStatusType.YELLOW;
        } else if (availableDisk.compareTo(getDecimal(errorSize)) >= 0) {
            availableDiskStatus = AlarmStatusType.RED;
        }
        setType(module, statusMap, warnPercent, errorPercent, bigDecimal, availableDiskStatus);
        setDescription(module, descriptionMap, statusMap);
    }

    public BigDecimal getDecimal(String val) {
        BigDecimal T = BigDecimal.valueOf(1024 * 1024 * 1024 * 1024L);
        BigDecimal G = BigDecimal.valueOf(1024 * 1024 * 1024L);
        BigDecimal M = BigDecimal.valueOf(1024 * 1024L);
        boolean containG = val.contains("g") || val.contains("G");
        boolean containM = val.contains("m") || val.contains("M");
        boolean containT = val.contains("t") || val.contains("T");
        if (containG) {
            val = val.replace("g", "").replace("G", "");
            return new BigDecimal(val).multiply(G);
        } else if (containM) {
            val = val.replace("m", "").replace("M", "");
            return new BigDecimal(val).multiply(M);
        } else if (containT) {
            val = val.replace("t", "").replace("T", "");
            return new BigDecimal(val).multiply(T);
        } else {
            return new BigDecimal(0);
        }
    }

    private void setDescription(String module, Map<AlarmStatusType, String> descriptionMap, Map<String, AlarmStatusType> statusMap) {
        AlarmStatusType type = statusMap.get(module);
        if (type == null) {
            return;
        }
        String description = descriptionMap.get(type);
        if (type == AlarmStatusType.RED) {
            String desc = module + "用量超过" + errorPercent + "%";
            if (module.contains("硬盘") || module.contains("目录")) {
                desc += ",且可用空间小于" + errorSize;
            }
            if (description != null) {
                description = description + "," + desc;
            } else {
                description = desc;
            }
            descriptionMap.put(type, description);
        } else if (type == AlarmStatusType.YELLOW) {
            String desc = module + "用量超过" + warnPercent + "%";
            if (module.contains("硬盘") || module.contains("目录")) {
                desc += ",且可用空间小于" + warnSize;
            }
            if (description != null) {
                description = description + "," + desc;
            } else {
                description = desc;
            }
            descriptionMap.put(type, description);
        } else {
            descriptionMap.put(type, "/");
        }
    }

    private void setType(String module, Map<String, AlarmStatusType> statusTypeMap, String low, String high, BigDecimal bigDecimal, AlarmStatusType availDiskStatus) {
        int warming = bigDecimal.compareTo(new BigDecimal(low));
        int alarm = bigDecimal.compareTo(new BigDecimal(high));
        if ((warming < 0 && alarm < 0) || availDiskStatus == AlarmStatusType.GREEN) {
            statusTypeMap.put(module, AlarmStatusType.GREEN);
        } else if (warming > 0 && alarm < 0 && availDiskStatus == AlarmStatusType.YELLOW) {
            statusTypeMap.put(module, AlarmStatusType.YELLOW);
        } else if (warming == 0 && alarm < 0 && availDiskStatus == AlarmStatusType.YELLOW) {
            statusTypeMap.put(module, AlarmStatusType.YELLOW);
        } else if (availDiskStatus == AlarmStatusType.RED) {
            statusTypeMap.put(module, AlarmStatusType.RED);
        } else {
            statusTypeMap.put(module, AlarmStatusType.GREEN);
        }
    }

    public void judgeCpuAndMemInfo(String module, Map<AlarmStatusType, String> descriptionMap, Map<String, AlarmStatusType> statusMap, BigDecimal bigDecimal) {
        setType(module, statusMap, bigDecimal);
        setDescription(module, descriptionMap, statusMap);
    }

    private void setType(String module, Map<String, AlarmStatusType> statusTypeMap, BigDecimal bigDecimal) {
        int warming = bigDecimal.compareTo(new BigDecimal(warnPercent));
        int alarm = bigDecimal.compareTo(new BigDecimal(errorPercent));
        if (warming < 0 && alarm < 0) {
            statusTypeMap.put(module, AlarmStatusType.GREEN);
        } else if (warming >= 0 && alarm < 0) {
            statusTypeMap.put(module, AlarmStatusType.YELLOW);
        } else {
            statusTypeMap.put(module, AlarmStatusType.RED);
        }
    }

}
