package com.maintain.server.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.log.Log;
import com.maintain.server.Constants;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.*;


public final class PropertiesUtil implements Constants {
    private PropertiesUtil() {
    }

    public static Map<String, String> loadProperties(List<String> keyList, String path) {
        Map<String, String> result = new HashMap<String, String>();
        if (ListUtil.isEmpty(keyList)) {
            return result;
        }
        Properties pro = new Properties();
        FileInputStream in = null;
        try {
            in = new FileInputStream(path);
        } catch (FileNotFoundException e) {
            Log.high.error(e.getMessage(), e);
        }
        if (in != null) {
            try {
                pro.load(new BufferedReader(new InputStreamReader(in, "UTF-8")));
                for (String key : keyList) {
                    Object obj = pro.get(key);
                    if (obj != null) {
                        String value = null;
                        value = (String) obj;
                        //value = value.replace("\"", "").trim();
                        result.put(key, value);
                    } else {
                        result.put(key, "");
                    }
                }
                return result;
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
                return result;
            } finally {
                FileUtil.closeInputStream(in);
                pro.clear();
            }
        }
        return result;
    }

    public static String loadProperty(String key, String path) {
        String result = "";
        List<String> list = new ArrayList<String>();
        list.add(key);
        Map<String, String> map = loadProperties(list, path);
        if (!map.isEmpty()) {
            result = map.get(key);
        }
        return result;
    }

    /**
     * 解析守护狗的配置文件
     *
     * @return Map
     */
    public static JSONArray parseIni(File file) throws IOException {
         /*= new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException(filePath + " file is not exists");
        }*/
        JSONArray jsonArray = new JSONArray();
        BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), Constants.GBK));
        JSONObject map = new JSONObject();
        jsonArray.add(map);
        JSONObject temp = null;
        String str;
        try {
            while ((str = reader.readLine()) != null) {
                if (str.contains("OneItem")) {
                    continue;
                }
                if (StringUtils.isEmpty(str)) {
                    //再读一行
                    str = reader.readLine();
                    if (str != null && str.contains("OneItem")) {
                        map = null;
                        temp = new JSONObject();
                        jsonArray.add(temp);
                    }
                } else {
                    String[] strs = str.split("=");
                    if (strs.length > 1) {
                        if (map != null) {
                            map.put(strs[0], strs[1]);
                        } else if (temp != null) {
                            temp.put(strs[0], strs[1]);
                        }
                    }
                }
            }
            return jsonArray;
        } finally {
            try {
                reader.close();
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        }
    }
}
