package com.maintain.server.utils;

import com.common.log.Log;
import com.maintain.server.Constants;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public final class DateUtil implements Constants {
    public static DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
    public static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD);

    private DateUtil() {
    }

    private static final ThreadLocal<SimpleDateFormat> threadLocal = new ThreadLocal<SimpleDateFormat>() {

        @Override
        protected SimpleDateFormat initialValue() {
            threadLocal.set(new SimpleDateFormat());
            return threadLocal.get();
        }

    };

    public static SimpleDateFormat getSdf(String pattern) {
        SimpleDateFormat sdf = threadLocal.get();
        if (sdf == null) {
            synchronized (DateUtil.class) {
                if (sdf == null) {
                    sdf = new SimpleDateFormat(pattern);
                    threadLocal.set(sdf);
                }
            }
        }
        if (pattern != null) {
            if (!pattern.equals(sdf.toPattern())) {
                sdf.applyPattern(pattern);
            }
        }
        return sdf;
    }

    public static Date parse(String date, String pattern) {
        try {
            if (StringUtils.isEmpty(date)) {
                return null;
            }
            return getSdf(pattern).parse(date);
        } catch (ParseException e) {
            Log.high.error(e.getMessage());
        }
        return null;
    }

    public static String format(Date date, String pattern) {
        return getSdf(pattern).format(date);
    }

    public static Date getNextDate(Date date, int number) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, number);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 当前日期的前一天
     * @param date
     * @return
     */
    public static String lastDateByDateStr(String date) {
        if (StringUtils.isEmpty(date)) {
            return date;
        }
        return LocalDateTime.parse(date, DATE_TIME_FORMATTER).minusDays(1).format(DATE_TIME_FORMATTER);
    }

    /**
     * 获取一段区间内的所有时间
     *
     * @param startTime startTime
     * @param endTime   endTime
     * @param pattern   pattern
     * @return list
     * @throws ParseException ParseException
     */
    public static List<String> getDates(String startTime, String endTime, String pattern) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return null;
        }
        try {
            List<String> list = new ArrayList<>();
            pattern = StringUtils.isEmpty(pattern) ? YYYY_MM_DD_HH_MM_SS : pattern;
            SimpleDateFormat format = getSdf(pattern);
            Calendar minCalendar = Calendar.getInstance();
            minCalendar.setTime(format.parse(startTime));
            Calendar maxCalendar = Calendar.getInstance();
            maxCalendar.setTime(format.parse(endTime));
            while (!minCalendar.after(maxCalendar)) {
                list.add(format.format(minCalendar.getTime()));
                minCalendar.add(Calendar.DATE, 1);
            }
            return list;
        } catch (ParseException e) {
            Log.high.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 判断两时间是否为同一天
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean isSameDay(String startTime, String endTime) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return false;
        }
        SimpleDateFormat format = getSdf(YYYY_MM_DD);
        try {
            Calendar minCalendar = Calendar.getInstance();
            minCalendar.setTime(format.parse(startTime));
            Calendar maxCalendar = Calendar.getInstance();
            maxCalendar.setTime(format.parse(endTime));
            return (!minCalendar.before(maxCalendar) && !minCalendar.after(maxCalendar));
        } catch (ParseException e) {
            Log.high.error(e.getMessage(), e);
        }
        return false;
    }

    public static String currentDayMinTime() {
        return LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).format(DATE_TIME_FORMATTER);
    }

    public static String yesterdayMinTime() {
        return LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0).format(DATE_TIME_FORMATTER);
    }

    public static String currentDayMaxTime() {
        return LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).format(DATE_TIME_FORMATTER);
    }

    public static String yesterdayMaxTime() {
        return LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59).format(DATE_TIME_FORMATTER);
    }

    public static String currentDay() {
        return LocalDate.now().format(DATE_FORMATTER);
    }

    public static String currentDateTime() {
        return LocalDateTime.now().format(DATE_TIME_FORMATTER);
    }

    public static String parseDateTime(LocalDateTime dateTime) {
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 判断传入的时间与当前时间的间隔是否在一小时内
     *
     * @param dateTime
     * @return
     */
    public static boolean intervalOneHourFromNow(String dateTime) {
        Duration duration = Duration.between(LocalDateTime.parse(dateTime, DATE_TIME_FORMATTER), LocalDateTime.now());
        return duration.toMinutes() < 60;
    }

    public static String formDate(LocalDateTime localDateTime) {
        return localDateTime.format(DATE_TIME_FORMATTER);
    }
}
