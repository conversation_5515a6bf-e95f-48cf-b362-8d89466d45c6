package com.maintain.server.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.maintain.server.Constants;

import java.util.Date;
import java.util.List;

/**
 * 大量用到fastjson的地方还是多封装一层比较好，以防以后需要更换序列化工具的时候不用改太多
 *
 * <AUTHOR>
 */
public final class JsonUtil {

    private JsonUtil() {
    }

    public static String toJsonString(Object object) {
        return JSON.toJSONString(object);
    }

    public static String toJSONStringWithDateFormat(Object object, String format) {
        return JSON.toJSONStringWithDateFormat(object, format, SerializerFeature.WriteDateUseDateFormat);
    }

    public static <T> T parseObject(String text, Class<T> clazz) {
        return JSON.parseObject(text, clazz);
    }

    public static <T> T parseObject(String text, TypeReference<T> type) {
        return JSON.parseObject(text, type.getType());
    }

    public static <T> List<T> parseArray(String text, Class<T> clazz) {
        return JSON.parseArray(text, clazz);
    }

    public static <T> List<T> toJavaList(JSONArray jsonArray, Class<T> clazz) {
        if (jsonArray == null || jsonArray.size() == 0) {
            return null;
        }
        return jsonArray.toJavaList(clazz);
    }
}
