package com.maintain.server.utils;

import com.alibaba.fastjson.JSONObject;
import com.common.log.Log;
import com.maintain.server.Constants;
import com.maintain.server.vo.ResponseVo;

/**
 * <AUTHOR>
 * @date 2020-12-23
 */
public class ReqRespHelper {

    private ReqRespHelper() {
    }


    public static String writeSuccessWithContent(Object content) {
        return writeResult(Constants.SUCCESS_RESPONSE_CODE, Constants.SUCCESS, content, new ResponseVo());
    }

    public static String writeSuccessWithMsgAndContent(String message,Object content) {
        return writeResult(Constants.SUCCESS_RESPONSE_CODE, message, content, new ResponseVo());
    }

    public static String writeResultFailureWithContent(Object content) {
        return writeResult(Constants.FAIL_RESPONSE_CODE,Constants.FAILURE, content, new ResponseVo());
    }

    public static String writeResultFailureWithMsgAndContent(String message,Object content) {
        return writeResult(Constants.FAIL_RESPONSE_CODE,message, content, new ResponseVo());
    }

    public static String writeResult(int status, String message, Object content, ResponseVo responseVo) {
        responseVo.setCode(status);
        responseVo.setMsg(message);
        responseVo.setData(content);
        try {
            return JSONObject.toJSONString(responseVo);
        } catch (Exception e) {
            Log.high.error("writeResult error", e);
            return "{\"code\":\"" + status + "\",\"msg\":\"" + message + "\",\"data\":\"" + content + "\"}";
        }
    }
}