package com.maintain.server.utils;

import com.common.log.Log;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcraft.jsch.ChannelExec;
import com.maintain.server.Constants;
import com.maintain.server.vo.FileNode;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.FileHeader;
import net.sf.sevenzipjbinding.IInArchive;
import net.sf.sevenzipjbinding.ISequentialOutStream;
import net.sf.sevenzipjbinding.SevenZip;
import net.sf.sevenzipjbinding.SevenZipException;
import net.sf.sevenzipjbinding.impl.RandomAccessFileInStream;
import net.sf.sevenzipjbinding.simple.ISimpleInArchive;
import net.sf.sevenzipjbinding.simple.ISimpleInArchiveItem;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 文件工具类
 *
 * <AUTHOR>
 */
public abstract class FileUtil implements Constants {

    public static final String SEPARATOR = File.separator;

    public static void closeInputStream(InputStream stream) {
        try {
            if (stream != null) {
                stream.close();
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(),e);
        }
    }

    public static void closeOutputStream(OutputStream stream) {
        try {
            if (stream != null) {
                stream.close();
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(),e);
        }
    }

    public static void closeReader(Reader reader) {
        try {
            if (reader != null) {
                reader.close();
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(),e);
        }
    }

    public static void closeWriter(Writer writer) {
        try {
            if (writer != null) {
                writer.close();
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(),e);
        }
    }


    /**
     * 删除文件夹
     *
     * @param dir 文件夹
     * @return 返回是否删除成功
     */
    public static boolean deleteDir(File dir) {
        if (!dir.exists()) {
            return true;
        }
        if (dir.isFile()) {
            return dir.delete();
        }
        File[] files = dir.listFiles();
        if (files != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (!deleteDir(file)) {
                        return false;
                    }
                } else {
                    if (!file.delete()) {
                        return false;
                    }
                }
            }
        }
        return dir.delete();
    }


    /**
     * 修改json文件
     *
     * @param filePath 文件路径
     * @param map      内容的map
     * @return 返回成功与否
     */
    public static boolean updateJson(String filePath, Map<String, Object> map) {
        File file = new File(filePath);
        if (!file.exists()) {
            Log.high.error("Dir is not a file," + filePath);
            return false;
        }
        Map<String, Object> config = null;
        try {
            JsonFactory factory = new JsonFactory();
            factory.enable(JsonParser.Feature.ALLOW_COMMENTS);
            ObjectMapper objectMapper = new ObjectMapper(factory);
            // 忽略JSON字符串中存在的但Java对象实际没有的属性
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            config = objectMapper.readValue(file, Map.class);
            for (Map.Entry<String, Object> s : map.entrySet()) {
                updateMap(config, s.getKey(), map.get(s.getKey()));
            }
            new ObjectMapper().writerWithDefaultPrettyPrinter().writeValue(file, config);
            return true;
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新map的值
     *
     * @param map   map
     * @param key   key
     * @param value 值
     */
    private static void updateMap(Map<String, Object> map, String key, Object value) {
        if (map.containsKey(key)) {
            map.put(key, value);
        } else {
            for (Object o : map.values()) {
                if (o.getClass().toString().toLowerCase().contains("map")) {
                    updateMap((Map<String, Object>) o, key, value);
                }
            }
        }
    }

    /**
     * 修改具有key=value类似格式的文件对应key的value值
     * 有多个值要修改时调用此方法
     *
     * @param filePath 文件路径
     * @param map      要修改的key-value的map
     * @return 返回修改成功与否
     */
    public static boolean updateKeyValue(String filePath, Map<String, String> map) {
        File file = new File(filePath);
        if (!file.exists()) {
            return false;
        }
        StringBuilder sb = new StringBuilder();

        try (FileInputStream fileInputStream = new FileInputStream(file);
             InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "UTF-8");
             BufferedReader reader = new BufferedReader(inputStreamReader)) {

            String line = "";
            while ((line = reader.readLine()) != null) {
                if (!line.startsWith("#") && line.contains("=")) {
                    String[] content = line.split("=");
                    if (map.containsKey(content[0])) {
                        line = line.replace(content[1], map.get(content[0]));
                    }
                }
                sb.append(line);
                sb.append("\r\n");
            }
        } catch (IOException e) {
            return false;
        }

        try (OutputStream outputStream = new FileOutputStream(file);
             PrintWriter writer = new PrintWriter(outputStream)) {
            writer.write(sb.toString());
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 修改公共配置文件
     */
   /* public static boolean updateCommonConfigValue(String filePath, Map<String, String> map) {
        File file = new File(filePath);
        if (!file.exists()) {
            return false;
        }
        StringBuilder sb = new StringBuilder();
        try (FileInputStream fileInputStream = new FileInputStream(file);
             InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "UTF-8");
             BufferedReader reader = new BufferedReader(inputStreamReader)) {
            String line = "";
            while ((line = reader.readLine()) != null) {
                if (!line.startsWith("//") && line.contains(":")) {
                    String key = line.substring(0,line.indexOf(":"));
                    String value = line.substring(line.indexOf(":")+1);
                    if (map.containsKey(key)) {
                        line = line.replace(value, map.get(key));
                    }
                }
                sb.append(line);
                sb.append("\r\n");
            }
        } catch (IOException e) {
            return false;
        }
        try (OutputStream outputStream = new FileOutputStream(file);
             PrintWriter writer = new PrintWriter(outputStream)) {
            writer.write(sb.toString());
            return true;
        } catch (IOException e) {
            return false;
        }
    }
*/
    /**
     * 向文件中写内容
     *
     * @param file     文件
     * @param content  内容
     * @param encode   编码
     * @param isAppend 是否追加，true追加，false不追加
     * @throws IOException 抛出IO异常
     */
    public static void writeStringToFile(File file, String content, String encode, boolean isAppend) throws IOException {
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream outputStream = new FileOutputStream(file, isAppend);
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, encode);
        PrintWriter writer = new PrintWriter(outputStreamWriter);
        writer.write(content);
        writer.close();
        outputStreamWriter.close();
        outputStream.close();
    }

    /**
     * 向文件中写内容
     *
     * @param filePath 文件路径
     * @param content  文件内容
     * @param encode   编码
     * @param isAppend 是否追加
     * @throws IOException 抛出IO异常
     */
    public static void writeStringToFile(String filePath, String content, String encode, boolean isAppend) throws IOException {
        File file = new File(filePath);
        writeStringToFile(file, content, encode, isAppend);
    }

    public static String readInputStream(InputStream is, String code) throws IOException {
        if (is == null) {
            return null;
        }
        BufferedReader reader = new BufferedReader(new InputStreamReader(is, code));
        try{
            StringBuilder sb = new StringBuilder();
            String str = "";
            while ((str = reader.readLine()) != null) {
                sb.append(str);
                return sb.toString();
            }
            return sb.toString();
        }finally {
            reader.close();
        }
    }


    /**
     * 读取ssh命令执行的输出流
     *
     * @param channelExec ssh连接的channel
     * @return 返回执行结果的map
     * @throws IOException 抛出IO异常
     */
    public static Map<String, String> readStream(ChannelExec channelExec) throws IOException {
        String error = readInputStream(channelExec.getErrStream(), Constants.GBK);
        //String out = readInputStream(channelExec.getInputStream(), Constants.GBK);
        Map<String, String> map = new HashMap<>();
        map.put(Constants.PROC_RESULT_OUT_TYPE_ERROR, error);
        //map.put(Constants.PROC_RESULT_OUT_TYPE_OUT, out);
        return map;
    }

    public static String getFileMd5(File file) {
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            if (!file.exists()) {
                return null;
            }
            MessageDigest messageDigest = MessageDigest.getInstance("md5");
            byte[] buffer = new byte[1024 * 5];
            int length;
            while ((length = fileInputStream.read(buffer)) != -1) {
                messageDigest.update(buffer, 0, length);
            }
            return new BigInteger(1, messageDigest.digest()).toString(16);
        } catch (NoSuchAlgorithmException | IOException e) {
            Log.high.error(e.getMessage(), e);
        }
        return null;
    }


    private static final AtomicInteger counter = new AtomicInteger(1);

    /**
     * 网上扒的.
     *
     * @param path
     * @param output
     * @param password
     * @throws IOException
     * @throws ZipException
     */
    public static void unzip(String path, String output, String password) throws IOException, ZipException {
        ZipFile zipFile = new ZipFile(path);
        zipFile.setFileNameCharset(Constants.GBK);
        if (zipFile.isEncrypted()) {
            zipFile.setPassword(password);
        }
        for (Object obj : zipFile.getFileHeaders()) {
            FileHeader fileHeader = (FileHeader) obj;
            String destFile = output + "/" + fileHeader.getFileName();
            createFile(destFile);
            try (InputStream is = zipFile.getInputStream(fileHeader)
                 ; OutputStream os = new FileOutputStream(destFile)) {
                int readLine = -1;
                byte[] buffer = new byte[10240];
                while ((readLine = is.read(buffer)) != -1) {
                    os.write(buffer, 0, readLine);
                }
            }
        }
    }


    public static boolean un7z(String file7zPath, final String outPutPath, String passWord) {
        IInArchive archive = null;
        RandomAccessFile randomAccessFile = null;
        try {
            randomAccessFile = new RandomAccessFile(file7zPath, "r");
            archive = SevenZip.openInArchive(null, new RandomAccessFileInStream(randomAccessFile), passWord);
            int numberOfItems = archive.getNumberOfItems();
            Map<String, List<String>> map = new HashMap<>(numberOfItems);
            ISimpleInArchive simpleInArchive = archive.getSimpleInterface();
            ExecutorService executorService = Executors.newFixedThreadPool(2000);
            for (final ISimpleInArchiveItem item : simpleInArchive.getArchiveItems()) {
                try {
                    if (!item.isFolder()) {
                        item.extractSlow(new ISequentialOutStream() {
                            public int write(byte[] data) throws SevenZipException {
                                if (data.length == 0) {
                                    return 0;
                                }
                                try {
                                    Thread.currentThread().setName("unzip-Thread" + counter.getAndIncrement());
                                    String path = outPutPath + File.separator + item.getPath();
                                    File file;
                                    synchronized (FileUtil.class) {
                                        file = createFile(path);
                                    }
                                    IOUtils.write(data, new FileOutputStream(file, true));
                                } catch (IOException e) {
                                    Log.high.error(e.getMessage(), e);
                                }
                                return 0;
                            }
                        }, passWord);
                    }
                } catch (Exception e) {
                    Log.high.error(e.getMessage(), e);
                }
            }
            executorService.shutdown();
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            return false;
        } finally {
            try {
                if (archive != null) {
                    archive.close();
                }
                randomAccessFile.close();
            } catch (IOException e) {
                Log.high.error(e.getMessage(),e);
            }
        }
        return true;
    }

    private static File createFile(String path) {
        File file = new File(path);
        FileNode last = null;
        if (!file.exists()) {
            FileNode node = new FileNode(file.getAbsolutePath());
            last = getFile(node, file.getParentFile());
            last.setNext(node);
        }
        while (last != null) {
            File f = new File(last.getPath());
            if (last.getPre() != null && !f.exists()) {
                f.mkdir();
            } else {
                try {
                    f.createNewFile();
                } catch (IOException e) {
                    Log.high.error(e.getMessage(),e);
                }
            }
            last = last.getPre();
        }
        return file;
    }

    private static FileNode getFile(FileNode node, File file) {
        if (!file.exists()) {
            FileNode fileNode = new FileNode(file.getAbsolutePath());
            node.setNext(fileNode);
            fileNode.setPre(node);
            return getFile(fileNode, file.getParentFile());
        }
        return node;
    }

    public static String getMonitorPath() {
        String monitorPath = "";
        if (ProcessUtil.isWindows()) {
            monitorPath = MAINTAIN_WINDOWS_REMOTE + "MaintainServer\\libs\\com\\maintain\\server\\plugin\\impl";
        } else {
            monitorPath = MAINTAIN_LINUX_REMOTE + "MaintainServer/libs/com/maintain/server/plugin/impl";
        }
        File file = new File(monitorPath);
        if (!file.exists()) {
            FileNode node = new FileNode(file.getAbsolutePath());
            FileNode last = getFile(node, file.getParentFile());
            last.setNext(node);
            while (last != null) {
                File f = new File(last.getPath());
                f.mkdir();
                last = last.getPre();
            }
        }
        return file.getAbsolutePath();
    }

    public static String getClazzName(File file) {
        String regex = "MaintainServer.";
        String rule = File.separator;
        if (ProcessUtil.isWindows()) {
            rule = rule.concat(File.separator);
        }
        return file.getAbsolutePath().replaceAll(rule, ".").split(regex)[1].replace(".class", "");
    }

    /**
     * 读取一个文件内容并进行base64编码
     *
     * @param path
     * @return
     */
    public static String readFile(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return null;
        }
        try {
            String content = FileUtils.readFileToString(file, Constants.UTF_8);
            byte[] bytes = content.getBytes(Charset.forName(Constants.UTF_8));
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
                 FileInputStream fis = new FileInputStream(file)) {
                byte[] buffer = new byte[1024];
                while ((fis.read(buffer)) != -1) {
                    baos.write(buffer);
                }
                String content = new String(baos.toByteArray(), Charset.forName(Constants.UTF_8)).trim();
                byte[] bytes = content.getBytes(Charset.forName(Constants.UTF_8));
                return Base64.getEncoder().encodeToString(bytes);
            } catch (IOException e1) {
                throw new RuntimeException("读取文件失败", e1);
            }

        }
        /* */
    }

    /**
     * 解密base64字符串，然后回写到相应配置文件
     *
     * @param content
     */
    public static void writeContentToFile(String path, String content) throws UnsupportedEncodingException {
        File file = new File(path);
        if (!file.exists()) {
            throw new RuntimeException(path + "不存在");
        }
        byte[] bytes = Base64.getDecoder().decode(content);
        content = new String(bytes, Charset.forName(Constants.UTF_8)).trim();
        //使用字节流输出会导致文件尾部出现多余符号，notepad++中出现NUL字样,原因未知
        try (BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(
                new FileOutputStream(file), Constants.UTF_8))) {
            bw.write(content);
            bw.flush();
        } catch (IOException e) {
            throw new RuntimeException("回写文件失败", e);
        }
    }

    /**
     * 计算文件夹大小
     */
    public static long getTotalSizeOfFilesInDir(final File file) {
        if (file.isFile())
            return file.length();
        final File[] children = file.listFiles();
        long total = 0;
        if (children != null)
            for (final File child : children)
                total += getTotalSizeOfFilesInDir(child);
        return total;
    }

    /**
     * 获取指定文件下的子级文件夹
     */
    public static List<File> getChildrenDirs(File file){
        List<File> fileList = new ArrayList<>();
        File[] files = file.listFiles();
        for (int i = 0; i < files.length; i++) {
            if (files[i].isDirectory()) {
                fileList.add(files[i]);
            }
        }
        return fileList;
    }
}
