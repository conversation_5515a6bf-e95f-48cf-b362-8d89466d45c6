package com.maintain.server.utils;

import com.common.log.Log;
import com.jcraft.jsch.*;
import com.maintain.server.Constants;
import com.maintain.server.thread.ReadStreamThread;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.maintain.server.Constants.LINUX_NEW_LINE;
import static com.maintain.server.Constants.UTF_8;

/**
 * <AUTHOR>
 * @date 2016/12/13
 */
public final class ExecUtil {
    private ExecUtil() {
    }



    /**
     * 读取ssh命令执行的输出流
     *
     * @param channelExec ssh连接的channel
     * @return 返回执行结果的map
     * @throws IOException 抛出IO异常
     */
    public static Map<String, String> readStream(ChannelExec channelExec) throws IOException {
        InputStream errStream = channelExec.getErrStream();
        InputStream outStream = channelExec.getInputStream();
        StringBuilder errBuilder = new StringBuilder();
        StringBuilder outBuilder = new StringBuilder();

        List<Runnable> threadList = new ArrayList<>();
        threadList.add(new ReadStreamThread(errStream, errBuilder, Constants.GBK));
        threadList.add(new ReadStreamThread(outStream, outBuilder, Constants.GBK));

        executeMultiThread(threadList, 2);

        Map<String, String> map = new HashMap<>();
        map.put(Constants.PROC_RESULT_OUT_TYPE_ERROR, errBuilder.toString());
        map.put(Constants.PROC_RESULT_OUT_TYPE_OUT, outBuilder.toString());

        return map;
    }

    /**
     * 执行多个线程，并等待线程执行结束
     *
     * @param threads      线程列表
     * @param maxThreadNum 线程池的大小
     */
    public static void executeMultiThread(List<Runnable> threads, int maxThreadNum) {
        if (threads.isEmpty()) {
            return;
        }
        ExecutorService executorService = Executors.newFixedThreadPool(maxThreadNum);
        for (int i = 0; i < threads.size(); i++) {
            executorService.execute(threads.get(i));
        }

        //退出使用线程池
        executorService.shutdown();
        try {
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
        } catch (InterruptedException e) {
            Log.high.error("executeMultiThread InterruptedException happened", e);
        }
    }


    /**
     * 在远程Windows上执行命令
     *
     * @param session SSH连接
     * @param commend 执行的命令
     * @return 命令执行成功与否
     */
    public static boolean execCmdOnRemoteWindows(Session session, String commend) {
        ChannelSftp sftp = null;
        try {
            sftp = ProcessUtil.openChannelSftp(session);
        } catch (JSchException e) {
            Log.high.error("execCmdOnRemoteWindows getChannel error", e);
            return false;
        }
        File batFile = new File("bat");
        if(!batFile.exists()){
            batFile.mkdirs();
        }
        String batName = UUID.randomUUID().toString() + ".bat";
        commend = "@echo off\r\n" + commend + "\r\nif %errorlevel% equ 0 (echo execCmdOnRemoteWindows success) "
                + "else (echo execCmdOnRemoteWindows fail)";
        Log.low.info("execCmdOnRemoteWindows:" + commend);
        try {
            IOUtil.writeStringToFile("bat\\" + batName, commend, UTF_8, false);
        } catch (IOException e) {
            Log.high.error("create bat file error", e);
            sftp.disconnect();
            return false;
        }
        //上传bat文件
        String fileName = "bat\\" + batName;
        try {
            sftp.put(fileName, batName, ChannelSftp.OVERWRITE);
        } catch (SftpException e) {
            Log.high.error("upload bat file error", e);
            sftp.disconnect();
            return false;
        }
        boolean result = true;
        //执行bat文件
        try {
            String s = runSshCommend(session, "cmd /c " + batName);
            Log.low.info("execute result:" + s);
            if (s.contains("execCmdOnRemoteWindows success")) {
                Log.low.info("run commend success");
            } else {
                Log.high.error("run commend error" + "\t" + s);
                result = false;
            }
        } catch (Exception e) {
            Log.high.error("run commend error", e);

            sftp.disconnect();
            return false;
        }
        //删除拷贝的bat文件
        try {
            FileUtil.deleteDir(batFile);
            sftp.rm(batName);
            Log.low.info("delete bat file success");
        } catch (SftpException e) {
            Log.high.error("delete bat file error", e);
        }

        sftp.disconnect();
        return result;
    }

    public static boolean execBashOnRemoteLinux(Session session, String command) {
        ChannelSftp sftp = null;
        try {
            sftp = ProcessUtil.openChannelSftp(session);
        } catch (JSchException e) {
            Log.high.error("execShellOnRemoteLinux getChannel error", e);
            return false;
        }
        File shFile = new File("sh");
        if(!shFile.exists()){
            shFile.mkdirs();
        }
        String bashName = UUID.randomUUID().toString() + ".sh";
        command = /*"#!/bin/bash" + LINUX_NEW_LINE + */command + LINUX_NEW_LINE + "if [ $? == 0 ];then"
                + LINUX_NEW_LINE + "\"echo execCmdOnRemoteWindows success\""
                + LINUX_NEW_LINE + "else" + LINUX_NEW_LINE + "\"echo execCmdOnRemoteWindows fail\""
                + LINUX_NEW_LINE + "fi";
        Log.low.info("execShellOnRemoteLinux:" + command);
        try {
            IOUtil.writeStringToFile("sh\\" + bashName, command, UTF_8, false);
            Log.low.info("create shell file  success");
        } catch (IOException e) {
            Log.high.error("create shell file error", e);

            sftp.disconnect();
            return false;
        }
        //上传shell文件
        try {
            sftp.put("sh\\" + bashName, bashName, ChannelSftp.OVERWRITE);
            Log.low.info("upload shell file success");
        } catch (SftpException e) {
            Log.high.error("upload shell file error", e);

            sftp.disconnect();
            return false;
        }
        FileUtil.deleteDir(shFile);
        //执行shell文件
        try {
            command = "chmod +x " + bashName + " && ./" + bashName;
            String s = runSshCommend(session, command);
            if (s.contains("execCmdOnRemoteWindows success")) {
                Log.low.info("run commend success");
            } else {
                Log.high.error("run commend error" + "\t" + s);
            }
        } catch (Exception e) {
            Log.high.error("run commend error", e);
            return false;
        }
        return true;
    }

    /**
     * 运行ssh命令
     *
     * @param session 连接的session
     * @param command 执行的命令
     * @return 返回命令执行结果
     * @throws Exception 抛出异常
     */
    public static String runSshCommend(Session session, String command) throws Exception {
        Channel channel;
        if (!session.isConnected()) {
            session.connect();
        }
        channel = session.openChannel("exec");
        ((ChannelExec) channel).setCommand(command);
        channel.connect();
       Map<String, String> resultMap = readStream(((ChannelExec) channel));
       try{
            channel.getOutputStream().close();
       }catch (Exception e){
           Log.high.error(e.getMessage(),e);
       }
        channel.disconnect();
        String res;
        if (resultMap.get(Constants.PROC_RESULT_OUT_TYPE_ERROR).isEmpty()) {
            res = "result:\r\n" + resultMap.get(Constants.PROC_RESULT_OUT_TYPE_OUT);
        } else {
            res = "result:\r\n" + resultMap.get(Constants.PROC_RESULT_OUT_TYPE_OUT)
                    + "\r\nerror:\r\n" + resultMap.get(Constants.PROC_RESULT_OUT_TYPE_ERROR);
        }
        return res;
    }
}
