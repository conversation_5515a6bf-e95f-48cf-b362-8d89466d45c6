package com.maintain.server.utils;


import com.common.log.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @date 2017-06-24
 */
public class SevenZipUtil {

    /**
     * 调用7Z解压工具对指定文件进行解压
     *
     * @param file
     */
    public static boolean unZip(String sevenZipPath, File file, String password) {
        if (!file.exists()) {
            return false;
        }
        File parentFile = file.getParentFile();
        if (!parentFile.exists()) {
            parentFile.getParentFile().mkdirs();
        }
        String osName= ProcessUtil.getOsName().toLowerCase();
        Log.low.info("os.name：" + osName);
        if(osName.contains("linux")){
            FileUtil.un7z(file.getAbsolutePath(),parentFile.getAbsoluteFile().getAbsolutePath(),password);
        }
        String exec = String.format("%s x -aoa \"%s\" -o\"%s\" -p\"%s\"",
                sevenZipPath, file.getAbsolutePath(), parentFile.getAbsoluteFile(), password);
        Log.low.info("开始解压" + exec);
        Runtime runtime = Runtime.getRuntime();
        Process process = null;
        BufferedReader br = null;
        BufferedReader error = null;
        try {
            process = runtime.exec(exec);
            br = new BufferedReader(new InputStreamReader(process.getInputStream()));
            error = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String s = null;
            while ((s = br.readLine()) != null) {
                Log.low.info(s);
            }
            System.out.println();
            while ((s = error.readLine()) != null) {
                Log.low.info(s);
            }
            int i = process.waitFor();
            Log.low.info(i);
        } catch (IOException | InterruptedException e) {
            Log.high.error(e.getMessage(), e);
            return false;
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    Log.high.error(e.getMessage(),e);
                }
            }
            if (error != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    Log.high.error(e.getMessage(),e);
                }
            }
            if (process != null) {
                process.destroy();
            }
        }
        return true;
    }
}
