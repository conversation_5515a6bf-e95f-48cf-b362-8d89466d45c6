package com.maintain.server.utils;

import java.util.Comparator;

/**
 * <AUTHOR>
 * @date 2019-01-15
 */
public class DoubleCompareAscUtil implements Comparator<Double> {

    /**
     *
     * @param o1 o1
     * @param o2 o2
     * @return 1 => 升序  -1 => 降序
     */
    @Override
    public int compare(Double o1, Double o2) {
        if (o1 > o2) {
            return 1;
        } else if (o1 < o2) {
            return -1;
        }
        return 0;
    }
}