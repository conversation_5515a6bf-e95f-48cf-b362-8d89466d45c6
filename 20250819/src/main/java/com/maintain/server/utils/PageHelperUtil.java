package com.maintain.server.utils;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.maintain.server.dto.PageDto;
import com.maintain.server.vo.PageVo;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class PageHelperUtil {
    public static <T> PageVo<T> startPage(PageDto pageDto, Supplier<List<T>> supplier) {
        PageHelper.startPage(pageDto.getPageNo(), pageDto.getPageSize());
        List<T> ts = supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(ts);
        PageVo<T> pageVo = new PageVo<>();
        pageVo.setPageNo(pageDto.getPageNo());
        pageVo.setPageSize(pageDto.getPageSize());
        pageVo.setTotal(pageInfo.getTotal());
        pageVo.setList(ts);
        return pageVo;
    }
}
