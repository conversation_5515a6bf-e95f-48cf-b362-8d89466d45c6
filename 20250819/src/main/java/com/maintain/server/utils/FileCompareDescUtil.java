package com.maintain.server.utils;

import java.io.File;
import java.util.Comparator;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-08-28
 */
public class FileCompareDescUtil implements Comparator<File> {

    /**
     * 1是降序
     *
     * @param o1 o1
     * @param o2 o2
     * @return
     */
    @Override
    public int compare(File o1, File o2) {
        if (o2.lastModified() > o1.lastModified()) {
            return 1;
        } else if (o2.lastModified() < o1.lastModified()) {
            return -1;
        } else {
            return 0;
        }
    }

}