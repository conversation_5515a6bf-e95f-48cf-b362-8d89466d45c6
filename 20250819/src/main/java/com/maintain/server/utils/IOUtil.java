package com.maintain.server.utils;

import java.io.*;

/**
 * <AUTHOR>
 * @date 2016/3/8
 */
public final class IOUtil {
    private IOUtil() {
    }

    /**
     * 把输入流转换成字符串
     *
     * @param input   输入流
     * @param charset 编码
     * @return 返回字符串
     * @throws IOException 抛出IO异常
     */
    public static String inputStreamToString(InputStream input, String charset) throws IOException {
        if (input == null) {
            return null;
        } else {
            InputStreamReader inputStreamReader = new InputStreamReader(input, charset);
            BufferedReader reader = new BufferedReader(inputStreamReader);
            String line = "";
            StringBuilder sb = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                sb.append(line + "\r\n");
            }

            reader.close();
            inputStreamReader.close();

            return sb.toString();
        }
    }

    /**
     * 向文件中写内容
     *
     * @param file     文件
     * @param content  内容
     * @param encode   编码
     * @param isAppend 是否追加，true追加，false不追加
     * @throws IOException 抛出IO异常
     */
    public static void writeStringToFile(File file, String content, String encode, boolean isAppend) throws IOException {
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream outputStream = new FileOutputStream(file, isAppend);
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, encode);
        PrintWriter writer = new PrintWriter(outputStreamWriter);
        writer.write(content);
        writer.close();
        outputStreamWriter.close();
        outputStream.close();
    }

    /**
     * 向文件中写内容
     *
     * @param filePath 文件路径
     * @param content  文件内容
     * @param encode   编码
     * @param isAppend 是否追加
     * @throws IOException 抛出IO异常
     */
    public static void writeStringToFile(String filePath, String content, String encode, boolean isAppend) throws IOException {
        File file = new File(filePath);
        writeStringToFile(file, content, encode, isAppend);
    }

    /**
     * 把文件读取成字节流
     *
     * @param filePath 文件路径
     * @return 返回字节数据
     * @throws IOException 抛出IO异常
     */
    public static byte[] readFileToBinary(String filePath) throws IOException {
        File file = new File(filePath);
        return readFileToBinary(file);
    }

    /**
     * 把文件读取成字节流
     *
     * @param file 文件
     * @return 返回字节数组
     * @throws IOException 抛出IO异常
     */
    public static byte[] readFileToBinary(File file) throws IOException {
        byte[] content = new byte[(int) file.length()];
        try (FileInputStream in = new FileInputStream(file)) {
            //读取文件内容到字节数组
            in.read(content);
            return content;
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 得到文件内容，转换成规定编码的字符串
     *
     * @param filePath 文件路径
     * @param encoding 编码
     * @return 返回文件内容的字符串
     * @throws IOException 抛出IO异常
     */
    public static String readFileToString(String filePath, String encoding) throws IOException {
        byte[] bytes = readFileToBinary(filePath);
        return new String(bytes, encoding);
    }

    /**
     * 得到文件内容，转换成规定编码的字符串
     *
     * @param file     文件
     * @param encoding 编码
     * @return 返回文件内容的字符串
     * @throws IOException 抛出IO异常
     */
    public static String readFileToString(File file, String encoding) throws IOException {
        byte[] bytes = readFileToBinary(file);
        return new String(bytes, encoding);
    }

}
