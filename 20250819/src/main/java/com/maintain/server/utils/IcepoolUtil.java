package com.maintain.server.utils;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020-11-27
 */
public class IcepoolUtil {

    public static String extractIcePort() {
        String filePath = "./deploy/icepool.conf";
        List<String> lines;
        try {
            lines = FileUtils.readLines(new File(filePath));
        } catch (IOException e) {
            return "60000";
        }
        Optional<String> optional = lines.stream().filter(l -> l.contains("Endpoints=")).findAny();
        String port = "";
        if (optional.isPresent()) {
            String[] infos = optional.get().split("=")[1].split("\\s+");
            for (int i = 0; i < infos.length; i++) {
                if ("-p".equals(infos[i]) && (i + 1) < infos.length) {
                    port = infos[i + 1];
                }
            }
        }
        if (StringUtils.isEmpty(port)) {
            port = "60000";
        }
        return port;
    }
}