package com.maintain.server.utils;

import com.common.log.Log;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-11-19
 * 解析格式如：
 * ip	        role
 * 1.0.x.1	预处理主
 * 1.0.x.2	预处理从
 * 1.0.x.3	预处理从
 * 1.0.x.4	数据挖掘主
 */
public class ExcelUtil {

    /**
     * @param file 路径
     * @return Map<Integer, List<String>> <第x列,第x列的数据>
     */
    public static Map<Integer, List<String>> parseExcel(File file) {
        Map<Integer, List<String>> data;
        FileInputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            data = new HashMap<>();
            fileInputStream = new FileInputStream(file);
            workbook = new XSSFWorkbook(fileInputStream);
            int i = 0;
            Sheet sheet = workbook.getSheetAt(i);
            for (Row row : sheet) {
                List<String> list = new ArrayList<>();
                data.put(i, list);
                for (Cell cell : row) {
                    String value = cell.getStringCellValue();
                    if (StringUtils.isNotEmpty(value)) {
                        list.add(value);
                    }
                }
                i++;
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
            Log.high.error(e.getMessage(), e);
            return null;
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
                if (workbook != null) {
                    workbook.close();
                }
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
        }
        return data;
    }
}