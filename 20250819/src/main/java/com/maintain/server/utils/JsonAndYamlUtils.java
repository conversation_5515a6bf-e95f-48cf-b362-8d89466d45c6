package com.maintain.server.utils;

import com.fasterxml.jackson.core.JsonProcessingException;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020-08-12
 */
public class JsonAndYamlUtils {
/*    *//**
     * 读取json并生成yaml
     *//*
    public static void readJsontoMap(String json_url,String yaml_url) {
        try {
            String param = readJson(json_url);
            createYaml(yaml_url,param);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    *//**
     * 將json转化为yaml格式并生成yaml文件
     * @param jsonString
     * @return
     * @throws JsonProcessingException
     * @throws IOException
     *//*
    public static void createYaml(String yaml_url,String jsonString) throws IOException {
        JsonNode jsonNodeTree = new ObjectMapper().readTree(jsonString);
        String jsonAsYaml = new YAMLMapper().writeValueAsString(jsonNodeTree);
        Yaml yaml = new Yaml();
        Map<String,Object> map = (Map<String, Object>) yaml.load(jsonAsYaml);

    }

    *//**
     * 将数据写入yaml文件
     * @param url yaml文件路径
     * @param data 需要写入的数据
     *//**//*
    public static void createYamlFile(String url,Map<String, Object> data) throws IOException {
        Yaml yaml = new Yaml();
        FileWriter writer;
        writer = new FileWriter(url);
        yaml.dump(data, writer);
    }*/

    /**
     * 读取json文件并返回字符串
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static String readJson(String url) throws IOException {
        File file = new File(url);
        FileReader fileReader = new FileReader(file);
        BufferedReader bufReader = new BufferedReader(fileReader);
        String message = new String();
        String line = null;
        while ((line = bufReader.readLine()) != null) {
            message += line;
        }
        return message;
    }

}