package com.maintain.server.utils;

import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.common.log.Log;
import com.google.gson.Gson;
import com.maintain.server.Constants;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.logging.LogType;
import org.openqa.selenium.logging.LoggingPreferences;
import org.openqa.selenium.phantomjs.PhantomJSDriver;
import org.openqa.selenium.phantomjs.PhantomJSDriverService;
import org.openqa.selenium.remote.DesiredCapabilities;

import java.io.*;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;

public class WordUtils {

    //配置信息
    private static Configuration configuration = null;
    //这里注意的是利用WordUtils的类加载器动态获得模板文件的位置
    // private static final String templateFolder = WordUtils.class.getClassLoader().getResource("../../").getPath() + "WEB-INF/templetes/";
    private static final String templateFolder = "./config/";
    static {
        configuration = new Configuration();
        configuration.setDefaultEncoding("utf-8");
        try {
            configuration.setDirectoryForTemplateLoading(new File(templateFolder));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private WordUtils() {
        throw new AssertionError();
    }

    public static String exportMillCertificateWord(Map map,String fileName) throws IOException {
        Template freemarkerTemplate = configuration.getTemplate("template.ftl");
        File file = null;
        try {
            // 调用工具类的createDoc方法生成Word文档
            file = createDoc(map,freemarkerTemplate);
            // 设置浏览器以下载的方式处理该文件名
            final File direc = new File("./temp");
            if(!direc.exists()){
                direc.mkdirs();
            }
            File newFile = new File("./temp/"+fileName);
            newFile.createNewFile();
            FileUtils.copyFile(file,newFile);
            return newFile.getAbsolutePath();
        }catch (Exception e){
            e.printStackTrace();
        } finally {
            // 删除临时文件
            if(file != null) file.delete();
        }
        return null;
    }

    private static File createDoc(Map<?, ?> dataMap, Template template) {
        String name =  "sellPlan.doc";
        File f = new File(name);
        Template t = template;
        try {
            // 这个地方不能使用FileWriter因为需要指定编码类型否则生成的Word文档会因为有无法识别的编码而无法打开
            Writer w = new OutputStreamWriter(new FileOutputStream(f), "utf-8");
            t.process(dataMap, w);
            w.close();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        return f;
    }


    /**
     * base64图片生成文件
     * @param base64Str
     * @param file
     * @throws Exception
     */
    public static void decodeFile(String base64Str,File file) throws Exception {
        FileOutputStream write = null;
        try {
            write = new FileOutputStream(file);
            byte[] decoderBytes = Base64.decodeBase64(base64Str.getBytes("UTF-8"));
            write.write(decoderBytes);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }finally {
            try {
                if(write!=null)write.flush();
                if(write!=null)write.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 调用本地浏览器驱动生成图片
     */
    public static String derverGenerateImag(Map<String,Object> data,String path){
        WebDriver driver = getPhantomJSDriver();
        //设置超时时间为-1秒
        JavascriptExecutor js=(JavascriptExecutor) driver;
        driver.get(path);
        //休眠2秒等浏览器渲染完成后获取图片
        Gson gson = new Gson();
        String dataStr = gson.toJson(data);
        //返回图片base64编码
        driver.manage().timeouts().implicitlyWait(5, TimeUnit.SECONDS);
        js.executeScript("return getImage("+dataStr+")");
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        Object res = js.executeScript("return returnEcharts()");
//        File file = new File("d://yunwei//"+ UUID.randomUUID()+".jpg");
//        try {
//            decodeFile(res.toString(),file);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        LogEntries logEntries =driver.manage().logs().get(LogType.BROWSER);
//        for(LogEntry entry : logEntries) {
//            //依次打印出console信息
//            System.out.println("控制台输出:"+entry.getMessage());
//        }

        driver.quit();
        return String.valueOf(res);
    }

    public static PhantomJSDriver getPhantomJSDriver(){
        //设置必要参数
        DesiredCapabilities dcaps = new DesiredCapabilities();
        //ssl证书支持
        dcaps.setCapability("acceptSslCerts", true);
        //截屏支持
        dcaps.setCapability("takesScreenshot", false);
        //css搜索支持
        dcaps.setCapability("cssSelectorsEnabled", true);
        //js支持
        dcaps.setJavascriptEnabled(true);

        LoggingPreferences logPrefs = new LoggingPreferences();
        logPrefs.enable(LogType.BROWSER, Level.INFO);//输入为info的日志

        //驱动支持
        dcaps.setCapability(PhantomJSDriverService.PHANTOMJS_EXECUTABLE_PATH_PROPERTY,Constants.PHANTOMJS_PATH);
        PhantomJSDriver driver = new PhantomJSDriver(dcaps);
        return  driver;
    }


    public static void wordTopdf(String wordPath,String pdfPath){
        License license = new License();
        FileOutputStream fileOutputStream = null;
        try {
            license.setLicense(Constants.LICENSE);
            final Document nodes = new Document(wordPath);
            fileOutputStream = new FileOutputStream(new File(pdfPath));
            nodes.save(fileOutputStream, SaveFormat.PDF);
        } catch (Exception e) {
            Log.low.error("word转pdf异常",e);
        }finally {
            if(fileOutputStream != null){
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                }
            }
            final File file = new File(wordPath);
            if(file.exists()){
                file.delete();
            }
        }
    }

}
