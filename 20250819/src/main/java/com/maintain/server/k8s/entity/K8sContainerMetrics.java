package com.maintain.server.k8s.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * K8s容器监控数据实体
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
public class K8sContainerMetrics {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 容器ID
     */
    private Long containerId;
    
    /**
     * CPU使用率(%)
     */
    private BigDecimal cpuUsage;
    
    /**
     * 内存使用量(bytes)
     */
    private Long memoryUsage;
    
    /**
     * 内存使用率(%)
     */
    private BigDecimal memoryUsagePercent;
    
    /**
     * 网络接收字节数
     */
    private Long networkRxBytes;
    
    /**
     * 网络发送字节数
     */
    private Long networkTxBytes;
    
    /**
     * 磁盘使用量(bytes)
     */
    private Long diskUsage;
    
    /**
     * 磁盘使用率(%)
     */
    private BigDecimal diskUsagePercent;
    
    /**
     * 采集时间
     */
    private Date collectTime;
}
