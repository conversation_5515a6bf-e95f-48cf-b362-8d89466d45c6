package com.maintain.server.k8s.mapper;

import com.maintain.server.k8s.entity.K8sContainerInfo;
import com.maintain.server.k8s.entity.K8sContainerMetrics;
import org.apache.ibatis.annotations.*;
import java.util.List;
import java.util.Map;

/**
 * K8s容器数据访问层
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface K8sContainerMapper {
    
    /**
     * 插入或更新容器信息
     */
    @Insert("INSERT INTO k8s_container_info (container_name, pod_name, namespace, node_name, image, image_tag, " +
            "pod_ip, host_ip, ports, labels, status, restart_count, created_time, start_time, is_active) " +
            "VALUES (#{containerName}, #{podName}, #{namespace}, #{nodeName}, #{image}, #{imageTag}, " +
            "#{podIp}, #{hostIp}, #{ports}, #{labels}, #{status}, #{restartCount}, #{createdTime}, #{startTime}, #{isActive}) " +
            "ON DUPLICATE KEY UPDATE " +
            "node_name=#{nodeName}, image=#{image}, image_tag=#{imageTag}, pod_ip=#{podIp}, host_ip=#{hostIp}, " +
            "ports=#{ports}, labels=#{labels}, status=#{status}, restart_count=#{restartCount}, " +
            "start_time=#{startTime}, is_active=#{isActive}")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertOrUpdateContainer(K8sContainerInfo containerInfo);
    
    /**
     * 根据命名空间获取容器列表
     */
    @Select("SELECT * FROM k8s_container_info WHERE namespace = #{namespace} AND is_active = 1 ORDER BY pod_name, container_name")
    List<K8sContainerInfo> getContainersByNamespace(@Param("namespace") String namespace);
    
    /**
     * 根据容器名称模糊查询
     */
    @Select("SELECT * FROM k8s_container_info WHERE namespace = #{namespace} AND container_name LIKE CONCAT('%', #{name}, '%') AND is_active = 1")
    List<K8sContainerInfo> getContainersByName(@Param("namespace") String namespace, @Param("name") String name);
    
    /**
     * 根据状态获取容器列表
     */
    @Select("SELECT * FROM k8s_container_info WHERE namespace = #{namespace} AND status = #{status} AND is_active = 1")
    List<K8sContainerInfo> getContainersByStatus(@Param("namespace") String namespace, @Param("status") String status);
    
    /**
     * 获取容器状态统计
     */
    @Select("SELECT " +
            "SUM(CASE WHEN status = 'Running' THEN 1 ELSE 0 END) as running_count, " +
            "SUM(CASE WHEN status = 'Waiting' THEN 1 ELSE 0 END) as waiting_count, " +
            "SUM(CASE WHEN status = 'Terminated' THEN 1 ELSE 0 END) as terminated_count, " +
            "SUM(CASE WHEN status IS NULL OR status = '' THEN 1 ELSE 0 END) as unknown_count " +
            "FROM k8s_container_info WHERE namespace = #{namespace} AND is_active = 1")
    Map<String, Integer> getContainerStatusCount(@Param("namespace") String namespace);
    
    /**
     * 根据ID获取容器详情
     */
    @Select("SELECT * FROM k8s_container_info WHERE id = #{id}")
    K8sContainerInfo getContainerById(@Param("id") Long id);
    
    /**
     * 标记容器为非活跃状态
     */
    @Update("UPDATE k8s_container_info SET is_active = 0 WHERE namespace = #{namespace}")
    int markContainersInactive(@Param("namespace") String namespace);
    
    /**
     * 插入容器监控数据
     */
    @Insert("INSERT INTO k8s_container_metrics (container_id, cpu_usage, memory_usage, memory_usage_percent, " +
            "network_rx_bytes, network_tx_bytes, disk_usage, disk_usage_percent, collect_time) " +
            "VALUES (#{containerId}, #{cpuUsage}, #{memoryUsage}, #{memoryUsagePercent}, " +
            "#{networkRxBytes}, #{networkTxBytes}, #{diskUsage}, #{diskUsagePercent}, #{collectTime})")
    int insertContainerMetrics(K8sContainerMetrics metrics);
    
    /**
     * 获取容器最新监控数据
     */
    @Select("SELECT * FROM k8s_container_metrics WHERE container_id = #{containerId} " +
            "ORDER BY collect_time DESC LIMIT 1")
    K8sContainerMetrics getLatestMetrics(@Param("containerId") Long containerId);
    
    /**
     * 获取容器历史监控数据
     */
    @Select("SELECT * FROM k8s_container_metrics WHERE container_id = #{containerId} " +
            "AND collect_time BETWEEN #{startTime} AND #{endTime} ORDER BY collect_time")
    List<K8sContainerMetrics> getHistoryMetrics(@Param("containerId") Long containerId, 
                                               @Param("startTime") String startTime, 
                                               @Param("endTime") String endTime);
    
    /**
     * 删除过期监控数据
     */
    @Delete("DELETE FROM k8s_container_metrics WHERE collect_time < #{expireTime}")
    int deleteExpiredMetrics(@Param("expireTime") String expireTime);
}
