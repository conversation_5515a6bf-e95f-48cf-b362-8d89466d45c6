package com.maintain.server.k8s;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;

/**
 * K8s连接测试工具
 * <AUTHOR>
 * @date 2025-01-19
 */
@Slf4j
public class K8sConnectionTest {

    private static final String API_SERVER = "https://192.168.40.202:6443";
    private static final String TOKEN = "your-bearer-token-here";
    private static final String NAMESPACE = "default";

    public static void main(String[] args) {
        K8sConnectionTest test = new K8sConnectionTest();
        test.testConnection();
    }

    public void testConnection() {
        try {
            log.info("开始测试K8s连接...");
            
            // 1. 测试API Server连接
            testApiServer();
            
            // 2. 测试Pod列表获取
            testPodList();
            
            // 3. 测试Pod指标获取
            testPodMetrics();
            
            log.info("K8s连接测试完成");
        } catch (Exception e) {
            log.error("K8s连接测试失败", e);
        }
    }

    private void testApiServer() {
        try {
            String url = API_SERVER + "/api/v1";
            String response = httpGet(url);
            
            JSONObject result = JSON.parseObject(response);
            log.info("API Server连接成功，版本信息: {}", result.toJSONString());
        } catch (Exception e) {
            log.error("API Server连接失败", e);
        }
    }

    private void testPodList() {
        try {
            String url = API_SERVER + "/api/v1/namespaces/" + NAMESPACE + "/pods";
            String response = httpGet(url);
            
            JSONObject result = JSON.parseObject(response);
            int podCount = result.getJSONArray("items").size();
            log.info("Pod列表获取成功，共{}个Pod", podCount);
        } catch (Exception e) {
            log.error("Pod列表获取失败", e);
        }
    }

    private void testPodMetrics() {
        try {
            String url = API_SERVER + "/apis/metrics.k8s.io/v1beta1/namespaces/" + NAMESPACE + "/pods";
            String response = httpGet(url);
            
            JSONObject result = JSON.parseObject(response);
            int metricsCount = result.getJSONArray("items").size();
            log.info("Pod指标获取成功，共{}个Pod指标", metricsCount);
        } catch (Exception e) {
            log.error("Pod指标获取失败", e);
        }
    }

    private String httpGet(String urlStr) throws Exception {
        // 忽略SSL证书验证
        disableSSLVerification();
        
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Authorization", "Bearer " + TOKEN);
        conn.setRequestProperty("Accept", "application/json");
        conn.setConnectTimeout(10000);
        conn.setReadTimeout(30000);
        
        int responseCode = conn.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("HTTP请求失败，响应码: " + responseCode);
        }
        
        BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        StringBuilder response = new StringBuilder();
        String line;
        
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        
        reader.close();
        conn.disconnect();
        
        return response.toString();
    }

    private void disableSSLVerification() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
        } catch (Exception e) {
            log.warn("禁用SSL验证失败", e);
        }
    }
}