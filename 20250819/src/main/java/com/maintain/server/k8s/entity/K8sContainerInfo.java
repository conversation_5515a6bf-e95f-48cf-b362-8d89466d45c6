package com.maintain.server.k8s.entity;

import lombok.Data;
import java.util.Date;

/**
 * K8s容器信息实体
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
public class K8sContainerInfo {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 容器名称
     */
    private String containerName;
    
    /**
     * Pod名称
     */
    private String podName;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 镜像名称
     */
    private String image;
    
    /**
     * 镜像标签
     */
    private String imageTag;
    
    /**
     * Pod IP
     */
    private String podIp;
    
    /**
     * 宿主机IP
     */
    private String hostIp;
    
    /**
     * 端口信息
     */
    private String ports;
    
    /**
     * 标签信息JSON
     */
    private String labels;
    
    /**
     * 容器状态
     */
    private String status;
    
    /**
     * 重启次数
     */
    private Integer restartCount;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 启动时间
     */
    private Date startTime;
    
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    
    /**
     * 是否活跃(1:是 0:否)
     */
    private Boolean isActive;
}
