package com.maintain.server.k8s.service;

import com.maintain.server.k8s.dto.ContainerDto;
import com.maintain.server.k8s.dto.PodDto;
import com.maintain.server.k8s.entity.K8sContainerInfo;
import com.maintain.server.k8s.entity.K8sContainerMetrics;
import com.maintain.server.k8s.mapper.K8sContainerMapper;
import com.maintain.server.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * K8s数据采集服务
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@Service
public class K8sDataCollectService {
    
    @Autowired
    private K8sClient k8sClient;
    
    @Autowired
    private K8sContainerMapper containerMapper;
    
    /**
     * 定时采集k8s容器数据
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    @Transactional
    public void collectContainerData() {
        try {
            log.info("开始采集k8s容器数据...");
            
            // 1. 获取所有Pod数据
            List<PodDto> pods = k8sClient.getPods("default");
            log.info("获取到{}个Pod", pods.size());
            
            // 2. 先标记所有容器为非活跃状态
            containerMapper.markContainersInactive("default");
            
            // 3. 处理每个Pod中的容器
            int containerCount = 0;
            for (PodDto pod : pods) {
                if (pod.getContainers() != null) {
                    for (ContainerDto container : pod.getContainers()) {
                        try {
                            // 保存容器基本信息
                            K8sContainerInfo containerInfo = convertToContainerInfo(pod, container);
                            containerMapper.insertOrUpdateContainer(containerInfo);
                            
                            // 采集并保存监控数据
                            collectContainerMetrics(containerInfo.getId(), pod, container);
                            
                            containerCount++;
                        } catch (Exception e) {
                            log.error("处理容器失败: pod={}, container={}", pod.getName(), container.getName(), e);
                        }
                    }
                }
            }
            
            log.info("k8s容器数据采集完成，共处理{}个容器", containerCount);
            
        } catch (Exception e) {
            log.error("k8s容器数据采集失败", e);
        }
    }
    
    /**
     * 转换Pod和Container数据为容器信息实体
     */
    private K8sContainerInfo convertToContainerInfo(PodDto pod, ContainerDto container) {
        K8sContainerInfo info = new K8sContainerInfo();
        
        // 基本信息
        info.setContainerName(container.getName());
        info.setPodName(pod.getName());
        info.setNamespace(pod.getNamespace());
        info.setNodeName(pod.getNodeName());
        info.setImage(container.getImage());
        info.setImageTag(extractImageTag(container.getImage()));
        info.setPodIp(pod.getPodIP());
        info.setHostIp(pod.getHostIP());
        info.setPorts(container.getPorts());
        info.setLabels(pod.getLabels());
        info.setStatus(container.getState());
        info.setRestartCount(container.getRestartCount());
        info.setIsActive(true);
        
        // 时间信息
        try {
            if (pod.getCreationTimestamp() != null) {
                info.setCreatedTime(parseK8sTime(pod.getCreationTimestamp()));
            }
            if (pod.getStartTime() != null) {
                info.setStartTime(parseK8sTime(pod.getStartTime()));
            }
        } catch (Exception e) {
            log.warn("解析时间失败: {}", e.getMessage());
        }
        
        return info;
    }
    
    /**
     * 采集容器监控数据
     */
    private void collectContainerMetrics(Long containerId, PodDto pod, ContainerDto container) {
        try {
            // 获取Pod指标数据
            String metricsJson = k8sClient.getPodMetrics(pod.getNamespace(), pod.getName());
            
            // 解析指标数据并保存
            K8sContainerMetrics metrics = parseMetricsData(containerId, metricsJson, container.getName());
            if (metrics != null) {
                containerMapper.insertContainerMetrics(metrics);
            }
            
        } catch (Exception e) {
            log.warn("采集容器监控数据失败: pod={}, container={}", pod.getName(), container.getName(), e);
            
            // 如果获取真实指标失败，生成模拟数据
            K8sContainerMetrics mockMetrics = generateMockMetrics(containerId);
            containerMapper.insertContainerMetrics(mockMetrics);
        }
    }
    
    /**
     * 解析k8s指标数据
     */
    private K8sContainerMetrics parseMetricsData(Long containerId, String metricsJson, String containerName) {
        try {
            // 这里需要根据实际的k8s metrics API响应格式来解析
            // 暂时返回null，使用模拟数据
            return null;
        } catch (Exception e) {
            log.error("解析指标数据失败", e);
            return null;
        }
    }
    
    /**
     * 生成模拟监控数据
     */
    private K8sContainerMetrics generateMockMetrics(Long containerId) {
        K8sContainerMetrics metrics = new K8sContainerMetrics();
        metrics.setContainerId(containerId);
        metrics.setCpuUsage(new BigDecimal(Math.random() * 50)); // 0-50%
        metrics.setMemoryUsage((long) (Math.random() * 2 * 1024 * 1024 * 1024)); // 0-2GB
        metrics.setMemoryUsagePercent(new BigDecimal(Math.random() * 80)); // 0-80%
        metrics.setNetworkRxBytes((long) (Math.random() * 1024 * 1024)); // 0-1MB
        metrics.setNetworkTxBytes((long) (Math.random() * 1024 * 1024)); // 0-1MB
        metrics.setDiskUsage((long) (Math.random() * 10 * 1024 * 1024 * 1024)); // 0-10GB
        metrics.setDiskUsagePercent(new BigDecimal(Math.random() * 60)); // 0-60%
        metrics.setCollectTime(new Date());
        return metrics;
    }
    
    /**
     * 提取镜像标签
     */
    private String extractImageTag(String image) {
        if (image != null && image.contains(":")) {
            return image.substring(image.lastIndexOf(":") + 1);
        }
        return "latest";
    }
    
    /**
     * 解析k8s时间格式
     */
    private Date parseK8sTime(String timeStr) {
        // 这里需要根据实际的k8s时间格式来解析
        // 暂时返回当前时间
        return new Date();
    }
    
    /**
     * 清理过期监控数据
     * 每天凌晨2点执行，删除7天前的数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredMetrics() {
        try {
            // 删除7天前的监控数据
            long expireTime = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L;
            String expireTimeStr = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(new Date(expireTime));
            
            int deletedCount = containerMapper.deleteExpiredMetrics(expireTimeStr);
            log.info("清理过期监控数据完成，删除{}条记录", deletedCount);
            
        } catch (Exception e) {
            log.error("清理过期监控数据失败", e);
        }
    }
}
