package com.maintain.server.k8s;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.SoftwareOperateType;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.SoftwareVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * K8s数据采集服务
 * <AUTHOR>
 * @date 2025-01-19
 */
@Slf4j
@Service
public class K8sDataCollector {

    @Autowired
    private SoftwareService softwareService;
    
    @Autowired
    private AgentService agentService;

    @Value("${k8s.api-server:}")
    private String apiServer;

    @Value("${k8s.token:}")
    private String token;

    @Value("${k8s.namespace:default}")
    private String namespace;

    @Value("${k8s.enabled:false}")
    private boolean enabled;

    /**
     * 每30秒采集一次K8s数据
     */
    @Scheduled(fixedRate = 30000)
    public void collectK8sData() {
        if (!enabled || StringUtils.isEmpty(apiServer) || StringUtils.isEmpty(token)) {
            return;
        }

        try {
            log.debug("开始采集K8s数据...");
            
            // 1. 获取Pod列表
            JSONArray pods = getPodList();
            if (pods == null || pods.isEmpty()) {
                log.warn("未获取到Pod数据");
                return;
            }

            // 2. 获取Pod指标数据
            Map<String, JSONObject> metricsMap = getPodMetrics();

            // 3. 转换并存储数据
            for (int i = 0; i < pods.size(); i++) {
                JSONObject pod = pods.getJSONObject(i);
                processPodData(pod, metricsMap);
            }

            log.debug("K8s数据采集完成，处理了{}个Pod", pods.size());
        } catch (Exception e) {
            log.error("K8s数据采集失败", e);
        }
    }

    /**
     * 获取Pod列表
     */
    private JSONArray getPodList() {
        try {
            String url = apiServer + "/api/v1/namespaces/" + namespace + "/pods";
            String response = httpGet(url);
            
            JSONObject result = JSON.parseObject(response);
            return result.getJSONArray("items");
        } catch (Exception e) {
            log.error("获取Pod列表失败", e);
            return null;
        }
    }

    /**
     * 获取Pod指标数据
     */
    private Map<String, JSONObject> getPodMetrics() {
        Map<String, JSONObject> metricsMap = new HashMap<>();
        try {
            String url = apiServer + "/apis/metrics.k8s.io/v1beta1/namespaces/" + namespace + "/pods";
            String response = httpGet(url);
            
            JSONObject result = JSON.parseObject(response);
            JSONArray items = result.getJSONArray("items");
            
            if (items != null) {
                for (int i = 0; i < items.size(); i++) {
                    JSONObject item = items.getJSONObject(i);
                    JSONObject metadata = item.getJSONObject("metadata");
                    String podName = metadata.getString("name");
                    metricsMap.put(podName, item);
                }
            }
        } catch (Exception e) {
            log.error("获取Pod指标数据失败", e);
        }
        return metricsMap;
    }

    /**
     * 处理单个Pod数据
     */
    private void processPodData(JSONObject pod, Map<String, JSONObject> metricsMap) {
        try {
            JSONObject metadata = pod.getJSONObject("metadata");
            JSONObject spec = pod.getJSONObject("spec");
            JSONObject status = pod.getJSONObject("status");

            String podName = metadata.getString("name");
            String nodeName = spec.getString("nodeName");
            String phase = status.getString("phase");
            String creationTimestamp = metadata.getString("creationTimestamp");

            // 获取节点IP
            String nodeIp = getNodeIp(nodeName);
            
            // 获取或创建Agent记录，设置serverId
            Integer serverId = getOrCreateAgentId(nodeIp);

            // 转换为SoftwareVo对象
            SoftwareVo softwareVo = new SoftwareVo();
            softwareVo.setName(podName);
            softwareVo.setServerIp(nodeIp);
            softwareVo.setHost(nodeIp);
            softwareVo.setServerId(serverId);
            
            // 设置容器数量
            JSONArray containers = spec.getJSONArray("containers");
            softwareVo.setProcessCount(containers != null ? containers.size() : 1);
            
            // 设置状态
            softwareVo.setStatus(convertPodStatus(phase));
            softwareVo.setProgramStatus(convertProgramStatus(phase));
            
            // 设置创建时间
            if (!StringUtils.isEmpty(creationTimestamp)) {
                softwareVo.setCreateTime(parseK8sTime(creationTimestamp));
            }

            // 设置资源使用情况和新增参数
            JSONObject metrics = metricsMap.get(podName);
            if (metrics != null) {
                setResourceUsage(softwareVo, metrics, nodeName);
            }
            
            // 1. 设置PID - Pod内主进程PID
            setPodPid(softwareVo, status);
            
            // 2. 设置启动参数 - 容器启动命令和参数
            setStartParam(softwareVo, spec);
            
            // 3. 设置磁盘使用率
            setDiskPercent(softwareVo, nodeName);

            // 设置其他默认值
            softwareVo.setVersion("k8s-pod");
            softwareVo.setAutoDaemon(1);
            softwareVo.setHeartMonitor(true);
            softwareVo.setConfig(false);
            softwareVo.setRestartCount(0);
            softwareVo.setLogCount(0);
            softwareVo.setIsExists(1);
            softwareVo.setOperateType(com.maintain.server.type.SoftwareOperateType.OPEN);
            
            // 保存到数据库，K8s数据不需要推送到Agent
            if (softwareService.getSoftwareInfo(createCriteria(softwareVo)) == null) {
                softwareVo.setConfig(Boolean.FALSE);
                softwareService.addSoftwareInfo(softwareVo);
            } else {
                softwareService.updateSoftwareInfo(softwareVo, false);
            }
            
        } catch (Exception e) {
            log.error("处理Pod数据失败", e);
        }
    }

    /**
     * 设置资源使用情况
     */
    private void setResourceUsage(SoftwareVo softwareVo, JSONObject metrics, String nodeName) {
        try {
            JSONArray containers = metrics.getJSONArray("containers");
            if (containers == null || containers.isEmpty()) {
                return;
            }

            // 1. 计算Pod资源使用总和
            long totalCpuNano = 0;
            long totalMemoryBytes = 0;

            for (int i = 0; i < containers.size(); i++) {
                JSONObject container = containers.getJSONObject(i);
                JSONObject usage = container.getJSONObject("usage");
                
                if (usage != null) {
                    String cpu = usage.getString("cpu");
                    String memory = usage.getString("memory");
                    
                    if (!StringUtils.isEmpty(cpu)) {
                        totalCpuNano += parseCpuUsage(cpu);
                    }
                    
                    if (!StringUtils.isEmpty(memory)) {
                        totalMemoryBytes += parseMemoryUsage(memory);
                    }
                }
            }

            // 2. 获取节点资源信息
            Map<String, Object> nodeResources = getNodeResources(nodeName);
            
            // 3. 计算CPU使用率
            if (totalCpuNano > 0 && nodeResources != null) {
                Integer cpuCores = (Integer) nodeResources.get("cpuCores");
                if (cpuCores != null && cpuCores > 0) {
                    // CPU使用率 = Pod使用纳秒 / (核数 * 10^9) * 100
                    double cpuPercent = (double) totalCpuNano / (cpuCores * 1000000000.0) * 100;
                    softwareVo.setCpuPercent(String.format("%.2f%%", Math.min(cpuPercent, 100.0)));
                }
            }

            // 4. 计算内存使用率和设置总内存
            if (totalMemoryBytes > 0) {
                double memoryMB = (double) totalMemoryBytes / 1024 / 1024;
                softwareVo.setUsedMemory(String.format("%.2fM", memoryMB));
                
                if (nodeResources != null) {
                    Long totalNodeMemory = (Long) nodeResources.get("totalMemoryBytes");
                    if (totalNodeMemory != null && totalNodeMemory > 0) {
                        // 内存使用率 = Pod使用字节 / 节点总内存字节 * 100
                        double memoryPercent = (double) totalMemoryBytes / totalNodeMemory * 100;
                        softwareVo.setMemoryPercent(String.format("%.2f%%", Math.min(memoryPercent, 100.0)));
                        
                        // 5. 设置TOTAL_MEMORY - Pod分配的总内存或节点总内存
                        softwareVo.setTotalMemory(totalNodeMemory);
                    }
                }
            }

        } catch (Exception e) {
            log.error("设置资源使用情况失败", e);
        }
    }

    /**
     * 解析CPU使用量（纳秒）
     */
    private long parseCpuUsage(String cpu) {
        if (StringUtils.isEmpty(cpu)) {
            return 0;
        }
        
        try {
            if (cpu.endsWith("n")) {
                return Long.parseLong(cpu.substring(0, cpu.length() - 1));
            } else if (cpu.endsWith("u")) {
                return Long.parseLong(cpu.substring(0, cpu.length() - 1)) * 1000;
            } else if (cpu.endsWith("m")) {
                return Long.parseLong(cpu.substring(0, cpu.length() - 1)) * 1000000;
            } else {
                return Long.parseLong(cpu) * 1000000000;
            }
        } catch (Exception e) {
            log.warn("解析CPU使用量失败: {}", cpu);
            return 0;
        }
    }

    /**
     * 解析内存使用量（字节）
     */
    private long parseMemoryUsage(String memory) {
        if (StringUtils.isEmpty(memory)) {
            return 0;
        }
        
        try {
            if (memory.endsWith("Ki")) {
                return Long.parseLong(memory.substring(0, memory.length() - 2)) * 1024;
            } else if (memory.endsWith("Mi")) {
                return Long.parseLong(memory.substring(0, memory.length() - 2)) * 1024 * 1024;
            } else if (memory.endsWith("Gi")) {
                return Long.parseLong(memory.substring(0, memory.length() - 2)) * 1024 * 1024 * 1024;
            } else {
                return Long.parseLong(memory);
            }
        } catch (Exception e) {
            log.warn("解析内存使用量失败: {}", memory);
            return 0;
        }
    }

    /**
     * 获取或创建Agent ID
     */
    private Integer getOrCreateAgentId(String nodeIp) {
        try {
            // 1. 先查询是否存在该IP的Agent
            AgentCriteria criteria = new AgentCriteria();
            criteria.setIp(nodeIp);
            AgentVo existingAgent = agentService.getAgentByCriteria(criteria);
            
            if (existingAgent != null) {
                return existingAgent.getId();
            }
            
            // 2. 创建K8s虚拟Agent记录，补齐所有NOT NULL字段
            AgentVo newAgent = new AgentVo();
            newAgent.setIp(nodeIp);
            newAgent.setVersion("1.0");
            newAgent.setName("K8s-Node-" + nodeIp);
            newAgent.setPswd("k8s-default");
            newAgent.setRootPswd("k8s-root");
            newAgent.setRole("K8s-Node");
            newAgent.setOs(1); // 1表示Linux系统
            newAgent.setPort(22);
            newAgent.setAutoNTP(0);
            newAgent.setAutoWatchDog(0);
            newAgent.setPid(-1); // -1表示未启动
            newAgent.setOperateType(SoftwareOperateType.OPEN);
            newAgent.setNtpStatus(0);
            newAgent.setExistsVpn(0);
            newAgent.setProgramStatus(ProgramStatusType.OPEN);
            newAgent.setStatus(AlarmStatusType.GREEN);
            newAgent.setDescription("K8s节点自动创建");
            newAgent.setNote("K8s集群节点");
            
            // 3. 添加Agent，MyBatis会自动设置ID
            agentService.addAgent(newAgent);
            
            // 4. 返回自动生成的ID
            return newAgent.getId();
            
        } catch (Exception e) {
            log.error("获取或创建Agent ID失败，IP: {}", nodeIp, e);
            return 1; // 返回默认ID
        }
    }

    /**
     * 获取节点资源信息
     */
    private Map<String, Object> getNodeResources(String nodeName) {
        try {
            // 1. 获取节点基本信息
            String nodeUrl = apiServer + "/api/v1/nodes/" + nodeName;
            String nodeResponse = httpGet(nodeUrl);
            JSONObject node = JSON.parseObject(nodeResponse);
            
            // 2. 获取节点指标信息
            String metricsUrl = apiServer + "/apis/metrics.k8s.io/v1beta1/nodes/" + nodeName;
            String metricsResponse = httpGet(metricsUrl);
            JSONObject nodeMetrics = JSON.parseObject(metricsResponse);
            
            Map<String, Object> resources = new HashMap<>();
            
            // 3. 解析CPU核数
            JSONObject status = node.getJSONObject("status");
            JSONObject capacity = status.getJSONObject("capacity");
            String cpuCapacity = capacity.getString("cpu"); // 如 "4" 表示4核
            resources.put("cpuCores", Integer.parseInt(cpuCapacity));
            
            // 4. 解析总内存
            String memoryCapacity = capacity.getString("memory"); // 如 "8Gi"
            long totalMemoryBytes = parseMemoryUsage(memoryCapacity);
            resources.put("totalMemoryBytes", totalMemoryBytes);
            
            // 5. 获取节点当前使用量（可选，用于更精确计算）
            if (nodeMetrics != null) {
                JSONObject usage = nodeMetrics.getJSONObject("usage");
                if (usage != null) {
                    String nodeCpuUsage = usage.getString("cpu");
                    String nodeMemoryUsage = usage.getString("memory");
                    resources.put("nodeCpuUsage", parseCpuUsage(nodeCpuUsage));
                    resources.put("nodeMemoryUsage", parseMemoryUsage(nodeMemoryUsage));
                }
            }
            
            return resources;
        } catch (Exception e) {
            log.warn("获取节点{}资源信息失败", nodeName, e);
            return null;
        }
    }

    /**
     * 获取节点IP
     */
    private String getNodeIp(String nodeName) {
        if (StringUtils.isEmpty(nodeName)) {
            return "unknown";
        }
        
        try {
            String url = apiServer + "/api/v1/nodes/" + nodeName;
            String response = httpGet(url);
            
            JSONObject node = JSON.parseObject(response);
            JSONObject status = node.getJSONObject("status");
            JSONArray addresses = status.getJSONArray("addresses");
            
            if (addresses != null) {
                for (int i = 0; i < addresses.size(); i++) {
                    JSONObject address = addresses.getJSONObject(i);
                    String type = address.getString("type");
                    if ("InternalIP".equals(type)) {
                        return address.getString("address");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取节点IP失败: {}", nodeName);
        }
        
        return nodeName; // 如果获取不到IP，返回节点名
    }

    /**
     * 转换Pod状态为告警状态
     */
    private AlarmStatusType convertPodStatus(String phase) {
        if (StringUtils.isEmpty(phase)) {
            return AlarmStatusType.GRAY;
        }
        
        switch (phase.toLowerCase()) {
            case "running":
                return AlarmStatusType.GREEN;
            case "pending":
                return AlarmStatusType.YELLOW;
            case "failed":
            case "unknown":
                return AlarmStatusType.RED;
            case "succeeded":
                return AlarmStatusType.GREEN;
            default:
                return AlarmStatusType.GRAY;
        }
    }

    /**
     * 转换Pod状态为程序状态
     */
    private ProgramStatusType convertProgramStatus(String phase) {
        if (StringUtils.isEmpty(phase)) {
            return ProgramStatusType.CLOSE;
        }
        
        switch (phase.toLowerCase()) {
            case "running":
            case "succeeded":
                return ProgramStatusType.OPEN;
            default:
                return ProgramStatusType.CLOSE;
        }
    }

    /**
     * 解析K8s时间格式
     */
    private Date parseK8sTime(String timeStr) {
        try {
            // K8s时间格式: 2025-01-19T10:30:00Z
            return new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").parse(timeStr);
        } catch (Exception e) {
            log.warn("解析K8s时间失败: {}", timeStr);
            return new Date();
        }
    }

    /**
     * HTTP GET请求
     */
    private String httpGet(String urlStr) throws Exception {
        // 忽略SSL证书验证
        disableSSLVerification();
        
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Authorization", "Bearer " + token);
        conn.setRequestProperty("Accept", "application/json");
        conn.setConnectTimeout(10000);
        conn.setReadTimeout(30000);
        
        int responseCode = conn.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("HTTP请求失败，响应码: " + responseCode);
        }
        
        BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        StringBuilder response = new StringBuilder();
        String line;
        
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        
        reader.close();
        conn.disconnect();
        
        return response.toString();
    }

    /**
     * 创建软件查询条件
     */
    private SoftwareCriteria createCriteria(SoftwareVo softwareVo) {
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setName(softwareVo.getName());
        criteria.setServerId(softwareVo.getServerId());
        return criteria;
    }

    /**
     * 设置Pod内主进程PID
     */
    private void setPodPid(SoftwareVo softwareVo, JSONObject status) {
        try {
            // 1. 从Pod状态中获取容器状态
            JSONArray containerStatuses = status.getJSONArray("containerStatuses");
            if (containerStatuses != null && !containerStatuses.isEmpty()) {
                // 2. 获取第一个容器的状态信息
                JSONObject firstContainer = containerStatuses.getJSONObject(0);
                JSONObject state = firstContainer.getJSONObject("state");
                
                if (state != null && state.containsKey("running")) {
                    // 3. 如果容器正在运行，设置一个模拟PID
                    // 在K8s环境下，无法直接获取容器内进程PID，使用容器ID的hash值作为PID
                    String containerId = firstContainer.getString("containerID");
                    if (!StringUtils.isEmpty(containerId)) {
                        // 4. 从containerID中提取短ID作为PID
                        String shortId = containerId.substring(containerId.lastIndexOf("/") + 1);
                        if (shortId.length() > 8) {
                            shortId = shortId.substring(0, 8);
                        }
                        // 5. 转换为数字PID
                        int pid = Math.abs(shortId.hashCode()) % 99999 + 1000;
                        softwareVo.setPid(pid);
                        log.debug("设置Pod {} PID: {}", softwareVo.getName(), pid);
                        return;
                    }
                }
            }
            
            // 6. 默认PID设置
            softwareVo.setPid(-1); // -1表示无法获取PID
            
        } catch (Exception e) {
            log.warn("设置Pod PID失败: {}", softwareVo.getName(), e);
            softwareVo.setPid(-1);
        }
    }

    /**
     * 设置容器启动参数
     */
    private void setStartParam(SoftwareVo softwareVo, JSONObject spec) {
        try {
            StringBuilder startParams = new StringBuilder();
            
            // 1. 获取容器配置
            JSONArray containers = spec.getJSONArray("containers");
            if (containers != null && !containers.isEmpty()) {
                JSONObject firstContainer = containers.getJSONObject(0);
                
                // 2. 获取镜像名称
                String image = firstContainer.getString("image");
                if (!StringUtils.isEmpty(image)) {
                    startParams.append("image=").append(image);
                }
                
                // 3. 获取启动命令
                JSONArray command = firstContainer.getJSONArray("command");
                if (command != null && !command.isEmpty()) {
                    if (startParams.length() > 0) startParams.append(" ");
                    startParams.append("command=[");
                    for (int i = 0; i < command.size(); i++) {
                        if (i > 0) startParams.append(",");
                        startParams.append(command.getString(i));
                    }
                    startParams.append("]");
                }
                
                // 4. 获取启动参数
                JSONArray args = firstContainer.getJSONArray("args");
                if (args != null && !args.isEmpty()) {
                    if (startParams.length() > 0) startParams.append(" ");
                    startParams.append("args=[");
                    for (int i = 0; i < args.size(); i++) {
                        if (i > 0) startParams.append(",");
                        startParams.append(args.getString(i));
                    }
                    startParams.append("]");
                }
                
                // 5. 获取环境变量（部分重要的）
                JSONArray env = firstContainer.getJSONArray("env");
                if (env != null && !env.isEmpty()) {
                    if (startParams.length() > 0) startParams.append(" ");
                    startParams.append("env=[");
                    int envCount = 0;
                    for (int i = 0; i < env.size() && envCount < 3; i++) {
                        JSONObject envVar = env.getJSONObject(i);
                        String name = envVar.getString("name");
                        String value = envVar.getString("value");
                        if (!StringUtils.isEmpty(name)) {
                            if (envCount > 0) startParams.append(",");
                            startParams.append(name).append("=").append(value != null ? value : "");
                            envCount++;
                        }
                    }
                    if (env.size() > 3) {
                        startParams.append(",...");
                    }
                    startParams.append("]");
                }
            }
            
            // 6. 设置启动参数，限制长度
            String finalParams = startParams.toString();
            if (finalParams.length() > 500) {
                finalParams = finalParams.substring(0, 497) + "...";
            }
            
            softwareVo.setStartParam(StringUtils.isEmpty(finalParams) ? "k8s-pod" : finalParams);
            log.debug("设置Pod {} 启动参数: {}", softwareVo.getName(), finalParams);
            
        } catch (Exception e) {
            log.warn("设置启动参数失败: {}", softwareVo.getName(), e);
            softwareVo.setStartParam("k8s-pod");
        }
    }

    /**
     * 设置磁盘使用率
     */
    private void setDiskPercent(SoftwareVo softwareVo, String nodeName) {
        try {
            // 1. 获取节点资源信息
            Map<String, Object> nodeResources = getNodeResources(nodeName);
            if (nodeResources == null) {
                softwareVo.setDiskPercent("0.00%");
                return;
            }
            
            // 2. 尝试获取节点磁盘使用情况
            // 注意：K8s metrics API通常不直接提供磁盘使用率
            // 这里使用节点总体磁盘使用情况作为Pod的磁盘使用率参考
            
            try {
                String nodeUrl = apiServer + "/api/v1/nodes/" + nodeName;
                String nodeResponse = httpGet(nodeUrl);
                JSONObject node = JSON.parseObject(nodeResponse);
                
                JSONObject status = node.getJSONObject("status");
                JSONObject capacity = status.getJSONObject("capacity");
                JSONObject allocatable = status.getJSONObject("allocatable");
                
                // 3. 获取存储容量信息
                String storageCapacity = capacity.getString("ephemeral-storage");
                String storageAllocatable = allocatable.getString("ephemeral-storage");
                
                if (!StringUtils.isEmpty(storageCapacity) && !StringUtils.isEmpty(storageAllocatable)) {
                    long totalStorage = parseMemoryUsage(storageCapacity);
                    long allocatableStorage = parseMemoryUsage(storageAllocatable);
                    long usedStorage = totalStorage - allocatableStorage;
                    
                    if (totalStorage > 0) {
                        double diskPercent = (double) usedStorage / totalStorage * 100;
                        softwareVo.setDiskPercent(String.format("%.2f%%", Math.min(diskPercent, 100.0)));
                        log.debug("设置Pod {} 磁盘使用率: {}", softwareVo.getName(), softwareVo.getDiskPercent());
                        return;
                    }
                }
            } catch (Exception e) {
                log.debug("获取节点磁盘信息失败，使用默认值", e);
            }
            
            // 4. 默认磁盘使用率（模拟值）
            // 在无法获取真实磁盘使用率的情况下，设置一个合理的默认值
            softwareVo.setDiskPercent("5.00%");
            
        } catch (Exception e) {
            log.warn("设置磁盘使用率失败: {}", softwareVo.getName(), e);
            softwareVo.setDiskPercent("0.00%");
        }
    }

    /**
     * 禁用SSL证书验证
     */
    private void disableSSLVerification() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
        } catch (Exception e) {
            log.warn("禁用SSL验证失败", e);
        }
    }
}