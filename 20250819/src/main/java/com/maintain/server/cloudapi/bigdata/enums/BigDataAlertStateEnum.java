package com.maintain.server.cloudapi.bigdata.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大数据告警状态
 *
 * 这五种告警状态定义了不同严重程度的系统告警级别：
 *
 * 1. OK(0) - 正常状态
 * 含义：系统或组件运行完全正常，无需任何关注或操作
 *
 * 注释说明："Alert does not need to be distributed. Normal Operation."
 *
 * 典型场景：系统各项指标均在正常范围内，无任何异常
 *
 * 2. WARNING(2) - 警告状态
 * 含义：系统可能出现潜在问题，虽然当前仍能正常运行，但有可能发展为严重问题
 *
 * 注释说明："Alert indicates there may be an issue. The component may be operating normally but may be in danger of becoming CRITICAL."
 *
 * 典型场景：
 *
 * 磁盘使用率达到85%
 *
 * CPU负载持续偏高但未超阈值
 *
 * 内存使用量接近上限
 *
 * 3. CRITICAL(3) - 严重状态
 * 含义：系统存在严重问题，需要立即处理
 *
 * 注释说明："Indicates there is a critical situation that needs to be addressed."
 *
 * 典型场景：
 *
 * 服务不可用
 *
 * 磁盘空间耗尽
 *
 * 关键进程崩溃
 *
 * 数据库连接池耗尽
 *
 * 4. UNKNOWN(1) - 未知状态
 * 含义：系统或组件状态无法确定
 *
 * 注释说明："The state of the alert is not known."
 *
 * 典型场景：
 *
 * 监控系统与被监控组件通信中断
 *
 * 无法获取组件状态信息
 *
 * 监控指标数据过期
 *
 * 5. SKIPPED(4) - 跳过状态
 * 含义：应忽略此告警的状态，但需要更新其时间戳以避免被视为陈旧告警
 *
 * 注释说明："Indicates that the state of the alert should be discarded, but the alert timestamps should be updated so that it is not considered stale."
 *
 * 典型场景：
 *
 * 计划内的维护期间临时忽略告警
 *
 * 已知问题正在解决中
 *
 * 非关键性指标的临时波动
 * <AUTHOR>
 * @date 2025/8/19
 */
@Getter
@AllArgsConstructor
public enum BigDataAlertStateEnum {
    /**
     * 告警
     */
    OK("OK","正常"),


    WARNING("WARNING","警告"),


    CRITICAL("CRITICAL","严重"),


    UNKNOWN("UNKNOWN","未知"),


    SKIPPED("SKIPPED","跳过");
    private final String code;
    private final String name;
}
