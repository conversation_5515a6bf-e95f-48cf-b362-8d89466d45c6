package com.maintain.server.cloudapi.bigdata.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@NoArgsConstructor
@Data
public class HistoryAlertApiDto {

    @JsonProperty("cluster_name")
    private String clusterName;
    @JsonProperty("component_name")
    private String componentName;
    @JsonProperty("definition_id")
    private Integer definitionId;
    @JsonProperty("definition_name")
    private String definitionName;
    @JsonProperty("host_name")
    private String hostName;
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("label")
    private String label;
    @JsonProperty("service_name")
    private String serviceName;
    @JsonProperty("state")
    private String state;
    @JsonProperty("text")
    private String text;
    @JsonProperty("timestamp")
    private Long timestamp;
}
