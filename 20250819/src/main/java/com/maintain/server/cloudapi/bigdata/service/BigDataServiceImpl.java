package com.maintain.server.cloudapi.bigdata.service;

import com.maintain.server.cloudapi.bigdata.dto.*;
import com.maintain.server.config.CommonConfigProperties;
import com.maintain.server.dto.Tuple;
import com.maintain.server.dto.req.*;
import com.maintain.server.entity.*;
import com.maintain.server.enums.ConfigModuleEnum;
import com.maintain.server.enums.ConfigTtypeEnum;
import com.maintain.server.mapper.bigdata.*;
import com.maintain.server.service.ConfigService;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.PageHelperUtil;
import com.maintain.server.vo.ConfigVo;
import com.maintain.server.vo.PageVo;
import com.maintain.server.vo.bigdata.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
@Service
public class BigDataServiceImpl implements BigDataService {
    public static final int BATCH_SIZE = 1000;
    @Autowired
    private BigdataApi bigdataApi;
    @Resource
    private BdHostMapper bdHostMapper;
    @Resource
    private BdServiceMapper bdServiceMapper;
    @Resource
    private BdComponentMapper bdComponentMapper;
    @Resource
    private BdAlertMapper bdAlertMapper;
    @Resource
    private BdHistoryAlertMapper bdHistoryAlertMapper;
    @Autowired
    private ConfigService configService;
    @Autowired
    private CommonConfigProperties commonConfigProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void apiDataToMysql() {
        String clusterName = bigdataApi.getClusterName();

        saveHosts(clusterName);
        saveServices(clusterName);
        saveComponents(clusterName);
        saveCurrentAlerts(clusterName);
        saveHistoryAlerts(clusterName);
    }

    @Override
    public PageVo<BdHostVo> pageHost(BdHostReq req) {
        PageVo<BdHostPo> pageList = PageHelperUtil.startPage(req, () -> bdHostMapper.findPage(req));

        List<BdHostVo> res = pageList.getList()
                .stream()
                .map(po -> {
                    BdHostVo vo = new BdHostVo();
                    BeanUtils.copyProperties(po, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), res);
    }

    @Override
    public PageVo<BdServiceVo> pageService(BdServiceReq req) {
        PageVo<BdServicePo> pageList = PageHelperUtil.startPage(req, () -> bdServiceMapper.findPage(req));

        List<BdServiceVo> res = pageList.getList()
                .stream()
                .map(po -> {
                    BdServiceVo vo = new BdServiceVo();
                    BeanUtils.copyProperties(po, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), res);
    }

    @Override
    public PageVo<BdComponentVo> pageComponent(BdComponentReq req) {
        PageVo<BdComponentPo> pageList = PageHelperUtil.startPage(req, () -> bdComponentMapper.findPage(req));

        List<BdComponentVo> res = pageList.getList()
                .stream()
                .map(po -> {
                    BdComponentVo vo = new BdComponentVo();
                    BeanUtils.copyProperties(po, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), res);
    }

    @Override
    public PageVo<BdAlertVo> getAlerts(BdAlertReq req) {
        PageVo<BdAlertPo> pageList = PageHelperUtil.startPage(req, () -> bdAlertMapper.findPage(req));

        List<BdAlertVo> res = pageList.getList()
                .stream()
                .map(po -> {
                    BdAlertVo vo = new BdAlertVo();
                    BeanUtils.copyProperties(po, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), res);
    }

    @Override
    public PageVo<BdHistoryAlertVo> getHistoryAlerts(BdHistoryAlertReq req) {
        PageVo<BdHistoryAlertPo> pageList = PageHelperUtil.startPage(req, () -> bdHistoryAlertMapper.findPage(req));

        List<BdHistoryAlertVo> dataTaskPos = pageList.getList()
                .stream()
                .map(po -> {
                    BdHistoryAlertVo bdHistoryAlertVo = new BdHistoryAlertVo();
                    BeanUtils.copyProperties(po, bdHistoryAlertVo);
                    return bdHistoryAlertVo;
                })
                .collect(Collectors.toList());

        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), dataTaskPos);
    }

    @Override
    public List<Tuple<String, Integer>> hostAlertCount() {
        return bdAlertMapper.hostAlertCount();
    }

    @Override
    public List<Tuple<String, Integer>> serviceAlertCount() {
        return bdAlertMapper.serviceAlertCount();
    }

    @Override
    public List<Tuple<String, Integer>> componentAlertCount(String serviceName) {
        return bdAlertMapper.componentAlertCount(serviceName);
    }

    private void saveHistoryAlerts(String clusterName) {
        List<HistoryAlertApiDto> historyAlerts;
        ConfigVo checkpoint = configService.getConfig(ConfigModuleEnum.CHECK_POINT
                , ConfigTtypeEnum.BIGDATA_HISTORY_ALERT_CHECKPOINT
                , ConfigTtypeEnum.BIGDATA_HISTORY_ALERT_CHECKPOINT.name());
        if (checkpoint != null) {
            historyAlerts = bigdataApi.getHistoryAlerts(clusterName, Integer.parseInt(checkpoint.getTValue()), BATCH_SIZE);
            configService.update(checkpoint.getId(), String.valueOf(Integer.parseInt(checkpoint.getTValue()) + historyAlerts.size()));
        } else {
            historyAlerts = bigdataApi.getHistoryAlerts(clusterName, 0, BATCH_SIZE);
            configService.save(ConfigModuleEnum.CHECK_POINT
                    , ConfigTtypeEnum.BIGDATA_HISTORY_ALERT_CHECKPOINT
                    , ConfigTtypeEnum.BIGDATA_HISTORY_ALERT_CHECKPOINT.name()
                    , String.valueOf(historyAlerts.size())
            );
        }
        if (!CollectionUtils.isEmpty(historyAlerts)) {
            List<BdHistoryAlertPo> historyAlertPos = historyAlerts.stream().map(this::convertToBdHistoryAlertPo).collect(Collectors.toList());
            bdHistoryAlertMapper.batchSaveOrUpdate(historyAlertPos);
        }
    }

    private void saveCurrentAlerts(String clusterName) {
        List<CurrentAlertApiDto> alerts = bigdataApi.getAlerts(clusterName);

        if (!CollectionUtils.isEmpty(alerts)) {
            List<BdAlertPo> alertPos = alerts.stream().map(this::convertToBdAlertPo).collect(Collectors.toList());
            bdAlertMapper.batchSaveOrUpdate(alertPos);
        }
    }

    private void saveComponents(String clusterName) {
        List<ComponentApiDto> components = bigdataApi.getComponents(clusterName);
        if (!CollectionUtils.isEmpty(components)) {
            List<BdComponentPo> componentPos = components.stream().map(this::convertToBdComponentPo).collect(Collectors.toList());
            bdComponentMapper.batchSaveOrUpdate(componentPos);
        }
    }

    private void saveServices(String clusterName) {
        List<ServiceApiDto> services = bigdataApi.getServices(clusterName);
        if (!CollectionUtils.isEmpty(services)) {
            List<BdServicePo> servicePos = services.stream().map(this::convertToBdServicePo).collect(Collectors.toList());
            bdServiceMapper.batchSaveOrUpdate(servicePos);
        }
    }

    private void saveHosts(String clusterName) {
        List<HostApiDto> hosts = bigdataApi.getHosts(clusterName);

        if (!CollectionUtils.isEmpty(hosts)) {
            List<BdHostPo> hostPos = hosts.stream().map(this::convertToBdHostPo).collect(Collectors.toList());
            bdHostMapper.batchSaveOrUpdate(hostPos);
        }
    }

    private BdHostPo convertToBdHostPo(HostApiDto dto) {
        BdHostPo po = new BdHostPo();
        BeanUtils.copyProperties(dto, po);
        po.setNode(commonConfigProperties.getNode());
        po.setDiskInfo(JsonUtil.toJsonString(dto.getDiskInfo()));
        return po;
    }

    private BdServicePo convertToBdServicePo(ServiceApiDto dto) {
        BdServicePo po = new BdServicePo();
        BeanUtils.copyProperties(dto, po);
        po.setNode(commonConfigProperties.getNode());
        return po;
    }

    private BdComponentPo convertToBdComponentPo(ComponentApiDto dto) {
        BdComponentPo po = new BdComponentPo();
        BeanUtils.copyProperties(dto, po);
        po.setNode(commonConfigProperties.getNode());
        return po;
    }

    private BdAlertPo convertToBdAlertPo(CurrentAlertApiDto dto) {
        BdAlertPo po = new BdAlertPo();
        BeanUtils.copyProperties(dto, po);
        po.setNode(commonConfigProperties.getNode());
        po.setOriginalTimestamp(new Date(dto.getOriginalTimestamp()));
        po.setLatestTimestamp(new Date(dto.getLatestTimestamp()));
        return po;
    }

    private BdHistoryAlertPo convertToBdHistoryAlertPo(HistoryAlertApiDto dto) {
        BdHistoryAlertPo po = new BdHistoryAlertPo();
        BeanUtils.copyProperties(dto, po);
        po.setNode(commonConfigProperties.getNode());
        po.setTimestamp(new Date(dto.getTimestamp()));
        return po;
    }
}
