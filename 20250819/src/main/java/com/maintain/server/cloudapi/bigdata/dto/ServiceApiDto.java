package com.maintain.server.cloudapi.bigdata.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@NoArgsConstructor
@Data
public class ServiceApiDto {

    @JsonProperty("cluster_name")
    private String clusterName;
    @JsonProperty("credential_store_enabled")
    private String credentialStoreEnabled;
    @JsonProperty("credential_store_supported")
    private String credentialStoreSupported;
    @JsonProperty("desired_repository_version_id")
    private Integer desiredRepositoryVersionId;
    @JsonProperty("desired_stack")
    private DesiredStackDTO desiredStack;
    @JsonProperty("kerberos_enabled")
    private Boolean kerberosEnabled;
    @JsonProperty("maintenance_state")
    private String maintenanceState;
    @JsonProperty("repository_state")
    private String repositoryState;
    @JsonProperty("service_name")
    private String serviceName;
    @JsonProperty("sso_integration_desired")
    private Boolean ssoIntegrationDesired;
    @JsonProperty("sso_integration_enabled")
    private Boolean ssoIntegrationEnabled;
    @JsonProperty("sso_integration_requires_kerberos")
    private Boolean ssoIntegrationRequiresKerberos;
    @JsonProperty("sso_integration_supported")
    private Boolean ssoIntegrationSupported;
    @JsonProperty("state")
    private String state;

    @NoArgsConstructor
    @Data
    public static class DesiredStackDTO {
        @JsonProperty("stackName")
        private String stackName;
        @JsonProperty("stackVersion")
        private String stackVersion;
        @JsonProperty("stackId")
        private String stackId;
    }
}
