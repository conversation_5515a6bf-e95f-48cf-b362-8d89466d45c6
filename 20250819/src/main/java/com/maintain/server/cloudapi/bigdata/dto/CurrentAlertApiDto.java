package com.maintain.server.cloudapi.bigdata.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@NoArgsConstructor
@Data
public class CurrentAlertApiDto {

    @JsonProperty("cluster_name")
    private String clusterName;
    @JsonProperty("component_name")
    private String componentName;
    @JsonProperty("definition_id")
    private Integer definitionId;
    @JsonProperty("definition_name")
    private String definitionName;
    @JsonProperty("firmness")
    private String firmness;
    @JsonProperty("host_name")
    private String hostName;
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("instance")
    private Object instance;
    @JsonProperty("label")
    private String label;
    @JsonProperty("latest_timestamp")
    private Long latestTimestamp;
    @JsonProperty("maintenance_state")
    private String maintenanceState;
    @JsonProperty("occurrences")
    private Integer occurrences;
    @JsonProperty("original_timestamp")
    private Long originalTimestamp;
    @JsonProperty("repeat_tolerance")
    private Integer repeatTolerance;
    @JsonProperty("repeat_tolerance_remaining")
    private Integer repeatToleranceRemaining;
    @JsonProperty("scope")
    private String scope;
    @JsonProperty("service_name")
    private String serviceName;
    @JsonProperty("state")
    private String state;
    @JsonProperty("text")
    private String text;
}
