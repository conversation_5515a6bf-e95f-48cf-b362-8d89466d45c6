package com.maintain.server.cloudapi.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@Slf4j
public class HttpUtil {

    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    // 默认请求头
    private static final HttpHeaders HTTP_HEADERS = new HttpHeaders();

    static {
        HTTP_HEADERS.setContentType(MediaType.APPLICATION_JSON);
    }

    /**
     * GET 请求
     *
     * @param url          请求地址
     * @param params       请求参数
     * @param headers      请求头
     * @param responseType 返回类型
     * @return 响应结果
     */
    public static <T> T get(String url, Map<String, Object> params, HttpHeaders headers, Class<T> responseType) {
        try {
            // 设置请求头
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.putAll(HTTP_HEADERS);
            if (headers != null) {
                requestHeaders.putAll(headers);
            }

            // 构建请求实体
            HttpEntity<?> requestEntity = new HttpEntity<>(requestHeaders);

            // 发送请求
            ResponseEntity<T> response = REST_TEMPLATE.exchange(
                    buildUrlWithParams(url, params),
                    HttpMethod.GET,
                    requestEntity,
                    responseType);

            logRequest("GET", url, params, null, response);
            return response.getBody();
        } catch (RestClientException e) {
            log.error("GET请求失败: url={}, params={}", url, params, e);
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * POST 请求（JSON格式）
     *
     * @param url          请求地址
     * @param body         请求体
     * @param headers      请求头
     * @param responseType 返回类型
     * @return 响应结果
     */
    public static <T> T postJson(String url, Object body, HttpHeaders headers, Class<T> responseType) {
        try {
            // 设置请求头
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.putAll(HTTP_HEADERS);
            if (headers != null) {
                requestHeaders.putAll(headers);
            }

            // 构建请求实体
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, requestHeaders);

            // 发送请求
            ResponseEntity<T> response = REST_TEMPLATE.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    responseType);

            logRequest("POST", url, null, body, response);
            return response.getBody();
        } catch (RestClientException e) {
            log.error("POST请求失败: url={}, body={}", url, body, e);
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * POST 表单请求
     *
     * @param url          请求地址
     * @param formData     表单数据
     * @param headers      请求头
     * @param responseType 返回类型
     * @return 响应结果
     */
    public static <T> T postForm(String url, Map<String, String> formData, HttpHeaders headers, Class<T> responseType) {
        try {
            // 设置请求头
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.putAll(HTTP_HEADERS);
            requestHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            if (headers != null) {
                requestHeaders.putAll(headers);
            }

            // 构建表单数据
            MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
            if (formData != null) {
                formData.forEach(form::add);
            }

            // 构建请求实体
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(form, requestHeaders);

            // 发送请求
            ResponseEntity<T> response = REST_TEMPLATE.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    responseType);

            logRequest("POST-FORM", url, null, formData, response);
            return response.getBody();
        } catch (RestClientException e) {
            log.error("POST表单请求失败: url={}, formData={}", url, formData, e);
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * PUT 请求
     *
     * @param url          请求地址
     * @param body         请求体
     * @param headers      请求头
     * @param responseType 返回类型
     * @return 响应结果
     */
    public static <T> T put(String url, Object body, HttpHeaders headers, Class<T> responseType) {
        try {
            // 设置请求头
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.putAll(HTTP_HEADERS);
            if (headers != null) {
                requestHeaders.putAll(headers);
            }

            // 构建请求实体
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, requestHeaders);

            // 发送请求
            ResponseEntity<T> response = REST_TEMPLATE.exchange(
                    url,
                    HttpMethod.PUT,
                    requestEntity,
                    responseType);

            logRequest("PUT", url, null, body, response);
            return response.getBody();
        } catch (RestClientException e) {
            log.error("PUT请求失败: url={}, body={}", url, body, e);
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * DELETE 请求
     *
     * @param url          请求地址
     * @param params       请求参数
     * @param headers      请求头
     * @param responseType 返回类型
     * @return 响应结果
     */
    public static <T> T delete(String url, Map<String, Object> params, HttpHeaders headers, Class<T> responseType) {
        try {
            // 设置请求头
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.putAll(HTTP_HEADERS);
            if (headers != null) {
                requestHeaders.putAll(headers);
            }

            // 构建请求实体
            HttpEntity<?> requestEntity = new HttpEntity<>(requestHeaders);

            // 发送请求
            ResponseEntity<T> response = REST_TEMPLATE.exchange(
                    buildUrlWithParams(url, params),
                    HttpMethod.DELETE,
                    requestEntity,
                    responseType);

            logRequest("DELETE", url, params, null, response);
            return response.getBody();
        } catch (RestClientException e) {
            log.error("DELETE请求失败: url={}, params={}", url, params, e);
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        }
    }

    // 构建带参数的URL
    private static String buildUrlWithParams(String url, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }

        StringBuilder sb = new StringBuilder(url);
        if (!url.contains("?")) {
            sb.append("?");
        } else if (!url.endsWith("&")) {
            sb.append("&");
        }

        params.forEach((key, value) -> sb.append(key).append("=").append(value).append("&"));
        return sb.substring(0, sb.length() - 1);
    }

    // 记录请求日志
    private static void logRequest(String method, String url, Map<String, Object> params, Object body, ResponseEntity<?> response) {
        if (log.isDebugEnabled()) {
            log.debug("HTTP请求: {} {}, 参数: {}, 请求体: {}, 响应: {}",
                    method,
                    url,
                    params,
                    body,
                    response.getStatusCode());
        }
    }
}
