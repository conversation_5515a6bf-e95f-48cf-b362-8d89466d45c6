package com.maintain.server.cloudapi.bigdata.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@NoArgsConstructor
@Data
public class ComponentApiDto {

    @JsonProperty("category")
    private String category;
    @JsonProperty("cluster_name")
    private String clusterName;
    @JsonProperty("component_name")
    private String componentName;
    @JsonProperty("desired_stack")
    private String desiredStack;
    @JsonProperty("desired_version")
    private String desiredVersion;
    @JsonProperty("display_name")
    private String displayName;
    @JsonProperty("init_count")
    private Integer initCount;
    @JsonProperty("install_failed_count")
    private Integer installFailedCount;
    @JsonProperty("installed_and_maintenance_off_count")
    private Integer installedAndMaintenanceOffCount;
    @JsonProperty("installed_count")
    private Integer installedCount;
    @JsonProperty("recovery_enabled")
    private String recoveryEnabled;
    @JsonProperty("repository_state")
    private String repositoryState;
    @JsonProperty("service_name")
    private String serviceName;
    @JsonProperty("started_count")
    private Integer startedCount;
    @JsonProperty("state")
    private String state;
    @JsonProperty("total_count")
    private Integer totalCount;
    @JsonProperty("unknown_count")
    private Integer unknownCount;
}
