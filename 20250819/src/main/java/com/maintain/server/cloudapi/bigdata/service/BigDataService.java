package com.maintain.server.cloudapi.bigdata.service;

import com.maintain.server.dto.Tuple;
import com.maintain.server.dto.req.*;
import com.maintain.server.vo.PageVo;
import com.maintain.server.vo.bigdata.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
public interface BigDataService {
    /**
     * 获取大数据接口数据到mysql
     */
    void apiDataToMysql();


    /**
     * 获取宿主机
     *
     * @param hostReq
     * @return
     */
    PageVo<BdHostVo> pageHost(BdHostReq hostReq);


    /**
     * 获取service
     *
     * @param req
     * @return
     */
    PageVo<BdServiceVo> pageService(BdServiceReq req);


    /**
     * 获取组件
     *
     * @param componentReq
     * @return
     */
    PageVo<BdComponentVo> pageComponent(BdComponentReq componentReq);


    /**
     * 根据host,service,component获取告警
     *
     * @param req
     * @return
     */
    PageVo<BdAlertVo> getAlerts(BdAlertReq req);


    /**
     * 根据时间、服务名称获取历史告警详情
     *
     * @param req
     * @return
     */
    PageVo<BdHistoryAlertVo> getHistoryAlerts(BdHistoryAlertReq req);


    /**
     * host告警统计
     *
     * @return
     */
    List<Tuple<String, Integer>> hostAlertCount();

    /**
     * service告警统计
     *
     * @return
     */
    List<Tuple<String, Integer>> serviceAlertCount();

    /**
     * component告警统计
     * @param serviceName 服务名称
     * @return
     */
    List<Tuple<String, Integer>> componentAlertCount(String serviceName);


}
