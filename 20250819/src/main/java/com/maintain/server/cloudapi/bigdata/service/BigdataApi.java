package com.maintain.server.cloudapi.bigdata.service;

import com.maintain.server.cloudapi.bigdata.dto.*;

import java.util.List;

/**
 * 大数据接口
 *
 * <AUTHOR>
 * @date 2025/8/14
 */
public interface BigdataApi {
    /**
     * 集群
     *
     * @return
     */
    String getClusterName();

    /**
     * 宿主机
     *
     * @param clusterName
     * @return
     */
    List<HostApiDto> getHosts(String clusterName);

    /**
     * 服务
     *
     * @param clusterName
     * @return
     */
    List<ServiceApiDto> getServices(String clusterName);

    /**
     * 组件
     *
     * @param clusterName
     * @return
     */
    List<ComponentApiDto> getComponents(String clusterName);

    /**
     * 当前所有告警
     *
     * @param clusterName
     * @return
     */
    List<CurrentAlertApiDto> getAlerts(String clusterName);

    /**
     * 根据时间返回查询历史告警
     *
     * @param clusterName 集群名称
     * @param from        偏移量
     * @param size        一页条数
     * @return
     */
    List<HistoryAlertApiDto> getHistoryAlerts(String clusterName, Integer from, Integer size);

}
