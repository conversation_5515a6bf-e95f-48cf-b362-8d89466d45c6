package com.maintain.server.cloudapi.bigdata.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.maintain.server.cloudapi.bigdata.config.BigDataProperties;
import com.maintain.server.cloudapi.bigdata.dto.*;
import com.maintain.server.cloudapi.util.HttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Base64;
import java.util.List;

/**
 * 大数据平台api client
 *
 * <AUTHOR>
 * @date 2025/8/14
 */
@Component
public class BigdataClient implements BigdataApi {
    @Autowired
    private BigDataProperties properties;
    private String token;

    @PostConstruct
    public void init() {
        String usernameAndPass = properties.getUsername() + ":" + properties.getPassword();
        token = Base64.getEncoder().encodeToString(usernameAndPass.getBytes());
    }

    @Override
    public String getClusterName() {
        String clusterJsonStr = HttpUtil.get(getUrlPrefix() + "/clusters", null, getHttpHeaders(), String.class);

        JSONObject clusterJson = JSONObject.parseObject(clusterJsonStr);

        return clusterJson.getJSONArray("items")
                .getJSONObject(0)
                .getJSONObject("Clusters")
                .getString("cluster_name");
    }


    @Override
    public List<HostApiDto> getHosts(String clusterName) {
        String clusterJsonStr = HttpUtil.get(getUrlPrefix()
                + "/clusters/"
                + clusterName
                + "/hosts?fields=*", null, getHttpHeaders(), String.class);
        return getDtoList(clusterJsonStr, "Hosts", HostApiDto.class);
    }

    @Override
    public List<ServiceApiDto> getServices(String clusterName) {
        String clusterJsonStr = HttpUtil.get(getUrlPrefix()
                + "/clusters/"
                + clusterName
                + "/services?fields=*", null, getHttpHeaders(), String.class);
        return getDtoList(clusterJsonStr, "ServiceInfo", ServiceApiDto.class);
    }


    @Override
    public List<ComponentApiDto> getComponents(String clusterName) {
        String clusterJsonStr = HttpUtil.get(getUrlPrefix()
                + "/clusters/"
                + clusterName
                + "/components?fields=*", null, getHttpHeaders(), String.class);
        return getDtoList(clusterJsonStr, "ServiceComponentInfo", ComponentApiDto.class);
    }

    @Override
    public List<CurrentAlertApiDto> getAlerts(String clusterName) {
        String clusterJsonStr = HttpUtil.get(getUrlPrefix()
                + "/clusters/"
                + clusterName
                + "/alerts?fields=*", null, getHttpHeaders(), String.class);
        return getDtoList(clusterJsonStr, "Alert", CurrentAlertApiDto.class);
    }

    @Override
    public List<HistoryAlertApiDto> getHistoryAlerts(String clusterName, Integer from, Integer size) {
        String clusterJsonStr = HttpUtil.get(getUrlPrefix()
                        + "/clusters/"
                        + clusterName
                        + "/alert_history?fields=*"
                        + "&from=" + from
                        + "&page_size=" + size
                , null, getHttpHeaders(), String.class);
        return getDtoList(clusterJsonStr, "AlertHistory", HistoryAlertApiDto.class);
    }

    private HttpHeaders getHttpHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + token);
        return headers;
    }

    private String getUrlPrefix() {
        return properties.getEndPoint() + properties.getApiVersion();
    }

    private <T> List<T> getDtoList(String clusterJsonStr, String key, Class<T> clazz) {
        JSONObject clusterJson = JSONObject.parseObject(clusterJsonStr);
        JSONArray items = clusterJson.getJSONArray("items");
        List<T> res = Lists.newArrayListWithCapacity(items.size());
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = items.getJSONObject(i);
            T t = item.getObject(key, clazz);
            res.add(t);
        }
        return res;
    }
}
