package com.maintain.server.cloudapi.bigdata.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
@ConfigurationProperties(prefix = "bigdata")
@Configuration
@Data
public class BigDataProperties {
    private String endPoint = "http://cie1:8080";
    private String apiVersion = "/api/v1";
    private String username = "admin";
    private String password = "Cestc_01";
}
