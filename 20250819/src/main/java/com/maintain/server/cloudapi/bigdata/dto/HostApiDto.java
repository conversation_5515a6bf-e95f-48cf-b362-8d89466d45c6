package com.maintain.server.cloudapi.bigdata.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@NoArgsConstructor
@Data
public class HostApiDto {
    @JsonProperty("cluster_name")
    private String clusterName;
    @JsonProperty("cpu_count")
    private Integer cpuCount;
    @JsonProperty("disk_info")
    private List<DiskInfoDTO> diskInfo;
    @JsonProperty("host_health_report")
    private String hostHealthReport;
    @JsonProperty("host_name")
    private String hostName;
    @JsonProperty("host_state")
    private String hostState;
    @JsonProperty("host_status")
    private String hostStatus;
    @JsonProperty("ip")
    private String ip;
    @JsonProperty("last_heartbeat_time")
    private Long lastHeartbeatTime;
    @JsonProperty("last_registration_time")
    private Long lastRegistrationTime;
    @JsonProperty("maintenance_state")
    private String maintenanceState;
    @JsonProperty("os_arch")
    private String osArch;
    @JsonProperty("os_family")
    private String hostOsFamily;
    @JsonProperty("os_type")
    private String osType;
    @JsonProperty("ph_cpu_count")
    private Integer phCpuCount;
    @JsonProperty("public_host_name")
    private String publicHostName;
    @JsonProperty("rack_info")
    private String rackInfo;
    @JsonProperty("recovery_summary")
    private String recoverySummary;
    @JsonProperty("total_mem")
    private Integer totalMem;

    @NoArgsConstructor
    @Data
    public static class DiskInfoDTO {
        @JsonProperty("available")
        private String available;
        @JsonProperty("device")
        private String device;
        @JsonProperty("used")
        private String used;
        @JsonProperty("percent")
        private String percent;
        @JsonProperty("size")
        private String size;
        @JsonProperty("type")
        private String type;
        @JsonProperty("mountpoint")
        private String mountpoint;
    }
}
