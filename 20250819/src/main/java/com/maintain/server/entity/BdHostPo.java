package com.maintain.server.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/14
 */
@Data
public class BdHostPo {
    private String clusterName;
    private String node;
    private String diskInfo;
    private String hostName;
    private String hostState;
    private String hostStatus;
    private String ip;
    private String osArch;
    private String hostOsFamily;
    private String osType;
    private String publicHostName;
    private String rackInfo;
    private Integer totalMem;
    private String alertsSummary;
    private String hostComponents;
    private Integer cpuCount;
    private Integer phCpuCount;
    private Date createTime;
    private Date updateTime;
}
