package com.maintain.server.controller;

import com.maintain.server.Constants;
import com.maintain.server.criteria.RequestRecordCriteria;
import com.maintain.server.service.RequestRecordService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2020-11-09
 */
@RestController
@RequestMapping("/request")
public class RequestRecordController extends BaseController implements Constants {

    @Autowired
    private RequestRecordService service;

    @RequestMapping("/record/statis")
    public void statisRecord(@RequestParam(name = "startTime") String startTime,
                             @RequestParam(name = "endTime") String endTime,
                             HttpServletResponse response) {
        try {
            ResponseVo responseVo = new ResponseVo();
            responseVo.setCode(ResponseType.SUCCESS.getCode());
            responseVo.setMsg(ResponseType.SUCCESS.getMsg());
            responseVo.setData(service.statisRequestRecord(startTime, endTime));
            outputResponse(JsonUtil.toJSONStringWithDateFormat(responseVo, YYYY_MM_DD_HH_MM_SS), response);
        }catch (Exception e){
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }
}