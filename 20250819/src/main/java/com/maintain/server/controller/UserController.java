package com.maintain.server.controller;

import com.common.log.Log;
import com.maintain.server.Constants;
import com.maintain.server.criteria.BaseCriteria;
import com.maintain.server.service.UserService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.PermissionVo;
import com.maintain.server.vo.ResponseVo;
import com.maintain.server.vo.RoleVo;
import com.maintain.server.vo.UserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.maintain.server.Constants.UTF_8;

/**
 * <AUTHOR>
 * @date 2018-10-21
 */
@Controller
@RequestMapping("/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @RequestMapping("/pass/confirm")
    public void confirmPass(@RequestParam(value = "username") String username,
                            @RequestParam(value = "password") String password,
                            HttpServletResponse response) {
        boolean exist = userService.confirmUserAndPass(username, password);
        ResponseVo responseVo = new ResponseVo(exist ? ResponseType.SUCCESS : ResponseType.OPERATE_ERROR);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping("/toLogin")
    public void toLogin(HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(ResponseType.LOGIN_FIRST.getCode());
        responseVo.setMsg(ResponseType.LOGIN_FIRST.getMsg());
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping("/registry")
    @ResponseBody
    public ResponseVo registryUser(@RequestParam(value = "username") String username,
                                   @RequestParam(value = "password") String password,
                                   @RequestParam(value = "department", required = false) String department,
                                   @RequestParam(value = "roleIds", required = false) String roleIds) {
        if (username.length() < 3 || username.length() > 15) {
            return getResponse(ResponseType.OPERATE_ERROR.getCode(), "账号长度需要在3--15位字符");
        }

        if (password.length() < 6 || password.length() > 20) {
            return getResponse(ResponseType.OPERATE_ERROR.getCode(), "密码长度需要在6--20位字符");
        }

        String regex = "^(?=.*[A-Za-z])(?=.*\\d)(?=.*[`~!@#$%^&*()_\\-+=<>?:\"{}|,.\\/;'])[A-Za-z\\d`~!@#$%^&*()_\\-+=<>?:\"{}|,.\\/;']{1,}$";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(password);
        if (!m.matches()) {
            return getResponse(ResponseType.OPERATE_ERROR.getCode(), "密码至少包含一位数字,英文字母和英文特殊字符");
        }
        UserVo user = new UserVo(username, password);
        user.setDepartment(department);
        List<Integer> roleIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(roleIds)) {
            roleIdList =
                    Arrays.stream(roleIds.split(","))
                            .map(Integer::parseInt).collect(Collectors.toList());
        }
        List<RoleVo> roles = userService.findRolesByIds(roleIdList);
        user.setRoles(roles);
        int result = userService.registry(user);
        if (result == -1) {
            return getResponse(ResponseType.OPERATE_ERROR.getCode(), "用户名已存在");
        } else if (result == roles.size()) {
            return getResponse(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg());
        }
        return getResponse(ResponseType.OPERATE_ERROR.getCode(), ResponseType.OPERATE_ERROR.getMsg());
    }


    @RequestMapping("/cancel")
    @ResponseBody
    public ResponseVo cancelUser(@RequestParam(value = "userId") int userId) {
        UserVo user = new UserVo();
        user.setId(userId);
        boolean result = userService.cancel(user);
        if (result) {
            return getResponse(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg());
        }
        return getResponse(ResponseType.OPERATE_ERROR.getCode(), ResponseType.OPERATE_ERROR.getMsg());
    }

    @RequestMapping("/initPassword")
    @ResponseBody
    public ResponseVo initPassword(@RequestParam(value = "userId") int userId) {
        UserVo user = new UserVo();
        user.setId(userId);
        user.setPswd(Constants.INIT_PASSWORD);
        boolean result = userService.initPassword(user);
        if (result) {
            return getResponse(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg());
        }
        return getResponse(ResponseType.OPERATE_ERROR.getCode(), ResponseType.OPERATE_ERROR.getMsg());
    }

    @RequestMapping(value = "/userList.json")
    public void getUserLists(@RequestParam(value = "pn", required = false, defaultValue = "1") Integer pn,
                             @RequestParam(value = "ps", required = false, defaultValue = "20") Integer ps,
                             HttpServletResponse response) {
        BaseCriteria criteria = new BaseCriteria();
        criteria.setPn(pn);
        criteria.setPs(ps);
        List<UserVo> list = userService.listUsers(criteria);
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        responseVo.setData(list);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping(value = "/roleList.json")
    public void getRoleLists(@RequestParam(value = "pn", required = false, defaultValue = "1") Integer pn,
                             @RequestParam(value = "ps", required = false, defaultValue = "20") Integer ps,
                             HttpServletResponse response) {
        BaseCriteria criteria = new BaseCriteria();
        criteria.setPn(pn);
        criteria.setPs(ps);
        List<RoleVo> list = userService.listRoles(criteria);
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        responseVo.setData(list);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }


    @RequestMapping(value = "/permissionList.json")
    @ResponseBody
    public Map<String, Object> getPermissionList(@RequestParam(value = "pn", required = false, defaultValue = "1") Integer pn,
                                                 @RequestParam(value = "ps", required = false, defaultValue = "20") Integer ps,
                                                 @RequestParam(value = "userId") int userId) {
        Map<String, Object> map = new HashMap<>();
        try {
            BaseCriteria criteria = new BaseCriteria();
            criteria.setPn(pn);
            criteria.setPs(ps);
            List<PermissionVo> list = userService.listPermissions(criteria);
            map.put("code", ResponseType.SUCCESS.getCode());
            map.put("msg", ResponseType.SUCCESS.getMsg());
            map.put("data", list);
            List<PermissionVo> permissionsOfUser = userService.loadPermissionByUserId(userId);
            if (permissionsOfUser == null) {
                map.put("checked", new ArrayList<>());
            } else {
                map.put("checked", permissionsOfUser.stream().map(PermissionVo::getId).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            map.put("code", ResponseType.OPERATE_ERROR.getCode());
            map.put("msg", ResponseType.OPERATE_ERROR.getMsg());
        }
        return map;
    }

    @RequestMapping(value = "/permissionsOfUser.json")
    public void getPermissionsOfUser(@RequestParam(value = "username") String username,
                                     HttpServletResponse response) {
        List<PermissionVo> list = userService.loadPermissionByUsername(username);
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        responseVo.setData(list.stream().map(PermissionVo::getName).collect(Collectors.toList()));
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping(value = "/assignRoles")
    public void assignRoles(@RequestParam(value = "userId") Integer userId,
                            @RequestParam(value = "roleIds") String roleIds,
                            HttpServletResponse response) {
        Log.low.info("assignRoles-->userId:" + userId + "\troleIds:" + roleIds);
        if (userId < 1 || StringUtils.isEmpty(roleIds)) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.REJECT)), response);
        }

        List<Integer> roleIdList =
                Arrays.stream(roleIds.split(","))
                        .map(Integer::parseInt).collect(Collectors.toList());
        try {
            userService.updateRolesOfUser(userId, roleIdList);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo
                    (ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }

        outputResponse(JsonUtil.toJsonString(new ResponseVo((ResponseType.SUCCESS))), response);
    }


    @RequestMapping(value = "/assignPermissions")
    public void assignPermissions(@RequestParam(value = "roleId") Integer roleId,
                                  @RequestParam(value = "permissionIds") String permissionIds,
                                  HttpServletResponse response) {
        Log.low.info("assignPermissions-->roleId:" + roleId + "\tpermissionIds:" + permissionIds);
        if (roleId < 1 || permissionIds.isEmpty()) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.REJECT)), response);
        }
        List<Integer> permissionIdList =
                Arrays.stream(permissionIds.split(","))
                        .map(Integer::parseInt).collect(Collectors.toList());

        try {
            userService.updatePermissionOfRole(roleId, permissionIdList);
        } catch (RuntimeException e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo
                    (ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
        outputResponse(JsonUtil.toJsonString(new ResponseVo((ResponseType.SUCCESS))), response);
    }

    @RequestMapping(value = "/permissionByModule")
    @ResponseBody
    public Callable<ResponseVo> permissionByName(@RequestParam(value = "name", required = false) String name,
                                                 @RequestParam(value = "username", required = false) String username) {
        return () -> {
            ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            if (!StringUtils.isEmpty(name)) {
                try {
                    String moduleName = URLDecoder.decode(name, UTF_8);
                    PermissionVo data = userService.findPermissionByName(moduleName);
                    responseVo.setData(data);
                    //如果传了username,那么就需要过滤权限，只返回username用户所拥有的权限
                    if (!StringUtils.isEmpty(username)) {
                        List<Integer> permissionids = userService.loadPermissionByUsername(username).stream()
                                .map(PermissionVo::getId).collect(Collectors.toList());
                        if (data == null || !permissionids.contains(data.getId())) {
                            responseVo.setData(null);
                        }
                        if (responseVo.getData() != null && data.getChildren() != null && !data.getChildren().isEmpty()) {
                            List<PermissionVo> permissionVoList =
                                    data.getChildren().stream().filter(p -> permissionids.contains(p.getId()))
                                            .collect(Collectors.toList());
                            data.setChildren(permissionVoList);
                        }
                    }
                    return responseVo;
                } catch (Exception e) {
                    Log.high.error("解码失败" + name, e);
                    responseVo.setCode(ResponseType.OPERATE_ERROR.getCode());
                    responseVo.setMsg("解码失败" + name + e.getMessage());
                }
            }
            return responseVo;
        };
    }

}