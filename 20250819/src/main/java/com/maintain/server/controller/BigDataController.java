package com.maintain.server.controller;

import com.maintain.server.cloudapi.bigdata.service.BigDataService;
import com.maintain.server.dto.Result;
import com.maintain.server.dto.Tuple;
import com.maintain.server.dto.req.*;
import com.maintain.server.vo.PageVo;
import com.maintain.server.vo.bigdata.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/19
 */
@RestController
@RequestMapping("/bigdata")
public class BigDataController {

    @Autowired
    private BigDataService bigDataService;

    @GetMapping("test")
    public Object test() {
        bigDataService.apiDataToMysql();
        return null;
    }

    @PostMapping("/pageHost")
    public Result<PageVo<BdHostVo>> pageHost(@RequestBody BdHostReq req) {
        return Result.success(bigDataService.pageHost(req));
    }

    @PostMapping("/pageService")
    public Result<PageVo<BdServiceVo>> pageService(@RequestBody BdServiceReq req) {
        return Result.success(bigDataService.pageService(req));
    }

    @PostMapping("/pageComponent")
    public Result<PageVo<BdComponentVo>> pageComponent(@RequestBody BdComponentReq req) {
        return Result.success(bigDataService.pageComponent(req));
    }

    @PostMapping("/pageAlerts")
    public Result<PageVo<BdAlertVo>> pageAlerts(@RequestBody BdAlertReq req) {
        return Result.success(bigDataService.getAlerts(req));
    }

    @PostMapping("/pageHistoryAlerts")
    public Result<PageVo<BdHistoryAlertVo>> pageHistoryAlerts(@RequestBody BdHistoryAlertReq req) {
        return Result.success(bigDataService.getHistoryAlerts(req));
    }

    @GetMapping("/hostAlertCount")
    public Result<List<Tuple<String, Integer>>> hostAlertCount() {
        return Result.success(bigDataService.hostAlertCount());
    }

    @GetMapping("/serviceAlertCount")
    public Result<List<Tuple<String, Integer>>> serviceAlertCount() {
        return Result.success(bigDataService.serviceAlertCount());
    }

    @GetMapping("/{serviceName}/componentAlertCount")
    public Result<List<Tuple<String, Integer>>> componentAlertCount(@PathVariable(value = "serviceName") String serviceName) {
        return Result.success(bigDataService.componentAlertCount(serviceName));
    }
}
