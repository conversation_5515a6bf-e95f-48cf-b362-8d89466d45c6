package com.maintain.server.controller;

import com.common.log.Log;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.maintain.server.Constants;
import com.maintain.server.business.SoftwareBusiness;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.ModifyConfigCriteria;
import com.maintain.server.criteria.SoftwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.AutoDeployService;
import com.maintain.server.service.SoftwareHistoryService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.OsType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.BaseConfigUtil;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-11-01
 */
@Controller
@RequestMapping("/software")
public class SoftwareController extends BaseController implements Constants {

    @Value("${server.localHost}")
    private String maintainServerIp;

    @Value("${seven-zip-path}")
    private String sevenZipPath;

    @Value("${dir.win}")
    private String windowsDirectory;

    @Value("${dir.lin}")
    private String linuxDirectory;

    @Value("${onlineUpgrade.isOpen}")
    private Boolean isOpen;


    @Autowired
    private AgentService agentService;

    @Autowired
    private SoftwareService softwareService;

    @Autowired
    private SoftwareBusiness softwareBusiness;

    @Autowired
    private SoftwareHistoryService softwareHistoryService;

    @Autowired
    private AutoDeployService autoDeployService;


    @RequestMapping("/auto_deploy.json")
    public void autoDeploy(HttpServletResponse response) {
        outputResponse(JsonUtil.toJsonString(getResponse(ResponseType.OPERATE_ERROR.getCode(), "此接口已废弃，请改用WebSocket版")), response);
    }

    /**
     * 下载单个log文件
     *
     * @param ip       ip
     * @param process  process
     * @param log      log
     * @param response response
     */
    @RequestMapping(value = "/log", method = RequestMethod.GET)
    public void downloadLogFile(@RequestParam("ip") String ip,
                                @RequestParam("process") String process,
                                @RequestParam("log") String log,
                                HttpServletResponse response) {
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
        InputStream is = null;
        try {
            byte[] buff = new byte[1024];
            is = softwareService.getLogFile(agentVo, process, log);
            setDownloadFileHeader(response, process + "--" + log);
            int length;
            while ((length = is.read(buff)) != -1) {
                response.getOutputStream().write(buff, 0, length);
            }
        } catch (Exception e) {
            ResponseVo responseVo = new ResponseVo(ResponseType.OPERATE_ERROR);
            responseVo.setMsg(e.getMessage());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    Log.high.error("关闭is流出错", e);
                }
            }
        }
    }

    @RequestMapping(value = "/config", method = RequestMethod.GET)
    public void downloadConfigFile(@RequestParam("ip") String ip,
                                   @RequestParam("process") String process,
                                   @RequestParam("config") String config,
                                   HttpServletResponse response) {
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
        try {
            String content = softwareService.readFileContent(agentVo, process, "conf", config, "false", "");
            if (content == null) {
                ResponseVo responseVo = new ResponseVo(ResponseType.OPERATE_ERROR);
                responseVo.setMsg("读取文件失败，请检查Agent连接状态!");
                outputResponse(JsonUtil.toJsonString(responseVo), response);
            }
            ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(content);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            ResponseVo responseVo = new ResponseVo(ResponseType.OPERATE_ERROR);
            responseVo.setMsg(e.getMessage());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        }
    }


    /**
     * 列出某个程序的所有日志文件
     *
     * @param ip       ip
     * @param process  process
     * @param response response
     */
    @RequestMapping(value = "/logs", method = RequestMethod.GET)
    public void listLogsByProcess(@RequestParam("ip") String ip,
                                  @RequestParam("process") String process,
                                  @RequestParam(value = "pn", required = false, defaultValue = "1") Integer pn,
                                  @RequestParam(value = "ps", required = false, defaultValue = "20") Integer ps,
                                  HttpServletResponse response) {
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
        if (agentVo == null) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR)), response);
            return;
        }
        try {
            SoftwareCriteria criteria = new SoftwareCriteria();
            criteria.setName(process);
            criteria.setServerId(agentVo.getId());
            SoftwareVo softwareVo = softwareService.getSoftwareInfo(criteria);
            if (softwareVo == null) {
                throw new ServiceException("找不到相关程序");
            }
            PageInfo<Map<String, Object>> pageInfo = softwareService.listAllLogs(agentVo, softwareVo, pn, ps);
            ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(pageInfo);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            Log.high.error("listLogsByProcess", e);
            ResponseVo responseVo = new ResponseVo(ResponseType.OPERATE_ERROR);
            responseVo.setMsg(e.getMessage());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        }
    }

    /**
     * 列出某个程序的配置文件列表
     *
     * @param ip       ip
     * @param process  process
     * @param response response
     */
    @RequestMapping(value = "/configs", method = RequestMethod.GET)
    public void listConfigsByProcess(@RequestParam("ip") String ip,
                                     @RequestParam("process") String process,
                                     HttpServletResponse response) {
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
        if (agentVo == null) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR)), response);
            return;
        }
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setName(process);
        criteria.setServerId(agentVo.getId());
        SoftwareVo softwareVo = softwareService.getSoftwareInfo(criteria);
        if (softwareVo == null) {
            throw new ServiceException("找不到相关程序");
        }
        try {
            List<Map<String, Object>> logFiles = softwareService.listAllConfigs(agentVo, softwareVo);
            ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(logFiles);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            ResponseVo responseVo = new ResponseVo(ResponseType.OPERATE_ERROR);
            responseVo.setMsg(e.getMessage());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        }
    }

    @RequestMapping(value = "/config", method = RequestMethod.POST)
    public void updateConfigFile(@RequestParam("ip") String ip,
                                 @RequestParam("process") String process,
                                 @RequestParam("config") String config,
                                 @RequestParam("content") String content,
                                 HttpServletResponse response) {
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
        try {
            boolean result = softwareService.saveConfigFile(agentVo, process, config, content, "conf", "false", "");
            if (result) {
                outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
            }
        } catch (Exception e) {
            Log.high.error("updateConfigFile", e);
        }
        outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR)), response);
    }

    @RequestMapping(value = "list.json")
    public void listSoftware(HttpServletResponse response, @RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "status", required = false) String status,
                             @RequestParam(value = "matchType", required = false) Integer matchType,
                             @RequestParam(value = "source", required = false, defaultValue = "agent") String source,
                             @RequestParam(value = "namespace", required = false, defaultValue = "default") String namespace) {
        try {
            Map<String, Object> map;

            // 1. 判断数据源：agent(裸机程序) 或 k8s(容器程序)
            if ("k8s".equals(source) && k8sService != null) {
                // 获取k8s容器数据
                List<SoftwareVo> containers = k8sService.getContainersAsSoftware(namespace);
                Map<String, Integer> statusMap = k8sService.getContainerStatusCount(namespace);

                // 名称过滤
                if (!StringUtils.isEmpty(name)) {
                    if (matchType == 0) { // 包含
                        containers.removeIf(container -> !container.getName().contains(name));
                    } else if (matchType == 1) { // 精确匹配
                        containers.removeIf(container -> !container.getName().equals(name));
                    } else if (matchType == 2) { // 不包含
                        containers.removeIf(container -> container.getName().contains(name));
                    }
                }

                // 状态过滤
                if (!StringUtils.isEmpty(status)) {
                    AlarmStatusType statusType = AlarmStatusType.getId(status);
                    containers.removeIf(container -> !container.getStatus().equals(statusType));
                }

                // 构造返回数据
                map = new HashMap<>();
                map.put("softwares", groupContainersByHost(containers));
                map.put("statusMap", statusMap);
                map.put("source", "k8s");
                map.put("namespace", namespace);
            } else {
                // 获取传统agent程序数据
                final SoftwareCriteria softwareCriteria = new SoftwareCriteria();
                if (matchType == 0) {
                    softwareCriteria.setFuzzyName(name);
                } else if (matchType == 1) {
                    softwareCriteria.setName(name);
                } else if (matchType == 2) {
                    softwareCriteria.setNotName(name);
                }
                if (!StringUtils.isEmpty(status)) {
                    softwareCriteria.setStatus(AlarmStatusType.getId(status));
                }
                map = softwareService.listAllSoftwaresAndStatus(softwareCriteria, false);
                map.put("source", "agent");
            }

            ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(map);
            GsonBuilder builder = new GsonBuilder();
            Gson gson = builder.setDateFormat(YYYY_MM_DD_HH_MM_SS).create();
            outputResponse(gson.toJson(responseVo), response);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR)), response);
        }
    }

    @RequestMapping(value = "detail_{softwareId}.json")
    public void softwareDetail(@PathVariable(value = "softwareId") Integer softwareId,
                               @RequestParam(value = "pn", defaultValue = "1", required = false) Integer pn,
                               @RequestParam(value = "ps", defaultValue = "20", required = false) Integer ps,
                               @RequestParam(value = "startTime", required = false) String startTime,
                               @RequestParam(value = "endTime", required = false) String endTime,
                               @RequestParam(value = "source", required = false, defaultValue = "agent") String source,
                               @RequestParam(value = "namespace", required = false, defaultValue = "default") String namespace,
                               @RequestParam(value = "podName", required = false) String podName,
                               @RequestParam(value = "containerName", required = false) String containerName,
                               HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);

        // 1. 判断是否为k8s容器详情
        if ("k8s".equals(source) && k8sService != null && podName != null && containerName != null) {
            SoftwareVo containerDetail = k8sService.getContainerDetailAsSoftware(namespace, podName, containerName);
            if (containerDetail != null) {
                // 构造与agent程序详情相同的数据格式
                Map<String, Object> result = new HashMap<>();
                result.put("softwareInfo", containerDetail);

                // 生成模拟的图表数据
                Map<String, Object> chartData = generateMockChartData(startTime, endTime);
                result.put("chartData", chartData);

                // 空的重启日志和业务监控数据
                result.put("restartLog", new HashMap<String, Object>() {{
                    put("list", new ArrayList<>());
                    put("total", 0);
                }});
                result.put("businessMonitor", new HashMap<String, Object>() {{
                    put("list", new ArrayList<>());
                    put("total", 0);
                }});

                responseVo.setData(result);
                outputResponse(JsonUtil.toJSONStringWithDateFormat(responseVo, Constants.YYYY_MM_DD_HH_MM_SS), response);
            } else {
                responseVo.setResponseType(ResponseType.OPERATE_ERROR);
                responseVo.setMsg("容器详情获取失败");
                outputResponse(JsonUtil.toJsonString(responseVo), response);
            }
            return;
        }

        // 2. 原有的agent程序详情逻辑
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setId(softwareId);
        criteria.setPn(pn);
        criteria.setPs(ps);
        criteria.setStartTime(startTime);
        criteria.setEndTime(endTime);
        Map<String, Object> result = softwareBusiness.getSoftwareDetail(criteria);
        if (result != null) {
            responseVo.setData(result);
            outputResponse(JsonUtil.toJSONStringWithDateFormat(responseVo, Constants.YYYY_MM_DD_HH_MM_SS), response);
        } else {
            responseVo.setResponseType(ResponseType.OPERATE_ERROR);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        }
    }

    @RequestMapping(value = "restart_history_{softwareId}.json")
    public void getRestartHistoryByTime(@PathVariable(value = "softwareId") Integer softwareId, @RequestParam(value = "pn", defaultValue = "1", required = false) Integer pn,
                                        @RequestParam(value = "ps", defaultValue = "20", required = false) Integer ps,
                                        @RequestParam(value = "startTime", required = false) String startTime,
                                        @RequestParam(value = "endTime", required = false) String endTime,
                                        HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        Map<String, Object> data = new HashMap<>();
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setId(softwareId);
        criteria.setPn(pn);
        criteria.setPs(ps);
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            criteria.setStartTime(startTime);
            criteria.setEndTime(endTime);
        }
        // 重启情况
        PageInfo<SoftwareRestartVo> restartVoPageInfo = softwareHistoryService.getRestartHistory(criteria);
        if (restartVoPageInfo != null && ListUtil.isNotEmpty(restartVoPageInfo.getList())) {
            data.put("restartHistory", restartVoPageInfo);
        }
        responseVo.setData(data);
        outputResponse(JsonUtil.toJSONStringWithDateFormat(responseVo, Constants.YYYY_MM_DD_HH_MM_SS), response);
    }

    @RequestMapping(value = "history_resource_{softwareId}.json")
    public void getHistoryResourceByTime(@PathVariable(value = "softwareId") Integer softwareId,
                                         @RequestParam(value = "pn", defaultValue = "1", required = false) Integer pn,
                                         @RequestParam(value = "ps", defaultValue = "3000", required = false) Integer ps,
                                         @RequestParam(value = "startTime", required = false) String startTime,
                                         @RequestParam(value = "endTime", required = false) String endTime,
                                         @RequestParam(value = "timePoint", required = false) String[] timePoint,
                                         HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        Map<String, Object> data = new HashMap<>();
        SoftwareCriteria criteria = new SoftwareCriteria();
        criteria.setId(softwareId);
        criteria.setPn(pn);
        criteria.setPs(ps);
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            criteria.setStartTime(startTime);
            criteria.setEndTime(endTime);
        } else if (timePoint != null && timePoint.length > 1) {
            criteria.setStartTime(timePoint[0]);
            criteria.setEndTime(timePoint[timePoint.length - 1]);
        }
        Map<String, Object> historyResourceResult = softwareBusiness.getHistoryResource(criteria);
        if (historyResourceResult != null) {
            data.put("historyResource", historyResourceResult);
        }
        responseVo.setData(data);
        outputResponse(JsonUtil.toJSONStringWithDateFormat(responseVo, Constants.YYYY_MM_DD_HH_MM), response);
    }

    @RequestMapping(value = "search_software.json", produces = "application/json;charset=utf-8")
    @ResponseBody
    public ResponseVo searchSoftware(@RequestParam(value = "ip", required = false) String ip,
                                     @RequestParam(value = "name", required = false) String name) {
        if (StringUtils.isEmpty(ip) && StringUtils.isEmpty(name)) {
            return new ResponseVo(ResponseType.REJECT);
        }
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        SoftwareCriteria softwareCriteria = new SoftwareCriteria();
        if (StringUtils.isNotEmpty(ip)) {
            AgentCriteria agentCriteria = new AgentCriteria();
            agentCriteria.setIp(ip);
            AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
            if (agentVo == null) {
                responseVo.setCode(ResponseType.REJECT.getCode());
                responseVo.setMsg(ResponseType.REJECT.getMsg());
                return responseVo;
            }
            softwareCriteria.setServerId(agentVo.getId());
        }
        softwareCriteria.setName(name);
        Map<String, Map<String, Object>> result = softwareService.listAllSoftwares(softwareCriteria, false);
        responseVo.setData(result);
        return responseVo;
    }

    @RequestMapping(value = "/verify_format")
    public void verifyFileFormat(@RequestParam(value = "content") String content,
                                 @RequestParam(value = "suffix") String suffix,
                                 HttpServletResponse response) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(suffix)) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), "参数不能为空")), response);
        }
        try {
            softwareService.verifyFileFormat(content, suffix);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), "文件格式不正确")), response);
        }
    }

    @RequestMapping(value = "/clear_restart_count", method = {RequestMethod.POST, RequestMethod.PUT})
    public void clearRestartCount(@RequestParam(value = "host", required = false) String host,
                                  @RequestParam(value = "id", required = false) Integer id,
                                  @RequestParam(value = "name", required = false) String name,
                                  @RequestParam(value = "software", required = false) String software,
                                  @RequestParam(value = "fuzzy", required = false) String fuzzy,
                                  @RequestParam(value = "status", required = false) String status,
                                  HttpServletResponse response) {
        try {
            if (id != null && name != null && host != null) {
                SoftwareVo softwareVo = new SoftwareVo();
                softwareVo.setHost(host);
                softwareVo.setName(name);
                softwareVo.setId(id);
                softwareService.clearRestartCount(softwareVo);
            } else if (host != null) {
                softwareService.clearRestartCount(host);
            } else {
                final SoftwareCriteria softwareCriteria = new SoftwareCriteria();
                if ("0".equals(fuzzy)) {
                    softwareCriteria.setFuzzyName(software);
                } else if ("2".equals(fuzzy)) {
                    softwareCriteria.setNotName(software);
                } else {
                    softwareCriteria.setName(software);
                }
                if (!StringUtils.isEmpty(status)) {
                    softwareCriteria.setStatus(AlarmStatusType.getId(status));
                }
                final List<SoftwareVo> list = softwareService.getSoftwareInfos(softwareCriteria).getList();
                for (SoftwareVo softwareVo : list) {
                    if (softwareVo.getHost() != null) {
                        try {
                            softwareService.clearRestartCount(softwareVo);
                        } catch (Exception e) {
                            Log.high.error("清空" + softwareVo.getHost() + "程序：" + softwareVo.getName() + "重启次数异常", e);
                        }
                    }
                }
            }
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    /**
     * 开启/关闭心跳监控
     *
     * @param id
     * @param heartMonitor
     * @param response
     */
    @RequestMapping(value = "/heart_monitor", method = {RequestMethod.POST, RequestMethod.PUT})
    public void heartMonitor(@RequestParam(value = "id") Integer id,
                             @RequestParam(value = "heartMonitor") Boolean heartMonitor,
                             HttpServletResponse response) {
        try {
            SoftwareVo softwareVo = new SoftwareVo();
            softwareVo.setId(id);
            softwareVo.setHeartMonitor(heartMonitor);
            softwareService.updateSoftware(softwareVo, false);
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareVo.getHeartMonitor());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/add_software", method = {RequestMethod.POST, RequestMethod.PUT})
    public void addSoftware(@RequestParam(value = "serverIp") String serverIp,
                            @RequestParam(value = "name") String name,
                            @RequestParam(value = "processCount", required = false) Integer processCount,
                            @RequestParam(value = "realDir", required = false) String realDir,
                            @RequestParam(value = "heartMonitor", required = false) Boolean heartMonitor,
                            @RequestParam(value = "config", required = false) Boolean config,
                            @RequestParam(value = "keys", required = false) String keys,
                            @RequestParam(value = "scriptPath", required = false) String scriptPath,
                            @RequestParam(value = "script", required = false) String script,
                            @RequestParam(value = "batch", required = false) Boolean batch,
                            @RequestParam(value = "closeScript", required = false) String closeScript,
                            @RequestParam(value = "baseDir", required = false) String baseDir,
                            @RequestParam(value = "logPath", required = false) String logPath,
                            @RequestParam(value = "logSuffix", required = false) String logSuffix,
                            @RequestParam(value = "configPath", required = false) String configPath,
                            @RequestParam(value = "source", required = false, defaultValue = "agent") String source,
                            @RequestParam(value = "namespace", required = false, defaultValue = "default") String namespace,
                            @RequestParam(value = "podName", required = false) String podName,
                            @RequestParam(value = "containerName", required = false) String containerName,
                            HttpServletResponse response) {

        try {
            // 1. 判断是否为k8s容器添加
            if ("k8s".equals(source) && k8sService != null) {
                boolean success = k8sService.addK8sProgram(namespace, podName, containerName);
                ResponseVo responseVo;
                if (success) {
                    responseVo = new ResponseVo(ResponseType.SUCCESS);
                    responseVo.setMsg("k8s程序添加成功");
                } else {
                    responseVo = new ResponseVo(ResponseType.FAILURE);
                    responseVo.setMsg("k8s程序添加失败");
                }
                outputResponse(JsonUtil.toJsonString(responseVo), response);
                return;
            }

            // 2. 原有的agent程序添加逻辑
            if (!Objects.isNull(batch) && batch) {
                final AgentCriteria agentCriteria = new AgentCriteria();
                agentCriteria.setIp(serverIp);
                final List<AgentVo> agentList = agentService.getAgentList(agentCriteria);
                if (agentList.isEmpty()) {
                    throw new ServiceException("没有该服务器ip：" + serverIp);
                }
                final AgentVo agentVo = agentList.get(0);
                if (StringUtils.isEmpty(baseDir)) {
                    baseDir = OsType.LINUX.getId().equals(agentVo.getOs()) ? "/dist/" : "D:\\dist\\";
                } else if (!baseDir.endsWith("/") && !baseDir.endsWith("\\")) {
                    String separater = OsType.LINUX.getId().equals(agentVo.getOs()) ? "/" : "\\";
                    baseDir = baseDir + separater;
                }
                final String[] names = name.split(",");
                final List<AgentVo> list = agentService.getAgentList(new AgentCriteria());
                HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
                for (AgentVo vo : list) {
                    stringIntegerHashMap.put(vo.getIp(), vo.getId());
                }


                for (String s : names) {
                    SoftwareVo softwareVo = new SoftwareVo();
                    softwareVo.setServerId(stringIntegerHashMap.get(serverIp));
                    softwareVo.setName(s);
                    softwareVo.setRealDir(baseDir + s);
                    softwareVo.setServerIp(serverIp);
                    softwareVo.setHost(serverIp);
                    HashMap<String, String> hashMap = BaseConfigUtil.softwareMap.get(s);
                    if (Objects.isNull(hashMap)) {
                        softwareVo.setConfig(Boolean.FALSE);
                        softwareVo.setProcessCount(1);
                        softwareVo.setHeartMonitor(Boolean.TRUE);
                    } else {
                        softwareVo.setConfig(Boolean.TRUE);
                        softwareVo.setCloseScript(closeScript);
                        softwareVo.setScriptPath(hashMap.get("path"));
                        softwareVo.setScript(OsType.LINUX.getId().equals(agentVo.getOs()) ? hashMap.get("lin_script") : hashMap.get("win_script"));
                        softwareVo.setKeys(hashMap.get("keys"));
                        softwareVo.setProcessCount(Integer.valueOf(Optional.ofNullable(hashMap.get("process")).orElse("1")));
                        softwareVo.setHeartMonitor(Optional.ofNullable(hashMap.get("monitor")).orElse("1").equals("1"));
                    }
                    softwareVo.setStatus(AlarmStatusType.YELLOW);
                    softwareVo.setDescription("程序未开启");
                    softwareVo.setProgramStatus(ProgramStatusType.CLOSE);
                    softwareService.addSoftwareInfo(softwareVo);
                }
                softwareService.pushSoftwareInfoToAgent(serverIp);
            } else {
                SoftwareVo softwareVo = new SoftwareVo();
                softwareVo.setName(name);
                softwareVo.setLogPath(logPath);
                softwareVo.setLogSuffix(logSuffix);
                softwareVo.setConfigPath(configPath);
                softwareVo.setProcessCount(processCount);
                softwareVo.setRealDir(realDir);
                softwareVo.setConfig(config);
                softwareVo.setKeys(keys);
                softwareVo.setScriptPath(scriptPath);
                softwareVo.setScript(script);
                softwareVo.setCloseScript(closeScript);
                softwareVo.setHeartMonitor(heartMonitor);
                softwareVo.setStatus(AlarmStatusType.YELLOW);
                softwareVo.setDescription("程序未开启");
                softwareVo.setProgramStatus(ProgramStatusType.CLOSE);
                final List<AgentVo> agentList = agentService.getAgentList(new AgentCriteria());
                HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
                for (AgentVo agentVo : agentList) {
                    stringIntegerHashMap.put(agentVo.getIp(), agentVo.getId());
                }
                if (serverIp.contains(",")) {
                    final String[] split = serverIp.split(",");
                    for (String s : split) {
                        softwareVo.setServerId(stringIntegerHashMap.get(s));
                        softwareVo.setServerIp(s);
                        softwareVo.setHost(s);
                        softwareService.addSoftwareInfo(softwareVo);
                        softwareService.pushSoftwareInfoToAgent(s);
                    }
                } else {
                    final SoftwareCriteria softwareCriteria = new SoftwareCriteria();
                    softwareCriteria.setServerId(stringIntegerHashMap.get(serverIp));
                    softwareCriteria.setName(name);
                    SoftwareVo softwareInfo = softwareService.getSoftwareInfo(softwareCriteria);
                    if (softwareInfo != null) {
                        outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), "该程序已添加")), response);
                        return;
                    }
                    softwareVo.setServerId(stringIntegerHashMap.get(serverIp));
                    softwareVo.setServerIp(serverIp);
                    softwareVo.setHost(serverIp);
                    softwareService.addSoftwareInfo(softwareVo);
                    softwareService.pushSoftwareInfoToAgent(serverIp);
                }
            }
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/hosts")
    public void getAllHost(HttpServletResponse response) {
        try {
            final List<Map<String, Object>> allHosts = agentService.getAllHosts(new AgentCriteria());
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(allHosts);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/update_soft", method = {RequestMethod.POST, RequestMethod.PUT})
    public void updateSoftware(@RequestParam(value = "id") Integer id,
                               @RequestParam(value = "serverIp") String serverIp,
                               @RequestParam(value = "name") String name,
                               @RequestParam(value = "processCount") Integer processCount,
                               @RequestParam(value = "realDir") String realDir,
                               @RequestParam(value = "heartMonitor") Boolean heartMonitor,
                               @RequestParam(value = "config") Boolean config,
                               @RequestParam(value = "keys", required = false) String keys,
                               @RequestParam(value = "scriptPath", required = false) String scriptPath,
                               @RequestParam(value = "script", required = false) String script,
                               @RequestParam(value = "closeScript", required = false) String closeScript,
                               @RequestParam(value = "logPath", required = false) String logPath,
                               @RequestParam(value = "logSuffix", required = false) String logSuffix,
                               @RequestParam(value = "configPath", required = false) String configPath,
                               HttpServletResponse response) {
        try {
            SoftwareVo softwareVo = new SoftwareVo();
            softwareVo.setId(id);
            softwareVo.setName(name);
            softwareVo.setProcessCount(processCount);
            softwareVo.setRealDir(realDir);
            softwareVo.setConfig(config);
            softwareVo.setKeys(keys);
            softwareVo.setScriptPath(scriptPath);
            softwareVo.setCloseScript(closeScript);
            softwareVo.setScript(script);
            softwareVo.setServerIp(serverIp);
            softwareVo.setHost(serverIp);
            softwareVo.setHeartMonitor(heartMonitor);
            softwareVo.setLogPath(logPath);
            softwareVo.setLogSuffix(logSuffix);
            softwareVo.setConfigPath(configPath);
            softwareService.updateSoftware(softwareVo, true);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/delete_soft", method = {RequestMethod.DELETE, RequestMethod.POST, RequestMethod.PUT})
    public void deleteSoftware(@RequestParam(value = "id") Integer id,
                               @RequestParam(value = "serverIp") String serverIp,
                               HttpServletResponse response) {
        try {
            SoftwareVo softwareVo = new SoftwareVo();
            softwareVo.setId(id);
            softwareVo.setServerIp(serverIp);
            softwareVo.setHost(serverIp);
            softwareService.deleteSoftware(softwareVo);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/soft_{id}.json")
    public void getSoftware(@PathVariable(value = "id") Integer id,
                            HttpServletResponse response) {
        final SoftwareVo softwareInfo = softwareService.getSoftware(id);
        try {
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareInfo);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/note", method = {RequestMethod.POST, RequestMethod.PUT})
    public void updateNote(HttpServletResponse response, @RequestParam("serverId") Integer serverId, @RequestParam("name") String name, @RequestParam("note") String note) {
        try {
            final SoftwareVo softwareVo = new SoftwareVo();
            softwareVo.setServerId(serverId);
            softwareVo.setName(name);
            softwareVo.setNote(note);
            softwareService.updateSoftwareInfo(softwareVo, false);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/heap", method = {RequestMethod.POST, RequestMethod.PUT})
    public void setHeapSize(HttpServletResponse response, @RequestParam("id") Integer id, @RequestParam("ip") String ip, @RequestParam("name") String name, @RequestParam("minHeapSize") Integer minHeapSize, @RequestParam("maxHeapSize") Integer maxHeapSize) {
        if (minHeapSize == null || maxHeapSize == null || minHeapSize == 0 || maxHeapSize == 0) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), "堆内存不能设置为空和0")), response);
        }
        if (minHeapSize > maxHeapSize) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), "最小堆内存不能大于最大堆内存")), response);
        }
        try {
            softwareService.setHeapSize(id, ip, name, minHeapSize, maxHeapSize);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/names")
    public void getSoftwareList(HttpServletResponse response, @RequestParam("ip") String ip, @RequestParam(value = "directory", required = false) String directory) {
        try {
            final Object softwareFromRemoteServer = softwareService.getSoftwareFromRemoteServer(ip, directory);
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareFromRemoteServer);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/directory")
    public void getDirectory(HttpServletResponse response, @RequestParam("os") Boolean os) {

        try {
            final ArrayList<String> list = new ArrayList<>();
            if (Objects.isNull(os) || os) {
                //windows
                final String[] split = windowsDirectory.split(",");
                for (String s : split) {
                    list.add(s);
                }
            } else {
                //linux
                final String[] split = linuxDirectory.split(",");
                for (String s : split) {
                    list.add(s);
                }
            }
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(list);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }


    @RequestMapping(value = "/default.json")
    public void getAddSoftwareInfo(HttpServletResponse response, @RequestParam("ip") String ip, @RequestParam("name") String name) {
        try {
            SoftwareVo softwareVo = new SoftwareVo();
            HashMap<String, String> addSoftwareInfo = BaseConfigUtil.softwareMap.get(name);
            final AgentCriteria agentCriteria = new AgentCriteria();
            String[] ipsArr = ip.split(",");
            AgentVo agentVo = null;
            for (String serverIp : ipsArr) {
                agentCriteria.setIp(serverIp);
                final List<AgentVo> agentList = agentService.getAgentList(agentCriteria);
                if (agentList == null || agentList.isEmpty()) {
                    throw new ServiceException("没有该服务器ip：" + ip);
                }
                agentVo = agentList.get(0);
            }
            String realDir = "";
            String script = "";
            String closeScript = "";
            if (OsType.LINUX.getId().equals(agentVo.getOs())) {
                if (name.equals("CodisMaster") || name.equals("CodisSlaver")) {
                    realDir = "/usr/local/codis";
                } else {
                    realDir = "/dist/" + name;
                }
            } else {
                realDir = "D:\\dist\\" + name;
            }
            if (addSoftwareInfo == null || addSoftwareInfo.isEmpty() || addSoftwareInfo.size() == 0) {
                if (OsType.LINUX.getId().equals(agentVo.getOs())) {
                    script = "sh start.sh";
                    closeScript = "sh stop.sh";
                } else {
                    script = "start.bat";
                    closeScript = "stop.bat";
                }
                softwareVo.setHeartMonitor(true);
                softwareVo.setProcessCount(1);
                softwareVo.setRealDir(realDir);
                //softwareVo.setBaseDir(baseDir);
                //softwareVo.setKeys(baseDir + name);
                //softwareVo.setScript(script);
                //softwareVo.setCloseScript(closeScript);
                //softwareVo.setScriptPath("bin");
                softwareVo.setConfig(false);
            } else {
                if (OsType.LINUX.getId().equals(agentVo.getOs())) {
                    script = addSoftwareInfo.get("lin_script");
                } else {
                    script = addSoftwareInfo.get("win_script");
                }
                softwareVo.setHeartMonitor(addSoftwareInfo.get("monitor").equals("0") ? false : true);
                softwareVo.setProcessCount(Integer.valueOf(addSoftwareInfo.get("process")));
                softwareVo.setRealDir(realDir);
                //softwareVo.setBaseDir(baseDir);
                softwareVo.setKeys(addSoftwareInfo.get("keys"));
                softwareVo.setScript(script);
                //softwareVo.setCloseScript(closeScript);
                softwareVo.setScriptPath(addSoftwareInfo.get("path"));
                softwareVo.setConfig(true);
            }
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareVo);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/checkUpgradePackage")
    public void checkUpgradePackage(HttpServletResponse response) {
        try {
            CheckUpgradePackageVo checkUpgradePackageVo = new CheckUpgradePackageVo();
            if (isOpen) {
                Boolean result = autoDeployService.checkUpgradePackage();
                checkUpgradePackageVo.setCheckResult(result);
            } else {
                checkUpgradePackageVo.setCheckResult(true);
            }
            checkUpgradePackageVo.setIsOpen(isOpen);
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(checkUpgradePackageVo);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @GetMapping(value = "/unzip_package")
    public void unzipPackage(HttpServletResponse response) {
        try {
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            Boolean result = autoDeployService.unzipPackage();
            UnzipResult unzipResult = new UnzipResult();
            unzipResult.setResult(result);
            responseVo.setData(unzipResult);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @GetMapping(value = "/check_unzip_package_format")
    public void checkUnzipPackageFormat(HttpServletResponse response) {
        try {
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(autoDeployService.checkUnzipPackageFormat());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @GetMapping(value = "/compare_deploy_task")
    public void compareDeployTask(HttpServletResponse response) {
        try {
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(autoDeployService.compareDeployTask());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @PostMapping("/deploy_task.json")
    public void lastDeployTask(HttpServletResponse response,
                               @RequestParam(value = "deploy") Map<String, Object> deploy) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        Map<String, Object> deployMap = autoDeployService.deployTask(deploy);
        responseVo.setData(deployMap);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @GetMapping(value = "/common_config")
    public void downloadCommonConfigFile(HttpServletResponse response) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            CommonConfigResultVo commonConfig = autoDeployService.getCommonConfig();
            if (commonConfig == null) {
                responseVo = new ResponseVo(ResponseType.OPERATE_ERROR);
                responseVo.setMsg("要上传的公共配置文件没有配置或不存在!");
                outputResponse(JsonUtil.toJsonString(responseVo), response);
            } else {
                responseVo.setData(commonConfig);
                responseVo.setMsg("成功获取公共配置文件!");
                outputResponse(JsonUtil.toJsonString(responseVo), response);
            }
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.OPERATE_ERROR);
            responseVo.setMsg(e.getMessage());
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        }
    }


    @PostMapping("/add_modify_program.json")
    public void addModifyProgram(HttpServletResponse response,
                                 @RequestParam(value = "deployPath") String deployPath,
                                 @RequestParam(value = "programName") String programName, @RequestParam(value = "configPath") String configPath) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
            modifyConfigCriteria.setDeployPath(deployPath);
            modifyConfigCriteria.setProgramName(programName);
            modifyConfigCriteria.setConfigPath(configPath.replace("\\", "/"));
            responseVo.setData(softwareService.addModifyConfig(modifyConfigCriteria));
            responseVo.setMsg("添加成功");
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
            responseVo.setMsg("添加出错");
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/delete_modify_program.json")
    public void deleteModifyProgram(HttpServletResponse response,
                                    @RequestParam(value = "id") Integer id) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
            modifyConfigCriteria.setId(id);
            softwareService.deleteModifyConfig(modifyConfigCriteria);
            responseVo.setMsg("删除成功");
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
            responseVo.setMsg("删除异常");
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/update_modify_program.json")
    public void updateModifyProgram(HttpServletResponse response, @RequestParam(value = "id") Integer id,
                                    @RequestParam(value = "deployPath") String deployPath,
                                    @RequestParam(value = "programName") String programName, @RequestParam(value = "configPath") String configPath) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
            modifyConfigCriteria.setId(id);
            modifyConfigCriteria.setDeployPath(deployPath);
            modifyConfigCriteria.setProgramName(programName);
            modifyConfigCriteria.setConfigPath(configPath.replace("\\", "/"));
            responseVo.setData(softwareService.updateModifyConfig(modifyConfigCriteria));
            responseVo.setMsg("修改成功");
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
            responseVo.setData("");
            responseVo.setMsg("修改异常");
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @GetMapping("/get_modify_program.json")
    public void getModifyProgram(HttpServletResponse response) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            ModifyConfigCriteria modifyConfigCriteria = new ModifyConfigCriteria();
            responseVo.setData(softwareService.queryModifyConfig(modifyConfigCriteria));
            responseVo.setMsg("查询成功");
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
            responseVo.setData("");
            responseVo.setMsg("查询失败");
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }


    @PostMapping("/write_modify_result_to_config.json")
    public void writeModifyResultToConfig(HttpServletResponse response, @RequestParam(value = "ip") String ip,
                                          @RequestParam(value = "programName") String programName,
                                          @RequestParam(value = "confPath") String confPath, @RequestParam(value = "content") String content) {
        ResponseVo responseVo;
        AgentCriteria criteria = new AgentCriteria();
        criteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(criteria);
        try {
            if (confPath.contains("\\")) {
                confPath = confPath.replace("\\", "/");
            }
            String confName;
            String path;
            if (confPath.contains("/")) {
                confName = confPath.substring(confPath.lastIndexOf("/") + 1);
                path = confPath.substring(0, confPath.lastIndexOf("/"));
            } else {
                confName = confPath;
                path = "";
            }
            boolean result = softwareService.saveConfigFile(agentVo, programName, confName, content, path, "false", "");
            if (result) {
                responseVo = new ResponseVo(ResponseType.SUCCESS);
                responseVo.setData("");
                responseVo.setMsg(agentVo.getRole() + " 配置保存成功");
            } else {
                responseVo = new ResponseVo(ResponseType.FAILURE);
                responseVo.setData("");
                responseVo.setMsg(agentVo.getRole() + " 配置保存失败");
            }
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
            responseVo.setData("");
            responseVo.setMsg(agentVo.getRole() + " 配置保存异常");
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/unzip_pwd.json")
    public void getUnzipPwd(HttpServletResponse response, @RequestParam(value = "pwd") String pwd) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            softwareService.setUnzipPwd(pwd);
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @GetMapping("/is_continue_upgrade")
    public void isContinueUpgrade(HttpServletResponse response) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareService.isContinueUpgrade());
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/get_ips")
    public void getIps(HttpServletResponse response, @RequestParam(value = "os") Integer os) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            List<String> ips = softwareService.getIps(os);
            responseVo.setData(ips);
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/check_path")
    public void checkPath(HttpServletResponse response, @RequestParam(value = "targetPath") String targetPath, @RequestParam(value = "ips") String ips) {
        ResponseVo responseVo;
        try {
            if (StringUtils.isEmpty(targetPath) || StringUtils.isEmpty(ips)) {
                throw new ServiceException("入参为空");
            }
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareService.checkPath(targetPath, ips));
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/execute_task")
    public void executeTask(HttpServletResponse response, @RequestParam(value = "filePath") String filePath, @RequestParam(value = "os") Integer os,
                            @RequestParam(value = "ips") String ips, @RequestParam(value = "targetPath") String targetPath, @RequestParam(value = "command") String command) {
        ResponseVo responseVo;
        try {
            if (StringUtils.isEmpty(filePath) || StringUtils.isEmpty(targetPath) || StringUtils.isEmpty(ips) || StringUtils.isEmpty(command)) {
                throw new ServiceException("参数不能为空!");
            }
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareService.executeTask(filePath, os, ips, targetPath, command));
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/try_again")
    public void tryAgain(HttpServletResponse response, @RequestParam(value = "ip") String ip, @RequestParam(value = "targetPath") String targetPath, @RequestParam(value = "command") String command) {
        ResponseVo responseVo;
        try {
            if (StringUtils.isEmpty(command)) {
                throw new ServiceException("参数不能为空!");
            }
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(softwareService.tryAgain(ip, targetPath, command));
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @PostMapping("/recordIsModifyCommonConfig")
    public void recordIsModifyCommonConfig(HttpServletResponse response, @RequestParam(value = "isModifyCommonConfig") Integer isModifyCommonConfig) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            autoDeployService.recordIsModifyCommonConfig(isModifyCommonConfig);
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @GetMapping("/getIsModifyCommonConfig")
    public void getIsModifyCommonConfig(HttpServletResponse response) {
        ResponseVo responseVo;
        try {
            responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(autoDeployService.getIsModifyCommonConfig());
        } catch (Exception e) {
            responseVo = new ResponseVo(ResponseType.FAILURE);
        }
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    /**
     * 按主机分组容器(兼容前端显示格式)
     */
    private Map<String, List<SoftwareVo>> groupContainersByHost(List<SoftwareVo> containers) {
        Map<String, List<SoftwareVo>> grouped = new HashMap<>();

        for (SoftwareVo container : containers) {
            String host = container.getHost();
            if (host == null) {
                host = "unknown";
            }

            grouped.computeIfAbsent(host, k -> new ArrayList<>()).add(container);
        }

        return grouped;
    }

    /**
     * 生成模拟图表数据(兼容前端图表格式)
     */
    private Map<String, Object> generateMockChartData(String startTime, String endTime) {
        Map<String, Object> chartData = new HashMap<>();

        // 生成时间轴
        List<String> timePoints = new ArrayList<>();
        List<Double> cpuData = new ArrayList<>();
        List<Double> memoryData = new ArrayList<>();
        List<Double> diskData = new ArrayList<>();

        // 生成12个时间点的模拟数据
        for (int i = 0; i < 12; i++) {
            timePoints.add(String.format("%02d:%02d", 10 + i/2, (i%2)*30));
            cpuData.add(Math.random() * 50); // 0-50%
            memoryData.add(Math.random() * 2); // 0-2GB
            diskData.add(Math.random() * 100); // 0-100MB/s
        }

        // CPU使用率
        Map<String, Object> cpuResource = new HashMap<>();
        cpuResource.put("x", timePoints);
        cpuResource.put("y", cpuData);
        cpuResource.put("content", "CPU使用率");
        chartData.put("cpuResource", cpuResource);

        // 内存使用量
        Map<String, Object> memoryResource = new HashMap<>();
        memoryResource.put("x", timePoints);
        memoryResource.put("y", memoryData);
        memoryResource.put("content", "内存使用量");
        chartData.put("usedMemoryResource", memoryResource);

        // 磁盘IO
        Map<String, Object> diskResource = new HashMap<>();
        diskResource.put("x", timePoints);
        diskResource.put("y", diskData);
        diskResource.put("content", "磁盘IO");
        chartData.put("diskResource", diskResource);

        return chartData;
    }

}