package com.maintain.server.controller;


import com.maintain.server.criteria.MysqlBackupCriteria;
import com.maintain.server.service.MysqlBackupService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.ResponseVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/mysqlbackup/")
public class MysqlBackupController extends BaseController {

    @Autowired
    private MysqlBackupService mysqlBackupService;


    /**
     * 查询七天的数据
     *
     * @return
     */
    @RequestMapping(value = "list.json")
    public void MysqlBackupList(@RequestParam(name = "startTime", required = false) String startTime,
                                @RequestParam(name = "endTime", required = false) String endTime,
                                @RequestParam(name = "type", required = false) int backupType,
                                HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        MysqlBackupCriteria mysqlBackupCriteria = new MysqlBackupCriteria();
        mysqlBackupCriteria.setStartTime(startTime);
        mysqlBackupCriteria.setEndTime(endTime);
        mysqlBackupCriteria.setBackupType(backupType);
        responseVo.setData(mysqlBackupService.findMysqlBackup(mysqlBackupCriteria));
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping(value = "ignore_status")
    public ResponseVo ignoreStatus(@RequestParam(name = "idList") String idList) {
        if (StringUtils.isEmpty(idList)) {
            ResponseVo responseVo = ResponseVo.getFailed();
            responseVo.setMsg("请传递正确的ID列表");
            return responseVo;
        }
        mysqlBackupService.markIgnoreStatus(Arrays.stream(idList.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList()));
        return ResponseVo.getSuccess();
    }
}
