package com.maintain.server.controller;

import com.alibaba.druid.stat.DruidStatManagerFacade;
import com.common.log.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class DruidStatController {

    @GetMapping("/druidstat.json")
    public Object druidStat(){
        Log.low.info("druidStat");
        // DruidStatManagerFacade#getDataSourceStatDataList 该方法可以获取所有数据源的监控数据，除此之外 DruidStatManagerFacade 还提供了一些其他方法，你可以按需选择使用。
        final List<Map<String, Object>> dataSourceStatDataList = DruidStatManagerFacade.getInstance().getDataSourceStatDataList();
//        final Object stat1 = DruidStatManagerFacade.getInstance().getDruidDataSourceById((Integer) (dataSourceStatDataList.get(0).get("Identity")));
//        final Object stat2 = DruidStatManagerFacade.getInstance().getDruidDataSourceById((Integer) (dataSourceStatDataList.get(1).get("Identity")));
        return dataSourceStatDataList;
    }
}
