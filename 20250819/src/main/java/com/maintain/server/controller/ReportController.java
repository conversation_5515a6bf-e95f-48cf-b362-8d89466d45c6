package com.maintain.server.controller;

import com.maintain.server.service.ReportService;
import com.maintain.server.type.ReportType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/report")
@Controller
public class ReportController {

    @Autowired
    private ReportService reportService;

    @RequestMapping("/list.json")
    @ResponseBody
    public Object reportList(@RequestParam("type") Integer type,
                             @RequestParam("pn") Integer pn,
                             @RequestParam("ps") Integer ps){
        return reportService.getReportPage(ReportType.parse(type),pn,ps);
    }

    @RequestMapping("/down")
    public ResponseEntity<byte[]> downLoadReport(@RequestParam("path") String path){
        try {
            return reportService.downLoadReport(path);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.SERVICE_UNAVAILABLE);
        }
    }
}
