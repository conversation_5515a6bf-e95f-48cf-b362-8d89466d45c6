package com.maintain.server.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maintain.server.Constants;
import com.maintain.server.service.IndexService;
import com.maintain.server.type.ReportType;
import com.maintain.server.type.ResponseType;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @date 2018-10-22
 */
@Controller
@RequestMapping("/home/")
public class HomeController extends BaseController implements Constants {

    @Autowired
    private IndexService indexService;


    @RequestMapping(value = "monitor.json", produces = "application/json; charset=utf-8")
    @ResponseBody
    public Callable<ResponseVo> getIndexJson() {
        return () -> {
            ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg());
            responseVo.setData(indexService.getMonitorData());
            return responseVo;
        };
    }

    @RequestMapping(value = "report.json")
    @ResponseBody
    public Map<String, Object> getReport(@RequestParam(value = "type") Integer type, @RequestParam(value = "startTime") String startTime, @RequestParam(value = "endTime") String endTime) throws JsonProcessingException {

        final Map<String, Object> report = indexService.getReport(ReportType.parse(type), startTime, endTime);
        return report;
    }

}