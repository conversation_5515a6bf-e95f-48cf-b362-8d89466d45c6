package com.maintain.server.controller;

import com.github.pagehelper.PageInfo;
import com.maintain.server.Constants;
import com.maintain.server.criteria.GroupCriteria;
import com.maintain.server.service.GroupHistoryService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.GroupType;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.GroupHistoryHealthVo;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.maintain.server.type.GroupType.*;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Controller
@RequestMapping("/group/")
public class GroupController extends BaseController {

    @Autowired
    private GroupHistoryService groupHistoryService;

    @Value("${es.secondes.enable}")
    private Boolean es2Enable;


    @RequestMapping("services")
    public void services(HttpServletResponse response) {
        List<GroupType> groupTypeList = Arrays.asList(ES, HDFS, YARN, HIVE, HBASE, ZK, KAFKA, RANGER, FLINK, SPARK, TRINO);
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        List<Map<String, Object>> groupList = groupTypeList.stream().map(gt -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", gt.getId());
            map.put("name", gt.getName());
            return map;
        }).collect(Collectors.toList());
        responseVo.setData(groupList);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping("list.json")
    public void list(
            @RequestParam(name = "pn", required = false) Integer pn,
            @RequestParam(name = "ps", required = false) Integer ps,
            @RequestParam(name = "startTime", required = false) String startTime,
            @RequestParam(name = "endTime", required = false) String endTime,
            @RequestParam(name = "groupId", required = false) Integer groupId,
            @RequestParam(name = "name", required = false) String name,
            HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        GroupCriteria criteria = new GroupCriteria();
        criteria.setName(name).setGroupId(groupId).setPn(pn).setPs(ps).setStartTime(startTime).setEndTime(endTime);
        PageInfo<GroupHistoryHealthVo> pageInfo = groupHistoryService.getGroupHistoryHealth(criteria);

        if (pageInfo != null && ListUtil.isNotEmpty(pageInfo.getList())) {
            Map<String, Object> data = parsePageInfo(pageInfo.getList());

            responseVo.setData(data);

            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } else {
            responseVo.setResponseType(ResponseType.SUCCESS);
            responseVo.setData(null);
            responseVo.setMsg("暂无数据");
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        }
    }

    /**
     * 数据分组
     *
     * @param list list
     * @return data
     */
    private Map<String, Object> parsePageInfo(List<GroupHistoryHealthVo> list) {
        Map<String, Object> data = new LinkedHashMap<>();
        Set<String> xTime = new LinkedHashSet<>();
        //List<GroupHistoryHealthVo> tempList;
        Map<String, Object> greenMap = new HashMap<>();
        List<Integer> greenList = new ArrayList<>();
        int green = 0;
        Map<String, Object> yellowMap = new HashMap<>();
        List<Integer> yellowList = new ArrayList<>();
        int yellow = 0;
        Map<String, Object> redMap = new HashMap<>();
        List<Integer> redList = new ArrayList<>();
        int red = 0;
        List<String> descriptionList = new ArrayList<>();
        for (GroupHistoryHealthVo vo : list) {
            Integer status = vo.getStatus();
            if (status == null) {
                continue;
            }
            Date date = vo.getCreateTime();
            xTime.add(DateUtil.format(date, Constants.YYYY_MM_DD_HH_MM_SS));
            if (AlarmStatusType.GREEN.getId().equals(status)) {
                greenList.add(status);
                ++green;
                yellowList.add(null);
                redList.add(null);
            } else if (AlarmStatusType.YELLOW.getId().equals(status)) {
                yellowList.add(status);
                ++yellow;
                greenList.add(null);
                redList.add(null);
            } else if (AlarmStatusType.RED.getId().equals(status)) {
                redList.add(status);
                ++red;
                greenList.add(null);
                yellowList.add(null);
            }
            descriptionList.add(vo.getDescription());
        }

        greenMap.put("num", green);
        greenMap.put("chart", greenList);
        yellowMap.put("num", yellow);
        yellowMap.put("chart", yellowList);
        redMap.put("num", red);
        redMap.put("chart", redList);
        data.put(AlarmStatusType.RED.name(), redMap);
        data.put(AlarmStatusType.YELLOW.name(), yellowMap);
        data.put(AlarmStatusType.GREEN.name(), greenMap);
        data.put("x", xTime);
        data.put("description", descriptionList);
        data.put("content", list.get(0).getName());
        return data;
    }
}