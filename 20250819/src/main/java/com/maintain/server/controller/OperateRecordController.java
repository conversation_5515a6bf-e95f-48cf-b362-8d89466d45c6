package com.maintain.server.controller;

import com.github.pagehelper.PageInfo;
import com.maintain.server.criteria.OperateRecordCriteria;
import com.maintain.server.service.OperateRecordService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.OperateRecordVo;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2018-11-28
 */
@Controller
@RequestMapping("/operate/")
public class OperateRecordController extends BaseController {

    @Autowired
    private OperateRecordService operateRecordService;

    @RequestMapping(value = "record.json")
    public void operateRecord(@RequestParam(name = "pn", required = false, defaultValue = "1") Integer pn,
                              @RequestParam(name = "ps", required = false, defaultValue = "20") Integer ps,
                              HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        OperateRecordCriteria criteria = new OperateRecordCriteria();
        criteria.setPn(pn).setPs(ps);
        PageInfo<OperateRecordVo> data = operateRecordService.getOperateRecord(criteria);
        responseVo.setData(data);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }
}