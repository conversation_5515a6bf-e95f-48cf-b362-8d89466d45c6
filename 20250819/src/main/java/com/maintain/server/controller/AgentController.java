package com.maintain.server.controller;

import com.maintain.server.Constants;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.service.AgentService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.OsType;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ProcessUtil;
import com.maintain.server.vo.AgentVo;
import com.maintain.server.vo.ImportResult;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-10-10
 */
@RestController
@RequestMapping("/agent")
public class AgentController extends BaseController implements Constants {

    @Autowired
    private AgentService agentService;

    @Value("${ntpServer}")
    private String ntpServer;

    @RequestMapping(value = "/list.json")
    public void getAgentList(@RequestParam(value = "pn", required = false, defaultValue = "1") Integer pn,
                             @RequestParam(value = "ps", required = false, defaultValue = "20") Integer ps,
                             @RequestParam(value = "status", required = false) String status,
                             HttpServletResponse response) {
        AgentCriteria criteria = new AgentCriteria();
        criteria.setPn(pn);
        criteria.setPs(ps);
        AlarmStatusType alarmStatusType = AlarmStatusType.getType(status);
        List<AgentVo> list = agentService.getAgentList(criteria);
        Map<String, Long> statusMap = list.stream()
                .collect(Collectors.groupingBy(v -> v.getStatus().getValue(), Collectors.counting()));
        final Map<String, Object> map = new HashMap<>();
        map.put("ntpServer",ntpServer);
        map.put("page",list.stream().filter(v -> alarmStatusType == null || alarmStatusType.equals(v.getStatus())).collect(Collectors.toList()));
        map.put("statusMap", statusMap);
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(ResponseType.SUCCESS.getCode());
        responseVo.setMsg(ResponseType.SUCCESS.getMsg());
        responseVo.setData(map);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping(value = "/roles.json")
    @ResponseBody
    public ResponseVo getRoles() {
        List<Map<String, Object>> data = agentService.getPlatformRole(null);
        ResponseVo responseVo = getResponse(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg());
        responseVo.setData(data);
        return responseVo;
    }

    @RequestMapping(value = "/prepareUpdate.json", produces = "application/json; charset=utf-8")
    @ResponseBody
    public ResponseVo prepareUpdate() {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        getMaintainAgentDir(responseVo);
        return responseVo;
    }

    private boolean getMaintainAgentDir(ResponseVo responseVo) {
        String osName = ProcessUtil.getOsName();
        Integer osId = OsType.getId(osName);
        if (osId == null) {
            responseVo.setCode(ResponseType.OPERATE_ERROR.getCode());
            responseVo.setMsg("未知操作系统");
            return false;
        }
        String localPath = "";
        String name = "MaintainAgent";
        if (osId.equals(LINUX)) {
            localPath = MAINTAIN_LINUX_TEMP + name;
        } else if (osId.equals(WINDOWS)) {
            localPath = MAINTAIN_WINDOWS_TEMP + name;
        }
        File file = new File(localPath);
        if (!file.exists()) {
            responseVo.setCode(ResponseType.OPERATE_ERROR.getCode());
            responseVo.setMsg("找不到路径" + localPath + ",请确保待上传程序已经存放于此。");
            return false;
        }
        responseVo.setData(file.getParent());
        return true;
    }

    @RequestMapping(value = "/note",method = {RequestMethod.POST,RequestMethod.PUT})
    public void updateNote(HttpServletResponse response,@RequestParam("ip") String ip, @RequestParam("note") String note) {
        try {
            final AgentVo agentVo = new AgentVo();
            agentVo.setIp(ip);
            agentVo.setNote(note);
            agentService.updateAgent(agentVo);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "/update",method = {RequestMethod.POST,RequestMethod.PUT})
    public void updateAgent(HttpServletResponse response,@RequestParam("ip") String ip,@RequestParam("role") String role,@RequestParam("existsVpn") Integer existsVpn,@RequestParam("note") String note) {
        try {
            final AgentVo agentVo = new AgentVo();
            agentVo.setIp(ip);
            agentVo.setRole(role);
            agentVo.setNote(note);
            agentVo.setExistsVpn(existsVpn);
            agentService.updateAgent(agentVo);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "/delete",method = {RequestMethod.POST,RequestMethod.PUT,RequestMethod.DELETE})
    public void deleteAgent(HttpServletResponse response,@RequestParam("ip") String ip) {
        try {
            agentService.deleteAgent(ip);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "/import",method = {RequestMethod.POST})
    public void importData(HttpServletResponse response,@RequestParam(name = "file",required = false) MultipartFile file){
        try {
            ImportResult importResult = agentService.importData(file);
            ResponseVo responseVo = new ResponseVo();
            responseVo.setCode(ResponseType.SUCCESS.getCode());
            responseVo.setMsg(ResponseType.SUCCESS.getMsg());
            responseVo.setData(importResult);
            //需要在web展示
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

}