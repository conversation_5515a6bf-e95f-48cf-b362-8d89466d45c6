package com.maintain.server.controller;

import com.alibaba.fastjson.TypeReference;
import com.common.log.Log;
import com.github.pagehelper.PageInfo;
import com.maintain.server.Constants;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.ice.IceFlag;
import com.maintain.server.service.HardwareService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ResponseType;
import com.maintain.server.type.WarnType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.HardwareHistoryHealth;
import com.maintain.server.vo.HardwareVo;
import com.maintain.server.vo.ResponseVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-10-25
 */
@Controller
@RequestMapping("/hardware")
public class HardwareController extends BaseController {

    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private SoftwareService softwareService;

    @RequestMapping(value = "/list.json")
    public void getHardwareInfos(HttpServletResponse response, @RequestParam(name = "status", defaultValue = "") String status) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        final HardwareCriteria hardwareCriteria = new HardwareCriteria();
        if (status != null && !"".equals(status)) {
            hardwareCriteria.setStatusType(AlarmStatusType.getType(status));
        }
        responseVo.setData(hardwareService.getHardwareInfosAndStatus(hardwareCriteria));
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping(value = "/history_resource_{agentId}.json")
    public void getHistoryResourceByTime(@PathVariable(value = "agentId") Integer agentId,
                                         @RequestParam(value = "startTime", required = false) String startTime,
                                         @RequestParam(value = "endTime", required = false) String endTime,
                                         @RequestParam(value = "timePoint", required = false) String[] timePoint,
                                         HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        Map<String, Object> data = new HashMap<>();
        final HardwareCriteria hardwareCriteria = new HardwareCriteria();
        hardwareCriteria.setAgentId(agentId);
        if (startTime != null && endTime != null) {
            hardwareCriteria.setStartTime(startTime);
            hardwareCriteria.setEndTime(endTime);
        } else {
            hardwareCriteria.setStartTime(timePoint[0]);
            hardwareCriteria.setEndTime(timePoint[timePoint.length - 1]);
        }
        final Map<String, Object> historyResource = hardwareService.getHistoryResource(hardwareCriteria, Boolean.FALSE);
        if (historyResource != null) {
            data.put("historyResource", historyResource);
        }
        responseVo.setData(data);
        outputResponse(JsonUtil.toJSONStringWithDateFormat(responseVo, Constants.YYYY_MM_DD_HH_MM), response);
    }

    @RequestMapping(value = "/detail_{agentId}.json")
    public void getHardwareDetail(HttpServletResponse response,
                                  @PathVariable(name = "agentId") Integer agentId,
                                  @RequestParam(name = "pn", defaultValue = "1") Integer pn,
                                  @RequestParam(name = "ps", defaultValue = "20") Integer ps) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        HardwareCriteria criteria = new HardwareCriteria();
        criteria.setAgentId(agentId);
        criteria.setPn(pn);
        criteria.setPs(ps);
        HardwareVo hardwareVo = hardwareService.getHardwareInfo(criteria);
        if (hardwareVo == null) {
            outputResponse(JsonUtil.toJsonString(responseVo), response);
            return;
        }
        PageInfo<HardwareHistoryHealth> pageInfo = hardwareService.getHardwareHistoryHealth(criteria);
        if (pageInfo != null && ListUtil.isNotEmpty(pageInfo.getList())) {
            hardwareVo.setHardwareHistoryHealthList(pageInfo);
        }
        String diskInfo = hardwareVo.getDbDiskInfo();
        String nicInfo = hardwareVo.getDbNicInfo();
        TypeReference<List<Map<String, String>>> typeReference = new TypeReference<List<Map<String, String>>>() {
        };
        TypeReference<List<HardwareVo.NicConfig>> typeReference2 = new TypeReference<List<HardwareVo.NicConfig>>() {
        };
        hardwareVo.setDiskInfo(JsonUtil.parseObject(diskInfo, typeReference));
        hardwareVo.setNicInfo(JsonUtil.parseObject(nicInfo, typeReference2));
        hardwareVo.setCpuTopProcessInfo(JsonUtil.parseObject(hardwareVo.getCpuTopProcess(), new TypeReference<List<HardwareVo.ProcessInfo>>() {
        }));
        hardwareVo.setCpuTopProcess(null);
        hardwareVo.setMemTopProcessInfo(JsonUtil.parseObject(hardwareVo.getMemTopProcess(), new TypeReference<List<HardwareVo.ProcessInfo>>() {
        }));
        hardwareVo.setMemTopProcess(null);
        responseVo.setData(hardwareVo);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping(value = "/operateDetail_{ip}.json")
    public void getOsOperateDetail(HttpServletResponse response,
                                   @PathVariable(name = "ip") String ip,
                                   @RequestParam(name = "pn", defaultValue = "1") Integer pn,
                                   @RequestParam(name = "ps", defaultValue = "20") Integer ps) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        responseVo.setData(hardwareService.getOsOperateLog(pn, ps, ip));
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @RequestMapping(value = "firewall-rule.json", produces = "application/json; charset=utf-8")
    public void firewallRule(@RequestParam("ip") String ip, HttpServletResponse response) {
        ResponseVo rep;
        HardwareCriteria criteria = new HardwareCriteria();
        criteria.setIp(ip);
        HardwareVo hardwareVo = hardwareService.getHardwareInfo(criteria);
        if (hardwareVo == null) {
            rep = new ResponseVo(ResponseType.REJECT);
            outputResponse(JsonUtil.toJsonString(rep), response);
            return;
        } /*else if (Constants.WINDOWS.equals(hardwareVo.getOs())) {
            rep = new ResponseVo(ResponseType.REJECT);
            rep.setMsg("暂不支持查看Windows防火墙策略...");
            outputResponse(JsonUtil.toJsonString(rep), response);
            return;
        }*/
        String result = softwareService.iceRequest(ip, IceFlag.OPERATE_FIREWALL, "2");
        if (StringUtils.isNotEmpty(result)) {
            rep = JsonUtil.parseObject(result, ResponseVo.class);
            Object data = rep.getData();
            if (data != null) {
                String dataStr = data.toString();
                final List<String> list = Arrays.asList(dataStr.split("\n"));
                rep.setData(list);
                outputResponse(JsonUtil.toJsonString(rep), response);
            }
            outputResponse(JsonUtil.toJsonString(rep), response);
        }
    }

    @RequestMapping(value = "/note", method = {RequestMethod.POST, RequestMethod.PUT})
    public void updateNote(HttpServletResponse response, @RequestParam("agentId") Integer agentId, @RequestParam("note") String note) {
        try {
            final HardwareVo hardwareVo = new HardwareVo();
            hardwareVo.setAgentId(agentId);
            hardwareVo.setNote(note);
            hardwareService.updateHardwareInfo(hardwareVo);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/warning")
    public void updateWarning(HttpServletResponse response, @RequestParam("ip") String ip, @RequestParam(value = "warnSize", required = false) String warn, @RequestParam(value = "errorSize", required = false) String error,
                              @RequestParam("status") Integer status, @RequestParam(value = "warnPercent", required = false) String warnPercent,
                              @RequestParam(value = "errorPercent", required = false) String errorPercent, @RequestParam("disk") String disk, @RequestParam("warnType") WarnType warnType) {
        try {
            hardwareService.updateWarning(ip, status, warn, error, warnPercent, errorPercent, disk, warnType);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/set_all_warning")
    public void updateAllIpWarning(HttpServletResponse response, @RequestParam("ips") String ips, @RequestParam(value = "warnSize", required = false) String warn, @RequestParam(value = "errorSize", required = false) String error,
                                   @RequestParam("status") Integer status, @RequestParam(value = "warnPercent", required = false) String warnPercent,
                                   @RequestParam(value = "errorPercent", required = false) String errorPercent, @RequestParam("disk") String disk, @RequestParam("warnType") WarnType warnType) {
        try {
            hardwareService.updateAllIpWarning(ips, status, warn, error, warnPercent, errorPercent, disk, warnType);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)), response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/queryWarning")
    public void getWarning(HttpServletResponse response, @RequestParam("ip") String ip) {
        try {
            Object data = hardwareService.getWarning(ip);
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(data);
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    @RequestMapping(value = "/queryDisk")
    public void getDisks(HttpServletResponse response, @RequestParam("ip") String ip) {
        try {
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(hardwareService.getDisks(ip));
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        } catch (Exception e) {
            Log.high.error(e);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }

    /*@RequestMapping(value = "/queryAllDisk")
    public void getAllDisks(HttpServletResponse response, @RequestParam("ips") String ips) {
        try {
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            if (ips == null) {
                outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.REJECT.getCode(), "参数错误")), response);
            } else {
                String[] split = ips.split(",");
                Set<String> stringSet = new LinkedHashSet<>();
                for (String ip : split) {
                    List<String> disks = hardwareService.getDisks(ip);
                    stringSet.addAll(disks);
                }
                responseVo.setData(stringSet);
                outputResponse(JsonUtil.toJsonString(responseVo), response);
            }
        } catch (Exception e) {
            Log.high.error(e);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())), response);
        }
    }*/

    @RequestMapping(value = "/network/exception")
    public void exceptionNetowrkInfo(HttpServletResponse response,
                                     @RequestParam(name = "ip") String ip,
                                     @RequestParam(name = "pn", defaultValue = "1") Integer pn,
                                     @RequestParam(name = "ps", defaultValue = "20") Integer ps) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        responseVo.setData(hardwareService.exceptionNetworkInfo(pn, ps, ip));
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }
}