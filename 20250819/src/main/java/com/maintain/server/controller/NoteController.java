package com.maintain.server.controller;

import com.maintain.server.criteria.NoteCriteria;
import com.maintain.server.service.NoteService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.NoteVo;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.yaml.snakeyaml.Yaml;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

@Controller
@RequestMapping("/note/")
public class NoteController extends BaseController{

    @Autowired
    private NoteService noteService;

    @RequestMapping("list.json")
    public void queryNotes(HttpServletResponse response, @RequestParam(value = "author",required = false) String author, @RequestParam(value = "title",required = false) String title, @RequestParam(value = "remind",required = false)Boolean remind,@RequestParam(value = "content" ,required = false) String content){
        try {
            final NoteCriteria noteCriteria = new NoteCriteria();
            noteCriteria.setAuthor(author);
            noteCriteria.setContent(content);
            noteCriteria.setTitle(title);
            noteCriteria.setRemind(remind);
            final List<NoteVo> noteVos = noteService.queryNotes(noteCriteria);
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(noteVos);
            outputResponse(JsonUtil.toJsonString(responseVo),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }

    }

    @RequestMapping(value = "addNote",method = {RequestMethod.POST,RequestMethod.PUT})
    public void addNote(HttpServletResponse response,@RequestParam("author") String author, @RequestParam("title") String title, @RequestParam("remind")Boolean remind,@RequestParam("content") String content){
        try {
            final NoteCriteria noteCriteria = new NoteCriteria();
            noteCriteria.setAuthor(author);
            noteCriteria.setContent(content);
            noteCriteria.setTitle(title);
            noteCriteria.setRemind(remind);
            noteService.addNote(noteCriteria);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "updateNote",method = {RequestMethod.POST,RequestMethod.PUT})
    public void updateNote(HttpServletResponse response,@RequestParam("id")Integer id,@RequestParam(value = "author",required = false) String author, @RequestParam(value = "title",required = false) String title, @RequestParam(value = "remind",required = false)Boolean remind,@RequestParam(value = "content" ,required = false) String content){
        try {
            final NoteCriteria noteCriteria = new NoteCriteria();
            noteCriteria.setAuthor(author);
            noteCriteria.setContent(content);
            noteCriteria.setRemind(remind);
            noteCriteria.setTitle(title);
            noteCriteria.setId(id);
            noteService.updateNote(noteCriteria);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "deleteNote",method = {RequestMethod.DELETE,RequestMethod.POST})
    public void deleteNote(HttpServletResponse response,@RequestParam("id")Integer id){
        try {
            final NoteCriteria noteCriteria = new NoteCriteria();
            noteCriteria.setId(id);
            noteService.deleteNote(noteCriteria);
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS)),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "count")
    public void count(HttpServletResponse response){
        try {
            final NoteCriteria noteCriteria = new NoteCriteria();
            noteCriteria.setRemind(Boolean.TRUE);
            final Integer count = noteService.count(noteCriteria);
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(count);
            outputResponse(JsonUtil.toJsonString(responseVo),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "version")
    public void version(HttpServletResponse response){
        try {
            final Yaml yaml = new Yaml();
            final LinkedHashMap hashMap = yaml.loadAs(new FileInputStream("./config/version.yml"), LinkedHashMap.class);
            final Set set = hashMap.keySet();
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(set);
            outputResponse(JsonUtil.toJsonString(responseVo),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

    @RequestMapping(value = "version/detail")
    public void versionDetail(HttpServletResponse response,String version){
        try {
            final Yaml yaml = new Yaml();
            final HashMap hashMap = yaml.loadAs(new FileInputStream("./config/version.yml"), HashMap.class);
            final ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
            responseVo.setData(hashMap.get(version));
            outputResponse(JsonUtil.toJsonString(responseVo),response);
        } catch (Exception e) {
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }

}
