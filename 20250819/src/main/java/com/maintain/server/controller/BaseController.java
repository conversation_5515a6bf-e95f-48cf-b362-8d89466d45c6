package com.maintain.server.controller;

import com.common.log.Log;
import com.maintain.server.type.ResponseType;
import com.maintain.server.vo.ResponseVo;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.Charset;

@ControllerAdvice
public class BaseController implements AsyncUncaughtExceptionHandler {

    /**
     * 结果输出
     *
     * @param str      字符串
     * @param response HttpServletResponse
     */
    protected void outputResponse(String str, HttpServletResponse response) {
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-type", "text/html;charset=UTF-8");
        BufferedOutputStream bf = null;
        try {
            bf = new BufferedOutputStream(response.getOutputStream());
            bf.write(str.getBytes(Charset.forName("UTF-8")));
            bf.flush();
        } catch (IOException e) {
            Log.low.error("system error", e);
        } finally {
            try {
                if (bf != null) {
                    bf.close();
                }
            } catch (IOException e) {
                Log.high.error(e.getMessage(),e);
            }
        }
    }

    protected BufferedOutputStream getOutputStream(HttpServletResponse response) {
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-type", "text/html;charset=UTF-8");
        try {
            return new BufferedOutputStream(response.getOutputStream());
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
        return null;
    }

    protected ResponseVo getResponse(Integer code, String msg) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(code);
        responseVo.setMsg(msg);
        return responseVo;
    }

    /**
     * 在下载文件时设置响应头
     *
     * @param resp 请求响应
     * @param name 文件名称
     */
    protected void setDownloadFileHeader(HttpServletResponse resp, String name) {
        resp.setHeader("content-type", "application/octet-stream");
        resp.setContentType("application/octet-stream");
        resp.setHeader("Content-Disposition", "attachment;filename=" + name);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    private ResponseVo excetionHandler(HttpServletResponse response, Exception e) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SYSTEM_ERROR);
        Log.high.error(e.getMessage(), e);
        responseVo.setData(e);
        responseVo.setMsg(ResponseType.SYSTEM_ERROR.getMsg() + "," + e.getMessage());
        return responseVo;
        //  outputResponse(JsonUtil.toJsonString(responseVo), response);
    }

    @Override
    public void handleUncaughtException(Throwable throwable, Method method, Object... objects) {
        Log.low.error("Unexpected error occurred invoking async method: " + method, throwable);
    }


    /*@ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    private ResponseVo argumentTypeMismatchException(HttpServletResponse response, Exception e) {
        ResponseVo responseVo = new ResponseVo(ResponseType.REJECT);
        Log.high.error(e.getMessage(), e);
        Log.high.error(e.getMessage(), e);
        responseVo.setData(e);
        responseVo.setMsg(ResponseType.REJECT.getMsg() + "," + e.getMessage());
        return responseVo;
    }*/


   /* @Override
    public ModelAndView resolveException(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, @Nullable Object o, Exception e) {
        return null;
    }*/
}
