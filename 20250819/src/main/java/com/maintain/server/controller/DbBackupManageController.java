package com.maintain.server.controller;

import com.common.log.Log;
import com.maintain.server.criteria.DbBackupManageCriteria;
import com.maintain.server.schedule.DbBackupSchedule;
import com.maintain.server.schedule.MysqlBackupMonitorSchedule;
import com.maintain.server.service.DbBackupManageService;
import com.maintain.server.vo.DbBackupManageVo;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2020-12-22
 */
@RestController
@RequestMapping("/dbbackup/")
public class DbBackupManageController {

    private DbBackupManageService dbBackupManageService;

    private volatile Lock rebackupLock = new ReentrantLock(true);

    @Autowired
    private MysqlBackupMonitorSchedule mysqlSchedule;

    @Autowired
    public void setDbBackupManageService(DbBackupManageService dbBackupManageService) {
        this.dbBackupManageService = dbBackupManageService;
    }

    @RequestMapping("list.json")
    public ResponseVo list() {
        List<DbBackupManageVo> voList = dbBackupManageService.list(new DbBackupManageCriteria());
        return ResponseVo.getSuccess(voList);
    }

    @RequestMapping("update")
    public ResponseVo update(DbBackupManageVo manageVo) {
        if (manageVo.getId() == null) {
            ResponseVo responseVo = ResponseVo.getFailed();
            responseVo.setMsg("请提供待更新的记录ID");
            return responseVo;
        }
        boolean flag = dbBackupManageService.editRecord(manageVo);
        if (flag) {
            return ResponseVo.getSuccess();
        }
        return ResponseVo.getFailed();
    }

    @RequestMapping("delete")
    public ResponseVo delete(@RequestParam("id") Integer id) {
        dbBackupManageService.deleteRecord(id);
        return ResponseVo.getSuccess();
    }

    @RequestMapping("add")
    public ResponseVo add(DbBackupManageVo vo) {
        dbBackupManageService.addRecord(vo);
        return ResponseVo.getSuccess(vo);
    }

    @RequestMapping("backup")
    public ResponseVo backup(Integer type) {
        dbBackupManageService.dbBackup(type);
        return ResponseVo.getSuccess();
    }

    @RequestMapping("rebackup")
    public ResponseVo rebackup(Integer type) {
        if (!rebackupLock.tryLock()) {
            ResponseVo failedResponse = ResponseVo.getFailed();
            failedResponse.setMsg("正在备份，请稍等");
            return failedResponse;
        }
        try {
            if (type == null) {
                type = 0;
            }
            dbBackupManageService.reBackup(type);
            return ResponseVo.getSuccess();
        } catch (Exception e) {
            Log.high.error("重新备份失败", e);
            return ResponseVo.getFailed();
        } finally {
            rebackupLock.unlock();
        }
    }

    @RequestMapping("check")
    public ResponseVo check(Integer type) {
        if (type == null || type == 0) {
            mysqlSchedule.monitorMysqlDailyBackup();
        } else {
            mysqlSchedule.monitorMysqlWeeklyBackup();
        }
        return ResponseVo.getSuccess();
    }

    @RequestMapping("testConnect")
    public ResponseVo testConnect(DbBackupManageVo vo) {
        if (dbBackupManageService.testConnect(vo)) {
            return ResponseVo.getSuccess();
        }
        ResponseVo responseVo = ResponseVo.getFailed();
        responseVo.setMsg("连接失败，原因可能有两项：1.用户名或密码错误。2.数据库没有配置服务器的访问权限，可以通过在mysql服务器上执行如下命令进行赋权：2.1 mysql -u数据库用户名 -p数据库密码 -e\"grant all privileges on *.* to '数据库用户名'@'运维系统机器IP' identified by '数据库密码' with grant option;\"\n" +
                "2.2 mysql -u数据库用户名 -p数据库密码 -e\"flush privileges;\"");
        return responseVo;
    }
}