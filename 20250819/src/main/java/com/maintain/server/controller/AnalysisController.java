package com.maintain.server.controller;


import com.maintain.server.service.impl.AnalysisService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;


@Controller
@RequestMapping("/analysis")
public class AnalysisController extends  BaseController{

    @Autowired
    private AnalysisService service;

    @RequestMapping("/list.json")
    public void getAnalysisList(@RequestParam(name = "startTime") String startTime,
                                @RequestParam(name = "endTime") String endTime,
                                HttpServletResponse response){
        try {
            ResponseVo responseVo = new ResponseVo();
            responseVo.setCode(ResponseType.SUCCESS.getCode());
            responseVo.setMsg(ResponseType.SUCCESS.getMsg());
            responseVo.setData(service.getAnalysisMap(startTime,endTime));
            outputResponse(JsonUtil.toJsonString(responseVo), response);
        }catch (Exception e){
            outputResponse(JsonUtil.toJsonString(new ResponseVo(ResponseType.OPERATE_ERROR.getCode(), e.getMessage())),response);
        }
    }
}
