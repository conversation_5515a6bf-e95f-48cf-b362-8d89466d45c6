package com.maintain.server.controller;

import com.alibaba.fastjson.JSONObject;
import com.maintain.server.service.PluginWarnMessageService;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.vo.PluginWarnMessageVo;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-02-26
 */
@Controller
@RequestMapping("/warn/")
public class PluginWarnMessageController extends BaseController {

    @Autowired
    private PluginWarnMessageService pluginWarnMessageService;


    /**
     * 获取集群告警公共接口
     * @param response
     */
    @RequestMapping("getAllGroupWarnInfo")
    public void getAllGroupWarnInfo(HttpServletResponse response) {
        ResponseVo responseVo = new ResponseVo(ResponseType.SUCCESS);
        List<PluginWarnMessageVo> groupWarnVos = pluginWarnMessageService.getAllGroupWarnInfo();
        List<Map<String,Object>> list = new LinkedList<>();
        for (PluginWarnMessageVo w : groupWarnVos) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("create_time",w.getCreateTime());
            map.put("metric_type",w.getMetricType());
            map.put("warn_message",w.getWarnMessage());
            list.add(map);
        }
        String jsonResult = JSONObject.toJSONString(list);
        responseVo.setData(jsonResult);
        outputResponse(JsonUtil.toJsonString(responseVo), response);
    }
}