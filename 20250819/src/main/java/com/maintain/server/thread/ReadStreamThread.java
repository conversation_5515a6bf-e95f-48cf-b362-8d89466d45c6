package com.maintain.server.thread;


import com.maintain.server.utils.IOUtil;

import java.io.IOException;
import java.io.InputStream;

/**
 * 读取输入流的线程，读取完毕之后关闭输入流
 *
 * <AUTHOR>
 * @date 2018-01-15
 */
public class ReadStreamThread extends BaseThread {

    /**
     * 输入流
     */
    private InputStream stream;
    /**
     * 输出流的读取结果
     */
    private StringBuilder builder;
    /**
     * 编码
     */
    private String encode;

    /**
     * Getter method for property <tt>stream</tt>.
     *
     * @return property value of stream
     */

    public InputStream getStream() {
        return stream;
    }

    /**
     * Setter method for property <tt>stream</tt>.
     *
     * @param stream value to be assigned to property stream
     */
    public void setStream(InputStream stream) {
        this.stream = stream;
    }

    /**
     * Getter method for property <tt>builder</tt>.
     *
     * @return property value of builder
     */

    public StringBuilder getBuilder() {
        return builder;
    }

    /**
     * Setter method for property <tt>builder</tt>.
     *
     * @param builder value to be assigned to property builder
     */
    public void setBuilder(StringBuilder builder) {
        this.builder = builder;
    }

    /**
     * Getter method for property <tt>encode</tt>.
     *
     * @return property value of encode
     */

    public String getEncode() {
        return encode;
    }

    /**
     * Setter method for property <tt>encode</tt>.
     *
     * @param encode value to be assigned to property encode
     */
    public void setEncode(String encode) {
        this.encode = encode;
    }

    public ReadStreamThread(InputStream stream, StringBuilder builder, String encode) {
        this.stream = stream;
        this.builder = builder;
        this.encode = encode;
    }

    @Override
    public void run() {
        try {
            String out = IOUtil.inputStreamToString(stream, encode);
            stream.close();
            builder.append(out);
            setSuccess(true);
        } catch (IOException e) {
            setSuccess(false);
        }
    }
}
