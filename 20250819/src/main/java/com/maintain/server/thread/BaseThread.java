package com.maintain.server.thread;

/**
 * <AUTHOR>
 * @date 2018-01-19
 */
public abstract class BaseThread implements Runnable {
    /**
     * 是否执行成功
     */
    private volatile boolean success;

    /**
     * Getter method for property <tt>success</tt>.
     *
     * @return property value of success
     */

    public boolean getSuccess() {
        return success;
    }

    /**
     * Setter method for property <tt>success</tt>.
     *
     * @param success value to be assigned to property success
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }
}
