package com.maintain.server.security;

import com.maintain.server.mapper.PermissionMapper;
import com.maintain.server.vo.PermissionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-10-21
 */
@Service
public class MaintainInvocationSecurityMetadataSourceService implements
        FilterInvocationSecurityMetadataSource {
    private ReadWriteLock lock = new ReentrantReadWriteLock();

    private List<PermissionVo> permissionVos;

    @Autowired
    private PermissionMapper permissionMapper;

    AntPathMatcher antPathMatcher = new AntPathMatcher();

    /**
     * 返回当前请求的URL需要的权限(角色列表，即那些角色拥有该URL的权限)
     *
     * @param o
     * @return
     * @throws IllegalArgumentException
     */
    @Override
    public Collection<ConfigAttribute> getAttributes(Object o)
            throws IllegalArgumentException {

        String requestUrl = ((FilterInvocation) o).getRequestUrl();
        List<PermissionVo> permissions = getPermissionVos();

        List<PermissionVo> matchPermissions =
                permissions.stream().filter(p -> antPathMatcher.match(p.getUrl(), requestUrl))
                .collect(Collectors.toList());

        if (matchPermissions != null && !matchPermissions.isEmpty()) {
            List<String> roleList = matchPermissions.stream().map(PermissionVo::getRoleName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (roleList == null || roleList.isEmpty()) {
                //该URL被控制，但为分配给任何角色，故直接说明其无权限
                return SecurityConfig.createList("ROLE_NO_PERMISSION");
            } else {
                return SecurityConfig.createList(roleList.toArray(new String[roleList.size()]));
            }
        }
        return SecurityConfig.createList("ROLE_LOGIN");
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return FilterInvocation.class.isAssignableFrom(aClass);
    }

    public void updatePermissionVos() {
        try {
            lock.writeLock().lock();
            permissionVos = permissionMapper.findAllResources();
        } finally {
            lock.writeLock().unlock();
        }
    }


    private List<PermissionVo> getPermissionVos() {
        if (permissionVos == null) {
            updatePermissionVos();
        }
        final Lock lock = this.lock.readLock();
        try {
            lock.lock();
            return permissionVos;
        } finally {
            lock.unlock();
        }
    }
}