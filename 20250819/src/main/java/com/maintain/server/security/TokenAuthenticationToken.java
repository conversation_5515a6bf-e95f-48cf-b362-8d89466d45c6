package com.maintain.server.security;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2020-07-29
 */
public class TokenAuthenticationToken extends UsernamePasswordAuthenticationToken {
    private static final long serialVersionUID = -6231962326068951783L;


    public TokenAuthenticationToken(Object principal) {
        super(principal, "");
    }


    public TokenAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(principal, "", authorities);
    }
}