package com.maintain.server.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maintain.server.type.ResponseType;
import com.maintain.server.vo.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.io.PrintWriter;

/**
 * <AUTHOR>
 * @date 2018-10-21
 */
@Configuration
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${auth.url}")
    private String authAddress;

    @Autowired
    private SessionRegistry sessionRegistry;

    @Autowired
    private MaintainUserDetails maintainUserDetails;

    @Autowired
    private MaintainAccessDecisionManager maintainAccessDecisionManager;

    @Autowired
    private MaintainInvocationSecurityMetadataSourceService maintainInvocationSecurityMetadataSourceService;


    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(maintainUserDetails)
                .passwordEncoder(new BCryptPasswordEncoder());
    }

    @Bean
    public TokenAuthenticationFilter tokenAuthenticationFilter() throws Exception {
        TokenAuthenticationFilter filter = new TokenAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManager());
        filter.setSessionRegistry(sessionRegistry);
        filter.setAuthAddress(authAddress);
        return filter;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.sessionManagement().maximumSessions(-1)
                .sessionRegistry(sessionRegistry);
        http.addFilterBefore(tokenAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        http.authorizeRequests()
                .withObjectPostProcessor(new ObjectPostProcessor<FilterSecurityInterceptor>() {
                    @Override
                    public <O extends FilterSecurityInterceptor> O postProcess(O o) {
                        o.setAccessDecisionManager(maintainAccessDecisionManager);
                        o.setSecurityMetadataSource(maintainInvocationSecurityMetadataSourceService);
                        return o;
                    }
                })
                .and().requiresChannel().antMatchers("/").requiresSecure()
                .and()
                .formLogin().loginPage("/user/toLogin").loginProcessingUrl("/user/login")
                .usernameParameter("username").passwordParameter("password")
                .failureHandler(new FailureHandler())
                .successHandler(new SuccessHandler())
                .permitAll()
                .and().logout().permitAll()
                .and().cors()
                .and().csrf().disable()

                .exceptionHandling()
                .accessDeniedHandler((res, resp, e) -> {
                    resp.setContentType("application/json;charset=utf-8");
                    ResponseVo response = new ResponseVo();
                    response.setMsg(ResponseType.NO_PERMISSION.getMsg());
                    response.setCode(ResponseType.NO_PERMISSION.getCode());
                    ObjectMapper om = new ObjectMapper();
                    PrintWriter out = resp.getWriter();
                    out.write(om.writeValueAsString(response));
                    out.flush();
                    out.close();
                });

        //测试放过
        //http.authorizeRequests().anyRequest().permitAll().and().logout().permitAll().and().csrf().disable();
    }


    @Override
    public void configure(WebSecurity web) throws Exception {
        web.ignoring().antMatchers("/css/**", "/static/**", "/user/toLogin", "/druid/**", "/user/permissionByModule");
        web.ignoring().antMatchers("/druid/*", "/request/**");
//        web.ignoring().antMatchers("/**");
    }
}