package com.maintain.server.security;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.web.FilterInvocation;
import org.springframework.stereotype.Service;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2018-10-23
 */
@Service
public class MaintainAccessDecisionManager implements AccessDecisionManager {

    @Autowired
    private SessionRegistry sessionRegistry;

    @Override
    public void decide(Authentication auth, Object o,
                       Collection<ConfigAttribute> cas) throws AccessDeniedException, InsufficientAuthenticationException {
        final Collection<? extends GrantedAuthority> authorities;
        String sessionId = ((FilterInvocation) o).getRequest().getHeader("sessionid");
        SessionInformation info = null;
        if (StringUtils.isNotEmpty(sessionId)) {
            info = sessionRegistry.getSessionInformation(sessionId);
            if (info != null) {
                authorities = ((User) info.getPrincipal()).getAuthorities();
            } else{
                authorities = auth.getAuthorities();
            }
        } else {
            authorities = auth.getAuthorities();
        }
        for (ConfigAttribute ca : cas) {
            //当前请求需要的权限
            String needRole = ca.getAttribute();
            if ("ROLE_NO_PERMISSION".equals(needRole)) {
                throw new AccessDeniedException("权限不足!");
            }
            if ("ROLE_ANY".equals(needRole)) {
                return;
            }
            if ("ROLE_LOGIN".equals(needRole)) {
                if (info != null) {
                    return;
                }
                if (auth instanceof AnonymousAuthenticationToken) {
                    throw new BadCredentialsException("未登录");
                } else {
                    return;
                }
            }

            for (GrantedAuthority authority : authorities) {
                if (authority.getAuthority().equals(needRole)) {
                    return;
                }
            }
        }
        throw new AccessDeniedException("权限不足!");
    }

    @Override
    public boolean supports(ConfigAttribute configAttribute) {
        return true;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return true;
    }
}