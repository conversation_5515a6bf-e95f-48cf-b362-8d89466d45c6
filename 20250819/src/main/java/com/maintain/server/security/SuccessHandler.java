package com.maintain.server.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maintain.server.criteria.UserCriteria;
import com.maintain.server.mapper.OperateRecordMapper;
import com.maintain.server.mapper.UserMapper;
import com.maintain.server.type.ResponseType;
import com.maintain.server.vo.OperateRecordVo;
import com.maintain.server.vo.PermissionVo;
import com.maintain.server.vo.ResponseVo;
import com.maintain.server.vo.UserVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.maintain.server.Constants.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2020-07-29
 */
@Component
public class SuccessHandler implements AuthenticationSuccessHandler {


    private static OperateRecordMapper operateRecordMapper;

    @Autowired
    private void setOperateRecordMapper(OperateRecordMapper operateRecordMapper) {
        SuccessHandler.operateRecordMapper = operateRecordMapper;
    }

    private static UserMapper userMapper;

    private static MaintainInvocationSecurityMetadataSourceService maintainInvocationSecurityMetadataSourceService;

    @Autowired
    public void setUserMapper(UserMapper userMapper) {
        SuccessHandler.userMapper = userMapper;
    }

    @Autowired
    public void setMaintainInvocationSecurityMetadataSourceService(MaintainInvocationSecurityMetadataSourceService maintainInvocationSecurityMetadataSourceService) {
        SuccessHandler.maintainInvocationSecurityMetadataSourceService = maintainInvocationSecurityMetadataSourceService;
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Authentication authentication) throws IOException, ServletException {
        //更新登陆时间
        UserVo user = new UserVo();
        user.setName(authentication.getName());
        user.setLastLoginTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS)));
        userMapper.updateUser(user);
        //刷新权限信息
        maintainInvocationSecurityMetadataSourceService.updatePermissionVos();
        List<PermissionVo> menus = userMapper.getFirstPermissionByUsername(user.getName());
        Map<String, Object> map = new HashMap<>(4);
        List<Map<String, Object>> mapList = new ArrayList<>();
        map.put("menuOptions", mapList);
        UserCriteria criteria = new UserCriteria();
        criteria.setName(authentication.getName());
        UserVo userVo = userMapper.findByUsername(criteria);
        if (userVo != null) {
            Map<String, Object> userMap = new HashMap<>();
            userMap.put("id", userVo.getId());
            userMap.put("name", userVo.getName());
            map.put("user", userMap);
        }
        for (PermissionVo menu : menus) {
            Map<String, Object> tempMap = new HashMap<>(4);
            tempMap.put("moduleName", menu.getName());
            tempMap.put("path", menu.getPathUrl());
            mapList.add(tempMap);
        }
        String setCookie = httpServletResponse.getHeader("Set-Cookie");
        if (StringUtils.isNotEmpty(setCookie)) {
            for (String info : setCookie.split(";")) {
                if (info.startsWith("JSESSIONID")) {
                    map.put("token", info.split("=")[1]);
                    break;
                }
            }
        } else {
            String cookie = httpServletResponse.getHeader("Cookie");
            if (StringUtils.isNotEmpty(cookie)) {
                for (String info : cookie.split(";")) {
                    if (info.startsWith("JSESSIONID")) {
                        map.put("token", info.split("=")[1]);
                        break;
                    }
                }
            }
        }
        addLoginLog(userVo, httpServletRequest);
        httpServletResponse.setContentType("application/json;charset=utf-8");
        ResponseVo response = new ResponseVo();
        response.setMsg("登陆成功");
        response.setCode(ResponseType.SUCCESS.getCode());
        response.setData(map);
        ObjectMapper om = new ObjectMapper();
        PrintWriter out = httpServletResponse.getWriter();
        out.write(om.writeValueAsString(response));
        out.flush();
        out.close();
    }

    private void addLoginLog(UserVo userVo, HttpServletRequest httpServletRequest) {
        String messageFormat = "%s从%s上登录系统成功";
        OperateRecordVo recordVo = new OperateRecordVo();
        recordVo.setUser(userVo.getName());
        recordVo.setOperateTime(new Date());
        recordVo.setOperate(String.format(messageFormat, userVo.getName(), getIp(httpServletRequest)));
        operateRecordMapper.addOperateRecord(recordVo);
    }

    private String getIp(HttpServletRequest request) {
        String ip = null;

        // X-Forwarded-For：Squid 服务代理
        String ipAddresses = request.getHeader("X-Forwarded-For");
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // Proxy-Client-IP：apache 服务代理
            ipAddresses = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // WL-Proxy-Client-IP：weblogic 服务代理
            ipAddresses = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // HTTP_CLIENT_IP：有些代理服务器
            ipAddresses = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // X-Real-IP：nginx服务代理
            ipAddresses = request.getHeader("X-Real-IP");
        }

        // 有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
        if (ipAddresses != null && ipAddresses.length() != 0) {
            ip = ipAddresses.split(",")[0];
        }

        // 还是不能获取到，最后再通过request.getRemoteAddr();获取
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}