package com.maintain.server.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maintain.server.mapper.OperateRecordMapper;
import com.maintain.server.type.ResponseType;
import com.maintain.server.vo.OperateRecordVo;
import com.maintain.server.vo.ResponseVo;
import com.maintain.server.vo.UserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-07-29
 */
@Component
public class FailureHandler implements AuthenticationFailureHandler {
    private static OperateRecordMapper operateRecordMapper;

    @Autowired
    private void setOperateRecordMapper(OperateRecordMapper operateRecordMapper) {
        FailureHandler.operateRecordMapper = operateRecordMapper;
    }

    @Override
    public void onAuthenticationFailure(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthenticationException e) throws IOException, ServletException {
        httpServletResponse.setContentType("application/json;charset=utf-8");
        String userName = httpServletRequest.getParameter("username");
        addLoginLog(userName, httpServletRequest);
        ResponseVo response = new ResponseVo();
        if (e instanceof BadCredentialsException ||
                e instanceof UsernameNotFoundException) {
            response.setMsg("账户名或者密码输入错误!");
            response.setCode(ResponseType.OPERATE_ERROR.getCode());
        }
        httpServletResponse.setStatus(200);
        ObjectMapper om = new ObjectMapper();
        PrintWriter out = httpServletResponse.getWriter();
        out.write(om.writeValueAsString(response));
        out.flush();
        out.close();
    }

    private void addLoginLog(String userName, HttpServletRequest httpServletRequest) {
        String messageFormat = "%s从%s上登录系统失败";
        OperateRecordVo recordVo = new OperateRecordVo();
        recordVo.setUser(userName);
        recordVo.setOperateTime(new Date());
        recordVo.setOperate(String.format(messageFormat, userName, getIp(httpServletRequest)));
        operateRecordMapper.addOperateRecord(recordVo);
    }

    private String getIp(HttpServletRequest request) {
        String ip = null;

        // X-Forwarded-For：Squid 服务代理
        String ipAddresses = request.getHeader("X-Forwarded-For");
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // Proxy-Client-IP：apache 服务代理
            ipAddresses = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // WL-Proxy-Client-IP：weblogic 服务代理
            ipAddresses = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // HTTP_CLIENT_IP：有些代理服务器
            ipAddresses = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            // X-Real-IP：nginx服务代理
            ipAddresses = request.getHeader("X-Real-IP");
        }

        // 有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
        if (ipAddresses != null && ipAddresses.length() != 0) {
            ip = ipAddresses.split(",")[0];
        }

        // 还是不能获取到，最后再通过request.getRemoteAddr();获取
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}