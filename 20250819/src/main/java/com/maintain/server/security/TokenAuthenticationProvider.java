package com.maintain.server.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * <AUTHOR>
 * @date 2020-07-29
 */
public class TokenAuthenticationProvider implements AuthenticationProvider {
    @Autowired
    private MaintainUserDetails maintainUserDetails;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {

        if (authentication.isAuthenticated()) {
            return authentication;
        }
        //获取过滤器封装的token信息
        TokenAuthenticationToken authenticationToken = (TokenAuthenticationToken) authentication;
        UserDetails userDetails = maintainUserDetails.loadUserByUsername((String)authenticationToken.getPrincipal());
//        不通过
        if (userDetails == null) {
            throw new BadCredentialsException("授权token无效，请重新登陆");
        }

        TokenAuthenticationToken authenticationResult = new TokenAuthenticationToken(userDetails.getUsername(), userDetails.getAuthorities());

        return authenticationResult;
    }


    @Override
    public boolean supports(Class<?> authentication) {
        return TokenAuthenticationToken.class.isAssignableFrom(authentication);
    }
}