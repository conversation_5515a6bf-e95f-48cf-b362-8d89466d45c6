package com.maintain.server.security;

import com.maintain.server.utils.CryptUtil;
import com.maintain.server.utils.HttpUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-07-29
 */
public class TokenAuthenticationFilter extends AbstractAuthenticationProcessingFilter {
    private SessionRegistry sessionRegistry;
    private String authAddress;

    private String tokenParameter = "token";
    private String testUserToke = "f3f0c28e3121a34fcaf8a1eaaf7dfeefc2519869bf30a3872a925f4d577b6088";

    private Map<String, String> tokenMap = new HashMap<>();


    public TokenAuthenticationFilter() {
        super("/quickLogin");
        super.setAuthenticationFailureHandler(new FailureHandler());
        super.setAuthenticationSuccessHandler(new SuccessHandler());
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException {
       /* if (!request.getMethod().equals(HttpMethod.POST.name())) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }*/
        String token = obtainToken(request);
        //调用大系统认证接口

        if (token == null || token.length() == 0) {
            throw new BadCredentialsException("token异常");
        }
        String decrypt = CryptUtil.decrypt(token);
        if (decrypt == null){
            throw new BadCredentialsException("token异常");
        }
        String[] str = decrypt.split(CryptUtil.SEPERATOR);
        if (str.length != 2){
            throw new BadCredentialsException("token异常");
        }
        String username = str[0];
        String password = str[1];
        if(username == null) {
            username = "";
        }
        if(password == null) {
            password = "";
        }
        username = username.trim();
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);
        this.setDetails(request, authRequest);

        Authentication auth = this.getAuthenticationManager().authenticate(authRequest);
        sessionRegistry.registerNewSession(request.getSession().getId(), auth.getPrincipal());

        return auth;
    }


    protected void setDetails(HttpServletRequest request, UsernamePasswordAuthenticationToken authRequest) {
        authRequest.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }

    protected String obtainToken(HttpServletRequest request) {
        String token = request.getParameter(this.tokenParameter);
        if (tokenMap.containsKey(token)) {
            return tokenMap.get(token);
        }
        //调用外部接口-对token进行鉴权
        if (!StringUtils.isBlank(authAddress)) {
            String decrypt = CryptUtil.decrypt(token);
            if (decrypt == null) {
                Map<String, String> params = new HashMap<>();
                params.put("Authorization", token);
                String responseCode = HttpUtils.getCodeHttpRequest(authAddress, params);
                if (responseCode != null && responseCode.equals("200")) {
                    //鉴权成功
                    tokenMap.put(token, testUserToke);
                    return testUserToke;
                } else {
                    return null;
                }
            }
        }
        return token == null ? "" : token.trim();

    }

    public void setSessionRegistry(SessionRegistry sessionRegistry) {
        this.sessionRegistry = sessionRegistry;
    }

    public void setAuthAddress(String authAddress) {
        this.authAddress = authAddress;
    }
}