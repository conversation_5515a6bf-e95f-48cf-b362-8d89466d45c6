package com.maintain.server.security;

import com.maintain.server.criteria.UserCriteria;
import com.maintain.server.mapper.PermissionMapper;
import com.maintain.server.mapper.UserMapper;
import com.maintain.server.vo.PermissionVo;
import com.maintain.server.vo.RoleVo;
import com.maintain.server.vo.UserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-10-21
 */
@Service
public class MaintainUserDetails implements UserDetailsService{

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserCriteria criteria = new UserCriteria();
        criteria.setName(username);
        UserVo user = userMapper.findByUsername(criteria);
        if (user == null) {
            throw new UsernameNotFoundException("用户名不存在");
        }
        List<PermissionVo> permissionVos = new ArrayList<>();
        for (RoleVo role : user.getRoles()) {
            permissionVos.addAll(permissionMapper.findByRoleId(role.getId()));
        }

        List<GrantedAuthority> grantedAuthorities = permissionVos.stream()
                .filter(permission -> permission != null && permission.getRoleName() != null)
                .map(PermissionVo::getRoleName).map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
        return new User(user.getName(), user.getPswd(), grantedAuthorities);
    }

}