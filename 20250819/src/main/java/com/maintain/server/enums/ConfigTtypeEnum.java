package com.maintain.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ConfigTtypeEnum {
    /**
     * 配置类型
     */
    BIGDATA_HISTORY_ALERT_CHECKPOINT("BIGDATA_HISTORY_ALERT_CHECKPOINT", "大数据api之历史告警同步检查点"),
    ;

    private final String code;
    private final String name;

    public static ConfigTtypeEnum getByCode(String code) {
        return Stream.of(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
