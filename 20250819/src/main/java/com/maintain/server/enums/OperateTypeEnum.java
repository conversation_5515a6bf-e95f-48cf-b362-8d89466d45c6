package com.maintain.server.enums;

import java.util.ArrayList;
import java.util.List;

public enum OperateTypeEnum {
    CLOSE(0, "关闭"),
    OPEN(1, "开启"),
    RESTART(2, "重启"),
    UPDATE(3, "更新"),
    AUTO_DEPLOY(4, "自动部署"),
    UPLOAD_LOGSTASH(5, "上传logstash"),
    UPDATE_LOGSTASH_CONFIG(6, "更新logstash配置文件"),
    SYNCHRO_TIME(7, "一键时钟同步"),
    UPGRADE_FRONT(8, "升级前端"),
    UPLOAD_COMMON_CONFIG(9, "分发公共配置文件"),
    CLOSE_PLATFORM(10, "一键关闭平台"),
    OPEN_PLATFORM(11, "一键开启平台");

    private int type;
    private String operate;

    OperateTypeEnum(int type, String operate) {
        this.type = type;
        this.operate = operate;
    }

    public int getType() {
        return type;
    }

    public String getOperate() {
        return operate;
    }

    public static OperateTypeEnum byType(int type) {
        for (OperateTypeEnum item : values()) {
            if (item.type == type) {
                return item;
            }
        }
        throw new RuntimeException("未知操作类型:" + type);
    }

    public static List<String> getAllOperate() {
        List<String> operateList = new ArrayList<>(values().length);
        for (OperateTypeEnum item : values()) {
            operateList.add(item.getOperate());
        }
        return operateList;
    }

}
