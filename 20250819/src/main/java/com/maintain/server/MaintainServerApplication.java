package com.maintain.server;

import com.common.log.Log;
import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import com.maintain.server.service.PlatformRoleService;
import com.maintain.server.utils.BaseConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.data.neo4j.Neo4jRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, DataSourceAutoConfiguration.class, PageHelperAutoConfiguration.class, JmxAutoConfiguration.class, MultipartAutoConfiguration.class, RabbitAutoConfiguration.class, Neo4jRepositoriesAutoConfiguration.class})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableScheduling
@ServletComponentScan("com.maintain.server.config")
public class MaintainServerApplication implements Constants, ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private PlatformRoleService platformRoleService;

    private ApplicationContext applicationContext;

    static {
        //初始化系统
        BaseConfigUtil.init();
    }


    public static void main(String[] args) {
        SpringApplication.run(MaintainServerApplication.class, args);
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        this.setApplicationContext(contextRefreshedEvent.getApplicationContext());
        try {
            new Thread(() -> {
                Thread.currentThread().setName(MAINTAIN_SERVER + "-Thread");
                MaintainServerApplication application = contextRefreshedEvent.getApplicationContext().getBean("maintainServerApplication", MaintainServerApplication.class);
            }).start();
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }


    public ApplicationContext getApplicationContext() {
        return this.applicationContext;
    }

    private void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

}
