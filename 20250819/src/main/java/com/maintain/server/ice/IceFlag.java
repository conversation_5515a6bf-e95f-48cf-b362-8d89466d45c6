package com.maintain.server.ice;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/10/11.
 */
public interface IceFlag {

    /**
     * 关闭Agent
     */
    int CLOSE_AGENT = 1;

    /**
     * 硬件监控
     */
    int HARDWARE_MONITOR = 3;

    /**
     * 程序监控
     */
    int SOFTWARE_MONITOR = 4;

    /**
     * 心跳检测
     */
    int PING = 5;
    int PONG = 5;

    /**
     * 程序备份
     */
    int BACKUP_PROGRAM = 6;

    /**
     * 业务监控
     */
    int BUSINESS_MONITOR = 7;

    /**
     * 修改统一配置文件
     */
    int MODIFY_CONFIG = 8;

    /**
     * 0关闭 1启动 2重启程序
     */
    int SOFTWARE_OPERATE = 9;

    int START_LOGSTASH = 10;

    int CONFIRM_LOGSTASH = 11;

    int OPERATE_FIREWALL = 12;

    int PID_FILE = 13;

    int PORT_LIST = 14;

    int EXEC_COMMAND_IN_LINUX = 15;

    int GET_SOFTWARE_INFO = 20040;

    int SEND_SOFTWARE_INFO = 20041;

    int UPDATE_SOFTWARE_INFO = 20043;

    int CLEAR_RESTART_COUNT = 20045;

    int RESTART_LOG = 20047;

    /**
     * 接口————获取预处理目录磁盘信息
     */
    int GET_PRE_DISK_INFO = 20044;

    /**
     * 接口————获取某主机中某程序的日志信息
     */
    int GET_HOST_PROC_LOGS = 20023;
    /**
     * 接口————获取某主机中某程序的配置文件信息
     */
    int GET_HOST_PROC_CONFIGS = 20026;

    /**
     * 接口————获取远程主机上的文件
     */
    int READ_HOST_FILE = 20027;

    /**
     * 接口————回写文本到远程主机上下相应文件
     */
    int WRITE_HOST_FILE = 20028;

    /**
     * 查看端口
     */
    int GET_PROCESS_PORT_INFO = 20030;

    /**
     * 接口————更新logstash配置文件
     */
    int UPDATE_LOGSTASH_CONFIG = 20046;

    /** 设置堆大小 */
    int SET_HEAP_SIZE = 20050;

    /** 获取程序目录 */
    int GET_SOFTWARE_LIST = 20052;

    /** 一键时间同步*/
    int SET_SYSCHRONISED_TIME = 20099;
    /** Ping所有的Agent */
    int PONG_ALL_AGENT = 20100;
    /** 检测数据库备份情况 */
    int CHECK_DB_BACKUP = 20101;

    int GET_ESLOG_PATH = 20054;

    int CHECK_NTP = 20057;

    int TIME = 20066;

    int DELETE_OLD_SOFTWARE = 20067;

    int REPLACE_CONFIG = 30000;

    int GET_FILE_SIZE = 30001;

    int PING_AGENT = 30002;

}
