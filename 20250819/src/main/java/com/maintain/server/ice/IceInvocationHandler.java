package com.maintain.server.ice;

import Ice.Communicator;
import com.common.log.Log;
import com.ice.common.CommonServicePrx;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2018-04-02
 */
public class IceInvocationHandler implements InvocationHandler {

    /**
     *
     */
    private CommonServicePrx target;

    public IceInvocationHandler(CommonServicePrx target) {
        this.target = target;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 日志打印
        logBefore(args);
        Communicator communicator = target.ice_getCommunicator();
        if (communicator == null || communicator.isShutdown()) {
            throw new RuntimeException("communicator already closed....");
        }
        Object obj = method.invoke(target, args);
        logAfter(obj);
        return obj;
    }

    /**
     * @param obj obj
     */
    private void logAfter(Object obj) {
        String response = obj == null ? "" : obj.toString();
        Log.low.debug("response:" + response);
    }

    /**
     * @param args args
     */
    private void logBefore(Object[] args) {
        if (args != null) {
            for (Object obj : args) {
                if (obj instanceof Integer) {
                    Integer flag = (Integer) obj;
                    switch (flag) {
                        case IceFlag.GET_PROCESS_PORT_INFO:
                        case IceFlag.SOFTWARE_OPERATE:
                        case IceFlag.PID_FILE:
                        case IceFlag.PORT_LIST:
                        case IceFlag.START_LOGSTASH:
                        case IceFlag.CONFIRM_LOGSTASH:
                        case IceFlag.PONG:
                        case IceFlag.TIME:
                            return;
                    }
                }
            }
           // Log.low.info("ICE request param:" + JsonUtil.toJsonString(args));
        }
    }
}