package com.maintain.server.ice;

import Ice.Current;
import com.alibaba.fastjson.TypeReference;
import com.common.log.Log;
import com.google.common.util.concurrent.RateLimiter;
import com.ice.common._CommonServiceDisp;
import com.ice.common.byteArrayHolder;
import com.maintain.server.Constants;
import com.maintain.server.criteria.AgentCriteria;
import com.maintain.server.criteria.HardwareCriteria;
import com.maintain.server.exception.ServiceException;
import com.maintain.server.service.AgentService;
import com.maintain.server.service.HardwareService;
import com.maintain.server.service.SoftwareService;
import com.maintain.server.type.AlarmStatusType;
import com.maintain.server.type.ProgramStatusType;
import com.maintain.server.type.ResponseType;
import com.maintain.server.utils.DateUtil;
import com.maintain.server.utils.JsonUtil;
import com.maintain.server.utils.ListUtil;
import com.maintain.server.vo.*;
import com.maintain.server.vo.HardwareVo.Info;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-09-27
 */
@RestController(Constants.MAINTAIN_SERVER)
public class IceMaintainServer implements ApplicationListener<ContextRefreshedEvent>, Constants, IceFlag {

    @Autowired
    private AgentService agentService;

    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private SoftwareService softwareService;

    private static final RateLimiter limiter = RateLimiter.create(5);

    private static final RateLimiter limiter2 = RateLimiter.create(10);


    private String diskRange = "80,90";

    @RequestMapping("/request")
    public String request(int flag, String param) {
        //Log.low.info("收到" + flag +"号接口请求, 请求入参: " + params);
        switch (flag) {
            case HARDWARE_MONITOR:
                //Log.low.info("receive hard info from " + current.ctx.get("ip"));
                try {
                    if (limiter.tryAcquire()) {
                        dealHardwareData(param);
                    }
                } catch (Exception e) {
                    Log.high.error("receive hardware info exception", e);
                }
                break;
            case SOFTWARE_MONITOR:
                try {
                    if (limiter2.tryAcquire()) {
                        dealSoftwareData(param);
                    }
                } catch (Exception e) {
                    Log.high.error("receive software info exception", e);
                }
                break;
            case RESTART_LOG:
                restartLog(param);
                break;
            case BUSINESS_MONITOR:
                dealBusinessMonitor(param);
                break;
            case PONG:
                return JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS.getCode(), "pong"));
            case GET_SOFTWARE_INFO:
                HashMap<String, Object> map = new HashMap<>();
                Log.low.debug("ip:" + param + "请求程序信息");
                map.put("softwareInfos", softwareService.getSoftwareInfoList(param));
                String s = JsonUtil.toJsonString(map);
                Log.low.debug("结果：" + s);
                return s;
            case SEND_SOFTWARE_INFO:
                ArrayList<SoftwareInfoVo> list = JsonUtil.parseObject(param, new TypeReference<ArrayList<SoftwareInfoVo>>() {
                });
                for (SoftwareInfoVo softwareInfoVo : list) {
                    softwareService.updateSoftwareMonitorInfo(softwareInfoVo);
                }
                return null;
            case CHECK_NTP:
                if(param != null){
                    final String[] split = param.split(",");
                    agentService.updateNtp(split[0],Integer.valueOf(split[1]));
                }
                return "seccuss";
            default:
                return JsonUtil.toJsonString(new ResponseVo(ResponseType.REJECT.getCode(), ResponseType.REJECT.getMsg()));
        }
        String response = JsonUtil.toJsonString(new ResponseVo(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg()));
        updateAgentHeartbeat(param);
        return response;
    }

    private void restartLog(String params) {
        final SoftwareRestartVo softwareRestartVo = JsonUtil.parseObject(params, SoftwareRestartVo.class);
        if (softwareRestartVo.getSoftwareId() != null && softwareRestartVo.getStatus() != null) {
            softwareService.addRestartHistory(Arrays.asList(softwareRestartVo));
        }
    }

    private void dealBusinessMonitor(String params) {
        BusinessMonitorVo monitorVo = JsonUtil.parseObject(params, BusinessMonitorVo.class);
        if (monitorVo != null && StringUtils.isNotEmpty(monitorVo.getSoftwareName()) && ListUtil.isNotEmpty(monitorVo.getData())) {
            try {
                softwareService.addBusinessMonitor(monitorVo);
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        } else {
            Log.high.error("data is null: " + params);
        }
    }

    private void softwareSetMemoryInfo(SoftwareVo softwareVo) {
        String usedMemory = softwareVo.getUsedMemory();
        String totalMemory = softwareVo.getTotalMemory();
        if (StringUtils.isNotEmpty(usedMemory) && StringUtils.isNotEmpty(totalMemory)) {
            if ((usedMemory.contains("m") || usedMemory.contains("M")) && totalMemory.contains("m") || totalMemory.contains("M")) {
                usedMemory = usedMemory.replace("m", "").replace("M", "");
                totalMemory = totalMemory.replace("m", "").replace("M", "");
                BigDecimal usedMemoryBigDecimal = new BigDecimal(usedMemory);
                BigDecimal totalMemoryBigDecimal = new BigDecimal(totalMemory);
                String memoryPercent = usedMemoryBigDecimal.multiply(new BigDecimal(100)).divide(totalMemoryBigDecimal, 2, BigDecimal.ROUND_HALF_UP).toString();
                softwareVo.setMemoryPercent(memoryPercent + "%");
            } else if ((usedMemory.contains("g") || usedMemory.contains("G")) && totalMemory.contains("g") || totalMemory.contains("G")) {
                usedMemory = usedMemory.replace("g", "").replace("G", "");
                totalMemory = totalMemory.replace("g", "").replace("G", "");
                BigDecimal usedMemoryBigDecimal = new BigDecimal(usedMemory);
                BigDecimal totalMemoryBigDecimal = new BigDecimal(totalMemory);
                String memoryPercent = usedMemoryBigDecimal.multiply(new BigDecimal(100)).divide(totalMemoryBigDecimal, 2, BigDecimal.ROUND_HALF_UP).toString();
                softwareVo.setMemoryPercent(memoryPercent + "%");
            } else {
                // 1KB=1024 1M=1048576 1G=1073741824
                try {
                    BigDecimal usedMemoryBigDecimal = new BigDecimal(usedMemory);
                    String used = "";
                    used = usedMemoryBigDecimal.divide(new BigDecimal(1073741824L), 2, BigDecimal.ROUND_HALF_UP).toString() + "G";
                    softwareVo.setUsedMemory(used);
                    BigDecimal totalMemoryBigDecimal = new BigDecimal(totalMemory);
                    String total = "";
                    total = totalMemoryBigDecimal.divide(new BigDecimal(1073741824L), 2, BigDecimal.ROUND_HALF_UP).toString() + "G";
                    softwareVo.setTotalMemory(total);
                    BigDecimal divide = usedMemoryBigDecimal.multiply(new BigDecimal(100)).divide(totalMemoryBigDecimal, 2, BigDecimal.ROUND_HALF_UP);
                    String memoryPercent = divide == null ? "0.00" : divide.toString();
                    softwareVo.setMemoryPercent(memoryPercent + "%");
                } catch (Exception e) {
                    Log.low.debug(e.getMessage(), e);
                }
            }
            // 其他两种情况不考虑，统统置为null
        }
    }

    private void softwareSetDiskInfo(SoftwareVo softwareVo) {
        Long processSize = softwareVo.getProgramSize();
        HardwareCriteria criteria = new HardwareCriteria();
        criteria.setId(softwareVo.getServerId());
        HardwareVo hardwareVo = hardwareService.getHardwareInfo(criteria);
        if (hardwareVo == null) {
            return;
        }
        Integer os = hardwareVo.getOs();
        String dbDiskInfo = hardwareVo.getDbDiskInfo();
        List<Map<String, String>> list = JsonUtil.parseObject(dbDiskInfo, new TypeReference<List<Map<String, String>>>() {
        });
        if (ListUtil.isNotEmpty(list)) {
            list.forEach(map -> {
                // 1KB=1024 1M=1048576 1G=1073741824
                String name = map.get("name");
                if (StringUtils.isNotEmpty(name)) {
                    String total = map.get("total");
                    BigDecimal totalBigDecimal = null;
                    if (total.contains("G") || total.contains("g")) {
                        total = total.replace("G", "").replace("g", "");
                        totalBigDecimal = new BigDecimal(total).multiply(new BigDecimal(1073741824L));
                    } else if (total.contains("M") || total.contains("m")) {
                        total = total.replace("M", "").replace("m", "");
                        totalBigDecimal = new BigDecimal(total).multiply(new BigDecimal(1048576));
                    } else if (total.contains("T") || total.contains("t")) {
                        total = total.replace("T", "").replace("t", "");
                        totalBigDecimal = new BigDecimal(total).multiply(new BigDecimal(1099511627776L));
                    }
                    if (LINUX.equals(os)) {
                        if ("/".equals(name)) {
                            String diskPercent = new BigDecimal(processSize).multiply(new BigDecimal(100)).divide(totalBigDecimal, 2, BigDecimal.ROUND_HALF_UP).toString() + "%";
                            softwareVo.setDiskPercent(diskPercent);
                        }
                    } else {
                        if ("D:\\".equals(name)) {
                            BigDecimal divide = new BigDecimal(processSize).multiply(new BigDecimal(100)).divide(totalBigDecimal, 2, BigDecimal.ROUND_HALF_UP);
                            String diskPercent = divide == null ? "0.00" : divide.toString() + "%";
                            softwareVo.setDiskPercent(diskPercent);
                        }
                    }
                }
            });
        }
    }

    private void handleSoftwareBaseInfo(Object info, SoftwareVo softwareVo) {
        if (info instanceof Map) {
            Map<String, Object> infoMap = (Map<String, Object>) info;
            Object common = infoMap.get("common");
            if (common != null) {
                softwareVo.setDbCommonInfo(JsonUtil.toJsonString(common));
                if (common instanceof Map) {
                    Map<String, String> commonMap = (Map<String, String>) common;
                    //  softwareVo.setUsedMemory(commonMap.get("memUse"));
                    //  softwareVo.setTotalMemory(commonMap.get("memSize"));
                    softwareVo.setCpuPercent(commonMap.get("cpuPercent"));
                    if (commonMap.get("pid") != null) {
                        softwareVo.setPid(Integer.valueOf(commonMap.get("pid")));
                    }
                    if (StringUtils.isEmpty(softwareVo.getStartParam())) {
                        softwareVo.setStartParam(commonMap.get("startParam"));
                    }
                    if (StringUtils.isNotEmpty(commonMap.get("startTime"))) {
                        softwareVo.setCreateTime(DateUtil.parse(commonMap.get("startTime"), YYYY_MM_DD_HH_MM_SS));
                    }
                }
            }
            Object self = infoMap.get("self");
            if (self != null) {
                softwareVo.setDbSelfInfo(JsonUtil.toJsonString(self));
            }
            Object log = infoMap.get("log");
            if (log != null) {
                softwareVo.setDbLogInfo(JsonUtil.toJsonString(log));
            }
            Object config = infoMap.get("config");
            if (config != null) {
                softwareVo.setDbConfInfo(JsonUtil.toJsonString(config));
            }
        }
    }

    private void dealSoftwareData(String params) {
        long l = System.currentTimeMillis();
        String softwareName = null;
        try {
            SoftwareVo softwareVo = JsonUtil.parseObject(params, SoftwareVo.class);
            if (softwareVo == null || StringUtils.isEmpty(softwareVo.getName())) {
                Log.high.error("NoSuch softwareName " + params);
                return;
            }
            List<String> host = JsonUtil.parseObject(softwareVo.getHost(), new TypeReference<ArrayList<String>>() {
            });
            softwareName = softwareVo.getName();
            if (ListUtil.isEmpty(host) || StringUtils.isEmpty(softwareName)) {
                return;
            }
            if ("MaintainServer".equals(softwareName) || "MaintainAgent".equals(softwareName)) {
                return;
            }
            AgentCriteria agentCriteria = new AgentCriteria();
            agentCriteria.setIps(host);
            AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
            if (agentVo == null) {
                Log.high.error("NoSuch agent " + host);
                return;
            }

            if (StringUtils.isEmpty(softwareVo.getMemoryPercent())) {
                softwareSetMemoryInfo(softwareVo);
            }

            if (StringUtils.isEmpty(softwareVo.getDiskPercent())) {
                softwareSetDiskInfo(softwareVo);
            }
            Object info = softwareVo.getInfo();
            if (info != null) {
                handleSoftwareBaseInfo(info, softwareVo);
            }
            if (softwareVo.getLastUpdateTime() == null) {
                softwareVo.setLastUpdateTime(new Date());
            }
            if (StringUtils.isEmpty(softwareVo.getMemoryPercent())) {
                softwareVo.setMemoryPercent("0.00%");
            }
            softwareVo.setServerId(agentVo.getId());

            softwareVo.setHeartMonitor(Boolean.TRUE);
            softwareVo.setHost(agentVo.getIp());
            softwareService.setSoftwareInfo(softwareVo, true);
        }catch (Exception e){
            Log.high.error("deal software info exception", e);
        }
        finally {
            long l1 = System.currentTimeMillis();
            long time = l1 - l;
            if (time > 6000) {
                Log.low.warn("处理 " + softwareName + " 程序心跳日志，耗时:" + time);
            }
        }
    }

    public void dealHardwareData(String params) {
        HardwareVo hardwareVo = JsonUtil.parseObject(params, HardwareVo.class);
        if (!"hardware".equals(hardwareVo.getType())) {
            return;
        }
        Info hardwareInfo = hardwareVo.getInfo();
        String ip = hardwareVo.getHost() == null ? hardwareVo.getIp() : hardwareVo.getHost();
        AgentCriteria agentCriteria = new AgentCriteria();
        agentCriteria.setIp(ip);
        AgentVo agentVo = agentService.getAgentByCriteria(agentCriteria);
        if (agentVo == null) {
            return;
        }
        hardwareVo.setIp(agentVo.getIp());
        hardwareVo.setAgentId(agentVo.getId());
        hardwareVo.setOs(agentVo.getOs());
        hardwareVo.setRole(agentVo.getRole());
        String osName = hardwareInfo.getOsName();
        if (hardwareInfo.getNetwork() != null && hardwareInfo.getNetwork().getInterval() != 0) {
            HardwareVo.Network network = hardwareInfo.getNetwork();
            hardwareVo.setSendBytePerSecond((int) (network.getSendBytes() / network.getInterval()));
            hardwareVo.setReceiveBytePerSecond((int) (network.getReceiveBytes() / network.getInterval()));
        }
        if (StringUtils.isNotEmpty(osName)) {
            hardwareVo.setOsName(osName);
        } else {
            hardwareVo.setOsName(hardwareInfo.getName());
        }
        List<String> ipArray = hardwareVo.getIpArray();
        if (ipArray != null) {
            String ncIp = "";
            Collections.sort(ipArray);
            for (String str : ipArray) {
                ncIp = ncIp.concat(str).concat(",");
            }
            ncIp = ncIp.substring(0, ncIp.lastIndexOf(","));
            hardwareVo.setIps(ncIp);
        }
        hardwareVo.setUsedCpu(hardwareInfo.getCpu().get("usedPercent"));
        Map<String, String> memoryInfo = hardwareInfo.getMem();
        hardwareVo.setUsedMemory(memoryInfo.get("actualUsed"));
        hardwareVo.setTotalMemory(memoryInfo.get("total"));
        hardwareVo.setMemoryUsedPercent(memoryInfo.get("usedPercent"));
        Map<String, String> diskInfo = hardwareInfo.getDiskInfo();
        if (diskInfo != null) {
            hardwareVo.setDiskUsedPercent(diskInfo.get("usedPercent"));
        }
        List<Map<String, String>> diskList = hardwareInfo.getDisk();
        if (ListUtil.isNotEmpty(diskList)) {
            //计算磁盘容量应该用字节！
            BigDecimal T = BigDecimal.valueOf(1024 * 1024 * 1024 * 1024L);
            BigDecimal G = BigDecimal.valueOf(1024 * 1024 * 1024L);
            BigDecimal M = BigDecimal.valueOf(1024 * 1024L);
            BigDecimal totalDisk = new BigDecimal(0);
            BigDecimal usedDisk = new BigDecimal(0);
            for (Map<String, String> map : diskList) {
                String total = map.get("total");
                String used = map.get("used");
                if (StringUtils.isNotEmpty(total)) {
                    boolean containG = total.contains("g") || total.contains("G");
                    boolean containM = total.contains("m") || total.contains("M");
                    boolean containT = total.contains("t") || total.contains("T");
                    if (containG) {
                        total = total.replace("g", "").replace("G", "");
                        totalDisk = totalDisk.add(new BigDecimal(total).multiply(G));
                    } else if (containM) {
                        total = total.replace("m", "").replace("M", "");
                        Log.low.debug("");
                        totalDisk = totalDisk.add(new BigDecimal(total).multiply(M));
                    } else if (containT) {
                        total = total.replace("t", "").replace("T", "");
                        Log.low.debug("");
                        totalDisk = totalDisk.add(new BigDecimal(total).multiply(T));
                    }
                }
                if (used != null && StringUtils.isNotEmpty(used) && used.contains("g") || used.contains("G") || used.contains("t") || used.contains("T")) {
                    boolean containG = used.contains("g") || used.contains("G");
                    boolean containM = used.contains("m") || used.contains("M");
                    boolean containT = used.contains("t") || used.contains("T");
                    if (containG) {
                        used = used.replace("g", "").replace("G", "");
                        usedDisk = usedDisk.add(new BigDecimal(used).multiply(G));
                    } else if (containM) {
                        used = used.replace("m", "").replace("M", "");
                        usedDisk = usedDisk.add(new BigDecimal(used).multiply(M));
                    } else if (containT) {
                        used = used.replace("t", "").replace("T", "");
                        usedDisk = usedDisk.add(new BigDecimal(used).multiply(T));
                    }
                }
                String usedPercent = map.get("usedPercent");
                //可用磁盘容量
                BigDecimal availableDisk = totalDisk.subtract(usedDisk);
                if (StringUtils.isNotEmpty(usedPercent)) {
                    usedPercent = usedPercent.replace("%", "");
                    String low = diskRange.split(",")[0];
                    String high = diskRange.split(",")[1];
                    BigDecimal bigDecimal = new BigDecimal(usedPercent);
                    int warming = bigDecimal.compareTo(new BigDecimal(low));
                    int alarm = bigDecimal.compareTo(new BigDecimal(high));
                    if ((warming < 0 && alarm < 0) || availableDisk.compareTo(T) >= 0) {
                        map.put("diskStatus", AlarmStatusType.GREEN.getValue());
                    } else if (warming > 0 && alarm < 0 && availableDisk.compareTo(G.multiply(new BigDecimal(500))) >= 0 && availableDisk.compareTo(T) < 0) {
                        map.put("diskStatus", AlarmStatusType.YELLOW.getValue());
                    } else if (availableDisk.compareTo(G.multiply(new BigDecimal(500))) < 0) {
                        map.put("diskStatus", AlarmStatusType.RED.getValue());
                    }
                }

            }
            //保留两位小数
            if (totalDisk.compareTo(T) >= 0) {
                hardwareVo.setTotalDisk(totalDisk.divide(T, 2, BigDecimal.ROUND_HALF_UP).toString() + "T");
            } else {
                hardwareVo.setTotalDisk(totalDisk.divide(G, 2, BigDecimal.ROUND_HALF_UP).toString() + "G");
            }
            if (usedDisk.compareTo(T) >= 0) {
                hardwareVo.setUsedDisk(usedDisk.divide(T, 2, BigDecimal.ROUND_HALF_UP).toString() + "T");
            } else {
                hardwareVo.setUsedDisk(usedDisk.divide(G, 2, BigDecimal.ROUND_HALF_UP).toString() + "G");
            }
        }
        hardwareVo.setFirewallStatus(hardwareInfo.getFirewallStatus());
        hardwareVo.setDbDiskInfo(JsonUtil.toJsonString(hardwareInfo.getDisk()));
        hardwareVo.setDbNicInfo(JsonUtil.toJsonString(hardwareInfo.getNic()));
        //TODO 记录top进程信息
        hardwareVo.setCpuTopProcess(JsonUtil.toJsonString(hardwareVo.getCpuTopProcessInfo()));
        hardwareVo.setMemTopProcess(JsonUtil.toJsonString(hardwareVo.getMemTopProcessInfo()));
        try {
            hardwareVo.setFirewallStatus(hardwareVo.getFirewallStatus() == null ? 0 : hardwareVo.getFirewallStatus());
            hardwareVo.setCreateTime(hardwareVo.getCreateTime() == null ? new Date() : hardwareVo.getCreateTime());
            hardwareService.addHardwareInfo(hardwareVo);
        } catch (Exception e) {
            Log.high.error("解析hardware信息异常", e);
        }
    }

    private void updateAgentHeartbeat(String params) throws ServiceException {
        try {
            Map<String, String> map = JsonUtil.parseObject(params, new TypeReference<Map<String, String>>() {
            });
            AgentVo agentVo = new AgentVo();
            Integer pid = null;
            if (map.get("pid") != null) {
                pid = Integer.valueOf(map.get("pid"));
                agentVo.setPid(pid);
            }
            String ip = map.get("ip");
            ip = ip == null ? map.get("host") : ip;
            List<String> ips = null;
            if (StringUtils.isNotEmpty(ip)) {
                if ((ip.contains("[") && ip.contains("]"))) {
                    ips = JsonUtil.parseObject(ip, new TypeReference<List<String>>() {
                    });
                }
                if (ListUtil.isNotEmpty(ips)) {
                    AgentCriteria criteria = new AgentCriteria();
                    criteria.setIps(ips);
                    List<AgentVo> agentList = agentService.getAgentList(criteria);
                    if (ListUtil.isNotEmpty(agentList)) {
                        for (AgentVo vo : agentList) {
                            if (DEFAULT_PID.equals(vo.getPid())) {
                                if (pid != null) {
                                    vo.setPid(pid);
                                    agentService.updateAgent(vo);
                                    continue;
                                }
                            }
                            if (pid != null) {
                                vo.setPid(pid);
                            }
                            if (vo.getLastHeartbeatTime() == null) {
                                agentService.updateAgent(vo);
                                continue;
                            }
                            //避免频繁访问数据库
                            Long lastHeartbeatTime = vo.getLastHeartbeatTime().getTime();
                            Long currentTime = System.currentTimeMillis();
                            if ((currentTime - lastHeartbeatTime) > 120000L) {
                                agentService.updateAgent(vo);
                            }
                        }
                        //  return;
                    }/*else{
                        agentService.addAgent(agentVo);
                    }*/
                } else {
                    AgentCriteria criteria = new AgentCriteria();
                    criteria.setIp(ip);
                    agentVo.setIp(ip);
                    AgentVo oldAgent = agentService.getAgentByCriteria(criteria);
                    if (oldAgent != null) {
                        //避免频繁访问数据库
                        if (oldAgent.getProgramStatus().equals(ProgramStatusType.OPEN) && !oldAgent.getStatus().equals(AlarmStatusType.GREEN)) {
                            agentService.updateAgent(agentVo);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            throw new ServiceException(e);
        }
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        Log.low.info("onApplicationContextRefreshedEvent");
    }

}