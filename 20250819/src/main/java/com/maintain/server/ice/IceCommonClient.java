package com.maintain.server.ice;

import Ice.*;
import com.common.ice.IceDirectClient;
import com.common.log.Log;
import com.ice.common.CommonServicePrx;
import com.ice.common.CommonServicePrxHelper;
import com.maintain.server.Constants;
import com.maintain.server.exception.ServiceException;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.FileInputStream;
import java.lang.Object;
import java.lang.reflect.Proxy;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR> Ice直连长连接
 * @date 2018-04-06
 */
public class IceCommonClient {

    private static final ConcurrentMap<String, IceCommonClient> commonClientMap = new ConcurrentHashMap<>();

    private static final ConcurrentMap<String, CommonServicePrx> proxyMap = new ConcurrentHashMap<>();

    /**
     * xxxService:tcp -h ip -p port.
     */
    private String location;

    /**
     *
     */
    private String[] value;

    private IceCommonClient(String location, String[] value) {
        this.location = location;
        this.value = value;
    }

    /**
     * @return location
     */
    private String getLocation() {
        return location;
    }

    /**
     * @param location location
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * @return value
     */
    public String[] getValue() {
        return value;
    }

    /**
     * @param value value
     */
    public void setValue(String[] value) {
        this.value = value;
    }

    /**
     * @param location location
     * @param value    value
     * @return commonClient
     */
    public static IceCommonClient getInstance(String location, String[] value) {
        IceCommonClient commonClient = commonClientMap.get(location);
        if (commonClient == null) {
            synchronized (IceCommonClient.class) {
                if (commonClient == null) {
                    commonClient = new IceCommonClient(location, value);
                    commonClientMap.put(location, commonClient);
                }
            }
        }
        return commonClientMap.get(location);
    }

    /**
     * @param autoShutdown 如果为false则需要手动关闭Communicator
     * @return CommonServicePrx
     */
    public CommonServicePrx getServicePrx(boolean autoShutdown) throws ServiceException {
        try {
            String commonClientLocation = this.getLocation();
            String[] args = this.getValue();
            CommonServicePrx proxy = proxyMap.get(commonClientLocation);
            if (proxy == null || proxy.ice_getCommunicator().isShutdown()) {
                synchronized (IceCommonClient.class) {
                    if (proxy == null || proxy.ice_getCommunicator().isShutdown()) {
                        Properties properties = getProperties(args);
                        InitializationData data = new InitializationData();
                        data.properties = properties;
                        Communicator communicator = Util.initialize(data);
                        IceDirectClient.setCommunicator(communicator);
                        ObjectPrx prx = IceDirectClient.getClient(commonClientLocation);
                        if (prx != null) {
                            proxy = CommonServicePrxHelper.checkedCast(prx);
                            if (proxy != null) {
                                Class<?> clazz = proxy.getClass();
                                Map<String, String> context = new HashMap<>();
                                context.put("client", "MaintainServer");
                                context.put("ip", getLocalHost());
                                proxy = (CommonServicePrx) proxy.ice_context(context);
                                IceInvocationHandler handler = new IceInvocationHandler(proxy);
                                CommonServicePrx proxyHandler = (CommonServicePrx) Proxy.newProxyInstance(clazz.getClassLoader(), clazz.getInterfaces(), handler);
                                proxyMap.put(commonClientLocation,proxyHandler) ;
                                return proxyHandler;
                            }
                        }else{
                            if(communicator != null) communicator.destroy();
                        }
                    }
                }
            }else{
                return proxy;
            }
        } catch (Exception e) {
            Log.high.error("can not create CommonServicePrx, " + e);
            shutdownNow();
            throw new ServiceException(e);
        } finally {
            if (autoShutdown) {
                safeShutdown();
            }
        }
        return null;
    }

    private String getLocalHost() {
        try {
            String filepath = "./config/application.yml";
            Yaml yaml = new Yaml();
            Map map = yaml.loadAs(new FileInputStream(new File(filepath)), Map.class);
            Object server = map.get("server");
            if (server != null && server instanceof Map) {
                Map serverMap = (Map) server;
                if (serverMap.get("localHost") != null) {
                    return serverMap.get("localHost").toString();
                }
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
        return getHostIP();
    }

    /**
     * 获取本机 V4 IP
     *
     * @return String
     */
    private String getHostIP() {
        String hostIPs = "";
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip = null;
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                if (ni.getDisplayName().contains("VMware Virtual")) {
                    continue;
                }
                Enumeration<InetAddress> inetAddresses = ni.getInetAddresses();
                String localhost = "127.0.0.1";
                while (inetAddresses.hasMoreElements()) {
                    ip = inetAddresses.nextElement();
                    if (!ip.getHostAddress().contains(localhost) && !ip.getHostAddress().contains(":")) {
                        hostIPs += ip.getHostAddress() + ",";
                    }
                }
            }
        } catch (Exception e) {
            Log.high.error("get host IP failed ...", e);
        }
        if (hostIPs.length() == 0) {
            return "notgetip";
        }
        return hostIPs.substring(0, hostIPs.length() - 1);
    }

    /**
     * @param args args
     * @return Properties
     */
    private Properties getProperties(String[] args) {
        Properties properties;
        if (args != null && args.length > 0) {
            properties = Util.createProperties(args);
        } else {
            properties = Util.createProperties();
            String configPath1 = "./deploy/icepool.conf";
            properties.load(configPath1);
        }
        return properties;
    }

    /**
     * 异步断开,延时10秒
     */
    private void safeShutdown() {
        ExecutorService executorService = new ThreadPoolExecutor(10, 10, 1, TimeUnit.MINUTES, new ArrayBlockingQueue<>(1024), Executors.defaultThreadFactory());
        executorService.execute(() -> {
            try {
                // x秒后断开连接
                Thread.sleep(Constants.SLEEP_TIME * 10);
                shutdownNow();
            } catch (InterruptedException e) {
                Log.high.error("while shutdown communicator throw an Exception," + e);
                Thread.currentThread().interrupt();
            }
        });
        executorService.shutdown();
    }

    /**
     * 立即断开,无需等待
     */
    private void shutdownNow() {
        CommonServicePrx proxy = proxyMap.get(this.getLocation());
        if (proxy != null) {
            try{
                proxy.ice_getCommunicator().destroy();
            }catch (Exception e){
                Log.high.warn("ice-communicator destroy exception",e);
            }
            proxyMap.remove(this.getLocation());
        }
    }

    public static void shutdownAll() {
        for (Map.Entry<String, CommonServicePrx> entry : proxyMap.entrySet()) {
            Communicator communicator = entry.getValue().ice_getCommunicator();
            communicator.destroy();
            proxyMap.remove(entry.getKey());
        }
        commonClientMap.clear();
    }
}