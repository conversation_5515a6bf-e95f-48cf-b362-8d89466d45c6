package com.maintain.server.ice.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-12-24
 */
@Data
public class CheckDbBackupRequest {
    /**
     * 备份路径
     */
   private String backupPath;
    /**
     * 备份类型，0：天，1：周
     */
   private int backupType;
    /**
     * 备份时间
     */
   private String backupTime;

    public CheckDbBackupRequest() {
    }

    public CheckDbBackupRequest(String backupPath, int backupType, String backupTime) {
        this.backupPath = backupPath;
        this.backupType = backupType;
        this.backupTime = backupTime;
    }
}