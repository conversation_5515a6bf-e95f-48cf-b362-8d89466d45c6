<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.HardwareMapper">
    <resultMap type="com.maintain.server.vo.HardwareVo" id="HARDWARE_RESULT">
        <result property="agentId" column="AGENT_ID"/>
        <result property="ip" column="IP"/>
        <result property="role" column="ROLE"/>
        <result property="firewallStatus" column="FIREWALL_STATUS"/>
        <result property="ips" column="IPS"/>
        <result property="os" column="OS"/>
        <result property="osName" column="OS_NAME"/>
        <result property="usedCpu" column="USED_CPU"/>
        <result property="dbDiskInfo" column="DISK_INFO"/>
        <result property="dbNicInfo" column="NIC_INFO"/>
        <result property="usedDisk" column="USED_DISK"/>
        <result property="totalDisk" column="TOTAL_DISK"/>
        <result property="diskUsedPercent" column="DISK_PERCENT"/>
        <result property="usedMemory" column="USED_MEMORY"/>
        <result property="totalMemory" column="TOTAL_MEMORY"/>
        <result property="memoryUsedPercent" column="MEMORY_PERCENT"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="lastHeartbeatTime" column="LAST_HEARTBEAT_TIME"/>
        <result property="ports" column="PORTS"/>
        <result property="note" column="NOTE"/>
        <result property="receiveBytePerSecond" column="RECEIVE_BYTE_PER_SECOND" />
        <result property="sendBytePerSecond" column="SEND_BYTE_PER_SECOND" />
        <result property="cpuTopProcess" column="cpu_top_process" />
        <result property="memTopProcess" column="mem_top_process" />
    </resultMap>

    <resultMap type="com.maintain.server.vo.HardwareHistoryHealth" id="HISTORY_HEALTH">
        <result property="id" column="ID"/>
        <result property="agentId" column="SERVER_ID"/>
        <result property="status" typeHandler="com.maintain.server.type.AlarmStatusTypeHandler" column="STATUS"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="cpuPercent" column="CPU_PERCENT"/>
        <result property="memoryPercent" column="MEMORY_PERCENT"/>
        <result property="usedDisk" column="USED_DISK"/>
        <result property="usedMemory" column="USED_MEMORY"/>
        <result property="diskPercent" column="DISK_PERCENT"/>
        <result property="receiveBytePerSecond" column="RECEIVE_BYTE_PER_SECOND" />
        <result property="sendBytePerSecond" column="SEND_BYTE_PER_SECOND" />
    </resultMap>

    <resultMap type="com.maintain.server.vo.OsOperateLogVo" id="OPERATE_LOG_RESULT">
        <result property="ip" column="ip"/>
        <result property="time" column="time"/>
        <result property="operateDesc" column="operate_desc"/>
    </resultMap>

    <select id="getOsOperateLog" parameterType="com.maintain.server.vo.OsOperateLogVo" resultMap="OPERATE_LOG_RESULT">
        select ip,DATE_FORMAT(`time`,'%Y-%m-%d %H:%i:%s') AS `time`,operate_desc from os_operate_log
        where 1=1
        <if test="ip != null">and ip = #{ip}</if>
        order by `time` DESC
    </select>

    <select id="getHardwareInfos" parameterType="com.maintain.server.criteria.HardwareCriteria"
            resultMap="HARDWARE_RESULT">
        SELECT AGENT_ID ,IP,IPS,ROLE,FIREWALL_STATUS,OS,OS_NAME,USED_CPU,DISK_INFO,NIC_INFO,USED_DISK,
        TOTAL_DISK,DISK_PERCENT,USED_MEMORY,TOTAL_MEMORY,MEMORY_PERCENT,h.CREATE_TIME,a.LAST_HEARTBEAT_TIME,PORTS,h.NOTE,
        h.RECEIVE_BYTE_PER_SECOND,h.SEND_BYTE_PER_SECOND,h.cpu_top_process,h.mem_top_process
        FROM HARDWARE_INFO h INNER JOIN
        AGENT a
        ON a.ID = h.AGENT_ID
        <where>
            <if test="agentId!=null">AGENT_ID = #{agentId}</if>
            <if test="ip!=null">AND IP = #{ip}</if>
            <if test="startTime!=null and endTime!=null">
                AND AGENT.LAST_HEARTBEAT_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY AGENT_ID
        ORDER BY AGENT_ID
        <!-- ORDER BY LAST_HEARTBEAT_TIME DESC -->
    </select>

    <select id="getHardwareInfo" parameterType="java.lang.String"
            resultMap="HARDWARE_RESULT">
        SELECT AGENT_ID ,IP,IPS,ROLE,FIREWALL_STATUS,OS,OS_NAME,USED_CPU,DISK_INFO,NIC_INFO,USED_DISK,
        TOTAL_DISK,DISK_PERCENT,USED_MEMORY,TOTAL_MEMORY,MEMORY_PERCENT,h.CREATE_TIME,a.LAST_HEARTBEAT_TIME,PORTS,h.NOTE,h.cpu_top_process,h.mem_top_process
        FROM HARDWARE_INFO h INNER JOIN
        AGENT a
        ON a.ID = h.AGENT_ID
        <where>
            IP = #{param}
        </where>
    </select>

    <insert id="addHardwareInfo" parameterType="com.maintain.server.vo.HardwareVo">
        INSERT INTO HARDWARE_INFO (AGENT_ID,FIREWALL_STATUS,OS_NAME,USED_CPU,DISK_INFO,NIC_INFO,USED_DISK,
        TOTAL_DISK,DISK_PERCENT,USED_MEMORY,TOTAL_MEMORY,MEMORY_PERCENT,CREATE_TIME,IPS,PORTS,SEND_BYTE_PER_SECOND,RECEIVE_BYTE_PER_SECOND,
        cpu_top_process,mem_top_process)
        VALUES(#{agentId},#{firewallStatus},#{osName},#{usedCpu},#{dbDiskInfo},#{dbNicInfo},#{usedDisk},
        #{totalDisk},#{diskUsedPercent},#{usedMemory},#{totalMemory},#{memoryUsedPercent},#{createTime},#{ips},#{ports},
        #{sendBytePerSecond},#{receiveBytePerSecond},#{cpuTopProcess},#{memTopProcess})
    </insert>

    <update id="updateHardware" parameterType="com.maintain.server.vo.HardwareVo">
        UPDATE HARDWARE_INFO
        <trim prefix="SET" prefixOverrides=",">
            <if test="firewallStatus!=null">
                ,FIREWALL_STATUS = #{firewallStatus}
            </if>
            <if test="osName!=null">
                ,OS_NAME = #{osName}
            </if>
            <if test="usedCpu!=null">
                ,USED_CPU = #{usedCpu}
            </if>
            <if test="dbDiskInfo!=null">
                ,DISK_INFO = #{dbDiskInfo}
            </if>
            <if test="dbNicInfo!=null">
                , NIC_INFO = #{dbNicInfo}
            </if>
            <if test="usedDisk!=null">
                , USED_DISK = #{usedDisk}
            </if>
            <if test="totalDisk!=null">
                , TOTAL_DISK = #{totalDisk}
            </if>
            <if test="usedMemory!=null">
                , USED_MEMORY = #{usedMemory}
            </if>
            <if test="totalMemory!=null">
                , TOTAL_MEMORY = #{totalMemory}
            </if>
            <if test="memoryUsedPercent!=null">
                ,MEMORY_PERCENT =#{memoryUsedPercent}
            </if>
            <if test="diskUsedPercent!=null">
                , DISK_PERCENT =#{diskUsedPercent}
            </if>
            <if test="ips!=null">
                ,IPS = #{ips}
            </if>
            <if test="ports!=null">
                ,PORTS = #{ports}
            </if>
            <if test="note!=null">
                ,NOTE = #{note}
            </if>
            <if test="receiveBytePerSecond != null">
                ,RECEIVE_BYTE_PER_SECOND=#{receiveBytePerSecond}
            </if>
            <if test="sendBytePerSecond != null">
                ,SEND_BYTE_PER_SECOND=#{sendBytePerSecond}
            </if>
            <if test="cpuTopProcess != null">
                ,cpu_top_process=#{cpuTopProcess}
            </if>
            <if test="memTopProcess != null">
                ,mem_top_process=#{memTopProcess}
            </if>
        </trim>
        WHERE AGENT_ID = #{agentId}
    </update>

    <insert id="addHardwareHistoryHealth" parameterType="java.util.List">
        INSERT IGNORE INTO HARDWARE_HISTORY_HEALTH (ID,AGENT_ID,STATUS,DESCRIPTION,CPU_PERCENT,MEMORY_PERCENT,DISK_PERCENT,USED_MEMORY,USED_DISK,CREATE_TIME,SEND_BYTE_PER_SECOND,RECEIVE_BYTE_PER_SECOND)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.agentId},#{item.status.id},#{item.description},#{item.cpuPercent},#{item.memoryPercent},#{item.diskPercent},#{item.usedMemory},#{item.usedDisk},now(),#{item.sendBytePerSecond},#{item.receiveBytePerSecond})
        </foreach>
    </insert>

    <select id="getHardwareHistoryHealth" parameterType="com.maintain.server.criteria.HardwareCriteria" resultMap="HISTORY_HEALTH">
        SELECT ID,AGENT_ID,STATUS,DESCRIPTION,CREATE_TIME,CPU_PERCENT,MEMORY_PERCENT,DISK_PERCENT,USED_MEMORY,USED_DISK,SEND_BYTE_PER_SECOND,RECEIVE_BYTE_PER_SECOND FROM HARDWARE_HISTORY_HEALTH
        <where>
            1 = 1
            <if test="agentId!=null">
                AND AGENT_ID = #{agentId}
            </if>
            <if test="startTime != null and endTime != null">
                AND CREATE_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME ASC
    </select>

    <delete id="clearHistoryData" parameterType="com.maintain.server.criteria.BaseCriteria">
        DELETE FROM HARDWARE_HISTORY_HEALTH
        <where>
            <choose>
                <when test="startTime!=null and endTime !=null">
                    CREATE_TIME BETWEEN #{startTime} AND #{endTime}
                </when>
                <when test="startTime !=null">
                    CREATE_TIME <![CDATA[<=]]> #{startTime}
                </when>
            </choose>
        </where>
    </delete>
</mapper>
