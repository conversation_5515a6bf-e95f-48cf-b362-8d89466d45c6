<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.WarnMapper">
    <resultMap type="com.maintain.server.vo.WarnVo" id="WARN_RESULT">
        <result property="id" column="ID"/>
        <result property="status" column="STATUS"/>
        <result property="param1" column="PARAM1"/>
        <result property="param2" column="PARAM2"/>
        <result property="param3" column="PARAM3"/>
        <result property="param4" column="PARAM4"/>
        <result property="param5" column="PARAM5"/>
        <result property="param6" column="PARAM6"/>
        <result property="param7" column="PARAM7"/>
        <result property="param8" column="PARAM8"/>
        <result property="param9" column="PARAM9"/>
        <result property="param10" column="PARAM10"/>
        <result property="type" typeHandler="com.maintain.server.type.WarnTypeHandler" column="WARN_TYPE"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="modifyTime" column="MODIFY_TIME"/>
    </resultMap>

    <select id="getWarn" parameterType="com.maintain.server.vo.WarnVo" resultMap="WARN_RESULT">
        SELECT ID,STATUS,PARAM1,PARAM2,PARAM3,PARAM4,PARAM5,PARAM6,PARAM7,PARAM8,PARAM9,PARAM10,WARN_TYPE,DESCRIPTION,MODIFY_TIME
        FROM warn
        <where>
            <if test="type != null and type.value != null">
                warn_type = #{type.value}
            </if>
            <if test="param1 != null">
                and param1 = #{param1}
            </if>
            <if test="param2 != null">
                and param2 = #{param2}
            </if>
            <if test="param3 != null">
                and param3 = #{param3}
            </if>
            <if test="param4 != null">
                and param4 = #{param4}
            </if>
            <if test="param5 != null">
                and param5 = #{param5}
            </if>
            <if test="param6 != null">
                and param6 = #{param6}
            </if>
            <if test="param7 != null">
                and param7 = #{param7}
            </if>
            <if test="param8 != null">
                and param8 = #{param8}
            </if>
            <if test="param9 != null">
                and param9 = #{param9}
            </if>
            <if test="param10 != null">
                and param10 = #{param10}
            </if>
        </where>

    </select>

    <select id="getWarnList" parameterType="com.maintain.server.vo.WarnVo" resultMap="WARN_RESULT">
        SELECT ID,STATUS,PARAM1,PARAM2,PARAM3,PARAM4,PARAM5,PARAM6,PARAM7,PARAM8,PARAM9,PARAM10,WARN_TYPE,DESCRIPTION,MODIFY_TIME
        FROM warn
        <where>
            <if test="type != null and type.value != null">
                warn_type = #{type.value}
            </if>
            <if test="param1 != null">
                and param1 = #{param1}
            </if>
            <if test="param2 != null">
                and param2 = #{param2}
            </if>
            <if test="param3 != null">
                and param3 = #{param3}
            </if>
            <if test="param4 != null">
                and param4 = #{param4}
            </if>
            <if test="param5 != null">
                and param5 = #{param5}
            </if>
            <if test="param6 != null">
                and param6 = #{param6}
            </if>
            <if test="param7 != null">
                and param7 = #{param7}
            </if>
            <if test="param8 != null">
                and param8 = #{param8}
            </if>
            <if test="param9 != null">
                and param9 = #{param9}
            </if>
            <if test="param10 != null">
                and param10 = #{param10}
            </if>
        </where>
    </select>

    <insert id="addWarnRule" parameterType="com.maintain.server.vo.WarnVo">
        INSERT INTO warn(ID,STATUS,PARAM1,PARAM2,PARAM3,PARAM4,PARAM5,PARAM6,PARAM7,PARAM8,PARAM9,PARAM10,WARN_TYPE,DESCRIPTION,MODIFY_TIME)
        VALUES
        (#{id},#{status},#{param1},#{param2},#{param3},#{param4},#{param5},#{param6},#{param7},#{param8},#{param9},#{param10},#{type.value},#{description},now())
    </insert>

    <update id="updateWarnRule" parameterType="com.maintain.server.vo.WarnVo">
        UPDATE warn set modify_time = now()
        <if test="param3 != null">,param3=#{param3}</if>
        <if test="param4 != null">,param4=#{param4}</if>
        <if test="param5 != null">,param5=#{param5}</if>
        <if test="param6 != null">,param6=#{param6}</if>
        <if test="param7 != null">,param7=#{param7}</if>
        <if test="param8 != null">,param8=#{param8}</if>
        <if test="param9 != null">,param9=#{param9}</if>
        <if test="param10 != null">,param10=#{param10}</if>
        <if test="description != null">,description=#{description}</if>
        <if test="status != null">,status=#{status}</if>
        where param1 = #{param1}
        <if test="param2 != null"> and param2 = #{param2} </if>
        and warn_type=#{type.value}
    </update>


</mapper>
