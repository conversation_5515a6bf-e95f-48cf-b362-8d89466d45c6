<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.OperateRecordMapper">
    <resultMap type="com.maintain.server.vo.OperateRecordVo" id="OPERATE_RECORD_RESULT_MAP">
        <result property="id" column="ID"/>
        <result property="user" column="USER"/>
        <result property="operate" column="OPERATE"/>
        <result property="operateTime" column="OPERATE_TIME"/>
    </resultMap>

    <resultMap type="com.maintain.server.vo.OperateRecordVo" id="OPERATE_RECORD_WITH_USER_ID_MAP">
        <result property="id" column="ID"/>
        <result property="user" column="USER"/>
        <result property="operate" column="OPERATE"/>
        <result property="operateTime" column="OPERATE_TIME"/>
        <result property="userId" column="USER_ID"/>
    </resultMap>


    <sql id="base-column">
        ID,USER,OPERATE,OPERATE_TIME
    </sql>

    <insert id="addOperateRecord" parameterType="com.maintain.server.vo.OperateRecordVo">
        INSERT INTO operate_record (<include refid="base-column"/>)
        VALUES (#{id},#{user},#{operate},NOW())
    </insert>
    <select id="getOperateRecord" resultMap="OPERATE_RECORD_RESULT_MAP">
        SELECT
        <include refid="base-column"/>
        FROM operate_record order by operate_time DESC
    </select>
    <select id="selectOperateRecord" resultMap="OPERATE_RECORD_WITH_USER_ID_MAP">
        select o.*,u.id USER_ID
        from operate_record o left join user u on o.user=u.name
        where operate_time &gt;= #{startTime} and operate_time &lt;= #{endTime}
        <choose>
            <when test="login">
                and operate like '%登录%'
            </when>
            <otherwise>
                and operate not like '%登录%'
            </otherwise>
        </choose>
    </select>
</mapper>
