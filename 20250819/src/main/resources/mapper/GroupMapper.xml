<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.GroupMapper">
    <resultMap type="com.maintain.server.vo.GroupHistoryHealthVo" id="GROUP_RESULT_TYPE">
        <result property="id" column="ID"/>
        <result property="groupId" column="GROUP_ID"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="createTime" column="CREATE_TIME"/>
    </resultMap>

    <sql id="base-column">
        ID,GROUP_ID,NAME,STATUS,DESCRIPTION,CREATE_TIME
    </sql>


    <insert id="addGroupHistoryHealth" parameterType="java.util.List">
        INSERT INTO GROUP_HISTORY_HEALTH (<include refid="base-column"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.groupId},#{item.name},#{item.status},#{item.description},now())
        </foreach>
    </insert>

    <select id="getGroupHistoryHealth" parameterType="com.maintain.server.criteria.GroupCriteria"
            resultMap="GROUP_RESULT_TYPE">
        SELECT
        <include refid="base-column"/>
        FROM GROUP_HISTORY_HEALTH
        <where>
            <if test="groupId!=null">
                 GROUP_ID = #{groupId}
            </if>
            <if test="startTime!=null and endTime !=null">
                AND  CREATE_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="name !=null">
                AND NAME = #{name}
            </if>
        </where>
        GROUP BY GROUP_ID, CREATE_TIME
    </select>

    <delete id="clearHistoryData" parameterType="com.maintain.server.criteria.BaseCriteria">
        DELETE FROM GROUP_HISTORY_HEALTH
        <where>
            <choose>
                <when test="startTime!=null and endTime !=null">
                    CREATE_TIME BETWEEN #{startTime} AND #{endTime}
                </when>
                <when test="startTime !=null">
                    CREATE_TIME <![CDATA[<=]]> #{startTime}
                </when>
            </choose>
        </where>
    </delete>
</mapper>
