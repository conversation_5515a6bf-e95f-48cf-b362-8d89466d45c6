<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.RequestRecordMapper">
    <resultMap type="com.maintain.server.vo.RequestRecordVo" id="REQUEST_RECORD_RESULT">
        <result property="id" column="ID"/>
        <result property="uri" column="URI"/>
        <result property="module" column="MODULE"/>
        <result property="startTime" column="START_TIME"/>
        <result property="endTime" column="END_TIME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="timeSpent" column="TIME_SPENT"/>
    </resultMap>


    <insert id="addRequestRecord" parameterType="com.maintain.server.vo.RequestRecordVo" useGeneratedKeys="true"
            keyProperty="id" keyColumn="ID">
        insert into request_record(URI,MODULE,START_TIME,END_TIME,CREATE_TIME,TIME_SPENT)
        values(#{uri},#{module},#{startTime},#{endTime},#{createTime},#{timeSpent})
    </insert>

    <select id="selectStatic" parameterType="com.maintain.server.criteria.RequestRecordCriteria"
            resultType="com.maintain.server.vo.RequestRecordStatVo">
        select module,uri,count(*) times,sum(time_spent) timeTotal,avg(time_spent) timeAvg,max(time_spent)
        timeMax,min(time_spent) timeMin from request_record
        <where>
            <if test="startTime != null and endTime != null">
                and CREATE_TIME between #{startTime} and #{endTime}
            </if>
        </where>
        group by module,uri
    </select>
</mapper>
