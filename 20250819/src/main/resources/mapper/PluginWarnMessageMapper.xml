<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.PluginWarnMessageMapper">
    <resultMap type="com.maintain.server.vo.PluginWarnMessageVo" id="WARN_RESULT">
        <result property="metricType" column="METRIC_TYPE"/>
        <result property="warnMessage" column="WARN_MESSAGE"/>
        <result property="createTime" column="CREATE_TIME"/>
    </resultMap>

    <select id="getAllGroupWarnInfo" resultMap="WARN_RESULT">
        SELECT
        METRIC_TYPE,WARN_MESSAGE,DATE_FORMAT(CREATE_TIME,'%Y-%m-%d %T') AS CREATE_TIME
        FROM plugin_warn_message
        WHERE CREATE_TIME IN(SELECT MAX(CREATE_TIME) FROM plugin_warn_message GROUP BY METRIC_TYPE)
        GROUP BY METRIC_TYPE
    </select>

</mapper>
