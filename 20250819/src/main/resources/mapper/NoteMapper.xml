<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.NoteMapper">
    <resultMap type="com.maintain.server.vo.NoteVo" id="NOTE_RESULT">
        <result property="id" column="id"/>
        <result property="author" column="author"/>
        <result property="content" column="content"/>
        <result property="title" column="title"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="remind" column="remind"/>
    </resultMap>
    <sql id="base-column">
        id,author,content,title,modify_time,remind
    </sql>

    <select id="queryNotes" parameterType="com.maintain.server.criteria.NoteCriteria" resultMap="NOTE_RESULT">
        SELECT
        <include refid="base-column"/>
        FROM note
        <trim prefix="where" prefixOverrides="AND">
            <if test="id!=null">
                AND id = #{id}
            </if>
            <if test="author!=null">
                AND author = #{author}
            </if>
            <if test="content!=null">
                AND content like concat('%',#{content},'%')
            </if>
            <if test="remind!=null">
                AND remind = #{remind}
            </if>
            <if test="title!=null">
                AND title = #{title}
            </if>
        </trim>
        ORDER BY modify_time desc
    </select>

    <select id="count" parameterType="com.maintain.server.criteria.NoteCriteria" resultType="Integer">
        SELECT
        count(*)
        FROM note
        <trim prefix="where" prefixOverrides="AND">
            <if test="id!=null">
                AND id = #{id}
            </if>
            <if test="author!=null">
                AND author = #{author}
            </if>
            <if test="content!=null">
                AND content like concat('%',#{content},'%')
            </if>
            <if test="remind!=null">
                AND remind = #{remind}
            </if>
            <if test="title!=null">
                AND title = #{title}
            </if>
        </trim>
    </select>

    <insert id="addNote" parameterType="com.maintain.server.criteria.NoteCriteria">
        INSERT INTO note (<include refid="base-column"/>)
        VALUES
        (#{id},#{author},#{content},#{title},now(),#{remind})
    </insert>

    <update id="updateNote" parameterType="com.maintain.server.criteria.NoteCriteria">
        UPDATE note
        set modify_time = now()
        <if test="content!=null">
            , content = #{content}
        </if>
        <if test="author!=null">
            , author = #{author}
        </if>
        <if test="title!=null">
            , title = #{title}
        </if>
        <if test="remind!=null">
            , remind = #{remind}
        </if>
        <where>
            id = #{id}
        </where>
    </update>

    <delete id="deleteNote" parameterType="com.maintain.server.criteria.NoteCriteria">
        delete from note
        <trim prefix="where" prefixOverrides="AND">
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="author != null">
                AND author = #{author}
            </if>
            <if test="title != null">
                AND title = #{title}
            </if>
        </trim>
    </delete>
</mapper>
