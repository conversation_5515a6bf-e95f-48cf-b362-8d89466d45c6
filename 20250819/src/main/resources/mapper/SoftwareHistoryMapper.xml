<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.SoftwareHistoryMapper">

    <resultMap id="SOFTWARE_HOME_OBJECT" type="com.maintain.server.vo.SoftwareVo">
        <result property="id" column="ID"/>
        <result property="serverIp" column="IP"/>
        <result property="role" column="ROLE"/>
        <result property="name" column="NAME"/>
        <result property="lastHeartbeatTime" column="LAST_HEARTBEAT_TIME"/>
        <result property="memoryPercent" column="MEMORY_PERCENT"/>
        <result property="usedMemory" column="USED_MEMORY"/>
        <result property="cpuPercent" column="CPU_PERCENT"/>
        <result property="pid" column="PID"/>
        <result property="totalDisk" column="TOTAL_DISK"/>
        <result property="serverId" column="AGENT_ID"/>
        <result property="diskPercent" column="DISK_PERCENT"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="totalDisk" column="USED_DISK"/>
    </resultMap>

    <resultMap id="SOFTWARE_RESTART_RESULT_TYPE" type="com.maintain.server.vo.SoftwareRestartVo">
        <result property="id" column="ID"/>
        <result property="softwareId" column="SOFTWARE_ID"/>
        <result property="status" column="STATUS"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="createTime" column="CREATE_TIME"/>
    </resultMap>

    <sql id="history-health-column">
        AGENT_ID,SOFTWARE_ID,CPU_PERCENT,USED_MEMORY,MEMORY_PERCENT,DISK_PERCENT,CREATE_TIME,USED_DISK
    </sql>

    <insert id="addRestartHistory" parameterType="java.util.List">
        INSERT INTO SOFTWARE_RESTART_HISTORY (ID,SOFTWARE_ID,STATUS,DESCRIPTION,CREATE_TIME)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.softwareId},#{item.status},#{item.description},now())
        </foreach>
    </insert>

    <select id="countRestartHistory" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT count(*) from SOFTWARE_RESTART_HISTORY
        WHERE  SOFTWARE_ID = #{id} AND CREATE_TIME > CURRENT_DATE
    </select>

    <select id="getRestartHistory" parameterType="com.maintain.server.criteria.SoftwareCriteria"
            resultMap="SOFTWARE_RESTART_RESULT_TYPE">
        SELECT ID,SOFTWARE_ID,STATUS,DESCRIPTION,CREATE_TIME FROM SOFTWARE_RESTART_HISTORY
        <where>
            <if test="id!=null">
                SOFTWARE_ID = #{id}
            </if>
            <if test="startTime !=null and endTime!=null">
                AND CREATE_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <insert id="addSoftwareHistoryInfo" parameterType="java.util.List">
        INSERT IGNORE INTO SOFTWARE_HISTORY_HEALTH (<include refid="history-health-column"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.serverId},#{item.id},#{item.cpuPercent},#{item.usedMemory},#{item.memoryPercent},#{item.diskPercent},now(),#{item.totalDisk})
        </foreach>
    </insert>

    <select id="getSoftwareHistoryInfo" parameterType="com.maintain.server.criteria.SoftwareCriteria"
            resultMap="SOFTWARE_HOME_OBJECT">
        SELECT
        <include refid="history-health-column"/>
        FROM SOFTWARE_HISTORY_HEALTH
        <where>
            <if test="serverId!=null">
                AGENT_ID = #{serverId}
            </if>
            <if test="id!=null">
                AND SOFTWARE_ID = #{id}
            </if>
            <if test="startTime !=null and endTime !=null">
                AND CREATE_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
            <!--  <if test="timePoint!=null">
                  AND CREATE_TIME IN
                  <foreach collection="timePoint" item="time" separator="," open="(" close=")">
                      #{time}
                  </foreach>
              </if>-->
        </where>
        ORDER BY CREATE_TIME ASC
    </select>

    <delete id="clearHistoryData" parameterType="com.maintain.server.criteria.BaseCriteria">
        DELETE FROM SOFTWARE_HISTORY_HEALTH
        <where>
            <choose>
                <when test="startTime!=null and endTime !=null">
                    CREATE_TIME BETWEEN #{startTime} AND #{endTime}
                </when>
                <when test="startTime !=null">
                    CREATE_TIME <![CDATA[<=]]> #{startTime}
                </when>
            </choose>
        </where>
    </delete>

    <delete id="clearRestartHistoryData" parameterType="com.maintain.server.criteria.BaseCriteria">
        DELETE FROM SOFTWARE_RESTART_HISTORY
        <where>
            <choose>
                <when test="startTime!=null and endTime !=null">
                    CREATE_TIME BETWEEN #{startTime} AND #{endTime}
                </when>
                <when test="startTime !=null">
                    CREATE_TIME <![CDATA[<=]]> #{startTime}
                </when>
            </choose>
        </where>
    </delete>
</mapper>
