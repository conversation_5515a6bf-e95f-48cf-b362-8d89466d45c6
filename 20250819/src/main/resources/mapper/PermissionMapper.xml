<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.PermissionMapper">

    <select id="findAllResources" resultType="com.maintain.server.vo.PermissionVo">
        SELECT p.*,r.name roleName FROM permission p
        LEFT JOIN role_permission rp on p.id=rp.permission_id
        LEFT JOIN role r on rp.role_id=r.id
    </select>

    <select id="findByRoleId" parameterType="java.lang.Integer" resultType="com.maintain.server.vo.PermissionVo">
        SELECT p.*,r.name roleName FROM permission p
        LEFT JOIN role_permission rp on p.id=rp.permission_id
        LEFT JOIN role r on rp.role_id=r.id
        WHERE r.id=#{roleId}
    </select>

    <select id="findByIds" parameterType="java.util.List" resultType="com.maintain.server.vo.PermissionVo">
        SELECT * FROM permission WHERE
        id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findById" parameterType="java.lang.Integer" resultType="com.maintain.server.vo.PermissionVo">
        SELECT * FROM permission WHERE id=#{id}
    </select>

    <select id="findByName" parameterType="java.lang.String" resultType="com.maintain.server.vo.PermissionVo">
        SELECT * FROM permission WHERE name=#{name}
    </select>

    <select id="findByParentId" parameterType="java.lang.Integer" resultType="com.maintain.server.vo.PermissionVo">
        SELECT * FROM permission WHERE parentId=#{id}
    </select>

</mapper>
