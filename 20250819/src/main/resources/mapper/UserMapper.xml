<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.UserMapper">
    <resultMap type="com.maintain.server.vo.UserVo" id="USER_RESULT">
        <id property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="pswd" column="PSWD"/>
        <collection property="roles" ofType="com.maintain.server.vo.RoleVo">
            <result property="id" column="ROLE_ID"/>
            <result property="name" column="ROLE_NAME"/>
            <result property="nickName" column="NICK_NAME"/>
        </collection>
    </resultMap>

    <resultMap type="com.maintain.server.vo.DynamicAndStaVoticThresholdVo" id="ThresholdVo_RESULT">
        <id property="id" column="id"/>
        <result property="analysisThreshold" column="analysis_threshold"/>
        <result property="comment" column="comment"/>
        <result property="creator" column="creator"/>
        <result property="createtime" column="create_time"/>
        <result property="updatetime" column="update_time"/>
    </resultMap>

    <select id="userExistByName" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(*) FROM user WHERE name=#{name}
    </select>

    <select id="findByUsername" parameterType="com.maintain.server.criteria.UserCriteria" resultMap="USER_RESULT">
        SELECT u.ID,u.NAME,u.PSWD,r.ID ROLE_ID,r.NAME ROLE_NAME,r.NICK_NAME NICK_NAME
        FROM user u
        LEFT JOIN user_role ur on u.id=ur.user_id
        LEFT JOIN role r on r.id = ur.role_id
        <where>
            <if test="id!=null">
                u.ID = #{id}
            </if>
            <if test="name!=null">
                AND u.NAME=#{name}
            </if>
        </where>
    </select>

    <select id="listUsers" resultType="com.maintain.server.vo.UserVo">
        SELECT u.ID,u.NAME,DATE_FORMAT(u.create_time, '%Y-%m-%d %H:%i:%s') createTime,
        DATE_FORMAT(u.last_login_time, '%Y-%m-%d %H:%i:%s') lastLoginTime,
        r.NICK_NAME department, r.id roleId
        FROM user u
        LEFT JOIN user_role ur on u.id=ur.user_id
        LEFT JOIN role r on r.id = ur.role_id where u.name != 'admin'
    </select>

    <select id="listRoles" resultType="com.maintain.server.vo.RoleVo">
        SELECT r.id id, r.nick_name nickName FROM role r
        WHERE r.name != 'ROLE_ADMIN';
    </select>

    <select id="findRoleByIds" parameterType="java.util.List" resultType="com.maintain.server.vo.RoleVo">
        SELECT r.id id, r.name name, r.nick_name nickName FROM role r WHERE
        id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getRootPermission" resultType="com.maintain.server.vo.PermissionVo">
        SELECT * FROM permission WHERE parentId is null and id != 1;
    </select>

    <select id="getChildPermission" parameterType="java.lang.Integer" resultType="com.maintain.server.vo.PermissionVo">
        SELECT * FROM permission WHERE parentId=#{parentId};
    </select>

    <select id="getFirstPermissionByUsername" parameterType="java.lang.String" resultType="com.maintain.server.vo.PermissionVo">
        select `name`,pathUrl from permission p where parentId is null and
        id in (select permission_id from role_permission
        where role_id in (select role_id from user_role
        where user_id= (select id from user where name=#{username})))
        order by p.order asc
    </select>

    <select id="findUserById" parameterType="java.lang.Integer" resultType="com.maintain.server.vo.UserVo">
        SELECT u.ID,u.NAME,DATE_FORMAT(u.create_time, '%Y-%m-%d %H:%i:%s') createTime,
        DATE_FORMAT(u.last_login_time, '%Y-%m-%d %H:%i:%s') lastLoginTime FROM user WHERE id=#{userId}
    </select>

    <select id="getAllPermissionByUsername" parameterType="java.lang.String"
            resultType="com.maintain.server.vo.PermissionVo">
        select * from permission where id in (
        select permission_id from role_permission where role_id in(
        select role_id from user_role where user_id=(select id from user where name=#{name})))
    </select>

    <select id="getAllPermissionByUserId" parameterType="java.lang.Integer"
            resultType="com.maintain.server.vo.PermissionVo">

        SELECT * FROM permission WHERE id IN(
          SELECT permission_id FROM role_permission WHERE role_id in(
            SELECT role_id from user_role WHERE user_id=#{userId}
          )
        )
    </select>

    <insert id="insertRolesOfUser" parameterType="com.maintain.server.vo.UserVo">
        INSERT INTO user_role(user_id,role_id)
        VALUES
        <foreach collection="roles" item="role" separator=",">
            (#{id}, #{role.id})
        </foreach>
    </insert>

    <insert id="insertPermissionOfRole" parameterType="com.maintain.server.vo.RoleVo">
        INSERT INTO role_permission(role_id,permission_id)
        VALUES
        <foreach collection="permissions" item="p" separator=",">
            (#{id},#{p.id})
        </foreach>
    </insert>

    <insert id="insertUser" keyProperty="id" useGeneratedKeys="true" parameterType="com.maintain.server.vo.UserVo">
        INSERT INTO user(name, pswd, create_time)
        VALUES(#{name}, #{pswd}, now())
    </insert>

    <update id="updateUser" parameterType="com.maintain.server.vo.UserVo">
        UPDATE user
        <set>
            <if test="pswd!=null">
                pswd=#{pswd}
            </if>
            <if test="lastLoginTime!=null">
                last_login_time=#{lastLoginTime}
            </if>
        </set>
        <choose>
            <when test="id!=null">
                WHERE id=#{id}
            </when>
            <otherwise>
                WHERE name=#{name}
            </otherwise>
        </choose>
    </update>

    <delete id="deleteUser" parameterType="java.lang.Integer">
        DELETE FROM user WHERE id=#{id}
    </delete>

    <delete id="deleteRolesOfUser" parameterType="java.lang.Integer">
        DELETE FROM user_role WHERE user_id=#{userId}
    </delete>

    <delete id="deletePermissionOfRole" parameterType="java.lang.Integer">
        DELETE FROM role_permission WHERE role_id=#{roleId}
    </delete>

    <insert id="addDynamicAndStatic" keyProperty="id" useGeneratedKeys="true" parameterType="com.maintain.server.vo.DynamicAndStaVoticThresholdVo">
        INSERT INTO dynamic_static_threshold(analysis_threshold, comment, creator, create_time, update_time)
        VALUES( #{analysisThreshold}, #{comment}, #{creator}, now(), now())
    </insert>

    <update id="updateDynamicAndStatic" parameterType="com.maintain.server.vo.DynamicAndStaVoticThresholdVo">
        UPDATE dynamic_static_threshold SET
        update_time = now()
        <if test="analysisThreshold!=null">
            , analysis_threshold = #{analysisThreshold}
        </if>
        <if test="comment!=null">
            , comment = #{comment}
        </if>
        <if test="creator!=null">
            , creator = #{creator}
        </if>
        <where>
            <if test="id!=null">
                id = #{id}
            </if>
        </where>
    </update>


    <select id="findThreshold" parameterType="com.maintain.server.criteria.DynamicAndStaticCriteria" resultMap="ThresholdVo_RESULT">
        SELECT id,analysis_threshold,comment,creator,create_time,update_time
        FROM dynamic_static_threshold
        <where>
            1 = 1
            <if test="id!=null">
              AND id = #{id}
            </if>
        </where>
    </select>

    <select id="getByName" parameterType="com.maintain.server.criteria.UserCriteria" resultType="com.maintain.server.vo.UserVo">
        select name,pswd from user where name=#{name} limit 1
    </select>
</mapper>
