<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.DynamicAndStaticMonitorMapper">
    <resultMap type="com.maintain.server.vo.DynamicAndStaticMonitorVo" id="DYNAMICANDSTATICMONITOR_RESULT">
        <id property="id" column="id"/>
        <result property="monitorTime" column="monitor_time"/>
        <result property="hisEmlNotAnalysis" column="his_eml_not_analysis"/>
        <result property="emlNotAnalysis" column="eml_not_analysis"/>
        <result property="hisEmlAnalysis" column="his_eml_analysis"/>
        <result property="emlAnalysis" column="eml_analysis"/>
        <result property="analysisType" column="analysis_type"/>
        <result property="hisSuccessNum" column="his_success_num"/>
        <result property="successNum" column="success_num"/>
        <result property="hisFailNum" column="his_fail_num"/>
        <result property="failNum" column="fail_num"/>
        <result property="hisNotAnalysisNum" column="his_not_analysis_num"/>
        <result property="notAnalysisNum" column="not_analysis_num"/>
        <result property="failAnalysisThreshold" column="fail_analysis_threshold"/>
        <result property="analysisThreshold" column="analysis_threshold"/>
        <result property="status" column="status"/>
        <result property="desc" column="desc"/>
    </resultMap>


    <insert id="addDynamicAndStaticMonitor" keyProperty="id" useGeneratedKeys="true" parameterType="com.maintain.server.vo.DynamicAndStaticMonitorVo">
        INSERT INTO dynamic_static_monitor(monitor_time,his_eml_not_analysis,eml_not_analysis,his_eml_analysis,eml_analysis, analysis_type,his_success_num, success_num, his_fail_num,fail_num ,his_not_analysis_num,not_analysis_num, fail_analysis_threshold, analysis_threshold, `status`, `desc`)
        VALUES(#{monitorTime},#{hisEmlNotAnalysis},#{emlNotAnalysis},#{hisEmlAnalysis},#{emlAnalysis} ,#{analysisType}, #{hisSuccessNum},#{successNum}, #{hisFailNum},#{failNum},#{hisNotAnalysisNum}, #{notAnalysisNum}, #{failAnalysisThreshold}, #{analysisThreshold}, #{status}, #{desc})
    </insert>

    <update id="updateDynamicAndStaticMonitor" parameterType="com.maintain.server.vo.DynamicAndStaticMonitorVo">
        UPDATE dynamic_static_monitor SET
        analysis_type = #{analysisType}
        <if test="monitorTime!=null">
            , monitor_time = #{monitorTime}
        </if>
        <if test="hisEmlNotAnalysis!=null">
            , his_eml_not_analysis = #{hisEmlNotAnalysis}
        </if>
        <if test="emlNotAnalysis!=null">
            , eml_not_analysis = #{emlNotAnalysis}
        </if>
        <if test="hisEmlAnalysis!=null">
            , his_eml_analysis = #{hisEmlAnalysis}
        </if>
        <if test="emlAnalysis!=null">
            , eml_analysis = #{emlAnalysis}
        </if>
        <if test="hisSuccessNum!=null">
            , his_success_num = #{hisSuccessNum}
        </if>
        <if test="successNum!=null">
            , success_num = #{successNum}
        </if>
        <if test="hisFailNum!=null">
            , his_fail_num = #{hisFailNum}
        </if>
        <if test="failNum!=null">
            , fail_num = #{failNum}
        </if>
        <if test="hisNotAnalysisNum!=null">
            , his_not_analysis_num = #{hisNotAnalysisNum}
        </if>
        <if test="notAnalysisNum!=null">
            , not_analysis_num = #{notAnalysisNum}
        </if>
        <if test="failAnalysisThreshold!=null">
            , fail_analysis_threshold = #{failAnalysisThreshold}
        </if>
        <if test="analysisThreshold!=null">
            , analysis_threshold = #{analysisThreshold}
        </if>
        <if test="status!=null">
            , `status` = #{status}
        </if>
        <if test="status!=null">
            , `desc` = #{desc}
        </if>
        <where>
            <if test="id!=null">
                id = #{id}
            </if>
        </where>
    </update>


    <select id="findDynamicAndStaticMonitor" parameterType="com.maintain.server.criteria.DynamicAndStaticMonitorCriteria" resultMap="DYNAMICANDSTATICMONITOR_RESULT">
        SELECT id,DATE_FORMAT(monitor_time,'%Y-%m-%d %H:%i:%s') monitor_time,his_eml_not_analysis,eml_not_analysis,his_eml_analysis,eml_analysis,analysis_type,his_success_num,success_num,his_fail_num,fail_num,his_not_analysis_num,not_analysis_num,fail_analysis_threshold,analysis_threshold,`status`,`desc`
        FROM dynamic_static_monitor
        <where>
            1 = 1
            <if test="id!=null">
                AND id = #{id}
            </if>
            <if test="analysisType!=null">
                AND analysis_type=#{analysisType}
            </if>
            <if test="status!=null">
                AND `status`=#{status}
            </if>
            <if test="startTime!=null">
                AND monitor_time &gt;= #{startTime}
            </if>
            <if test="endTime!=null">
                AND monitor_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY id desc
    </select>

    <delete id="deleteHistoryTableInfo">
        DELETE FROM ${tableName} WHERE ${timeField} &lt;= #{time}
    </delete>
</mapper>
