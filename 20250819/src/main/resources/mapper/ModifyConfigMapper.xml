<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.ModifyConfigMapper">
    <resultMap type="com.maintain.server.vo.ModifyConfigVo" id="RESULT">
        <result property="id" column="id"/>
        <result property="deployPath" column="deploy_path"/>
        <result property="programName" column="program_name"/>
        <result property="configPath" column="config_path"/>
    </resultMap>

    <select id="queryModifyConfig" parameterType="com.maintain.server.criteria.ModifyConfigCriteria" resultMap="RESULT">
        SELECT
        id,deploy_path,program_name,config_path
        FROM modify_config
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="programName != null">
                program_name = #{programName}
            </if>
        </where>
    </select>

    <insert id="addModifyConfig" parameterType="com.maintain.server.criteria.ModifyConfigCriteria">
        INSERT INTO modify_config (deploy_path,program_name,config_path)
        VALUES
        (#{deployPath},#{programName},#{configPath})
    </insert>

    <delete id="deleteModifyConfig" parameterType="com.maintain.server.criteria.ModifyConfigCriteria">
        delete from modify_config
        <where>
            <if test="id != null">
                id = #{id}
            </if>
        </where>
    </delete>

    <update id="updateModifyConfig" parameterType="com.maintain.server.criteria.ModifyConfigCriteria">
        UPDATE modify_config
        <set>
            <if test="ip != null">
              ip = #{ip},
            </if>
            <if test="deployPath != null">
              deploy_path = #{deployPath},
            </if>
            <if test="programName != null">
              program_name = #{programName},
            </if>
            <if test="configPath != null">
                config_path = #{configPath},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>
