<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.MysqlBackupMapper">
    <resultMap type="com.maintain.server.vo.MysqlBackupVo" id="MYSQLBACKUP_RESULT">
        <id property="id" column="id" />
        <result property="backupTime" column="backup_time"/>
        <result property="backupType" column="backup_type"/>
        <result property="backupDatabases" column="backup_databases"/>
        <result property="ip" column="ip"/>
        <result property="remoteSuccess" column="remote_success"/>
        <result property="ignoreStatus" column="ignore_status"/>
    </resultMap>


    <insert id="insertMysqlBackup" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO mysql_backup_info(backup_time,backup_type,backup_databases,success,ip,remote_success)
        VALUES
        <foreach collection="list" item="mysqlBackupVo" index="index" separator=",">
            (#{mysqlBackupVo.backupTime},#{mysqlBackupVo.backupType},#{mysqlBackupVo.backupDatabases},#{mysqlBackupVo.success},#{mysqlBackupVo.ip},#{mysqlBackupVo.remoteSuccess})
        </foreach>
    </insert>


    <select id="findMysqlBackup" resultMap="MYSQLBACKUP_RESULT"
            parameterType="com.maintain.server.criteria.MysqlBackupCriteria">
        SELECT id,DATE_FORMAT(backup_time,'%Y-%m-%d') backup_time,backup_type,backup_databases,success,ip,remote_success,ignore_status
        FROM mysql_backup_info
        <where>
            <if test="startTime!=null and endTime!=null">
                <![CDATA[backup_time >= #{startTime} AND backup_time <= #{endTime}]]>
            </if>
            <if test="backupType!=null">
                AND backup_type = #{backupType}
            </if>
            <if test="failedOrRemoteFailed != null and failedOrRemoteFailed == true">
                AND (success = 0 or remote_success = 0)
            </if>
            <if test="success!= null">
                AND success = #{success}
            </if>
            <if test="ignoreStatus != null">
                AND ignore_status = #{ignoreStatus}
            </if>
        </where>
        ORDER BY backup_time DESC
    </select>

    <select id="findByBackupTime" resultMap="MYSQLBACKUP_RESULT">
        SELECT id,DATE_FORMAT(backup_time,'%Y%m%d') backup_time,backup_type,backup_databases,success,ip,remote_success
        FROM mysql_backup_info
        <where>
            <if test="backupTime!=null and backupType!=null and backupDatabases!=null">
                <![CDATA[backup_time = #{backupTime} AND backup_type = #{backupType} AND backup_databases = #{backupDatabases}]]>
            </if>
        </where>
    </select>


    <update id="updateIgnoreStatusByIdList" parameterType="java.util.List">
        UPDATE mysql_backup_info set ignore_status=#{ignoreStatus}
        <where>
            id in
            <foreach collection="idList" item="id" index="i" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </update>

    <delete id="deleteRecords" parameterType="com.maintain.server.criteria.MysqlBackupCriteria">
        delete from mysql_backup_info
        <where>
            <if test="backupTime != null">
                AND backup_time = #{backupTime}
            </if>
            <if test="backupType != null">
                AND backup_type = #{backupType}
            </if>
            <if test="success != null">
                AND success = #{success}
            </if>
            <if test="remoteSuccess != null">
                AND remote_success = #{remoteSuccess}
            </if>
        </where>
    </delete>

</mapper>
