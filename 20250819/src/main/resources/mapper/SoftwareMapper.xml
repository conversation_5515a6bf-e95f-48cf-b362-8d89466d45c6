<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.SoftwareMapper">
    <resultMap type="com.maintain.server.vo.SoftwareVo" id="SOFTWARE_RESULT">
        <result property="id" column="ID"/>
        <result property="serverId" column="SERVER_ID"/>
        <result property="pid" column="PID"/>
        <result property="usedMemory" column="USED_MEMORY"/>
        <result property="totalMemory" column="TOTAL_MEMORY"/>
        <result property="memoryPercent" column="MEMORY_PERCENT"/>
        <result property="cpuPercent" column="CPU_PERCENT"/>
        <result property="diskPercent" column="DISK_PERCENT"/>
        <result property="totalDisk" column="TOTAL_DISK"/>
        <result property="host" column="IP"/>
        <result property="serverIp" column="IP"/>
        <!--  <result property="watchdogStatus" column="WATCH_DOG_STATUS"/> -->
        <!-- <result property="autoDaemon" column="AUTO_DAEMON"/> -->
        <result property="programSize" column="PROGRAM_SIZE"/>
        <result property="name" column="NAME"/>
        <result property="baseDir" column="BASE_DIR"/>
        <result property="version" column="VERSION"/>
        <result property="realDir" column="REAL_DIR"/>
        <result property="dbLogInfo" column="LOG_INFO"/>
        <result property="note" column="NOTE"/>
        <result property="dbConfInfo" column="CONF_INFO"/>
        <result property="dbCommonInfo" column="COMMON_INFO"/>
        <result property="dbSelfInfo" column="SELF_INFO"/>
        <result property="lastUpdateTime" column="LAST_HEARTBEAT_TIME"/>
        <result property="lastHeartbeatTime" column="LAST_HEARTBEAT_TIME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="startParam" column="START_PARAM"/>
        <result property="role" column="ROLE"/>
        <result property="startType" typeHandler="com.maintain.server.type.OperateTypeHandler" column="OPERATE_TYPE"/>
        <result property="programStatus" typeHandler="com.maintain.server.type.ProgramStatusTypeHandler" column="PROGRAM_STATUS"/>
        <result property="restartCount" column="RESTART_COUNT"/>
        <result property="restartTime" column="RESTART_TIME"/>
        <result property="status" typeHandler="com.maintain.server.type.AlarmStatusTypeHandler" column="STATUS"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="logCount" column="LOG_COUNT"/>
        <result property="isExists" column="IS_EXISTS"/>
        <result property="processCount" column="PROCESS_COUNT"/>
        <result property="heartMonitor" column="HEART_MONITOR"/>
        <result property="config" column="CONFIG"/>
        <result property="keys" column="SOFTWARE_KEYS"/>
        <result property="scriptPath" column="SCRIPT_PATH"/>
        <result property="script" column="SCRIPT"/>
        <result property="closeScript" column="CLOSE_SCRIPT"/>
        <result property="maxHeapSize" column="HEAP_SIZE"/>
        <result property="minHeapSize" column="MIN_HEAP_SIZE"/>
        <result property="logPath" column="LOG_PATH"/>
        <result property="logSuffix" column="LOG_SUFFIX"/>
        <result property="configPath" column="CONFIG_PATH"/>
    </resultMap>

    <resultMap type="com.maintain.server.vo.BusinessMonitorVo" id="BUSINESS_MONITOR_RESULT">
        <result property="id" column="ID"/>
        <result property="ip" column="IP"/>
        <result property="softwareName" column="SOFTWARE_NAME"/>
        <result property="startTime" column="START_TIME"/>
        <result property="endTime" column="END_TIME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="version" column="LAST_HEARTBEAT_TIME"/>
        <collection property="data" ofType="com.maintain.server.vo.BusinessMonitorVo$BusinessData">
            <result property="module" column="MODULE"/>
            <result property="content" column="CONTENT"/>
            <result property="result" column="RESULT"/>
            <result property="description" column="DESCRIPTION"/>
        </collection>
    </resultMap>

    <resultMap type="com.maintain.server.vo.SoftwareInfoVo" id="SOFTWARE_INFO_RESULT">
        <result property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="pid" column="PID"/>
        <result property="programStatus" column="PROGRAM_STATUS"/>
        <result property="restartCount" column="RESTART_COUNT"/>
        <result property="operateType" column="OPERATE_TYPE"/>
        <result property="restartTime" column="RESTART_TIME"/>
        <result property="logCount" column="LOG_COUNT"/>
        <result property="realDir" column="REAL_DIR"/>
        <result property="processCount" column="PROCESS_COUNT"/>
        <result property="maxHeapSize" column="HEAP_SIZE"/>
        <result property="minHeapSize" column="MIN_HEAP_SIZE"/>
        <result property="config" column="CONFIG"/>
        <result property="keys" column="SOFTWARE_KEYS"/>
        <result property="scriptPath" column="SCRIPT_PATH"/>
        <result property="script" column="SCRIPT"/>
        <result property="closeScript" column="CLOSE_SCRIPT"/>
        <result property="heartMonitor" column="HEART_MONITOR" />
        <result property="logPath" column="LOG_PATH" />
        <result property="logSuffix" column="LOG_SUFFIX" />
        <result property="configPath" column="CONFIG_PATH" />
    </resultMap>

    <sql id="base-column">
        ID,SERVER_ID,PID,<!--WATCH_DOG_STATUS,-->
        PROGRAM_SIZE,NAME,VERSION,BASE_DIR,REAL_DIR,LOG_INFO,CONF_INFO,COMMON_INFO,SELF_INFO,LAST_HEARTBEAT_TIME,CREATE_TIME
        ,USED_MEMORY,TOTAL_MEMORY,CPU_PERCENT,START_PARAM,MEMORY_PERCENT,DISK_PERCENT,HEART_MONITOR,NOTE,PROGRAM_STATUS,STATUS,DESCRIPTION
        ,CONFIG,SOFTWARE_KEYS,SCRIPT_PATH,SCRIPT,HEAP_SIZE,MIN_HEAP_SIZE,PROCESS_COUNT,CLOSE_SCRIPT,LOG_PATH,LOG_SUFFIX,CONFIG_PATH
    </sql>



    <insert id="addSoftwareInfo" parameterType="com.maintain.server.vo.SoftwareVo">
        INSERT INTO software_info (<include refid="base-column"/>)
        VALUES (#{id},#{serverId},#{pid},#{programSize},#{name},#{version},#{baseDir},#{realDir},#{dbLogInfo},#{dbConfInfo},
        #{dbCommonInfo},#{dbSelfInfo},#{lastUpdateTime},now(),#{usedMemory},#{totalMemory},#{cpuPercent},#{startParam},#{memoryPercent},#{diskPercent},#{heartMonitor},#{note},#{programStatus.value},#{status.id},#{description}
        ,#{config},#{keys},#{scriptPath},#{script},#{maxHeapSize},#{minHeapSize},#{processCount},#{closeScript},#{logPath},#{logSuffix},#{configPath})
    </insert>

    <update id="updateSoftwareInfo" parameterType="com.maintain.server.vo.SoftwareVo">
        UPDATE software_info SET
        NAME = NAME
        <if test="pid != null">
            <choose>
                <when test="pid == -1">
                    ,LAST_HEARTBEAT_TIME = #{lastUpdateTime}
                </when>
                <otherwise>
                    ,LAST_HEARTBEAT_TIME = NOW()
                </otherwise>
            </choose>
        </if>

        <if test="pid!=null">
            ,PID = #{pid}
        </if>
        <if test="memoryPercent!=null">
            ,MEMORY_PERCENT = #{memoryPercent}
        </if>
        <if test="cpuPercent!=null">
            ,CPU_PERCENT = #{cpuPercent}
        </if>
        <if test="startParam!=null">
            ,START_PARAM = #{startParam}
        </if>
        <if test="usedMemory!=null">
            ,USED_MEMORY = #{usedMemory}
        </if>
        <if test="totalMemory!=null">
            ,TOTAL_MEMORY=#{totalMemory}
        </if>
        <if test="programSize!=null">
            ,PROGRAM_SIZE = #{programSize}
        </if>
        <if test="name!=null">
            ,NAME = #{name}
        </if>
        <if test="version!=null">
            ,VERSION = #{version}
        </if>
        <if test="baseDir!=null">
            ,BASE_DIR = #{baseDir}
        </if>
        <if test="realDir!=null">
            ,REAL_DIR = #{realDir}
        </if>
        <if test="dbLogInfo!=null">
            ,LOG_INFO = #{dbLogInfo}
        </if>
        <if test="dbConfInfo!=null">
            ,CONF_INFO = #{dbConfInfo}
        </if>
        <if test="dbCommonInfo!=null">
            ,COMMON_INFO = #{dbCommonInfo}
        </if>
        <if test="dbSelfInfo!=null">
            ,SELF_INFO = #{dbSelfInfo}
        </if>
        <if test="diskPercent!=null">
            ,DISK_PERCENT = #{diskPercent}
        </if>
        <if test="startType!=null">
            ,OPERATE_TYPE = #{startType.value}
        </if>
        <if test="programStatus!=null">
            ,PROGRAM_STATUS = #{programStatus.value}
        </if>
        <if test="status!=null">
            ,STATUS = #{status.id}
        </if>
        <if test="restartCount!=null">
            ,RESTART_COUNT = #{restartCount}
        </if>
        <if test="restartTime!=null">
            ,RESTART_TIME = #{restartTime}
        </if>
        <if test="description!=null">
            ,DESCRIPTION = #{description}
        </if>
        <if test="heartMonitor!=null">
            ,HEART_MONITOR = #{heartMonitor}
        </if>
        <if test="note!=null">
            ,NOTE = #{note}
        </if>
        <if test="config!=null">
            ,CONFIG = #{config}
        </if>
        <if test="keys!=null">
            ,SOFTWARE_KEYS = #{keys}
        </if>
        <if test="scriptPath!=null">
            ,SCRIPT_PATH = #{scriptPath}
        </if>
        <if test="script!=null">
            ,SCRIPT = #{script}
        </if>
        <if test="maxHeapSize!=null">
            ,HEAP_SIZE = #{maxHeapSize}
        </if>
        <if test="minHeapSize!=null">
            ,MIN_HEAP_SIZE = #{minHeapSize}
        </if>
        <if test="closeScript!=null">
            ,CLOSE_SCRIPT = #{closeScript}
        </if>
        where SERVER_ID = #{serverId} AND NAME = #{name}
    </update>

    <update id="updateSoftware" parameterType="com.maintain.server.vo.SoftwareVo">
        UPDATE software_info SET
        ID = #{id}
        <if test="name!=null">
            ,NAME = #{name}
        </if>
        <if test="baseDir!=null">
            ,BASE_DIR = #{baseDir}
        </if>
        <if test="realDir!=null">
            ,REAL_DIR = #{realDir}
        </if>
        <if test="processCount!=null">
            ,PROCESS_COUNT = #{processCount}
        </if>
        <if test="heartMonitor!=null">
            ,HEART_MONITOR = #{heartMonitor}
        </if>
        <if test="config!=null">
            ,CONFIG = #{config}
        </if>
        <if test="keys!=null">
            ,SOFTWARE_KEYS = #{keys}
        </if>
        <if test="scriptPath!=null">
            ,SCRIPT_PATH = #{scriptPath}
        </if>
        <if test="script!=null">
            ,SCRIPT = #{script}
        </if>
        <if test="maxHeapSize!=null">
            ,HEAP_SIZE = #{maxHeapSize}
        </if>
        <if test="minHeapSize!=null">
            ,MIN_HEAP_SIZE = #{minHeapSize}
        </if>
        <if test="closeScript!=null">
            ,CLOSE_SCRIPT = #{closeScript}
        </if>
        <if test="logPath!=null">
            ,LOG_PATH = #{logPath}
        </if>
        <if test="logSuffix!=null">
            ,LOG_SUFFIX = #{logSuffix}
        </if>
        <if test="configPath!=null">
            ,CONFIG_PATH = #{configPath}
        </if>
        where ID = #{id}
    </update>

    <update id="updateSoftwareMonitorInfo" parameterType = "com.maintain.server.vo.SoftwareInfoVo">
        UPDATE software_info SET
        RESTART_COUNT = #{restartCount}
        <if test="pid!=null">
            ,PID = #{pid}
        </if>
        <if test="operateType!=null">
            ,OPERATE_TYPE = #{operateType}
        </if>
        <if test="restartTime!=null">
            ,RESTART_TIME = #{restartTime}
        </if>
        <if test="logCount!=null">
            ,LOG_COUNT = #{logCount}
        </if>
        <if test="isExists!=null">
            ,IS_EXISTS = #{isExists}
        </if>
        <if test="maxHeapSize!=null">
            ,HEAP_SIZE = #{maxHeapSize}
        </if>
        <if test="minHeapSize!=null">
            ,MIN_HEAP_SIZE = #{minHeapSize}
        </if>
        where ID = #{id}
    </update>

    <select id="getSoftwareInfos" parameterType="com.maintain.server.criteria.SoftwareCriteria" resultMap="SOFTWARE_RESULT">
        SELECT
        software_info.ID,SERVER_ID,software_info.PID AS PID,software_info.MEMORY_PERCENT,SOFTWARE_INFO.CPU_PERCENT,
        PROGRAM_SIZE,software_info.NAME AS NAME ,software_info.VERSION AS
        VERSION,BASE_DIR,LOG_INFO,CONF_INFO,COMMON_INFO,SELF_INFO,software_info.LAST_HEARTBEAT_TIME AS
        LAST_HEARTBEAT_TIME,software_info.CREATE_TIME AS CREATE_TIME
        ,USED_MEMORY,TOTAL_MEMORY,IP ,START_PARAM,ROLE,software_info.REAL_DIR,software_info.STATUS,software_info.OPERATE_TYPE,
        software_info.RESTART_COUNT,software_info.RESTART_TIME,software_info.LOG_COUNT,software_info.IS_EXISTS,software_info.DESCRIPTION,software_info.PROGRAM_STATUS,
        software_info.PROCESS_COUNT,software_info.HEART_MONITOR,software_info.NOTE,software_info.CONFIG,software_info.SOFTWARE_KEYS,software_info.SCRIPT_PATH
        ,software_info.SCRIPT,software_info.HEAP_SIZE,software_info.MIN_HEAP_SIZE,software_info.CLOSE_SCRIPT,
        software_info.LOG_PATH,software_info.LOG_SUFFIX,software_info.CONFIG_PATH
        FROM software_info LEFT JOIN AGENT ON software_info.SERVER_ID = AGENT.ID
        <where>
            1 = 1
            <if test="id!=null">
                AND software_info.ID = #{id}
            </if>
            <if test="serverId!=null">
                AND SERVER_ID = #{serverId}
            </if>
            <if test="name!=null">
                AND software_info.NAME = #{name}
            </if>
            <if test="status!=null">
                AND software_info.STATUS = #{status}
            </if>
            <if test="programStatus!=null">
                AND software_info.PROGRAM_STATUS = #{programStatus}
            </if>
            <if test="fuzzyName!=null">
                AND software_info.NAME like CONCAT('%',#{fuzzyName},'%')
            </if>
            <if test="notName!=null">
                AND software_info.NAME not like CONCAT('%',#{notName},'%')
            </if>
            <if test="startTime!=null and endTime!=null">
                AND software_info.CREATE_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="names!=null and !names.isEmpty()">
                AND software_info.NAME IN
                <foreach collection="names" item="name" open="(" close=")" separator=",">
                    #{name}
                </foreach>
            </if>
            <if test="notNames!=null and !notNames.isEmpty()">
                AND software_info.NAME NOT IN
                <foreach collection="notNames" item="name" open="(" close=")" separator=",">
                    #{name}
                </foreach>
            </if>
            <if test="platformFlag!=null">
                AND software_info.OPERATE_PLATFORM=#{platformFlag}
            </if>
            AND software_info.NAME != 'Web'
        </where>
        ORDER BY AGENT.IP asc ,STATUS desc
    </select>

    <select id="getSoftwareInfoList" parameterType="String" resultMap="SOFTWARE_INFO_RESULT">
        SELECT s.HEART_MONITOR,s.ID,s.NAME,s.PID,s.PROGRAM_STATUS,s.RESTART_COUNT,s.OPERATE_TYPE,s.RESTART_TIME,s.LOG_COUNT,s.PROCESS_COUNT,s.REAL_DIR,s.CONFIG,s.SOFTWARE_KEYS,s.SCRIPT_PATH,s.SCRIPT,s.HEAP_SIZE,s.MIN_HEAP_SIZE,s.CLOSE_SCRIPT,s.LOG_PATH,s.LOG_SUFFIX,s.CONFIG_PATH
        FROM agent a
        INNER JOIN software_info s
        ON a.ID = s.SERVER_ID
        <if test="ip != null">
            WHERE a.IP = #{ip}
        </if>
    </select>

    <select id="getSoftware" parameterType="Integer" resultMap="SOFTWARE_RESULT">
        select s.ID,s.NAME,a.IP,s.BASE_DIR,s.REAL_DIR,s.RESTART_COUNT,s.LOG_COUNT,s.PROCESS_COUNT,s.HEART_MONITOR,s.CONFIG,s.SOFTWARE_KEYS,s.SCRIPT_PATH,s.SCRIPT,s.HEAP_SIZE,s.MIN_HEAP_SIZE,s.CLOSE_SCRIPT
        from software_info s inner join agent a on a.ID = s.SERVER_ID
        where s.ID = #{param}
    </select>

    <select id="listAllSoftwares" parameterType="com.maintain.server.criteria.SoftwareCriteria"
            resultMap="SOFTWARE_RESULT">
        select a.IP,a.ROLE,s.ID,s.NAME,s.LAST_HEARTBEAT_TIME,s.MEMORY_PERCENT,s.CPU_PERCENT,s.PID,s.BASE_DIR,s.REAL_DIR from software_info s
        left join agent a on a.id=s.SERVER_ID
        WHERE S.NAME NOT IN("MaintainAgent","maintain-agent")
        <if test="id!=null">
            S.ID = #{id}
        </if>
        <if test="serverId!=null">
            AND SERVER_ID = #{serverId}
        </if>
        <if test="name!=null">
            AND S.NAME = #{name}
        </if>
        <if test="startTime!=null and endTime!=null">
            AND software_info.CREATE_TIME BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="names!=null">
            AND S.NAME IN
            <foreach collection="names" item="name" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        order by A.IP
    </select>

    <select id="listSoftwaresResource" parameterType="com.maintain.server.criteria.SoftwareCriteria"
            resultMap="SOFTWARE_RESULT">
        select s.ID,s.SERVER_ID,s.NAME,s.PROGRAM_SIZE,
        s.MEMORY_PERCENT,s.CPU_PERCENT,h.TOTAL_DISK,
        s.LAST_HEARTBEAT_TIME
        from software_info s LEFT JOIN HARDWARE_INFO h on s.SERVER_ID=h.AGENT_ID
        <where>
            <if test="startTime!=null and endTime!=nul">
                s.LAST_HEARTBEAT_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>



    <sql id="business-monitor-column">
        ID,IP,SOFTWARE_NAME,MODULE,CONTENT,RESULT,DESCRIPTION,START_TIME,END_TIME,CREATE_TIME,VERSION
    </sql>

    <insert id="addBusinessMonitor" parameterType="com.maintain.server.vo.BusinessMonitorVo">
        INSERT INTO BUSINESS_MONITOR (<include refid="business-monitor-column"/>)
        VALUES
        <foreach collection="data" item="item" separator=",">
            (#{id},#{ip},#{softwareName},#{item.module},#{item.content},#{item.result},#{item.description},#{startTime},#{endTime},#{createTime},#{version})
        </foreach>
    </insert>

    <select id="getBusinessMonitor" parameterType="com.maintain.server.criteria.BusinessMonitorCriteria"
            resultMap="BUSINESS_MONITOR_RESULT">
        SELECT
        <include refid="business-monitor-column"/>
        FROM BUSINESS_MONITOR
        WHERE SOFTWARE_NAME = #{softwareName} AND IP = #{ip}
        ORDER BY CREATE_TIME DESC
    </select>

    <delete id="deleteSoftware" parameterType="com.maintain.server.vo.SoftwareVo">
        delete from software_info
        where id = #{id}
    </delete>

    <update id="batchUpdatePlatformFlag">
        UPDATE software_info SET OPERATE_PLATFORM=#{flag},PROGRAM_STATUS=#{programStatus}
        WHERE ID in
        <foreach collection="idList" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>