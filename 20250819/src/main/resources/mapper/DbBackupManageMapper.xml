<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.DbBackupManageMapper">
    <resultMap id="BaseResultMap" type="com.maintain.server.vo.DbBackupManageVo">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="LOCAL_PATH" jdbcType="VARCHAR" property="localPath"/>
        <result column="LOCAL_IP" jdbcType="VARCHAR" property="localIp"/>
        <result column="DB_USER" jdbcType="VARCHAR" property="dbUser"/>
        <result column="DB_PASS" jdbcType="VARCHAR" property="dbPass"/>
        <result column="REMOTE_PATH" jdbcType="VARCHAR" property="remotePath"/>
        <result column="REMOTE_IP" jdbcType="VARCHAR" property="remoteIp"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, LOCAL_PATH, LOCAL_IP, DB_USER, DB_PASS, REMOTE_PATH, REMOTE_IP
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from db_backup
        where ID = #{id,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from db_backup
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.maintain.server.vo.DbBackupManageVo">
        insert into db_backup (ID, LOCAL_PATH, LOCAL_IP,
        DB_USER, DB_PASS, REMOTE_PATH,
        REMOTE_IP)
        values (#{id,jdbcType=INTEGER}, #{localPath,jdbcType=VARCHAR}, #{localIp,jdbcType=VARCHAR},
        #{dbUser,jdbcType=VARCHAR}, #{dbPass,jdbcType=VARCHAR}, #{remotePath,jdbcType=VARCHAR},
        #{remoteIp,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective" parameterType="com.maintain.server.vo.DbBackupManageVo" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into db_backup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="localPath != null">
                LOCAL_PATH,
            </if>
            <if test="localIp != null">
                LOCAL_IP,
            </if>
            <if test="dbUser != null">
                DB_USER,
            </if>
            <if test="dbPass != null">
                DB_PASS,
            </if>
            <if test="remotePath != null">
                REMOTE_PATH,
            </if>
            <if test="remoteIp != null">
                REMOTE_IP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="localPath != null">
                #{localPath,jdbcType=VARCHAR},
            </if>
            <if test="localIp != null">
                #{localIp,jdbcType=VARCHAR},
            </if>
            <if test="dbUser != null">
                #{dbUser,jdbcType=VARCHAR},
            </if>
            <if test="dbPass != null">
                #{dbPass,jdbcType=VARCHAR},
            </if>
            <if test="remotePath != null">
                #{remotePath,jdbcType=VARCHAR},
            </if>
            <if test="remoteIp != null">
                #{remoteIp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.maintain.server.vo.DbBackupManageVo">
        update db_backup
        <set>
            <if test="localPath != null">
                LOCAL_PATH = #{localPath,jdbcType=VARCHAR},
            </if>
            <if test="localIp != null">
                LOCAL_IP = #{localIp,jdbcType=VARCHAR},
            </if>
            <if test="dbUser != null">
                DB_USER = #{dbUser,jdbcType=VARCHAR},
            </if>
            <if test="dbPass != null">
                DB_PASS = #{dbPass,jdbcType=VARCHAR},
            </if>
            <if test="remotePath != null">
                REMOTE_PATH = #{remotePath,jdbcType=VARCHAR},
            </if>
            <if test="remoteIp != null">
                REMOTE_IP = #{remoteIp,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.maintain.server.vo.DbBackupManageVo">
        update db_backup
        set LOCAL_PATH = #{localPath,jdbcType=VARCHAR},
        LOCAL_IP = #{localIp,jdbcType=VARCHAR},
        DB_USER = #{dbUser,jdbcType=VARCHAR},
        DB_PASS = #{dbPass,jdbcType=VARCHAR},
        REMOTE_PATH = #{remotePath,jdbcType=VARCHAR},
        REMOTE_IP = #{remoteIp,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectList" parameterType="com.maintain.server.criteria.DbBackupManageCriteria"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from db_backup
    </select>
</mapper>