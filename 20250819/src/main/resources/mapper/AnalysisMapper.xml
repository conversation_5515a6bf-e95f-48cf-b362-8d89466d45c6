<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.AnalysisMapper">
    <resultMap type="com.maintain.server.vo.AnalysisVo" id="ANA_RESULT">
        <result property="id" column="id"/>
        <result property="status" typeHandler="com.maintain.server.type.AlarmStatusTypeHandler" column="status"/>
        <result property="ip" column="ip"/>
        <result property="process" column="process"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getAnalysis" parameterType="String" resultMap="ANA_RESULT">
        select * from analysis where ip=#{param}
    </select>

    <select id="getAnalysisList" resultMap="ANA_RESULT">
        select * from analysis where update_time between #{startTime} and #{endTime}
    </select>

    <insert id="addAnalysis" parameterType="com.maintain.server.vo.AnalysisVo">
        insert into analysis(ip,status,process,update_time) values(#{ip},#{status.id},#{process},#{updateTime})
    </insert>

    <update id="updateAnalysis" parameterType="com.maintain.server.vo.AnalysisVo">
        update analysis set update_time=now()
        <if test="status != null">,status=#{status.id}</if>
        <if test="status != null">,process=#{process}</if>
    </update>

    <delete id="deleteAnalysis" parameterType="com.maintain.server.vo.AnalysisVo">
        delete from analysis where ip=#{ip}
    </delete>

</mapper>
