<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.PlatformRoleMapper">
    <!--<resultMap type="" id="">
        <result property="" column=""/>
    </resultMap>-->

    <insert id="addPlatformRole" parameterType="java.util.List">
        INSERT IGNORE INTO platform_role (NAME) VALUES
        <foreach collection="names" item="name" separator=",">
            (#{name})
        </foreach>
    </insert>

    <select id="getPlatformRoles" resultType="java.lang.String">
        SELECT NAME FROM platform_role
    </select>
</mapper>
