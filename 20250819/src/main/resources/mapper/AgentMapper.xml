<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.AgentMapper">
    <resultMap type="com.maintain.server.vo.AgentVo" id="AGENT_RESULT">
        <result property="id" column="ID"/>
        <result property="status" typeHandler="com.maintain.server.type.AlarmStatusTypeHandler" column="STATUS"/>
        <result property="version" column="VERSION"/>
        <result property="programStatus" typeHandler="com.maintain.server.type.ProgramStatusTypeHandler" column="PROGRAM_STATUS"/>
        <result property="operateType" typeHandler="com.maintain.server.type.SoftwareOperateTypeHandler" column="OPERATE_TYPE"/>
        <result property="ip" column="IP"/>
        <result property="os" column="OS"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="lastHeartbeatTime" column="LAST_HEARTBEAT_TIME"/>
        <result property="role" column="ROLE"/>
        <result property="name" column="NAME"/>
        <result property="pswd" column="PSWD"/>
        <result property="rootPswd" column="ROOT_PSWD"/>
        <result property="port" column="PORT"/>
        <result property="autoNTP" column="AUTO_NTP"/>
        <result property="pid" column="PID"/>
        <result property="autoWatchDog" column="AUTO_WATCHDOG"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="note" column="NOTE"/>
        <result property="ntpStatus" column="NTP_STATUS"/>
        <result property="existsVpn" column="EXISTS_VPN"/>
    </resultMap>
    <sql id="base-column">
        ID,VERSION,IP,OS,CREATE_TIME,LAST_HEARTBEAT_TIME,ROLE,NAME,PSWD,ROOT_PSWD,PORT,AUTO_NTP,PID,AUTO_WATCHDOG,STATUS,OPERATE_TYPE,DESCRIPTION,PROGRAM_STATUS,NOTE,NTP_STATUS,EXISTS_VPN
    </sql>

    <select id="getAgentForPlugin" parameterType="String" resultMap="AGENT_RESULT">
        SELECT ID,VERSION,IP,OS,CREATE_TIME,LAST_HEARTBEAT_TIME,ROLE,PORT,PID
        FROM agent
        <if test="plugin != null">
            where ROLE like CONCAT('%',#{plugin},'%')
        </if>
    </select>

    <select id="getAgent" parameterType="com.maintain.server.criteria.AgentCriteria" resultMap="AGENT_RESULT">
        SELECT
          <include refid="base-column"/>
        FROM AGENT
        <where>
            <if test="ip!=null">
                IP = #{ip}
            </if>
            <if test="id!=null">
                AND ID = #{id}
            </if>
            <if test="ids!=null">
                AND ID IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="ips!=null">
                AND IP IN
                <foreach collection="ips" item="str" open="(" separator="," close=")">
                    #{str}
                </foreach>
            </if>
            <if test="roleFuzzy!=null">
                AND ROLE like CONCAT('%',#{roleFuzzy},'%')
            </if>
            <if test="programStatus!=null">
                AND PROGRAM_STATUS=#{programStatus}
            </if>
            <if test="status != null">
                AND STATUS=#{status}
            </if>
        </where>
        ORDER BY STATUS desc,ID asc
    </select>

    <select id="getPlatformRole" resultType="java.util.HashMap" parameterType="java.util.ArrayList">
        SELECT ID AS id,NAME AS name FROM PLATFORM_ROLE
        <where>
            <if test="ids!=null">
                ID IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="addAgent" parameterType="com.maintain.server.vo.AgentVo">
        INSERT INTO AGENT (<include refid="base-column"/>)
        VALUES
        (#{id},#{version},#{ip},#{os},now(),null,#{role},#{name},#{pswd},#{rootPswd},#{port},#{autoNTP},#{pid},#{autoWatchDog},#{status.id},#{operateType.id},#{description},#{programStatus.value},#{note},#{ntpStatus},#{existsVpn})
    </insert>

    <update id="updateAgent" parameterType="com.maintain.server.vo.AgentVo">
        UPDATE AGENT SET
        IP = #{ip}
        <if test="pid != null">
            <choose>
                <when test="pid != -1">
                    ,LAST_HEARTBEAT_TIME = now()
                </when>
                <otherwise>
                    ,LAST_HEARTBEAT_TIME = #{lastHeartbeatTime}
                </otherwise>
            </choose>
        </if>
        <if test="pid!=null">
            , PID = #{pid}
        </if>
        <if test="status!=null">
            , STATUS = #{status.id}
        </if>
        <if test="programStatus!=null">
            , PROGRAM_STATUS = #{programStatus.value}
        </if>
        <if test="operateType!=null">
            , OPERATE_TYPE = #{operateType.id}
        </if>
        <if test="description!=null">
            , DESCRIPTION = #{description}
        </if>
        <if test="version!=null">
            , VERSION = #{version}
        </if>
        <if test="role!=null">
            , ROLE = #{role}
        </if>
        <if test="note!=null">
            , NOTE = #{note}
        </if>
        <if test="ntpStatus!=null">
            , ntp_status = #{ntpStatus}
        </if>
        <if test="existsVpn!=null">
            , EXISTS_VPN = #{existsVpn}
        </if>
        <where>
            <if test="ip!=null">
                IP = #{ip}
            </if>
            <if test="ips!=null">
                AND IP IN
                <foreach collection="ips" item="str" open="(" separator="," close=")">
                    #{str}
                </foreach>
            </if>
        </where>
    </update>

    <select id="getAllHosts" resultType="java.util.HashMap" parameterType="com.maintain.server.criteria.AgentCriteria">
        select distinct ip,os from agent
        <where>
            <if test="ip!=null">
                ip = #{ip}
            </if>
        </where>
    </select>

    <delete id="deleteAgent" parameterType="String">
        delete from agent where ip = #{param}
    </delete>

    <select id="getIpRoleVoList" resultType="com.maintain.server.vo.IpRoleVo">
        select IP AS ip,ROLE AS role from agent
    </select>

</mapper>
