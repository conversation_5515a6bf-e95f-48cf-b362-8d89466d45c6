<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.PingResultMapper">

    <resultMap id="PING_RESULT" type="com.maintain.server.vo.PingResult">
        <id column="ID" property="id"/>
        <result column="CALL_TIME" property="callTime"/>
        <result column="START_TIME" property="startTime"/>
        <result column="SOURCE" property="source"/>
        <result column="DEST" property="dest"/>
        <result column="TIME_SPENT" property="timeSpent"/>
        <result column="CONNECT_FLAG" property="connectFlag"/>
    </resultMap>

    <select id="list" parameterType="com.maintain.server.criteria.PingResultCriteria" resultMap="PING_RESULT">
        select * from ping_agent_result
        <where>
            <if test="ip != null and ''!=ip">
                SOURCE=#{ip} or DEST=#{ip}
            </if>
        </where>
        order by START_TIME desc
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into ping_agent_result(CALL_TIME,START_TIME,SOURCE,DEST,TIME_SPENT,CONNECT_FLAG)
        values
        <foreach collection="results" item="item" separator=",">
            (#{item.callTime},#{item.startTime},#{item.source},#{item.dest},#{item.timeSpent},#{item.connectFlag})
        </foreach>
    </insert>

    <delete id="deleteOverdueData" parameterType="java.lang.String">
        delete from ping_agent_result where call_time &lt;= #{time}
    </delete>
</mapper>
