<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.AutoDeployMapper">
    <resultMap type="com.maintain.server.vo.DeployRecordVo" id="PLATFORM_RESULT">
        <result property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="version" column="VERSION"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="remark1" column="REMARK1"/>
        <result property="remark2" column="REMARK2"/>
        <result property="remark3" column="REMARK3"/>
        <result property="remark4" column="REMARK4"/>
        <result property="remark5" column="REMARK5"/>
        <result property="status" column="STATUS"/>
        <result property="modifyTime" column="MODIFY_TIME"/>
        <result property="configPath" column="CONFIG_PATH"/>
        <result property="product" column="PRODUCT"/>
        <result property="programPaths" column="PROGRAM_PATHS"/>
        <result property="fullPackage" column="FULL_PACKAGE"/>
        <result property="isModifyCommonConfig" column="IS_MODIFY_COMMON_CONFIG"/>
    </resultMap>

    <resultMap type="com.maintain.server.vo.AutoDeployTaskVo" id="TASK_RESULT">
        <result property="id" column="ID"/>
        <result property="ip" column="IP"/>
        <result property="name" column="NAME"/>
        <result property="product" column="PRODUCT"/>
        <result property="remark" column="REMARK"/>
        <result property="status" column="STATUS"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="modifyTime" column="MODIFY_TIME"/>
        <result property="sourcePath" column="SOURCE_PATH"/>
        <result property="targetPath" column="TARGET_PATH"/>
        <result property="needDeleteFiles" column="NEED_DELETE_FILES"/>
        <result property="isFullPackage" column="IS_FULL_PACKAGE"/>
        <result property="ignoreBackup" column="IGNORE_BACKUP"/>
        <result property="oldName" column="OLD_NAME"/>

    </resultMap>

    <sql id="platform-column">
        ID,NAME,VERSION,CREATE_TIME,STATUS,MODIFY_TIME,REMARK1,REMARK2,REMARK3,REMARK4,REMARK5,CONFIG_PATH,PRODUCT,PROGRAM_PATHS,FULL_PACKAGE,IS_MODIFY_COMMON_CONFIG
    </sql>

    <sql id="task-column">
        IP,NAME,PRODUCT,REMARK,STATUS,CREATE_TIME,MODIFY_TIME,SOURCE_PATH,TARGET_PATH,NEED_DELETE_FILES,IS_FULL_PACKAGE,IGNORE_BACKUP,OLD_NAME
    </sql>

  <!--  <select id="platformCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM deploy_record
    </select>-->

    <insert id="addDeployRecord" parameterType="com.maintain.server.vo.DeployRecordVo">
        INSERT INTO deploy_record (`NAME`,VERSION,CREATE_TIME,CONFIG_PATH,PRODUCT,PROGRAM_PATHS,FULL_PACKAGE)
        VALUES (#{name},#{version},now(),#{configPath},#{product},#{programPaths},#{fullPackage})
    </insert>


    <select id="getPlatformVersionInfo" parameterType="java.util.HashMap" resultMap="PLATFORM_RESULT">
        SELECT
        <include refid="platform-column"/>
        FROM deploy_record
        <where>
            <if test="param.platform_regular!=null">
                VERSION <![CDATA[${param.platform_regular}]]>
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="getDeployRecordInfo" parameterType="com.maintain.server.vo.DeployRecordVo" resultMap="PLATFORM_RESULT">
        SELECT
        <include refid="platform-column"/>
        FROM deploy_record
        <where>
            <if test="status!=null">
                and STATUS = ${status}
            </if>
            <if test="name!=null">
                and `NAME` = ${name}
            </if>
            <if test="version!=null">
                and VERSION = ${version}
            </if>
        </where>
    </select>

    <update id="lockDeployTask" parameterType="com.maintain.server.vo.DeployRecordVo">
        UPDATE deploy_record SET MODIFY_TIME = NOW()
        <if test="remark1!=null">,REMARK1 = #{remark1}</if>
        <if test="remark2!=null">,REMARK2 = #{remark2}</if>
        <if test="remark3!=null">,REMARK3 = #{remark3}</if>
        <if test="remark4!=null">,REMARK4 = #{remark4}</if>
        <if test="remark5!=null">,REMARK5 = #{remark5}</if>
        <if test="status!=null">,STATUS = #{status}</if>
        <if test="name!=null">,`NAME` = #{name}</if>
        <if test="version!=null">,VERSION = #{version}</if>
        <if test="configPath!=null">,CONFIG_PATH = #{configPath}</if>
        <if test="product!=null">,PRODUCT = #{product}</if>
        <if test="programPaths!=null">,PROGRAM_PATHS = #{programPaths}</if>
        <if test="fullPackage!=null">,FULL_PACKAGE = #{fullPackage}</if>
        <if test="isModifyCommonConfig!=null">,IS_MODIFY_COMMON_CONFIG = #{isModifyCommonConfig}</if>
    </update>

    <insert id="addDeployTask" parameterType="java.util.List" useGeneratedKeys="false">
        INSERT INTO deploy_task (<include refid="task-column"/>)
        VALUES
        <!--  ID,IP,NAME,PRODUCT,DEPLOY_DIR,REMARK,STATUS,CREATE_TIME,MODIFY_TIME,SOURCE_PATH,TARGET_PATH,NEED_DELETE_FILES,OLD_NAME -->
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.ip},#{item.name},#{item.product},<![CDATA[""]]>,0,NOW(),NOW(),#{item.sourcePath},#{item.targetPath},#{item.needDeleteFiles},#{item.isFullPackage},#{item.ignoreBackup},#{item.oldName})
        </foreach>
    </insert>

    <update id="lockTask" parameterType="com.maintain.server.vo.AutoDeployTaskVo">
        UPDATE deploy_task SET STATUS = #{status},MODIFY_TIME = NOW()
        <if test="remark!=null">,REMARK = #{remark}</if>
        WHERE ID = #{id}
    </update>

    <select id="getAutoDeployTasks" parameterType="com.maintain.server.criteria.AutoDeployTaskCriteria"
            resultMap="TASK_RESULT">
        SELECT ID,NAME,IP,PRODUCT,REMARK,STATUS,CREATE_TIME,MODIFY_TIME,SOURCE_PATH,TARGET_PATH,NEED_DELETE_FILES,IS_FULL_PACKAGE,IGNORE_BACKUP,OLD_NAME FROM DEPLOY_TASK
        <where>
            <if test="id!=null">
                ID = #{id}
            </if>
            <if test="ip!=null">
                AND IP = #{ip}
            </if>
            <if test="status!=null">
                AND STATUS = #{status}
            </if>
            <if test="notInStatus!=null">
                AND STATUS NOT IN (#{notInStatus})
            </if>
            <if test="name!=null">
                AND NAME = #{name}
            </if>
            <if test="product!=null">
                AND PRODUCT = #{product}
            </if>
        </where>
    </select>
</mapper>
