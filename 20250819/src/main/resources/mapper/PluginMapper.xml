<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.PluginMapper">
    <resultMap type="com.maintain.server.vo.PluginVo" id="PLUGIN_RESULT_MAP">
        <result property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="path" column="PATH"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="lastUpdateTime" column="LAST_UPDATE_TIME"/>
        <result property="isDelete" column="IS_DELETE"/>
    </resultMap>

    <sql id="base-column">
        ID,NAME,PATH,CREATE_TIME,LAST_UPDATE_TIME,IS_DELETE
    </sql>

    <insert id="addPlugin" parameterType="com.maintain.server.vo.PluginVo">
        INSERT INTO PLUGIN_INFO (
        <include refid="base-column"/>
        )
        VALUE (#{id},#{name},#{path},now(),now(),#{isDelete})
    </insert>

    <update id="updatePlugin" parameterType="com.maintain.server.vo.PluginVo">
        UPDATE PLUGIN_INFO SET LAST_UPDATE_TIME = NOW()
        <if test="name!=null">
            ,NAME = #{name}
        </if>
        <if test="path!=null">
            ,PATH = #{path}
        </if>
        <if test="isDelete!=null">
            ,IS_DELETE = #{isDelete}
        </if>
        <where>
            <if test="id!=null">
                ID = #{id}
            </if>
            <if test="name!=null">
                AND NAME = #{name}
            </if>
        </where>
    </update>

    <select id="getPlugins" parameterType="com.maintain.server.criteria.PluginCriteria" resultMap="PLUGIN_RESULT_MAP">
        SELECT
        <include refid="base-column"/>
        FROM PLUGIN_INFO
        <where>
            <if test="id!=null">
                ID = #{id}
            </if>
            <if test="name!=null">
                AND NAME = #{name}
            </if>
           <!-- <if test="isDelete!=null">
                AND IS_DELETE = #{isDelete}
            </if>-->
            AND IS_DELETE =0
        </where>
    </select>
</mapper>
