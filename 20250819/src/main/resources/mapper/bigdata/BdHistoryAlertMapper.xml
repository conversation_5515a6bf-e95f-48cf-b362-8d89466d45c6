<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.bigdata.BdHistoryAlertMapper">
    <insert id="batchSaveOrUpdate" parameterType="com.maintain.server.entity.BdHistoryAlertPo">
        INSERT INTO bd_history_alert (
        id, node, cluster_name, component_name, host_name,
        label, service_name, state, text,
        timestamp
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.node}, #{item.clusterName}, #{item.componentName}, #{item.hostName},
            #{item.label}, #{item.serviceName}, #{item.state}, #{item.text},
            #{item.timestamp}
            )
        </foreach>
    </insert>

    <select id="findPage" resultType="com.maintain.server.entity.BdHistoryAlertPo">
        SELECT *
        FROM bd_history_alert
        <where>
            <if test="node != null and node != ''">
                AND node = #{node}
            </if>
            <if test="hostName != null and hostName != ''">
                AND host_name = #{hostName}
            </if>
            <if test="serviceName != null and serviceName != ''">
                AND service_name = #{serviceName}
            </if>
            <if test="componentName != null and componentName != ''">
                AND component_name = #{componentName}
            </if>
            <if test="alertStartTime != null and alertStartTime != ''">
                AND `timestamp` >= #{alertStartTime}
            </if>
            <if test="alertEndTime != null and alertEndTime != ''">
                AND `timestamp` &lt; #{alertEndTime}
            </if>
        </where>
        ORDER BY timestamp DESC
    </select>
</mapper>