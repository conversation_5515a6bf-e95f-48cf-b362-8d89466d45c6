<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.bigdata.BdAlertMapper">
    <insert id="batchSaveOrUpdate" parameterType="com.maintain.server.entity.BdAlertPo">
        INSERT INTO bd_alert (
        id,node, cluster_name, component_name, firmness, host_name,
        label, occurrences, service_name, state, text,
        original_timestamp, latest_timestamp
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.node}, #{item.clusterName}, #{item.componentName}, #{item.firmness}, #{item.hostName},
            #{item.label}, #{item.occurrences}, #{item.serviceName}, #{item.state}, #{item.text},
            #{item.originalTimestamp}, #{item.latestTimestamp}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        firmness = VALUES(firmness),
        occurrences = VALUES(occurrences),
        host_name = VALUES(host_name),
        state = VALUES(state),
        text = VALUES(text),
        latest_timestamp = VALUES(latest_timestamp),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <select id="findPage" resultType="com.maintain.server.entity.BdAlertPo">
        SELECT *
        FROM bd_alert
        <where>
            <if test="node != null and node != ''">
                AND node = #{node}
            </if>
            <if test="hostName != null and hostName != ''">
                AND host_name = #{hostName}
            </if>
            <if test="serviceName != null and serviceName != ''">
                AND service_name = #{serviceName}
            </if>
            <if test="componentName != null and componentName != ''">
                AND component_name = #{componentName}
            </if>
        </where>
        ORDER BY latest_timestamp DESC
    </select>

    <select id="hostAlertCount" resultType="com.maintain.server.dto.Tuple">
        select host_name x, count(*) y
        from bd_alert
        where host_name is not null
        group by host_name
    </select>

    <select id="serviceAlertCount" resultType="com.maintain.server.dto.Tuple">
        select service_name x, count(*) y
        from bd_alert
        group by service_name
    </select>

    <select id="componentAlertCount" resultType="com.maintain.server.dto.Tuple">
        select component_name x, count(*) y
        from bd_alert
        where component_name is not null
          and service_name = #{serviceName}
        group by component_name
    </select>
</mapper>