<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.bigdata.BdHostMapper">
    <insert id="batchSaveOrUpdate" parameterType="com.maintain.server.entity.BdHostPo">
        INSERT INTO bd_host (
        cluster_name, node, disk_info, host_name, host_state, host_status,
        ip, os_arch, host_os_family, os_type, public_host_name, rack_info,
        total_mem, alerts_summary, host_components, cpu_count, ph_cpu_count
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.clusterName}, #{item.node}, #{item.diskInfo}, #{item.hostName}, #{item.hostState},
            #{item.hostStatus},
            #{item.ip}, #{item.osArch}, #{item.hostOsFamily}, #{item.osType}, #{item.publicHostName}, #{item.rackInfo},
            #{item.totalMem}, #{item.alertsSummary}, #{item.hostComponents}, #{item.cpuCount}, #{item.phCpuCount}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        disk_info = VALUES(disk_info),
        host_state = VALUES(host_state),
        host_status = VALUES(host_status),
        ip = VALUES(ip),
        os_arch = VALUES(os_arch),
        host_os_family = VALUES(host_os_family),
        os_type = VALUES(os_type),
        public_host_name = VALUES(public_host_name),
        rack_info = VALUES(rack_info),
        total_mem = VALUES(total_mem),
        alerts_summary = VALUES(alerts_summary),
        host_components = VALUES(host_components),
        cpu_count = VALUES(cpu_count),
        ph_cpu_count = VALUES(ph_cpu_count),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <select id="findPage" resultType="com.maintain.server.entity.BdHostPo">
        SELECT *
        FROM bd_host
    </select>
</mapper>