<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.bigdata.BdServiceMapper">
    <insert id="batchSaveOrUpdate" parameterType="com.maintain.server.entity.BdServicePo">
        INSERT INTO bd_service (
        node, service_name, service_type, cluster_name, state,
        alerts_summary, components
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.node}, #{item.serviceName}, #{item.serviceType}, #{item.clusterName}, #{item.state},
            #{item.alertsSummary}, #{item.components}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        service_type = VALUES(service_type),
        cluster_name = VALUES(cluster_name),
        state = VALUES(state),
        alerts_summary = VALUES(alerts_summary),
        components = VALUES(components),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <select id="findPage" resultType="com.maintain.server.entity.BdServicePo">
        SELECT *
        FROM bd_service
    </select>
</mapper>