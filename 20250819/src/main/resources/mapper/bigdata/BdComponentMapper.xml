<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.maintain.server.mapper.bigdata.BdComponentMapper">
    <insert id="batchSaveOrUpdate" parameterType="com.maintain.server.entity.BdComponentPo">
        INSERT INTO bd_component (
        node, category, cluster_name, component_name, desired_stack,
        desired_version, display_name, service_name, started_count, state,
        total_count, unknown_count
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.node}, #{item.category}, #{item.clusterName}, #{item.componentName}, #{item.desiredStack},
            #{item.desiredVersion}, #{item.displayName}, #{item.serviceName}, #{item.startedCount}, #{item.state},
            #{item.totalCount}, #{item.unknownCount}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        category = VALUES(category),
        desired_stack = VALUES(desired_stack),
        desired_version = VALUES(desired_version),
        display_name = VALUES(display_name),
        service_name = VALUES(service_name),
        started_count = VALUES(started_count),
        state = VALUES(state),
        total_count = VALUES(total_count),
        unknown_count = VALUES(unknown_count),
        update_time = CURRENT_TIMESTAMP
    </insert>

    <select id="findPage" resultType="com.maintain.server.entity.BdComponentPo">
        SELECT *
        FROM bd_component
    </select>
</mapper>