# K8s容器监控前端适配方案

## 核心思路

保持现有前端界面完全不变，通过在程序管理页面添加数据源切换功能，实现裸机程序和k8s容器程序的统一管理。

## 前端修改点

### 1. 程序管理页面 (procedureManage.vue)

#### 在搜索表单中添加数据源选择

```html
<!-- 在现有搜索表单中添加数据源选择 -->
<el-form-item label="数据源">
  <el-select v-model="form.source" @change="sourceChange">
    <el-option label="裸机程序" value="agent"></el-option>
    <el-option label="K8s容器" value="k8s"></el-option>
  </el-select>
</el-form-item>

<el-form-item label="命名空间" v-if="form.source === 'k8s'">
  <el-input v-model="form.namespace" placeholder="请输入k8s命名空间"></el-input>
</el-form-item>
```

#### 修改data数据

```javascript
data() {
  return {
    form: {
      name: '',
      status: '',
      matchType: 0,
      source: 'agent',  // 新增：数据源选择
      namespace: 'default'  // 新增：k8s命名空间
    },
    sourceOptions: [
      { label: '裸机程序', value: 'agent' },
      { label: 'K8s容器', value: 'k8s' }
    ]
  }
}
```

#### 修改methods方法

```javascript
methods: {
  // 数据源切换事件
  sourceChange() {
    this.form.name = '';
    this.form.status = '';
    this.getDataList();
  },
  
  // 修改搜索方法，传递新参数
  getDataList(IntervalLoading) {
    let _this = this;
    _this.fullscreenLoading = IntervalLoading ? false : true;
    
    // 添加source和namespace参数
    let params = Object.assign({}, _this.form);
    
    _this.$http
      .post("software/list.json", _this.qs.stringify(params))
      .then((res) => {
        // 处理响应数据，逻辑保持不变
        _this.fullscreenLoading = false;
        if (res.data.code == 0) {
          this.statusData = res.data.data.statusMap || {
            RED: 0, YELLOW: 0, GREEN: 0, GRAY: 0
          };
          
          if (res.data.data.softwares && JSON.stringify(res.data.data.softwares) != "{}") {
            _this.data = res.data.data.softwares;
            _this.getTable(_this.data);
          } else {
            _this.dataShow = true;
          }
        }
      })
      .catch((error) => {
        _this.fullscreenLoading = false;
        _this.erFn();
      });
  }
}
```

### 2. 添加程序对话框 (procedureDialog.vue)

#### 为k8s容器添加特殊的添加逻辑

```html
<!-- 在现有表单中添加k8s相关字段 -->
<el-row v-if="form.source === 'k8s'">
  <el-col :span="24">
    <el-form-item label="命名空间:" prop="namespace">
      <el-input v-model="addForm.namespace" placeholder="请输入k8s命名空间"></el-input>
    </el-form-item>
  </el-col>
  <el-col :span="24">
    <el-form-item label="Pod名称:" prop="podName">
      <el-input v-model="addForm.podName" placeholder="请输入Pod名称"></el-input>
    </el-form-item>
  </el-col>
  <el-col :span="24">
    <el-form-item label="容器名称:" prop="containerName">
      <el-input v-model="addForm.containerName" placeholder="请输入容器名称"></el-input>
    </el-form-item>
  </el-col>
</el-row>
```

#### 修改添加逻辑

```javascript
methods: {
  // 修改添加程序逻辑
  addData(obj) {
    let _this = this, data = {}, url = "", text = "";

    switch (_this.dialogData.dialogName) {
      case "添加程序":
        if (_this.form.source === 'k8s') {
          // k8s容器添加逻辑
          url = "software/add_software";
          text = "k8s程序添加成功";
          data = {
            source: 'k8s',
            namespace: obj.namespace,
            podName: obj.podName,
            containerName: obj.containerName,
            name: obj.containerName // 使用容器名作为程序名
          };
        } else {
          // 原有裸机程序添加逻辑
          url = "software/add_software";
          text = "添加成功";
          data = { ...obj };
          data.batch = false;
          data.serverIp = data.selectIP.join(",");
          delete data["selectIP"];
        }
        _this.addSoft(data, url, text);
        break;
    }
  },
  
  // 获取k8s可用容器列表
  async getK8sContainers() {
    try {
      const response = await this.$http.get('/k8s/software/available.json', {
        params: { namespace: this.addForm.namespace || 'default' }
      });
      
      if (response.data.code === 0) {
        this.k8sContainers = response.data.data.containers;
      }
    } catch (error) {
      this.$message.error('获取k8s容器列表失败');
    }
  }
}
```

### 3. 程序详情页面 (procedureDetail.vue)

#### 根据程序类型显示不同的详情信息

```javascript
methods: {
  // 修改初始化方法
  initData() {
    this.twoRow = this.$route.query;
    
    // 判断是否为k8s容器
    if (this.twoRow.source === 'k8s') {
      this.isK8sContainer = true;
    }
    
    this.chartForm.endTime = parseTime(Date.parse(new Date()), "{y}-{m}-{d} {h}:{i}");
    this.chartForm.startTime = parseTime(Date.parse(new Date()) - 60 * 60 * 1000, "{y}-{m}-{d} {h}:{i}");
  },
  
  // 修改数据获取方法
  getDataList(IntervalLoading, data, pageForm) {
    let _this = this;
    _this.fullscreenLoading = IntervalLoading ? false : true;
    pageForm.startTime = this.chartForm.startTime;
    pageForm.endTime = this.chartForm.endTime;
    
    // 添加k8s相关参数
    if (this.isK8sContainer) {
      pageForm.source = 'k8s';
      pageForm.namespace = data.namespace;
      pageForm.podName = data.podName;
      pageForm.containerName = data.containerName;
    }
    
    _this.$http
      .post("software/detail_" + data.id + ".json", _this.qs.stringify(pageForm))
      .then((res) => {
        // 处理响应数据，逻辑保持不变
        _this.fullscreenLoading = false;
        if (res.data.code == 0) {
          _this.formatData(res.data.data);
        }
      })
      .catch((error) => {
        _this.fullscreenLoading = false;
        _this.erFn();
      });
  }
}
```

## 后端接口兼容性

### 1. 程序列表接口 (/software/list.json)

**新增参数：**
- `source`: 数据源类型 (agent/k8s)
- `namespace`: k8s命名空间

**返回数据格式保持不变：**
```json
{
  "code": 0,
  "data": {
    "statusMap": {"GREEN": 10, "YELLOW": 2, "RED": 1, "GRAY": 0},
    "softwares": {
      "**************": [
        {
          "id": 1,
          "name": "datacenter/app",
          "host": "**************",
          "status": "GREEN",
          "cpuPercent": 15.5,
          "memoryPercent": 45.2
        }
      ]
    },
    "source": "k8s",
    "namespace": "default"
  }
}
```

### 2. 程序详情接口 (/software/detail_{id}.json)

**新增参数：**
- `source`: 数据源类型
- `namespace`: k8s命名空间
- `podName`: Pod名称
- `containerName`: 容器名称

**返回数据格式保持不变，确保前端无需修改显示逻辑**

### 3. 添加程序接口 (/software/add_software)

**新增参数：**
- `source`: 数据源类型
- `namespace`: k8s命名空间
- `podName`: Pod名称
- `containerName`: 容器名称

## 实施步骤

1. **后端开发** ✓
   - 创建k8s模块
   - 扩展SoftwareController
   - 添加配置文件

2. **前端适配**
   - 修改程序管理页面添加数据源选择
   - 修改添加程序对话框支持k8s容器选择
   - 修改程序详情页面支持k8s信息显示

3. **测试验证**
   - 测试k8s接口连通性
   - 测试前后端数据交互
   - 测试用户操作流程

## 优势

1. **前端改动最小**：复用现有界面和交互逻辑
2. **用户体验一致**：统一的操作界面和流程
3. **数据格式兼容**：后端适配层确保数据格式一致
4. **渐进式集成**：支持裸机和容器程序并存
5. **扩展性好**：未来可以轻松添加其他数据源

## 关键点

1. **保持接口格式不变**：确保前端无需大幅修改
2. **数据源参数控制**：通过source参数区分数据来源
3. **容器信息传递**：通过路由参数传递k8s容器信息
4. **状态统一管理**：将容器状态映射为程序状态
5. **图表数据兼容**：生成与裸机程序相同格式的监控图表
