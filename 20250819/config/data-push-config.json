[
  {
    "dataType": "攻击日志",
    "timeField":"@createtime",
    "open": true,
    "produceType": 0,
    "source": "inf-http-alarm-risk",
    "dest": "attack-log-subject",
    //原始字段--规范字段
    "keyMapping": {
      "maltype": "TOOL_NAME",
      "dip": "DIP,EQU_IP",
      "risklevel": "WARN_LEVEL",
      "url": "URL",
      "dport": "DP",
      "ap_key": "COLLECT_IP",
      "sipcountry": "SIP_LOC",
      "dipcountry": "DIP_LOC",
      "_id": "ID",
      "time": "COLLECT_TIME",
      "sip": "SIP",
      "sport": "SP",
      "@createtime": "TIME",
      "filerisk": "WARN_LEVEL"
    },
    //默认的key及其对应值
    "defaultKeyValue": {
      "PACKET_COUNT": null,
      "EVENT_TYPE": "SWV",
      "PROTOCOL": "http"
    },
    //规范字段集合
    "fieldSet": [
      "ID",
      "COLLECT_IP",
      "EQU_IP",
      "COLLECT_TIME",
      "PROTOCOL",
      "SIP",
      "SP",
      "SIP_LOC",
      "DIP",
      "DP",
      "DIP_LOC",
      "PACKET_COUNT",
      "TIME",
      "URL",
      "TOOL_NAME",
      "EVENT_TYPE",
      "WARN_LEVEL"
    ],
    "needFields": [
      "maltype",
      "dip",
      "risklevel",
      "url",
      "dport",
      "ap_key",
      "sipcountry",
      "dipcountry",
      "siparea",
      "diparea",
      "time",
      "sip",
      "sport",
      "@createtime"
    ]
  },
  {
    "dataType": "攻击日志",
    "timeField":"updatetime",
    "produceType": 2,
    "open": true,
    "source": "inf-basic-ftp-file",
    "dest": "attack-log-subject",
    //原始字段--规范字段
    "keyMapping": {
      "dport": "DP",
      "sipcountry": "SIP_LOC",
      "maltype": "TOOL_NAME",
      "dipcountry": "DIP_LOC",
      "dip": "DIP,EQU_IP",
      "_id": "ID",
      "time": "COLLECT_TIME",
      "sip": "SIP",
      "filerisk": "WARN_LEVEL",
      "sport": "SP",
      "@createtime": "TIME"
    },
    //默认的key及其对应值
    "defaultKeyValue": {
      "PACKET_COUNT": null,
      "EVENT_TYPE": "SWV",
      "PROTOCOL": "ftp",
      "URL": null
    },
    //规范字段集合
    "fieldSet": [
      "ID",
      "COLLECT_IP",
      "EQU_IP",
      "COLLECT_TIME",
      "PROTOCOL",
      "SIP",
      "SP",
      "SIP_LOC",
      "DIP",
      "DP",
      "DIP_LOC",
      "PACKET_COUNT",
      "TIME",
      "URL",
      "TOOL_NAME",
      "EVENT_TYPE",
      "WARN_LEVEL"
    ],
    "needFields": [
      "dport",
      "sipcountry",
      "maltype",
      "dipcountry",
      "dip",
      "_id",
      "time",
      "sip",
      "risklevel",
      "sport",
      "@createtime",
      "filerisk",
      "siparea",
      "diparea"
    ]
  },
  {
    "dataType": "攻击日志",
    "timeField":"@createtime",
    "produceType": 0,
    "open": true,
    "source": "inf-http-attack",
    "dest": "attack-log-subject",
    //原始字段--规范字段
    "keyMapping": {
      "attacktype": "TAG",
      "yamlerdescription": "EVENT_NAME",
      "dip": "DST_IP,EQU_IP",
      "h-req-host": "EQU_NAME",
      "h-req-url": "URL",
      "risklevel": "SERVERITY",
      "dport": "DST_PORT",
      "ap_key": "COLLECT_IP",
      "h-req-user-agent": "CLIENT_ EDITION",
      "_id": "ID",
      "time": "COLLECT_TIME",
      "sip": "SRC_IP",
      "sport": "SRC_PORT",
      "@createtime": "CREATE_TIME"
    },
    //默认的key及其对应值
    "defaultKeyValue": {
      "ACTION": "告警",
      "RAW_MSG": null
    },
    //规范字段集合
    "fieldSet": [
      "ID",
      "COLLECT_IP",
      "EQU_IP",
      "COLLECT_TIME",
      "CREATE_TIME",
      "RAW_MSG",
      "EQU_NAME",
      "TAG",
      "EVENT_NAME",
      "SERVERITY",
      "SRC_IP",
      "SRC_PORT",
      "DST_IP",
      "DST_PORT",
      "URL",
      "CLIENT_ EDITION",
      "ACTION"
    ],
    "needFields": [
      "attacktype",
      "yamlerdescription",
      "dip",
      "h-req-host",
      "h-req-url",
      "risklevel",
      "dport",
      "ap_key",
      "h-req-user-agent",
      "_id",
      "time",
      "sip",
      "sport",
      "@createtime"
    ]
  },
  {
    "dataType": "攻击日志",
    "timeField":"@createtime",
    "produceType": 1,
    "open": true,
    "source": "inf-snort",
    "dest": "attack-log-subject",
    //原始字段--规范字段
    "keyMapping": {
      "dportservice": "DST_SEV",
      "msg": "MESSAGE",
      "maltype": "TOOL_NAME",
      "dip": "DST_IP,EQU_IP",
      "risklevel": "filerisk",
      "sid": "EVENT_CATEGORY,EVENT_NAME",
      "dport": "DST_PORT",
      "ap_key": "COLLECT_IP",
      "protocol": "PROTOCOL",
      "sipcountry": "SIP_LOC",
      "dipcountry": "DIP_LOC",
      "eventcount": "BASE_EVENT_COUNT",
      "sportservice": "SRC_SEV",
      "_id": "ID",
      "time": "COLLECT_TIME",
      "sip": "SRC_IP",
      "sport": "SRC_PORT",
      "filecontent": "RAW_MSG",
      "@createtime": "CREATE_TIME"
    },
    //默认的key及其对应值
    "defaultKeyValue": {
      "SERVERITY": "中危",
      "PRIORITY": 4
    },
    //规范字段集合
    "fieldSet": [
      "ID",
      "COLLECT_IP",
      "EQU_IP",
      "COLLECT_TIME",
      "PRIORITY",
      "PROTOCOL",
      "RAW_MSG",
      "SERVERITY",
      "SRC_IP",
      "SRC_PORT",
      "SRC_SEV",
      "DST_IP",
      "DST_PORT",
      "DST_SEV",
      "CREATE_TIME",
      "BASE_EVENT_COUNT",
      "EVENT_CATEGORY",
      "EVENT_NAME",
      "MESSAGE"
    ],
    "needFields": [
      "dportservice",
      "msg",
      "maltype",
      "dip",
      "risklevel",
      "sid",
      "dport",
      "ap_key",
      "protocol",
      "sipcountry",
      "dipcountry",
      "eventcount",
      "sportservice",
      "_id",
      "time",
      "sip",
      "sport",
      "filecontent.asciidata",
      "@createtime",
      "siparea",
      "diparea"
    ]
  },
  {
    "dataType": "管理配置日志",
    "open": true,
    "source": "operate_record",
    "dest": "manage-config-log-subject",
    //默认的key及其对应值
    "defaultKeyValue": {
      "FUNCTION_ID": null,
      "OPERATE_ID": null
    },
    //规范字段集合
    "fieldSet": [
      "ID",
      "USER_ID",
      "OPERATE_TIME",
      "FUNCTION_ID",
      "OPERATE_ID",
      "OPERATE_NAME",
      "OPERATE_RESULT",
      "CONTENT"
    ]
  },
  {
    "dataType": "系统告警日志",
    "open": true,
    "dest": "system-alarm-log-subject",
    //规范字段集合
    "fieldSet": [
      "MODULE",
      "STATUS",
      "ID",
      "DESC"
    ]
  },
  {
    "dataType": "操作日志",
    "open": true,
    "dest": "operate-log-subject",
    "defaultKeyValue": {
      "PROTOCOL": "https"
    },
    //规范字段集合
    "fieldSet": [
      "SESSION_ID",
      "PERSON_NAME",
      "PERSON_AREA_ID",
      "PERSON_AREA_NAME",
      "PERSON_ORG_ID",
      "PERSON_ORG_NAME",
      "MAIN_ACCT_ID",
      "MAIN_ACCT_NAME",
      "SUB_ACCT_ID",
      "SUB_ACCT_NAME",
      "CLIENT_NAME",
      "CLIENT_IP",
      "CLIENT_AREA_ID",
      "CLIENT_AREA_NAME",
      "PROTOCOL",
      "DEVICE_ID",
      "DEVICE_IP",
      "DEVICE_NAME",
      "DEVICE_PORT",
      "CMD_SUMMARY",
      "LOGIN_TIME",
      "LOGOUT_TIME",
      "CREATE_TIME",
      "IS_WORK_TIME",
      "IS_WORK_DAY",
      "LOG_SOURCE",
      "DB_INSTINCE_NAME",
      "OPERATE_RESULT",
      "REMARK",
      "DOUBLE_CHECK_APPROVE",
      "DOUBLE_CHECKFLAG",
      "DOUBLE_CHECK_ISNEED",
      "DOUBLE_CHECK_APPR_MAIN_ACCT_ID",
      "DOUBLE_CHECK_APPR_PERSON_NAME",
      "DOUBLE_CHECK_ISOPEN",
      "DOUBLE_CHECK_SCENE_ID",
      "DOUBLE_CHECK_SCENE_NAME",
      "DOUBLE_CHECK_APPLY_TYPE",
      "DOUBLE_CHECK_LAST_CHECK_STATUS",
      "DOUBLE_CHECK_LAST_CHECK_TIME",
      "DOUBLE_CHECK_FAIED_CHECK_COUNT",
      "DOUBLE_CHECK_TRIGGER_TYPE"
    ]
  }
]