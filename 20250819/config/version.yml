2.2.3:
    releaseTime: "2021-01-11"
    changeItems:
      - 程序管理：升级一键部署功能，支持全量、增量升级，替换配置项，按版本备份，删除待部署机多余文件等功能
      - 服务器管理：硬件信息增加网络IO监控
      - 业务管理：数据监控页面，新增是否忽略选项和状态过滤，状态排序；修改动静态分析若干统计原则；数据库备份支持异机监控
      - 集群管理：增加监控kafka和hbase异常重启
      - 修复了若干bug
2.2.2:
    releaseTime: "2020-11-23"
    changeItems:
      - 新增接口调用页面：可以查看各个服务接口的调用耗时
      - 优化启动依赖：当CDH中的zookeeper不正常时，不影响运维系统启动以及其他模块的访问
      - 程序管理：支持在线升级
      - 服务器管理：新增网络异常告警
      - 审计管理：审计粒度细化
      - Agent管理：提供单个服务器时钟同步功能
      - 前端管理：支持忽略异常项
      - 数据监控：支持忽略监控项
      - Agent管理：新增"一键开启/关闭平台"功能
      - 程序管理：一键关闭操作，需密码输入操作确认
      - 修复了若干bug
2.2.1:
    releaseTime:
    changeItems:
      - 前端管理：前端设备状态统计提高准确度
      - 程序管理：程序状态监控提高准确度
      - 程序管理：优化一键部署功能
      - 程序管理：程序支持按状态过滤
      - 服务器管理：支持windows2016非标准版告警
      - 修复了若干bug
2.2.0:
    releaseTime:
    changeItems:
      - 程序管理：新增codis集群监控
      - 程序管理：添加程序功能中新增获取默认配置功能
      - Agent管理：新增一键时间同步功能
      - Agent管理：添加和更新agent时, 增加添加成功和失败的agent数量统计
      - Agent管理：添加和更新agent时, 新增检查Linux系统yum, expect是否安装功能
      - 前端管理：新增展示探针系统异常信息, 一键升级和跳转探针系统功能
      - 业务管理：优化数据监控告警逻辑
      - 业务管理：新增网页挂马监控和mysql备份监控功能
      - 业务管理：入库信息新增选择全部节点选项和显示节点名称
      - 修复了若干bug
2.1.0:
    releaseTime:
    changeItems:
      - 重构了前端管理模块
      - 新增业务管理->入库统计
      - 新增业务管理->数据监控
      - 新增业务管理->数据同步监控
      - agent新增ntp服务告警