#
# 运维系统配置文件，有#**********#标注的项需要注意修改
#
autoConfig: false
mybatis:
  mapper-locations:
      - 'classpath:mapper/**/*.xml'
  configuration:
    map-underscore-to-camel-case: true
#isTurnOn默认false,是否开启让运维系统生成运维系统模块监控数据
#platform**********#平台名称，请参考同文件夹下的platform.json文件填写
#monitoringDirPath生成运维系统模块监控数据的文件夹路径
maintainHealth: {isTurnOn: false, platform: 海南, monitoringDirPath: 'D:\Data\transfer\'}
#前端管理展示模式 0-运维默认模式；1-探知单机版模式；
frontDeviceMode: 1
#isOpen默认false,是否开启前端管理页面下的探针前端一键升级功能
#userName,passwd调用探针前端系统的用户名和密码
#frontSystemUrl调用前端探针系统的端口
#frontUpgradePackagePath探针前端一键升级的升级包路径
connFrontWeb: {isOpen: false, userName: 《运维管理系统》, passwd: 36ba619a2c015bc317419ad065af6ffa,
  frontSystemUrl: 'https://#:8443', frontUpgradePackagePath: 'D:\MAINTAIN_TEMP\FrontUpgradePackage\'}
#在线升级配置项，username，host，port，password为FTP服务器配置
onlineUpgrade: {isOpen: false, username: 'root', host: '**************', port: 22, password: '0okm9ijn*UHB', localPath: 'D:\MAINTAIN_TEMP\',
  sftpPath: '/data/ywxt_upgrade_package/'}
server:
  #**********# 本机ip
  localHost: *************
  port: 8085
  web-socket-port: 8089
  web-socket-thread: 10
  web-socket-lost-time: 30
  #ssl: {key-store: ./config/ssl.keystore, key-password: 123456}
  session:
    cookie:
      secure: false
  servlet:
    context-path: /maintain
    session:
      timeout: 86400
      cookie:
        http-only: false
        secure: false
  connection-timeout: 300000
  undertow: {io-threads: 10, direct-buffers: true, worker-threads: 10}
  tomcat: {remote-ip-header: X-Forwarded-For, protocol-header: X-Forwarded-Proto,
    protocol-header-https-value: https}
heartbeatTimeout: 300
#开启/关闭 心跳过滤，开启：接收未添加程序的心跳不会操作；关闭：接收未知程序的心跳则会自动添加该程序（用于初次部署，后建议关闭）
validateHeart: true
seven-zip-path: C:/Program Files/7-Zip/7z.exe
#前端设备告警范围
front-device:
  daemon-range: 3,10
  soft-lost-ratio: 3,10
  over-time: 1
  heartBeatIndex: inf-eml
  wait-send-size-limit: 10,100
  exception-transmit-limit: 0.95,0.98

log-range: 1000
#配置一键开启程序时优先开启的程序，忽略大小写，顺序优先开启
first-open: hbaseservice,kafkaservice,esservice
es:
#**********# es集群1配置,若平台是双集群，则此项配置的集群是存储邮件数据的集群(clusterName-集群名称 ; security-集群校验信息 ; url-使用9300端口，建立TCP通讯，多个连接以”,”隔开 ;
#httpUrl-使用9200端口，建立http通讯，多个连接以”,”隔开 ; monitor	 集群的可视化插件路径)
  firstes: {clusterName: cloud-shield, security: 'elastic:kt@8ik9ol', url: '***************:9300',
            httpUrl: '***************:9200', monitor: 'http://***************:9000/#/overview?host=http:%2F%2F***************:9200'}
#**********# es集群2配置,若平台是双集群，此项配置是存储IP、DNS数据的集群；若是单集群，则不用管此项配置
#注意，如果是双集群则将enable设置为true，否则设置为false，这将影响到历史健康图表的展示)
  secondes: {enable: false, clusterName: '', security: '', url: '', httpUrl: '', monitor: ''}
#**********# 索引前缀
  prefix: da85-

#**********# kafka配置(kafkaZookeeperConnStr-zookeeper地址,开发环境后面加上/gwtz，生产环境去掉/gwtz ; kafkaBrokerConnStr-broker地址)
kafka: {kafkaZookeeperConnStr: '***************:2181,***************:2181,***************:2181/gwtz',
  kafkaBrokerConnStr: '***************:9092,***************:9092,***************:9092'}
#**********# 配置为CDH-NN服务器ip
ntpServer: ***************
logstash-exclude-software: logstash,elasticsearch-cloud-shield,elasticsearch-cloud-shield-data
#在指定的时间段内，如果没有修改文件，则logstash释放文件IO
logstash:
  closeoldertime: 1
monitor-dir: /opt
transfer-data-index: ip-*
dirSizeThreshold: 536870912000
availThreshold: 536870912000
ignore-dir: /boot,R:\
report: {day: ./report/day, week: ./report/week, month: ./report/month}
port-monitor: {yellow: 30000, red: 50000}
restart-count-monitor: {yellow: 2, red: 5}
ping-count-monitor: {yellow: 1, red: 11}
dir: {win: 'D:\dist,D:\serverdist,D:\sy-dist', lin: '/dist,/serverdist,/sy-dist'}
#统计近7天节点数据变化的索引
front-indexs: inf-sign-net,ip-*,dns-*,inf-maillogin,inf-eml,http-*,inf-http-alarm,inf-server,inf-remote,inf-shell-cmd,inf-server-risk,inf-dns-scan,inf-network-block-report,inf-sign-http,inf-http-elf-file,inf-tcp-file,inf-basic-db,inf-http-abnormal-file,inf-im-file,inf-state-monitor,inf-ssl-certificate,inf-apk-file,inf-http-shell-cmd,inf-som-devinfo,inf-eml-box-forward,inf-eml-box-contact,inf-agent,inf-vpn,inf-im-login,inf-wifi-phone,inf-wifi-phone-analysis,inf-router-dataflow,inf-http-snort,inf-snort,inf-basic-samba-login,inf-basic-samba-file,inf-dns-hack,inf-cmd-result,inf-html-clouddetection,inf-eml-clouddetection,inf-basic-samba-flow,inf-basic-ftp-login
scan-index: {fail-threshold: 100, static-scan-analysis: 'inf-im-file,inf-upload-file,inf-tcp-file,inf-http-abnormal-file,inf-http-alarm,inf-attachment,inf-basic-samba-file,inf-http-elf-file',
  dynamic-analysis: 'inf-im-file,inf-upload-file,inf-tcp-file,inf-http-abnormal-file,inf-http-alarm,inf-attachment,inf-basic-samba-file,inf-http-elf-file',
  preprocess-count: 'ip-*,inf-eml,inf-attachment,inf-shell-cmd,inf-black-ip,inf-attachment-risk,inf-http-alarm,inf-http-abnormal-file',
  rediskey: 'low_task_queue,medium_task_queue,high_task_queue,vip_task_queue'}
custom:
  historyTableInfoList[0]: {tableName: business_monitor, timeField: CREATE_TIME, days: 30}
  historyTableInfoList[1]: {tableName: dynamic_static_monitor, timeField: monitor_time,
    days: 30}
  historyTableInfoList[2]: {tableName: group_history_health, timeField: CREATE_TIME,
    days: 30}
  historyTableInfoList[3]: {tableName: hardware_history_health, timeField: CREATE_TIME,
    days: 30}
  historyTableInfoList[4]: {tableName: plugin_warn_message, timeField: CREATE_TIME,
    days: 30}
  historyTableInfoList[5]: {tableName: preprocess_index_count, timeField: monitor_time,
    days: 60}
  historyTableInfoList[6]: {tableName: software_history_health, timeField: create_time,
    days: 30}
  historyTableInfoList[7]: {tableName: software_restart_history, timeField: CREATE_TIME,
    days: 30}
spring:
  session: {timeout: 1800}
  mvc:
    async: {request-timeout: 300000}
  aop: {proxy-target-class: true}
  datasource:
    druid:
      aop-patterns: com.maintain.server.service.*
      stat-view-servlet: {enabled: true, url-pattern: /druid/*, exclusions: '/*.js,*.gif,*.jpg,*.png,*.css,*.ico,*.jsp,/druid/*,/download/*',
        reset-enable: true, login-username: admin, login-password: admin}
      web-stat-filter: {enabled: true, url-pattern: /*, exclusions: '/*.js,*.gif,*.jpg,*.png,*.css,*.ico,*.jsp,/druid/*,/download/*'}
      filter:
        stat: {enabled: true, connection-stack-trace-enable: true, merge-sql: true,
          log-slow-sql: true, slow-sql-millis: 5000}
        log4j: {enabled: true, statement-create-after-log-enabled: true, statement-close-after-log-enabled: false,
          result-set-open-after-log-enabled: false, result-set-close-after-log-enabled: false,
          statementExecutableSqlLogEnable: true, statement-log-enabled: true, statement-log-error-enabled: true}
        slf4j: {enabled: true, statement-create-after-log-enabled: true, statement-close-after-log-enabled: false,
          result-set-open-after-log-enabled: false, result-set-close-after-log-enabled: false,
          statementExecutableSqlLogEnable: true, statement-log-enabled: true, statement-log-error-enabled: true}
        log4j2: {enabled: true}
        wall:
          config: {multi-statement-allow: true, none-base-statement-allow: true}
        commons-log: {data-source-log-enabled: true, statement-log-enabled: true,
          statement-executable-sql-log-enable: true}
      #**********# 数据库用户名，密码，连接地址
      maintain: {username: deploy, password: 'ansec_888_999_2019', url: 'jdbc:mysql://**************:3306/maintain-2.0?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowMultiQueries=true&autoReconnect=true',
        driver-class-name: com.mysql.jdbc.Driver, initialSize: 10, minIdle: 1, maxActive: 256,
        timeBetweenEvictionRunsMillis: 60000, minEvictableIdleTimeMillis: 300000,
        validationQuery: select 'x', validationQueryTimeout: 10, testWhileIdle: true,
        testOnBorrow: true, testOnReturn: false, maxWait: 30000, useGlobalDataSourceStat: true}
      #**********# 数据库用户名，密码，连接地址(guard库为配置存放DEV_AP表的数据库相关信息,如该数据库是Oracle，还要修改drive-class-name，修改为Oracle的驱动)
      guard: {username: deploy, password: 'ansec_888_999_2019', url: '************************************************************************************************************************************************',
        driver-class-name: com.mysql.jdbc.Driver, initialSize: 10, minIdle: 1, maxActive: 64,
        timeBetweenEvictionRunsMillis: 60000, minEvictableIdleTimeMillis: 300000,
        validationQuery: select 'x', validationQueryTimeout: 10, testWhileIdle: true,
        testOnBorrow: false, testOnReturn: false, maxWait: 30000, useGlobalDataSourceStat: true}
logging:
  file: ./log/console.txt
  level: {org.springframework: warn, org.apache.ibatis: warn, com.maintain.server.mapper: info, org.java_websocket: trace}
ping:
  timeout: 20
dynamic-no-monitor: 
  yellow: 50
  red: 100
config:
  dbbackup:
    #是否开启数据库备份功能
    open: true
    #数据库备份过期时间
    overdue-day: 7
  data-push:
    #是否开启数据推送功能
    open: false
    intervalInSecond: 300
syslog:
  ip: ***************
  port: 514
  protocol: udp
  encode: utf-8
  maxMessageLength: 10240000

ambari:
  address: ***************:8080
  auth: admin:Cestc_01
auth:
  url: https://
#  以下为分到总运维管理配置，包含大数据，云平台，k8s监控
common:
    node: sichuan