<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ECharts数据统计图</title>
</head>
<body>
<!-- 为 ECharts 准备一个具备大小（宽高）的 DOM -->
<div id="main" style="width:950px; height:660px;"></div>
<script type="text/javascript" src="jquery-3.1.1.min.js"></script>
<script type="text/javascript" src="echarts.min.js"></script>
<script type="text/javascript">
    var myChart = echarts.init(document.getElementById('main'));
    // 指定图表的配置项和数据
    var option = {
        title : {
            text: '占比',
            x:'center',
            y:'bottom'
        },
        graid:{
            x:100,
            x2:100
        },
        tooltip : {
            trigger: 'item',
            formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
//        legend: {
//            orient: 'horizontal',
//            //data: ['直接访问','邮件营销','联盟广告','视频广告','搜索引擎'],
//            x:"center",
//            y:"bottom"
//        },
        series : [
            {
                color:['#9fdabf','#d48265'],
                avoidLabelOverlap: true,   //是否启用防止标签重叠策略
                name: '访问来源',
                type: 'pie',
                radius : '50%',
                center: ['50%', '50%'],
                data:[],
                itemStyle: {
                    emphasis: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label:{
                    show: true,
                    formatter: "{b}:{d}%",
                    textStyle:{
                        "fontSize":20
                    }
//                    normal:{
//                        textStyle:{
//                            fontSize:25
//                        },
//                        formatter: function(v) {
//                            var text = v.name + ':' + Math.round(v.percent)+'%';
//                            if(text.length <= 4)
//                            {
//                                return text;
//                            }else if(text.length > 4 && text.length <= 8){
//                                return text = text.slice(0,4)+'\n'+text.slice(4);
//                            }else if(text.length > 8 && text.length <= 12){
//                                return text = text.slice(0,4)+'\n'+text.slice(4,8)+'\n'+text.slice(8);
//                            }else if(text.length > 12 && text.length <= 16){
//                                return text = text.slice(0,4)+'\n'+text.slice(4,8)+'\n'+text.slice(8,12)+'\n'+text.slice(12);
//                            }else if(text.length > 16){
//                                return text = text.slice(0,4)+'\n'+text.slice(4,8)+'\n'+text.slice(8,12)+'\n'+text.slice(12,16)+'\n'+text.slice(16);
//                            }
//                            return text;
//
//                        }
//
//                    }


                }
//                labelLine:{
//                    normal:{
//                        length:5,
//                        length2:5
//                    }
//                },
            }
        ]
    };

    myChart.setOption(option,true);

    //核心方法
    function getImage(data){
        option.series[0].data = data.data;
        option.title.text = data.name;
        myChart.setOption(option,true);
    }

    function returnEcharts(){
//        return myChart.getDataURL().replace("data:image/png;base64,","");
        var imgData = getFullCanvasDataURL(document.getElementById('main'));
        myChart.clear();
        return imgData;
    }

    function getFullCanvasDataURL(el) {
        //将第一个画布作为基准。
        var baseCanvas = $(el).find("canvas").first()[0];
        if (!baseCanvas) {
            return false;
        };
        var width = el.width;
        var height = el.height;
        var ctx = baseCanvas.getContext("2d");
        //遍历，将后续的画布添加到在第一个上
        $(el).find("canvas").each(function(i, canvasObj) {
            if (i > 0) {
                var canvasTmp = $(canvasObj)[0];
                ctx.drawImage(canvasTmp, 0, 0, width+100, height);
            }
        });
        //获取base64位的url
        return baseCanvas.toDataURL('image/png',0.5).replace("data:image/png;base64,","");
    }

</script>
</body>
</html>