{"logstash": {"keys": "ywxtes.conf", "path": "bin", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "elasticsearch-cloud-shield": {"keys": "/dist/elasticsearch-cloud-shield/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "elasticsearch-cloud-shield-data": {"keys": "/dist/elasticsearch-cloud-shield-data/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "LogstashSyncService": {"keys": "Logstash", "path": "", "win_script": "", "lin_script": "nohup sh start_sync.sh >/dev/null &", "process": "3", "monitor": "1"}, "SusdomainMiningService": {"keys": "SusdomainMiningService", "path": "deploy", "win_script": "", "lin_script": "sh start_all.sh", "process": "4", "monitor": "0"}, "CertificateMiningService": {"keys": "CertificateMiningService", "path": "deploy", "win_script": "", "lin_script": "sh start_all.sh", "process": "4", "monitor": "0"}, "SpamMailAnalysis": {"keys": "SpamMailAnalysis", "path": "deploy", "win_script": "", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "DnsTunnelDetectService": {"keys": "DnsTunnelDetect", "path": "deploy", "win_script": "", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "MalwareClassficationService": {"keys": "'MalwareClassfication-TrainTmodel\\|MalwareClassfication',python", "path": "deploy", "win_script": "", "lin_script": "sh start.sh", "process": "2", "monitor": "0"}, "elasticsearch-cloud-shield-data1": {"keys": "/dist/elasticsearch-cloud-shield-data1/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "elasticsearch-cloud-shield-data2": {"keys": "/dist/elasticsearch-cloud-shield-data2/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "CodisMaster": {"keys": "codis", "path": "", "win_script": "", "lin_script": "sh start_master.sh", "process": "6", "monitor": "0"}, "CodisSlaver": {"keys": "codis", "path": "", "win_script": "", "lin_script": "sh start_slave.sh", "process": "4", "monitor": "0"}}