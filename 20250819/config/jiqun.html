<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ECharts数据统计图</title>
</head>
<body>
<!-- 为 ECharts 准备一个具备大小（宽高）的 DOM -->
<div id="main" style="width:950px; height:660px;"></div>
<script type="text/javascript" src="jquery-3.1.1.min.js"></script>
<script type="text/javascript" src="echarts.min.js"></script>
<script type="text/javascript">
    var myChart = echarts.init(document.getElementById('main'));
    // 指定图表的配置项和数据
    var option = {
            title: {
            text: '折线图',
            textStyle: {
                fontSize: 14,
                color: '#1e85e6'
            }
            },
             tooltip: {
                 trigger: 'axis',
                 axisPointer: {
                     lineStyle: { type: 'dashed' },
                 },
             },
             calculable: true,
             grid: {
                 top: 45,
                 bottom: "20%",
                 left: "10%",
                 right: "10%",
                 splitLine: {
                     show: false
                 },
             },
             xAxis: {
                 type: 'category',
                 name: '时间',
                 nameTextStyle: {
                     color: '#203e66'
                 },
                 boundaryGap: ['10%', '10%'],
                 axisLine: {
                     onZero: false,
                     lineStyle: { color: '#e6e9f1' }
                 },
                 axisLabel: {
                     color: '#203e66',
                     length: 7,
                     formatter: function(val){
                         var str = val.split(' ');
                         return str.join('\n')
                     }
                 },
                 data: []
             },
             yAxis: [{
                 type: 'value',
                 name: '状态',
                 nameGap: 30,
                 nameTextStyle: {
                     color: '#203e66'
                 },
                 min: -1,
                 max: 2,
                 splitNumber: 3,
                 axisTick: { show: false },//坐标轴刻度
                 axisLine: {
                     lineStyle: { color: '#e6e9f1' }
                 },
                 axisLabel: {
                     show: true,
                     textStyle: {
                         color: '#203e66',
                     },
                     formatter: function(val) {
                         switch (val) {
                             case -1:
                                 val = '';
                                 break;
                             case 0:
                                 val = '良好';
                                 break;
                             case 1:
                                 val = '告警';
                                 break;
                             case 2:
                                 val = '错误';
                                 break;
                         }
                         return val;
                     }
                 },
                 splitLine: {
                     show: false
                 }
             }],
             legend: {
                 textStyle: {
                     color: '#203e66'
                 },
                 icon: 'rect',
                 itemWidth: 10,
                 itemHeight: 10,
                 itemGap: 30,
                 left: '12%',
                 top: 5,
                 padding: [0, 0, 0, 30],
                 data: [
                     {
                         name: '良好'
                     },
                     {
                         name: '告警'
                     },
                     {
                         name: '报警'
                     }
                 ]
             },
             series: [
                 {
                     name: '良好',
                     type: 'line',
                     showSymbol: true,
                     symbol: "circle",
                     symbolSize: 4,
                     smooth: true,
                     itemStyle: {
                         normal: {
                             color: '#21db8d',
                         },
                         emphasis: {
                             borderColor: '#21db8d',
                         }
                     },
                     lineStyle: {
                         width: 4
                     },
                     data: []
                 },
                 {
                     name: '告警',
                     type: 'line',
                     smooth: true,
                     showSymbol: true,
                     symbol: "circle",
                     symbolSize: 4,
                     itemStyle: {
                         normal: {
                             color: '#fc833d',
                         },
                         emphasis: {
                             borderColor: '#fc833d',
                         }
                     },
                     lineStyle: {
                         width: 4
                     },
                     data: []
                 },
                 {
                     name: '报警',
                     type: 'line',
                     smooth: true,
                     showSymbol: true,
                     symbol: "circle",
                     symbolSize: 4,
                     itemStyle: {
                         normal: {
                             color: '#f53749',
                         },
                         emphasis: {
                             borderColor: '#f53749',
                         }
                     },
                     lineStyle: {
                         width: 4
                     },
                     data: []
                 },
             ]
    };

    myChart.setOption(option,true);

    //核心方法
    function getImage(requestData){
        option.xAxis.data = requestData.x;
        option.title.text = requestData.content;
        option.legend.data[0].name = '良好(' + requestData.GREEN.num + ')';
        option.legend.data[1].name = '告警(' + requestData.YELLOW.num + ')';
        option.legend.data[2].name = '报警(' + requestData.RED.num + ')';

        option.series[0].name = '良好(' + requestData.GREEN.num + ')';
        option.series[1].name = '告警(' + requestData.YELLOW.num + ')';
        option.series[2].name = '报警(' + requestData.RED.num + ')';

        option.series[0].data = requestData.GREEN.chart;
        option.series[1].data = requestData.YELLOW.chart;
        option.series[2].data = requestData.RED.chart;

        myChart.setOption(option,true);
    }

    function returnEcharts(){
//        return myChart.getDataURL().replace("data:image/png;base64,","");
        var imgData = getFullCanvasDataURL(document.getElementById('main'));
        myChart.clear();
        return imgData;
    }

    function getFullCanvasDataURL(el) {
        //将第一个画布作为基准。
        var baseCanvas = $(el).find("canvas").first()[0];
        if (!baseCanvas) {
            return false;
        };
        var width = el.width;
        var height = el.height;
        var ctx = baseCanvas.getContext("2d");
        //遍历，将后续的画布添加到在第一个上
        $(el).find("canvas").each(function(i, canvasObj) {
            if (i > 0) {
                var canvasTmp = $(canvasObj)[0];
                ctx.drawImage(canvasTmp, 0, 0, width+100, height);
            }
        });
        //获取base64位的url
        return baseCanvas.toDataURL('image/png',0.5).replace("data:image/png;base64,","");
    }

</script>
</body>
</html>