# K8s集成部署实施指南

## 部署前准备

### 1. 环境检查

**K8s集群环境**:
- K8s API Server: https://**************:6443
- 集群状态: 正常运行
- 可访问的Pod: datacenter, maintainserver, o2server
- Bearer Token: 已获取并验证

**服务器环境**:
- maintain-server-2.0: **************:8085
- JDK版本: 1.8+
- 内存: 建议4GB+
- 磁盘: 建议10GB+可用空间

### 2. 依赖检查

**Maven依赖** (已在pom.xml中添加):
```xml
<!-- K8s Java客户端 -->
<dependency>
    <groupId>io.kubernetes</groupId>
    <artifactId>client-java</artifactId>
    <version>18.0.0</version>
</dependency>
```

**配置文件检查**:
- application.yml中k8s配置已添加
- Token和API Server地址已配置

## 部署步骤

### 第一阶段: 后端部署

#### 步骤1: 代码部署
```bash
# 1. 停止现有服务
cd /opt/maintain-server-2.0
./stop.sh

# 2. 备份现有代码
cp -r /opt/maintain-server-2.0 /opt/maintain-server-2.0.backup.$(date +%Y%m%d)

# 3. 部署新代码
# 将新的jar包和配置文件复制到服务器
cp maintain-server-2.0-2.2.0-SNAPSHOT.jar /opt/maintain-server-2.0/
cp application.yml /opt/maintain-server-2.0/config/

# 4. 启动服务
./start.sh
```

#### 步骤2: 验证后端部署
```bash
# 1. 检查服务启动状态
ps -ef | grep maintain-server

# 2. 检查日志
tail -f logs/maintain-server.log

# 3. 测试基本接口
curl http://**************:8085/health

# 4. 测试k8s接口
curl -X POST "http://**************:8085/software/list.json" \
-d "source=k8s&namespace=default"
```

#### 步骤3: 配置验证
```bash
# 1. 验证k8s连接
curl -k -H "Authorization: Bearer YOUR_TOKEN" \
https://**************:6443/api/v1/namespaces/default/pods

# 2. 检查配置文件
cat config/application.yml | grep -A 10 "k8s:"

# 3. 验证日志输出
grep -i "k8s" logs/maintain-server.log
```

### 第二阶段: 前端部署

#### 步骤1: 前端代码修改

**修改程序管理页面** (procedureManage.vue):
```javascript
// 在data中添加
data() {
  return {
    form: {
      // 现有字段...
      source: 'agent',
      namespace: 'default'
    }
  }
}

// 在methods中添加
methods: {
  sourceChange() {
    this.form.name = '';
    this.form.status = '';
    this.getDataList();
  }
}
```

**修改添加程序对话框** (procedureDialog.vue):
```javascript
// 添加k8s相关字段处理
addData(obj) {
  if (this.form.source === 'k8s') {
    // k8s容器添加逻辑
    data = {
      source: 'k8s',
      namespace: obj.namespace,
      podName: obj.podName,
      containerName: obj.containerName
    };
  }
  // 原有逻辑...
}
```

#### 步骤2: 前端部署
```bash
# 1. 备份现有前端
cp -r /opt/maintain-web-2.0 /opt/maintain-web-2.0.backup.$(date +%Y%m%d)

# 2. 部署修改后的前端文件
cp procedureManage.vue /opt/maintain-web-2.0/src/views/procedureManage/
cp procedureDialog.vue /opt/maintain-web-2.0/src/views/procedureManage/
cp procedureDetail.vue /opt/maintain-web-2.0/src/views/procedureManage/

# 3. 重新构建前端 (如果需要)
cd /opt/maintain-web-2.0
npm run build

# 4. 重启前端服务
systemctl restart nginx  # 或相应的web服务器
```

### 第三阶段: 集成测试

#### 步骤1: 功能测试
```bash
# 1. 测试agent数据源 (原有功能)
curl -X POST "http://**************:8085/software/list.json" \
-d "source=agent"

# 2. 测试k8s数据源 (新功能)
curl -X POST "http://**************:8085/software/list.json" \
-d "source=k8s&namespace=default"

# 3. 测试添加k8s程序
curl -X POST "http://**************:8085/software/add_software" \
-d "source=k8s&namespace=default&podName=datacenter&containerName=app"
```

#### 步骤2: 前端界面测试
1. 访问程序管理页面
2. 验证数据源选择器显示正常
3. 切换到"K8s容器"数据源
4. 验证容器列表显示正常
5. 测试添加k8s程序功能
6. 测试容器程序详情页面

#### 步骤3: 性能测试
```bash
# 并发测试
ab -n 100 -c 10 -p post_data.txt -T "application/x-www-form-urlencoded" \
"http://**************:8085/software/list.json"

# 内存使用监控
top -p $(pgrep -f maintain-server)

# 响应时间测试
time curl -X POST "http://**************:8085/software/list.json" \
-d "source=k8s&namespace=default"
```

## 配置管理

### 1. 生产环境配置

**application.yml配置**:
```yaml
k8s:
  api-server: https://**************:6443
  token: eyJhbGciOiJSUzI1NiIs...  # 生产环境Token
  namespace: default
  timeout: 30000
  ssl-verify: false
```

### 2. 日志配置

**增加k8s相关日志**:
```xml
<!-- 在log4j2.xml中添加 -->
<Logger name="com.maintain.server.k8s" level="INFO" additivity="false">
    <AppenderRef ref="Console"/>
    <AppenderRef ref="RollingFile"/>
</Logger>
```

### 3. 监控配置

**添加k8s接口监控**:
- 监控k8s API连接状态
- 监控容器数据获取性能
- 监控错误率和响应时间

## 回滚方案

### 1. 快速回滚
```bash
# 1. 停止服务
./stop.sh

# 2. 恢复备份
rm -rf /opt/maintain-server-2.0
mv /opt/maintain-server-2.0.backup.$(date +%Y%m%d) /opt/maintain-server-2.0

# 3. 启动原有服务
./start.sh
```

### 2. 部分回滚
```bash
# 只回滚配置文件
cp /opt/maintain-server-2.0.backup.$(date +%Y%m%d)/config/application.yml \
   /opt/maintain-server-2.0/config/

# 重启服务
./restart.sh
```

## 运维监控

### 1. 关键指标监控
- K8s API连接状态
- 容器数据获取成功率
- 接口响应时间
- 系统资源使用率

### 2. 日志监控
```bash
# 监控k8s相关错误
tail -f logs/maintain-server.log | grep -i "k8s\|error"

# 监控接口调用
tail -f logs/maintain-server.log | grep "software/list.json"
```

### 3. 告警设置
- K8s集群不可用告警
- 接口响应时间超时告警
- 错误率超阈值告警

## 故障排查

### 1. 常见问题

**问题1: K8s连接失败**
```bash
# 检查网络连通性
telnet ************** 6443

# 检查Token有效性
curl -k -H "Authorization: Bearer YOUR_TOKEN" \
https://**************:6443/api/v1/namespaces/default/pods
```

**问题2: 容器数据为空**
```bash
# 检查命名空间
kubectl get pods -n default

# 检查Pod状态
kubectl describe pod POD_NAME -n default
```

**问题3: 前端显示异常**
```bash
# 检查浏览器控制台错误
# 检查网络请求状态
# 验证返回数据格式
```

### 2. 调试模式
```bash
# 启用调试日志
export JAVA_OPTS="-Dlogging.level.com.maintain.server.k8s=DEBUG"
./start.sh

# 查看详细日志
tail -f logs/maintain-server.log | grep DEBUG
```

## 成功标准

### 1. 功能验收
- ✅ K8s容器列表正常显示
- ✅ 容器程序详情正常显示
- ✅ 添加k8s程序功能正常
- ✅ 数据源切换功能正常
- ✅ 原有agent功能不受影响

### 2. 性能验收
- ✅ 接口响应时间 < 3秒
- ✅ 并发处理能力 > 50 QPS
- ✅ 系统资源使用率 < 80%

### 3. 稳定性验收
- ✅ 连续运行24小时无故障
- ✅ K8s集群异常时系统正常运行
- ✅ 错误率 < 1%

部署完成后，系统将支持裸机程序和k8s容器的统一监控管理，用户可以无缝切换数据源，享受一致的操作体验。
