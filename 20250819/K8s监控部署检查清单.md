# K8s监控部署检查清单

## 部署前检查

### 1. 环境准备
- [ ] K8s集群正常运行
- [ ] API Server地址可访问：https://**************:6443
- [ ] metrics-server已安装并正常运行
- [ ] 网络连通性正常

### 2. 权限配置
- [ ] 创建ServiceAccount：maintain-monitor
- [ ] 绑定cluster-admin权限
- [ ] 获取Bearer Token
- [ ] Token权限验证通过

### 3. 配置文件修改
- [ ] application.yml中添加k8s配置段
- [ ] enabled设置为true
- [ ] api-server地址正确
- [ ] token配置正确
- [ ] namespace配置正确

## 部署步骤

### 1. 代码部署
- [ ] K8sDataCollector.java已添加
- [ ] K8sConnectionTest.java已添加
- [ ] 配置文件已修改
- [ ] 项目重新编译

### 2. 功能测试
- [ ] 运行K8sConnectionTest验证连接
- [ ] 检查应用启动日志
- [ ] 确认定时任务正常执行
- [ ] 验证数据库中有K8s数据

### 3. 前端验证
- [ ] 程序管理页面显示Pod数据
- [ ] 状态统计包含Pod状态
- [ ] 点击Pod名称可查看详情
- [ ] 监控图表正常显示

## 部署后验证

### 1. ��据采集验证
```sql
-- 查询K8s Pod数据
SELECT * FROM software_info WHERE version = 'k8s-pod';

-- 查询最近采集的数据
SELECT * FROM software_info WHERE version = 'k8s-pod' 
ORDER BY create_time DESC LIMIT 10;
```

### 2. 日志检查
```bash
# 查看应用日志
tail -f log/console.txt | grep -i k8s

# 关键日志内容
grep "开始采集K8s数据" log/console.txt
grep "K8s数据采集完成" log/console.txt
grep "处理了.*个Pod" log/console.txt
```

### 3. 前端功能验证
- [ ] 访问程序管理页面：http://ip:8085/maintain/procedureManage
- [ ] 确认Pod数据在列表中显示
- [ ] 点击Pod名称查看详情页面
- [ ] 验证状态统计数据正确

## 常见问题排查

### 1. 连接问题
**现象**：日志显示连接失败
**排查**：
- [ ] 检查API Server地址
- [ ] 验证网络连通性：`telnet ************** 6443`
- [ ] 确认Token有效性

### 2. 权限问题
**现象**：HTTP 403 Forbidden
**排查**：
- [ ] 验证ServiceAccount权限
- [ ] 重新生成Token
- [ ] 检查ClusterRoleBinding配置

### 3. 数据问题
**现象**：前端无Pod数据显示
**排查**：
- [ ] 检查数据库中是否有数据
- [ ] 确认namespace配置正确
- [ ] 验证Pod是否存在

### 4. 指标问题
**现象**：CPU/内存数据为空
**排查**：
- [ ] 确���metrics-server运行状态
- [ ] 检查metrics API可访问性
- [ ] 验证Pod资源使用情况

## 性能监控

### 1. 采集频率
- 默认30秒采集一次
- 可根据需要调整@Scheduled注解中的fixedRate值

### 2. 资源消耗
- 每次采集消耗约100-500ms
- 内存占用增加约10-50MB
- 网络流量约1-10KB/次

### 3. 数据库影响
- 每个Pod对应一条software_info记录
- 建议定期清理过期数据

## 维护建议

### 1. 定期检查
- [ ] 每周检查K8s连接状态
- [ ] 每月检查数据采集准确性
- [ ] 每季度检查Token有效期

### 2. 监控告警
- [ ] 设置K8s连接失败告警
- [ ] 设置数据采集异常告警
- [ ] 设置Token过期提醒

### 3. 数据清理
- [ ] 定期清理已删除Pod的数据
- [ ] 清理过期的历史监控数据
- [ ] 优化数据库索引

## 联系信息

- 开发者：xiong
- 部署日期：2025-01-19
- 文档版本：1.0