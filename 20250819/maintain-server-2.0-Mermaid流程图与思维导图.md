# maintain-server-2.0 Mermaid流程图与思维导图

## 1. 系统整体架构图

```mermaid
graph TB
    subgraph "maintain-agent (数据采集端)"
        A1[MaintainAgentMain]
        A2[HardInfo]
        A3[SoftwareMonitor]
        A4[conf/config.json]
    end
    
    subgraph "maintain-server (数据处理中心)"
        S1[IceMaintainServer]
        S2[BigdataClient]
        S3[HttpUtil]
        S4[application.yml]
    end
    
    subgraph "maintain-web (数据展示端)"
        W1[Vue.js 2.5.2]
        W2[Element UI]
        W3[ECharts 4.2.0]
        W4[端口: 8080]
    end
    
    subgraph "外部系统集成"
        E1[Elasticsearch<br/>9200/9300]
        E2[Kafka<br/>9092]
        E3[MySQL<br/>3306]
        E4[HBase/CDH/Ambari]
    end
    
    A1 -->|ICE协议| S1
    S1 -->|HTTP/WebSocket| W1
    S1 --> E1
    S1 --> E2
    S1 --> E3
    S1 --> E4
```

## 2. 数据接��与处理核心流程图

```mermaid
flowchart TD
    A[Agent数据上报] --> B[/request接口<br/>IceMaintainServer.java<br/>request(int flag, String param)]
    B --> C[限流检查<br/>RateLimiter.create(5)<br/>RateLimiter.create(10)]
    C --> D[Flag参数路由]
    
    D -->|flag=1| E1[硬件监控数据<br/>HARDWARE_MONITOR<br/>dealHardwareData()<br/>HardwareVo, HardwareService<br/>30秒间隔]
    D -->|flag=2| E2[软件监控数据<br/>SOFTWARE_MONITOR<br/>dealSoftwareData()<br/>SoftwareVo, SoftwareService<br/>180秒间隔]
    D -->|flag=3| E3[业务监控数据<br/>BUSINESS_MONITOR<br/>dealBusinessMonitor()<br/>BusinessMonitorVo]
    D -->|flag=4| E4[重启日志<br/>RESTART_LOG<br/>restartLog()<br/>SoftwareRestartVo]
    D -->|flag=5| E5[获取软件信息<br/>GET_SOFTWARE_INFO<br/>getSoftwareInfoList()<br/>JSON格式软件配置]
    D -->|flag=6| E6[心跳响应<br/>PONG<br/>返回: pong<br/>连接状态检测]
```

## 3. 硬件数据处理详细流程图

```mermaid
flowchart TD
    A[Agent硬件数据上报] --> B[JSON数据解析<br/>HardwareVo<br/>type=hardware]
    B --> C[Agent信息验证<br/>AgentService<br/>getAgentByCriteria()<br/>agent表]
    C --> D[硬件信息提取<br/>HardwareVo.Info<br/>cpu, mem, disk, network]
    
    D --> E1[CPU处理<br/>setUsedCpu()<br/>hardwareInfo.getCpu()]
    D --> E2[内存处理<br/>setUsedMemory()<br/>setMemoryUsedPercent()<br/>actualUsed/total]
    D --> E3[磁盘处理<br/>单位转换: G/M/T<br/>告警级别: GREEN/YELLOW/RED<br/>diskRange = 80,90]
    D --> E4[网络处理<br/>setSendBytePerSecond()<br/>setReceiveBytePerSecond()]
    
    E1 --> F[数据库存储<br/>HardwareService<br/>addHardwareInfo()<br/>hardware_history_health表]
    E2 --> F
    E3 --> F
    E4 --> F
```

## 4. 软件数据处理详细流程图

```mermaid
flowchart TD
    A[Agent软件数据上报] --> B[JSON数据解析<br/>SoftwareVo<br/>name, host, pid, status]
    B --> C[程序名称验证<br/>过滤: MaintainServer, MaintainAgent<br/>StringUtils.isEmpty()]
    C --> D[Agent关联查询<br/>AgentService<br/>getAgentByCriteria()]
    D --> E[内存信息处理<br/>softwareSetMemoryInfo()<br/>单位转换: m/M, g/G, 字节<br/>used/total*100]
    E --> F[磁盘信息处理<br/>softwareSetDiskInfo()<br/>HardwareService<br/>Linux=/, Windows=D:\]
    F --> G[进程信息处理<br/>handleSoftwareBaseInfo()<br/>common/self/log<br/>cpuPercent, pid, startParam<br/>startTime -> createTime]
    G --> H[数据库存储<br/>SoftwareService<br/>setSoftwareInfo()<br/>software表]
```

## 5. cloudapi模块架构思维导图

```mermaid
mindmap
  root((cloudapi模块))
    bigdata
      config
        BigDataProperties
          endPoint
          username
          password
          apiVersion
      dto
        HostApiDto
        ServiceApiDto
        ComponentApiDto
        CurrentAlertApiDto
        HistoryAlertApiDto
      service
        BigdataApi(标准接口)
        BigdataClient(Ambari实现)
          认证处理(Basic Auth Base64编码)
          数据解析(JSON转DTO FastJSON解析)
          API调用(/api/v1/* RESTful接口)
    cloud
      (云平台-待扩展)
    util
      HttpUtil.java(HTTP客户端)
        GET
        POST
        PUT
        DELETE
        RestTemplate(Spring HTTP)
```

## 6. 配置体系思维导图

```mermaid
mindmap
  root((maintain-server-2.0 配置体系))
    application.yml(主配置文件)
      服务器配置
        localHost
        port:8085
        websocket port:8089
      数据库配置(双数据源配置)
        maintain库
        guard库
        连接池配置(Druid)
        事务管理(JTA)
      外部集成
        ES/Kafka集群地址
        认证信息
        索引配置(prefix前缀)
    JSON配置文件
      platform.json
      software.json
      module.json
      data-push-config.json
    外部配置(环境相关)
      SSL证书(keystore)
      环境变量(JAVA_OPTS)
      JVM参数(-Xmx)
      系统属性
      内存设置
    告警配置
      阈值设置
      多级告警
      通知方式
    监控配置
      采集间隔
      存储路径
      日志级别
    业务配置
      前端模式
      升级配置
      备份策略
```

## 7. 数据流转完整流程图

```mermaid
flowchart TB
    subgraph "Agent数据采集"
        A1[硬件监控:30秒<br/>HardInfo]
        A2[软件监控:180秒<br/>SoftwareMonitor]
        A3[日志监控:600秒<br/>MonitoringService]
    end
    
    subgraph "Server数据接收"
        S1[/request接口<br/>flag参数路由<br/>限流处理]
        S2[IceMaintainServer<br/>AgentService<br/>HardwareService<br/>SoftwareService]
    end
    
    subgraph "数据处理与计算"
        P1[硬件指标计算<br/>dealHardwareData]
        P2[软件状态分析<br/>dealSoftwareData]
        P3[告警阈值判断<br/>updateAgentHeart]
    end
    
    subgraph "MySQL数据存储"
        M1[hardware_history]
        M2[software]
        M3[business_monitor]
        M4[agent]
    end
    
    subgraph "实时数据推送"
        W1[WebSocket:8089<br/>前端实时更新<br/>监控数据变化<br/>WebSocketService]
    end
    
    subgraph "外部系统集成"
        E1[Elasticsearch<br/>日志存储]
        E2[Kafka<br/>消息推送]
        E3[HBase<br/>大数据存储]
        E4[CDH/Ambari<br/>集群监控]
    end
    
    subgraph "前端数据查询"
        F1[HTTP REST API<br/>分页查询<br/>条件筛选]
        F2[HardwareController<br/>SoftwareController<br/>AgentController]
    end
    
    subgraph "告警通知处理"
        AL1[多级告警机制<br/>阈值配置<br/>通知推送<br/>GREEN/YELLOW/RED]
    end
    
    subgraph "定时任务调度"
        T1[数据清理<br/>报告生成<br/>集群数据同步<br/>Spring Scheduler<br/>@Scheduled]
    end
    
    A1 -->|ICE协议| S1
    A2 -->|ICE协议| S1
    A3 -->|ICE协议| S1
    S1 --> S2
    S2 --> P1
    S2 --> P2
    S2 --> P3
    P1 --> M1
    P2 --> M2
    P3 --> M3
    S2 --> M4
    S2 --> W1
    S2 --> E1
    S2 --> E2
    S2 --> E3
    S2 --> E4
    M1 --> F1
    M2 --> F1
    M3 --> F1
    M4 --> F1
    F1 --> F2
    P1 --> AL1
    P2 --> AL1
    P3 --> AL1
    S2 --> T1
```

## 8. cloudapi大数据平台集成流程图

```mermaid
sequenceDiagram
    participant Timer as 定时任务触发
    participant Service as BigDataService<br/>BigDataServiceImpl<br/>apiDataToMysql()
    participant Client as BigdataClient<br/>BigdataApi接口
    participant API as Ambari API
    participant HTTP as HttpUtil<br/>RestTemplate<br/>Basic Auth
    participant JSON as JSON解析<br/>FastJSON<br/>getDtoList()
    participant DB as MySQL���据库<br/>bd_*表
    participant Controller as BigDataController<br/>REST接口
    
    Timer->>Service: 触发数据同步
    Service->>Client: 调用API方法
    
    Client->>API: getClusterName()<br/>/clusters
    API-->>Client: cluster_name
    
    Client->>API: getHosts()<br/>/clusters/{name}/hosts
    API-->>Client: HostApiDto<br/>cpu_count, total_mem, disk_info
    
    Client->>API: getServices()<br/>/clusters/{name}/services
    API-->>Client: ServiceApiDto<br/>service_name, state
    
    Client->>API: getComponents()<br/>/clusters/{name}/components
    API-->>Client: ComponentApiDto<br/>component_name, state
    
    Client->>API: getAlerts()<br/>/clusters/{name}/alerts
    API-->>Client: CurrentAlertApiDto<br/>alert_name, state, text
    
    Client->>HTTP: HTTP请求处理<br/>get(), postJson()
    HTTP->>JSON: JSON数据解析<br/>items数组 -> DTO列表
    JSON->>DB: MySQL数据存储<br/>bd_host, bd_service<br/>bd_component, bd_alert
    DB->>Controller: 前端API接口<br/>/bigdata/pageHost<br/>/bigdata/pageService<br/>/bigdata/pageAlerts
```

## 9. 前后端交互架构图

```mermaid
graph LR
    subgraph "maintain-web (Vue.js 2.5.2)"
        W1[前端组件<br/>Element UI<br/>ECharts图表<br/>Axios HTTP]
        W2[请求格式<br/>{pageNum: 1,<br/>pageSize: 10,<br/>hostName: xx}]
        W3[状态管理<br/>Vuex Store]
        W4[路由管理<br/>Vue Router<br/>/hardware<br/>/software<br/>/alerts<br/>/bigdata]
        W5[权限控制<br/>登录验证<br/>角色权限<br/>操作日志]
    end
    
    subgraph "maintain-server (Spring Boot)"
        S1[后端控制器<br/>@RestController<br/>HardwareController<br/>SoftwareController<br/>AgentController<br/>BigDataController]
        S2[响应格式<br/>{code: 200,<br/>message: ok,<br/>data: {total: 100,<br/>list: [...]}}]
        S3[服务层处理<br/>HardwareService<br/>SoftwareService<br/>AgentService<br/>BigDataService]
        S4[数据库访问<br/>MyBatis Mapper<br/>HardwareMapper<br/>SoftwareMapper<br/>AgentMapper]
    end
    
    subgraph "实时数据推送"
        WS1[WebSocket<br/>端口: 8089<br/>线程数: 10<br/>超时: 30秒]
        WS2[推送内容<br/>监控数据变化<br/>告警信息<br/>系统状态<br/>WebSocketService]
    end
    
    W1 -->|HTTP请求| S1
    S1 --> S2
    S1 --> S3
    S3 --> S4
    S1 -->|WebSocket| WS1
    WS1 --> WS2
    WS2 --> W1
```

## 10. 系统启动与初始化流程图

```mermaid
flowchart TD
    A[JVM启动] --> B[Spring Boot启动<br/>MaintainServerApplication<br/>@SpringBootApplication<br/>排除: SecurityAutoConfiguration等]
    B --> C[配置文件加载<br/>application.yml<br/>BaseConfigUtil.init()<br/>数据库、ES、Kafka等]
    C --> D[数据源初始化<br/>spring.datasource.druid<br/>maintain + guard<br/>初始10，最大256]
    D --> E[MyBatis初始化<br/>mybatis.mapper-locations<br/>com.maintain.server.mapper<br/>classpath:mapper/**/*.xml]
    E --> F[外部系统连接<br/>ES客户端: TransportClient<br/>Kafka客户端: KafkaProducer<br/>HBase客户端: Connection]
    F --> G[Web容器启动<br/>Undertow<br/>端口: 8085<br/>上下文: /maintain]
    G --> H[WebSocket启动<br/>端口: 8089<br/>线程数: 10<br/>超时: 30秒]
    H --> I[定时任务启动<br/>@EnableScheduling<br/>数据清理、报告生成<br/>集群数据同步]
    I --> J[ICE服务注册<br/>IceMaintainServer<br/>接口: /request<br/>协议: ICE]
    J --> K[应用就绪<br/>ContextRefreshedEvent<br/>ApplicationListener<br/>状态: RUNNING]
```

## 11. cloudapi模块类关系图

```mermaid
classDiagram
    class BigdataApi {
        <<interface>>
        +getClusterName() String
        +getHosts(clusterName) List~HostApiDto~
        +getServices(clusterName) List~ServiceApiDto~
        +getComponents(clusterName) List~ComponentApiDto~
        +getAlerts(clusterName) List~CurrentAlertApiDto~
        +getHistoryAlerts(clusterName, from, size) List~HistoryAlertApiDto~
    }
    
    class BigdataClient {
        -BigDataProperties properties
        -String token
        +init() void
        +getClusterName() String
        +getHosts(clusterName) List~HostApiDto~
        +getServices(clusterName) List~ServiceApiDto~
        +getComponents(clusterName) List~ComponentApiDto~
        +getAlerts(clusterName) List~CurrentAlertApiDto~
        +getHistoryAlerts(clusterName, from, size) List~HistoryAlertApiDto~
        -getHttpHeaders() HttpHeaders
        -getUrlPrefix() String
        -getDtoList(jsonStr, key, clazz) List~T~
    }
    
    class BigDataProperties {
        -String endPoint
        -String apiVersion
        -String username
        -String password
    }
    
    class HttpUtil {
        -RestTemplate REST_TEMPLATE
        -HttpHeaders HTTP_HEADERS
        +get(url, params, headers, responseType) T
        +postJson(url, body, headers, responseType) T
        +postForm(url, formData, headers, responseType) T
        +put(url, body, headers, responseType) T
        +delete(url, params, headers, responseType) T
        -buildUrlWithParams(url, params) String
        -logRequest(method, url, params, body, response) void
    }
    
    class BigDataService {
        <<interface>>
        +apiDataToMysql() void
        +pageHost(req) PageVo~BdHostVo~
        +pageService(req) PageVo~BdServiceVo~
        +pageComponent(req) PageVo~BdComponentVo~
        +getAlerts(req) PageVo~BdAlertVo~
        +getHistoryAlerts(req) PageVo~BdHistoryAlertVo~
    }
    
    class BigDataController {
        -BigDataService bigDataService
        +test() Object
        +pageHost(req) Result~PageVo~BdHostVo~~
        +pageService(req) Result~PageVo~BdServiceVo~~
        +pageComponent(req) Result~PageVo~BdComponentVo~~
        +pageAlerts(req) Result~PageVo~BdAlertVo~~
        +pageHistoryAlerts(req) Result~PageVo~BdHistoryAlertVo~~
        +hostAlertCount() Result~List~Tuple~String,Integer~~~
        +serviceAlertCount() Result~List~Tuple~String,Integer~~~
        +componentAlertCount(serviceName) Result~List~Tuple~String,Integer~~~
    }
    
    BigdataApi <|.. BigdataClient
    BigdataClient --> BigDataProperties
    BigdataClient --> HttpUtil
    BigDataController --> BigDataService
    BigDataService --> BigdataClient
```

## 12. 监控数据流转状态图

```mermaid
stateDiagram-v2
    [*] --> AgentStartup: Agent启动
    AgentStartup --> DataCollection: 初始化完成
    
    state DataCollection {
        [*] --> HardwareMonitor
        [*] --> SoftwareMonitor
        [*] --> LogMonitor
        
        HardwareMonitor --> HardwareData: 30秒间隔
        SoftwareMonitor --> SoftwareData: 180秒间��
        LogMonitor --> LogData: 600秒间隔
        
        HardwareData --> DataSend
        SoftwareData --> DataSend
        LogData --> DataSend
    }
    
    DataSend --> ServerReceive: ICE协议传输
    
    state ServerReceive {
        [*] --> RateLimit: 限流检查
        RateLimit --> FlagRoute: flag参数路由
        
        FlagRoute --> HardwareProcess: flag=1
        FlagRoute --> SoftwareProcess: flag=2
        FlagRoute --> BusinessProcess: flag=3
        FlagRoute --> RestartLog: flag=4
        FlagRoute --> GetSoftwareInfo: flag=5
        FlagRoute --> Heartbeat: flag=6
    }
    
    state DataProcess {
        HardwareProcess --> HardwareCalc: CPU/内存/磁盘计算
        SoftwareProcess --> SoftwareCalc: 状态/资源计算
        BusinessProcess --> BusinessCalc: 业务指标处理
        
        HardwareCalc --> AlarmCheck: 告警阈值判断
        SoftwareCalc --> AlarmCheck
        BusinessCalc --> AlarmCheck
    }
    
    ServerReceive --> DataProcess
    DataProcess --> DataStorage: 数据存储
    
    state DataStorage {
        [*] --> MySQL: 主数据存储
        [*] --> Elasticsearch: 日志存储
        [*] --> Kafka: 消息推送
        [*] --> HBase: 大数据存储
    }
    
    DataStorage --> FrontendQuery: 前端查询
    DataStorage --> RealtimePush: 实时推送
    DataStorage --> AlertNotify: 告警通知
    
    FrontendQuery --> [*]: 查询完成
    RealtimePush --> [*]: 推送完成
    AlertNotify --> [*]: 通知完成
```

这些Mermaid图表提供了maintain-server-2.0项目的完整技术视图，每个节点都标注了对应的类名、方法名、配置文件和技术细节，便于理解系统架构和数据流转过程。