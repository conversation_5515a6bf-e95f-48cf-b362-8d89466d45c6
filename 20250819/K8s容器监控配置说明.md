# K8s容器监控配置说明

## 功能概述

本功能实现了K8s容器监控数据的自动采集，将K8s Pod数据转换为与现有裸机程序相同的格式存储到数据库中，前端可以通过现有的程序管理界面查看K8s容器数据。

## 核心特性

1. **数据格式统一**：K8s Pod数据完全按照现有SoftwareVo格式存储
2. **前端零改动**：复用现有程序列表、程序详情等所有前端功能
3. **自动采集**：每30秒自动采集一次K8s数据
4. **状态映射**：Pod状态自动映射为程序状态（Running→绿色，Pending→黄色，Failed→红色）

## 配置步骤

### 1. 修改配置文件

在 `config/application.yml` 文件末尾添加K8s配置：

```yaml
# K8s容器监控配置
k8s:
  enabled: true
  api-server: "https://192.168.40.202:6443"
  token: "your-bearer-token-here"
  namespace: "default"
```

### 2. 配置参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| enabled | 是否启用K8s监控 | true/false |
| api-server | K8s API Server地址 | https://192.168.40.202:6443 |
| token | Bearer Token认证令牌 | eyJhbGciOiJSUzI1NiIsImtpZCI6... |
| namespace | 监控的命名空间 | default |

### 3. 获取Bearer Token

在K8s集群中执行以下命令获取Token：

```bash
# 创建ServiceAccount
kubectl create serviceaccount maintain-monitor

# 创建ClusterRoleBinding
kubectl create clusterrolebinding maintain-monitor --clusterrole=cluster-admin --serviceaccount=default:maintain-monitor

# 获取Token
kubectl get secret $(kubectl get serviceaccount maintain-monitor -o jsonpath='{.secrets[0].name}') -o jsonpath='{.data.token}' | base64 -d
```

### 4. 测试连接

可以使用提供的测试工具验证K8s连接：

```java
// 运行K8sConnectionTest.main()方法
// 查看日志输出确认连接状态
```

## 数据映射关系

| K8s Pod字段 | SoftwareVo字段 | 转换逻辑 |
|------------|----------------|----------|
| Pod名称 | name | 直接映射 |
| Node IP | serverIp/host | 从Pod调度的Node获取IP |
| 容器数量 | processCount | Pod.spec.containers.length |
| Pod状态 | status | Running→GREEN, Pending→YELLOW, Failed→RED |
| 创建时间 | createTime | Pod.metadata.creationTimestamp |
| CPU使用率 | cpuPercent | 从metrics API计算 |
| 内存使用量 | usedMemory | 从metrics API获取 |

## 前端展示效果

启用K8s监控后，前端程序管理页面将显示：

1. **程序列表**：K8s Pod与裸机程序混合显示
2. **状态统计**：包含K8s Pod的状态统计
3. **程序详情**：点击Pod名称可查看详细信息
4. **监控图表**：显示Pod的CPU、内存使用趋势

## 注意事项

1. **Token权限**：确保Token具有足够权限访问Pod和Metrics API
2. **网络连通性**：确保maintain-server能够访问K8s API Server
3. **SSL证书**：代码已自动忽略SSL证书验证
4. **数据刷新**：K8s数据每30秒刷新一次
5. **命名空间**：目前只监控指定命名空间的Pod

## 故障排查

### 1. 连接失败
- 检查API Server地址是否正确
- 确认网络连通性
- 验证Token是否有效

### 2. 无数据显示
- 检查namespace配置是否正确
- 确认Pod是否存在
- 查看应用日志中的错误信息

### 3. 指标数据缺失
- 确认K8s集群已安装metrics-server
- 检查metrics API是否可访问

## 日志查看

K8s监控相关日志会输出到应用日志中，关键日志包括：

```
开始采集K8s数据...
K8s数据采集完成，处理了X个Pod
获取Pod列表失败
处理Pod数据失败
```

## 作者信息

- 作者：xiong
- 日期：2025-01-19
- 版本：1.0