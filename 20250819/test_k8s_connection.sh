#!/bin/bash

echo "K8s连接测试工具"
echo "=================="

cd "$(dirname "$0")"

echo "正在编译Java文件..."
javac -cp "target/classes:target/lib/*" src/main/java/com/maintain/server/k8s/K8sConnectionTest.java

if [ $? -ne 0 ]; then
    echo "编译失败，请检查Java环境"
    exit 1
fi

echo "正在运行连接测试..."
java -cp "src/main/java:target/classes:target/lib/*" com.maintain.server.k8s.K8sConnectionTest

echo ""
echo "测试完成，请查看上方输出结果"