@echo off
cls
color 2f
echo.
echo **********************************************
echo.
echo һ����װ Java SE Development Kit �� C ��
echo.
echo ��װ�밴��������˳�ֱ�ӹرմ���
echo.
echo **********************************************
echo.
pause
set myjavapath=C:\Program Files\Java\jdk1.8.0_102
echo. 
if exist "%myjavapath%\bin\java.exe" (
echo. 
echo �� C �̷���java����
echo.
echo ���û��������밴�����, ������������Ѿ����ú�ֱ�ӹرմ���
echo �˳�ֱ�ӹرմ���
echo.
pause
goto SETENV
)
 
echo.
echo ���ڰ�װjdk���벻Ҫִ����������
echo.
echo ���Եȣ����ʱ���Լ��Ҫ����������
echo.
start /WAIT jdk-8u102-windows-x64.exe /s INSTALLDIR="%myjavapath%"
echo ����װ��ϣ��������û�������
echo.
goto SETENV
 
:SETENV
setx /M JAVA_HOME "%myjavapath%"
setx /M CLASSPATH .;"%myjavapath%\lib\tools.jar";"%myjavapath%\lib\dt.jar";"%myjavapath%\jre\lib\rt.jar"
setx /M PATH "%PATH%;%myjavapath%\bin"
echo.
echo JDK�����������óɹ� 
echo.
echo ��װ��ϣ������¿���
echo.
call "%myjavapath%\bin\java.exe" -version
echo.
 
if %errorlevel% == 0 (
echo ף�����ɹ���װ��Java SE Development Kit !
echo.
goto END
)
echo ò�ư�װ���ɹ��������Լ���취��
echo.
goto END
 
:END
pause
exit