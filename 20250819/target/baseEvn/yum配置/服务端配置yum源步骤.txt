第一步:在服务端机器，一般是NN，创建yum源文件夹
7.2版本：
mkdir /var/www/html/centos7/
6.5版本：
mkdir /var/www/html/yum/

第二步:查看yum配置是否正确
vi /etc/httpd/conf/httpd.conf

第三步:寻找DocumentRoot配置项是否是"/var/www/html"
DocumentRoot "/var/www/html"

第四步:不是则修改并重启httpd服务
service httpd restart

第五步:拷贝Packages到对应文件夹下面(Packages从iso镜像文件解压得到)
7.2版本：
cp -r /Packages /var/www/html/centos7/
6.5版本:
cp -r /Packages /var/www/html/yum/

第六步:创建yum
7.2版本：
createrepo /var/www/html/centos7/
6.5版本：
createrepo /var/www/html/yum/

第七步:刷新yum
yum clean all