#!/bin/bash

#安装基础环境
echo "开始配置yum.......，输入yum源IP,一般为 CDH集群 NN节点IP"
if [ -z "$1" ]
then
	echo "请您输入第一个参数,输入yum源IP,一般为 CDH集群 NN节点IP"
	exit;
fi
yumnode=($1)
#判断系统类型
os6=$(grep "6.5" /etc/redhat-release)
if [ -z "$os6" ]
 then
    url="baseurl=http://$yumnode/centos7/"
    echo "本机操作系统为 centos is 7.2"	
else
    url="baseurl=http://$yumnode/yum/"
	echo "本机操作系统为 centos is 6.5"
fi

#修改yum配置
#if [ ! -f "/etc/yum.repos.d/localyum.repo" ]
#	then
	    echo "开始修改yum配置......"
		mkdir /etc/yum.repos.d/bak
		mv /etc/yum.repos.d/*.* /etc/yum.repos.d/bak
		echo "[localyum]" > /etc/yum.repos.d/localyum.repo
		echo "Name=localyum" >> /etc/yum.repos.d/localyum.repo
		echo "$url" >> /etc/yum.repos.d/localyum.repo
		echo "enable=1" >> /etc/yum.repos.d/localyum.repo
		echo "gpgcheck=0" >> /etc/yum.repos.d/localyum.repo
		yum clean all
		echo "yum配置完成......"	
#fi

if [ ! -f "/root/.ssh/authorized_keys" ]
	then
	    #ssh密码登录安装
		echo "begin install openssh...."
		yum install -y openssh-clients
		yum install -y openssh-askpass
		ssh-keygen -t rsa -f /root/.ssh/id_rsa -P ""
		cat ~/.ssh/id_rsa.pub >> /root/.ssh/authorized_keys
		chmod 600 /root/.ssh/authorized_keys
		echo "请执 which scp 指令判断scp指令是否安装"
fi

# 安装 ntp 服务
chattr -i /ect/passwd
echo "begin install ntp server ..."
yum install -y ntp
yes|cp /usr/share/zoneinfo/Asia/Chongqing /etc/localtime
ntpdir=/etc/ntp.conf
mv $ntpdir /etc/ntp.confbackup
echo "driftfile /var/lib/ntp/drift" >> $ntpdir
echo "server $yumnode" >> $ntpdir
echo "includefile /etc/ntp/crypto/pw" >> $ntpdir
echo "keys /etc/ntp/keys" >> $ntpdir
chkconfig ntpd on
if [ -z "$os6" ]
 then
    systemctl enable ntpd.service
    systemctl stop ntpd.service
    ntpdate $yumnode
    systemctl start ntpd.service	
else
    chkconfig ntpd on
    service ntpd stop
    ntpdate $yumnode
    service ntpd start
fi
chattr +i /ect/passwd
echo "时钟设置成功!"
echo "ntp install success......"	

