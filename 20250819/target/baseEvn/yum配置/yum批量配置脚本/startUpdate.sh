#!/bin/bash

#安装基础环境
echo "开始配置yum....yum源IP,一般为 CDH集群 NN节点IP"
echo "总共四个参数：分别为 CDH NN 节点IP, 其他主机IP，密码，远程端口"
if [ -z "$1" ]
then
	echo "请您输入第一个参数,输入yum源IP,一般为 CDH集群 NN节点IP"
	exit;
fi
if [ -z "$2" ]
then
	echo "请您输入第二个参数,需要修改yum的主机IP，多个主机用,分割（'**************,**************'）且所有主机登录密码一样"
	exit;
fi
if [ -z "$3" ]
then
	echo "请您输入第三个参数,主机密码"
	exit;
fi
if [ -z "$4" ]
then
	echo "请您输入第四个参数,访问主机的scp 端口"
	exit;
fi
yumnode=($1)
nodes=($2)
passwd=($3)
port=($4)
dir=`pwd`
chmod +x updateYum.sh startUpdate.sh scp.ex
for node in ${nodes[@]};
do
	echo "开始设置${node}上的yum 配置以及安装 ntp服务"
	$dir/scp.ex $node $passwd $port $yumnode
	echo "${node}上的yum 配置以及安装 ntp服务完成"
done
