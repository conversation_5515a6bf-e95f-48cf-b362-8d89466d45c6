Centos 安装配置 ntp 方法
1、将 yumConfig 文件拷贝到 任意一台centos机器任意目录下下（一般为CDH NN主机的/root/目录下）
2、执行脚本 sh startUpdate.sh
    sh startUpdate.sh ************ '*************** ***************' 1qaz2wsx#EDC 22
	参数1：yum 源主机IP,一般为CDH NN主机
	参数2：修改更新yum配置以及安装ntp服务的主机，支持多个相同密码的主机如 '*************** ***************'
	参数3：主机密码
	参数4：远程主机端口

winserver2016 开启配置 ntp 方法：
1、修改开启 Windows Time 服务,并设置成开机自启动
2、点击右下角时间-->日期和时间设置-->区域和语言-->其它日期、时间和区域设置-->日期和时间-->Internet时间-->更改设置-->修改服务器配置（time.windows.com为 CDH NN 节点IP）
3、立即更新，成功后会显示 时钟在 XXX 与 IP 同步成功