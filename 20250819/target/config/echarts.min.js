!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),a&&(i.edge=!0,i.version=a[1]),o&&(i.weChat=!0),{browser:i,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=11),domSupported:"undefined"!=typeof document}}function i(t,e){"createCanvas"===t&&(rp=null),ip[t]=e}function n(t){if(null==t||"object"!=typeof t)return t;var e=t,i=Zf.call(t);if("[object Array]"===i){if(!z(t)){e=[];for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}else if(qf[i]){if(!z(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}}else if(!Yf[i]&&!z(t)&&!T(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=n(t[s]))}return e}function r(t,e,i){if(!S(e)||!S(t))return i?n(e):t;for(var a in e)if(e.hasOwnProperty(a)){var o=t[a],s=e[a];!S(s)||!S(o)||x(s)||x(o)||T(s)||T(o)||M(s)||M(o)||z(s)||z(o)?!i&&a in t||(t[a]=n(e[a],!0)):r(o,s,i)}return t}function a(t,e){for(var i=t[0],n=1,a=t.length;a>n;n++)i=r(i,t[n],e);return i}function o(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function s(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function l(){return rp||(rp=np().getContext("2d")),rp}function h(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i}return-1}function u(t,e){function i(){}var n=t.prototype;i.prototype=e.prototype,t.prototype=new i;for(var r in n)t.prototype[r]=n[r];t.prototype.constructor=t,t.superClass=e}function c(t,e,i){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,s(t,e,i)}function d(t){return t?"string"==typeof t?!1:"number"==typeof t.length:void 0}function f(t,e,i){if(t&&e)if(t.forEach&&t.forEach===Kf)t.forEach(e,i);else if(t.length===+t.length)for(var n=0,r=t.length;r>n;n++)e.call(i,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function p(t,e,i){if(t&&e){if(t.map&&t.map===tp)return t.map(e,i);for(var n=[],r=0,a=t.length;a>r;r++)n.push(e.call(i,t[r],r,t));return n}}function g(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===ep)return t.reduce(e,i,n);for(var r=0,a=t.length;a>r;r++)i=e.call(n,i,t[r],r,t);return i}}function v(t,e,i){if(t&&e){if(t.filter&&t.filter===Qf)return t.filter(e,i);for(var n=[],r=0,a=t.length;a>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function m(t,e,i){if(t&&e)for(var n=0,r=t.length;r>n;n++)if(e.call(i,t[n],n,t))return t[n]}function y(t,e){var i=Jf.call(arguments,2);return function(){return t.apply(e,i.concat(Jf.call(arguments)))}}function _(t){var e=Jf.call(arguments,1);return function(){return t.apply(this,e.concat(Jf.call(arguments)))}}function x(t){return"[object Array]"===Zf.call(t)}function w(t){return"function"==typeof t}function b(t){return"[object String]"===Zf.call(t)}function S(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function M(t){return!!Yf[Zf.call(t)]}function I(t){return!!qf[Zf.call(t)]}function T(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function C(t){return t!==t}function D(){for(var t=0,e=arguments.length;e>t;t++)if(null!=arguments[t])return arguments[t]}function A(t,e){return null!=t?t:e}function k(t,e,i){return null!=t?t:null!=e?e:i}function P(){return Function.call.apply(Jf,arguments)}function L(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function O(t,e){if(!t)throw new Error(e)}function E(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function B(t){t[ap]=!0}function z(t){return t[ap]}function R(t){function e(t,e){i?n.set(t,e):n.set(e,t)}var i=x(t);this.data={};var n=this;t instanceof R?t.each(e):t&&f(t,e)}function N(t){return new R(t)}function F(t,e){for(var i=new t.constructor(t.length+e.length),n=0;n<t.length;n++)i[n]=t[n];var r=t.length;for(n=0;n<e.length;n++)i[n+r]=e[n];return i}function V(){}function H(t,e){var i=new sp(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function W(t,e){return t[0]=e[0],t[1]=e[1],t}function G(t){var e=new sp(2);return e[0]=t[0],e[1]=t[1],e}function U(t,e,i){return t[0]=e,t[1]=i,t}function X(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t}function j(t,e,i,n){return t[0]=e[0]+i[0]*n,t[1]=e[1]+i[1]*n,t}function Y(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function q(t){return Math.sqrt(Z(t))}function Z(t){return t[0]*t[0]+t[1]*t[1]}function $(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t}function K(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t}function Q(t,e){return t[0]*e[0]+t[1]*e[1]}function J(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t}function te(t,e){var i=q(e);return 0===i?(t[0]=0,t[1]=0):(t[0]=e[0]/i,t[1]=e[1]/i),t}function ee(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function ie(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function ne(t,e){return t[0]=-e[0],t[1]=-e[1],t}function re(t,e,i,n){return t[0]=e[0]+n*(i[0]-e[0]),t[1]=e[1]+n*(i[1]-e[1]),t}function ae(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t}function oe(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t}function se(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}function le(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}function he(t,e){return{target:t,topTarget:e&&e.topTarget}}function ue(t,e){var i=t._$eventProcessor;return null!=e&&i&&i.normalizeQuery&&(e=i.normalizeQuery(e)),e}function ce(t,e,i,n,r,a){var o=t._$handlers;if("function"==typeof i&&(r=n,n=i,i=null),!n||!e)return t;i=ue(t,i),o[e]||(o[e]=[]);for(var s=0;s<o[e].length;s++)if(o[e][s].h===n)return t;var l={h:n,one:a,query:i,ctx:r||t,callAtLast:n.zrEventfulCallAtLast},h=o[e].length-1,u=o[e][h];return u&&u.callAtLast?o[e].splice(h,0,l):o[e].push(l),t}function de(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function fe(t,e,i,n){return i=i||{},n||!jf.canvasSupported?pe(t,e,i):jf.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):pe(t,e,i),i}function pe(t,e,i){var n=de(t);i.zrX=e.clientX-n.left,i.zrY=e.clientY-n.top}function ge(t,e,i){if(e=e||window.event,null!=e.zrX)return e;var n=e.type,r=n&&n.indexOf("touch")>=0;if(r){var a="touchend"!==n?e.targetTouches[0]:e.changedTouches[0];a&&fe(t,a,e,i)}else fe(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&vp.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function ve(t,e,i){gp?t.addEventListener(e,i):t.attachEvent("on"+e,i)}function me(t,e,i){gp?t.removeEventListener(e,i):t.detachEvent("on"+e,i)}function ye(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function _e(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function xe(t,e,i){return{type:t,event:i,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch,which:i.which,stop:we}}function we(){mp(this.event)}function be(){}function Se(t,e,i){if(t[t.rectHover?"rectContain":"contain"](e,i)){for(var n,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,i))return!1;r.silent&&(n=!0),r=r.parent}return n?xp:!0}return!1}function Me(){var t=new Sp(6);return Ie(t),t}function Ie(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Te(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Ce(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t}function De(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function Ae(t,e,i){var n=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],h=Math.sin(i),u=Math.cos(i);return t[0]=n*u+o*h,t[1]=-n*h+o*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*a+h*l,t[5]=u*l-h*a,t}function ke(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t}function Pe(t,e){var i=e[0],n=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=i*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-o*r)*l,t[5]=(a*r-i*s)*l,t):null}function Le(t){var e=Me();return Te(e,t),e}function Oe(t){return t>Tp||-Tp>t}function Ee(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function Be(t){return t=Math.round(t),0>t?0:t>255?255:t}function ze(t){return t=Math.round(t),0>t?0:t>360?360:t}function Re(t){return 0>t?0:t>1?1:t}function Ne(t){return Be(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function Fe(t){return Re(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function Ve(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function He(t,e,i){return t+(e-t)*i}function We(t,e,i,n,r){return t[0]=e,t[1]=i,t[2]=n,t[3]=r,t}function Ge(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function Ue(t,e){Vp&&Ge(Vp,e),Vp=Fp.put(t,Vp||e.slice())}function Xe(t,e){if(t){e=e||[];var i=Fp.get(t);if(i)return Ge(e,i);t+="";var n=t.replace(/ /g,"").toLowerCase();if(n in Np)return Ge(e,Np[n]),Ue(t,e),e;if("#"!==n.charAt(0)){var r=n.indexOf("("),a=n.indexOf(")");if(-1!==r&&a+1===n.length){var o=n.substr(0,r),s=n.substr(r+1,a-(r+1)).split(","),l=1;switch(o){case"rgba":if(4!==s.length)return void We(e,0,0,0,1);l=Fe(s.pop());case"rgb":return 3!==s.length?void We(e,0,0,0,1):(We(e,Ne(s[0]),Ne(s[1]),Ne(s[2]),l),Ue(t,e),e);case"hsla":return 4!==s.length?void We(e,0,0,0,1):(s[3]=Fe(s[3]),je(s,e),Ue(t,e),e);case"hsl":return 3!==s.length?void We(e,0,0,0,1):(je(s,e),Ue(t,e),e);default:return}}We(e,0,0,0,1)}else{if(4===n.length){var h=parseInt(n.substr(1),16);return h>=0&&4095>=h?(We(e,(3840&h)>>4|(3840&h)>>8,240&h|(240&h)>>4,15&h|(15&h)<<4,1),Ue(t,e),e):void We(e,0,0,0,1)}if(7===n.length){var h=parseInt(n.substr(1),16);return h>=0&&16777215>=h?(We(e,(16711680&h)>>16,(65280&h)>>8,255&h,1),Ue(t,e),e):void We(e,0,0,0,1)}}}}function je(t,e){var i=(parseFloat(t[0])%360+360)%360/360,n=Fe(t[1]),r=Fe(t[2]),a=.5>=r?r*(n+1):r+n-r*n,o=2*r-a;return e=e||[],We(e,Be(255*Ve(o,a,i+1/3)),Be(255*Ve(o,a,i)),Be(255*Ve(o,a,i-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Ye(t){if(t){var e,i,n=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(n,r,a),s=Math.max(n,r,a),l=s-o,h=(s+o)/2;if(0===l)e=0,i=0;else{i=.5>h?l/(s+o):l/(2-s-o);var u=((s-n)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-a)/6+l/2)/l;n===s?e=d-c:r===s?e=1/3+u-d:a===s&&(e=2/3+c-u),0>e&&(e+=1),e>1&&(e-=1)}var f=[360*e,i,h];return null!=t[3]&&f.push(t[3]),f}}function qe(t,e){var i=Xe(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0,i[n]>255?i[n]=255:t[n]<0&&(i[n]=0);return ti(i,4===i.length?"rgba":"rgb")}}function Ze(t){var e=Xe(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function $e(t,e,i){if(e&&e.length&&t>=0&&1>=t){i=i||[];var n=t*(e.length-1),r=Math.floor(n),a=Math.ceil(n),o=e[r],s=e[a],l=n-r;return i[0]=Be(He(o[0],s[0],l)),i[1]=Be(He(o[1],s[1],l)),i[2]=Be(He(o[2],s[2],l)),i[3]=Re(He(o[3],s[3],l)),i}}function Ke(t,e,i){if(e&&e.length&&t>=0&&1>=t){var n=t*(e.length-1),r=Math.floor(n),a=Math.ceil(n),o=Xe(e[r]),s=Xe(e[a]),l=n-r,h=ti([Be(He(o[0],s[0],l)),Be(He(o[1],s[1],l)),Be(He(o[2],s[2],l)),Re(He(o[3],s[3],l))],"rgba");return i?{color:h,leftIndex:r,rightIndex:a,value:n}:h}}function Qe(t,e,i,n){return t=Xe(t),t?(t=Ye(t),null!=e&&(t[0]=ze(e)),null!=i&&(t[1]=Fe(i)),null!=n&&(t[2]=Fe(n)),ti(je(t),"rgba")):void 0}function Je(t,e){return t=Xe(t),t&&null!=e?(t[3]=Re(e),ti(t,"rgba")):void 0}function ti(t,e){if(t&&t.length){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}}function ei(t,e){return t[e]}function ii(t,e,i){t[e]=i}function ni(t,e,i){return(e-t)*i+t}function ri(t,e,i){return i>.5?e:t}function ai(t,e,i,n,r){var a=t.length;if(1===r)for(var o=0;a>o;o++)n[o]=ni(t[o],e[o],i);else for(var s=a&&t[0].length,o=0;a>o;o++)for(var l=0;s>l;l++)n[o][l]=ni(t[o][l],e[o][l],i)}function oi(t,e,i){var n=t.length,r=e.length;if(n!==r){var a=n>r;if(a)t.length=r;else for(var o=n;r>o;o++)t.push(1===i?e[o]:Up.call(e[o]))}for(var s=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function si(t,e,i){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0].length,r=0;n>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function li(t,e,i,n,r,a,o,s,l){var h=t.length;if(1===l)for(var u=0;h>u;u++)s[u]=hi(t[u],e[u],i[u],n[u],r,a,o);else for(var c=t[0].length,u=0;h>u;u++)for(var d=0;c>d;d++)s[u][d]=hi(t[u][d],e[u][d],i[u][d],n[u][d],r,a,o)}function hi(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function ui(t){if(d(t)){var e=t.length;if(d(t[0])){for(var i=[],n=0;e>n;n++)i.push(Up.call(t[n]));return i}return Up.call(t)}return t}function ci(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function di(t){var e=t[t.length-1].value;return d(e&&e[0])?2:1}function fi(t,e,i,n,r,a){var o=t._getter,s=t._setter,l="spline"===e,h=n.length;if(h){var u,c=n[0].value,f=d(c),p=!1,g=!1,v=f?di(n):0;n.sort(function(t,e){return t.time-e.time}),u=n[h-1].time;for(var m=[],y=[],_=n[0].value,x=!0,w=0;h>w;w++){m.push(n[w].time/u);var b=n[w].value;if(f&&si(b,_,v)||!f&&b===_||(x=!1),_=b,"string"==typeof b){var S=Xe(b);S?(b=S,p=!0):g=!0}y.push(b)}if(a||!x){for(var M=y[h-1],w=0;h-1>w;w++)f?oi(y[w],M,v):!isNaN(y[w])||isNaN(M)||g||p||(y[w]=M);f&&oi(o(t._target,r),M,v);var I,T,C,D,A,k,P=0,L=0;if(p)var O=[0,0,0,0];var E=function(t,e){var i;if(0>e)i=0;else if(L>e){for(I=Math.min(P+1,h-1),i=I;i>=0&&!(m[i]<=e);i--);i=Math.min(i,h-2)}else{for(i=P;h>i&&!(m[i]>e);i++);i=Math.min(i-1,h-2)}P=i,L=e;var n=m[i+1]-m[i];if(0!==n)if(T=(e-m[i])/n,l)if(D=y[i],C=y[0===i?i:i-1],A=y[i>h-2?h-1:i+1],k=y[i>h-3?h-1:i+2],f)li(C,D,A,k,T,T*T,T*T*T,o(t,r),v);else{var a;if(p)a=li(C,D,A,k,T,T*T,T*T*T,O,1),a=ci(O);else{if(g)return ri(D,A,T);a=hi(C,D,A,k,T,T*T,T*T*T)}s(t,r,a)}else if(f)ai(y[i],y[i+1],T,o(t,r),v);else{var a;if(p)ai(y[i],y[i+1],T,O,1),a=ci(O);else{if(g)return ri(y[i],y[i+1],T);a=ni(y[i],y[i+1],T)}s(t,r,a)}},B=new Ee({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:E,ondestroy:i});return e&&"spline"!==e&&(B.easing=e),B}}}function pi(t,e,i,n,r,a,o,s){function l(){u--,u||a&&a()}b(n)?(a=r,r=n,n=0):w(r)?(a=r,r="linear",n=0):w(n)?(a=n,n=0):w(i)?(a=i,i=500):i||(i=500),t.stopAnimation(),gi(t,"",t,e,i,n,s);var h=t.animators.slice(),u=h.length;u||a&&a();for(var c=0;c<h.length;c++)h[c].done(l).start(r,o)}function gi(t,e,i,n,r,a,o){var s={},l=0;for(var h in n)n.hasOwnProperty(h)&&(null!=i[h]?S(n[h])&&!d(n[h])?gi(t,e?e+"."+h:h,i[h],n[h],r,a,o):(o?(s[h]=i[h],vi(t,e,h,n[h])):s[h]=n[h],l++):null==n[h]||o||vi(t,e,h,n[h]));l>0&&t.animate(e,!1).when(null==r?500:r,s).delay(a||0)}function vi(t,e,i,n){if(e){var r={};r[e]={},r[e][i]=n,t.attr(r)}else t.attr(i,n)}function mi(t,e,i,n){0>i&&(t+=i,i=-i),0>n&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}function yi(t){for(var e=0;t>=ng;)e|=1&t,t>>=1;return t+e}function _i(t,e,i,n){var r=e+1;if(r===i)return 1;if(n(t[r++],t[e])<0){for(;i>r&&n(t[r],t[r-1])<0;)r++;xi(t,e,r)}else for(;i>r&&n(t[r],t[r-1])>=0;)r++;return r-e}function xi(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function wi(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var a,o=t[n],s=e,l=n;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var h=n-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;h>0;)t[s+h]=t[s+h-1],h--}t[s]=o}}function bi(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])>0){for(s=n-r;s>l&&a(t,e[i+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[i+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])>0?o=u+1:l=u}return l}function Si(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])<0){for(s=r+1;s>l&&a(t,e[i+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}else{for(s=n-r;s>l&&a(t,e[i+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])<0?l=u:o=u+1}return l}function Mi(t,e){function i(t,e){l[c]=t,h[c]=e,c+=1}function n(){for(;c>1;){var t=c-2;if(t>=1&&h[t-1]<=h[t]+h[t+1]||t>=2&&h[t-2]<=h[t]+h[t-1])h[t-1]<h[t+1]&&t--;else if(h[t]>h[t+1])break;a(t)}}function r(){for(;c>1;){var t=c-2;t>0&&h[t-1]<h[t+1]&&t--,a(t)}}function a(i){var n=l[i],r=h[i],a=l[i+1],u=h[i+1];h[i]=r+u,i===c-3&&(l[i+1]=l[i+2],h[i+1]=h[i+2]),c--;var d=Si(t[a],t,n,r,0,e);n+=d,r-=d,0!==r&&(u=bi(t[n+r-1],t,a,u,u-1,e),0!==u&&(u>=r?o(n,r,a,u):s(n,r,a,u)))}function o(i,n,r,a){var o=0;for(o=0;n>o;o++)d[o]=t[i+o];var s=0,l=r,h=i;if(t[h++]=t[l++],0!==--a){if(1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];return void(t[h+a]=d[s])}for(var c,f,p,g=u;;){c=0,f=0,p=!1;do if(e(t[l],d[s])<0){if(t[h++]=t[l++],f++,c=0,0===--a){p=!0;break}}else if(t[h++]=d[s++],c++,f=0,1===--n){p=!0;break}while(g>(c|f));if(p)break;do{if(c=Si(t[l],d,s,n,0,e),0!==c){for(o=0;c>o;o++)t[h+o]=d[s+o];if(h+=c,s+=c,n-=c,1>=n){p=!0;break}}if(t[h++]=t[l++],0===--a){p=!0;break}if(f=bi(d[s],t,l,a,0,e),0!==f){for(o=0;f>o;o++)t[h+o]=t[l+o];if(h+=f,l+=f,a-=f,0===a){p=!0;break}}if(t[h++]=d[s++],1===--n){p=!0;break}g--}while(c>=rg||f>=rg);if(p)break;0>g&&(g=0),g+=2}if(u=g,1>u&&(u=1),1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];t[h+a]=d[s]}else{if(0===n)throw new Error;for(o=0;n>o;o++)t[h+o]=d[s+o]}}else for(o=0;n>o;o++)t[h+o]=d[s+o]}function s(i,n,r,a){var o=0;for(o=0;a>o;o++)d[o]=t[r+o];var s=i+n-1,l=a-1,h=r+a-1,c=0,f=0;if(t[h--]=t[s--],0!==--n){if(1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];return void(t[h]=d[l])}for(var p=u;;){var g=0,v=0,m=!1;do if(e(d[l],t[s])<0){if(t[h--]=t[s--],g++,v=0,0===--n){m=!0;break}}else if(t[h--]=d[l--],v++,g=0,1===--a){m=!0;break}while(p>(g|v));if(m)break;do{if(g=n-Si(d[l],t,i,n,n-1,e),0!==g){for(h-=g,s-=g,n-=g,f=h+1,c=s+1,o=g-1;o>=0;o--)t[f+o]=t[c+o];if(0===n){m=!0;break}}if(t[h--]=d[l--],1===--a){m=!0;break}if(v=a-bi(t[s],d,0,a,a-1,e),0!==v){for(h-=v,l-=v,a-=v,f=h+1,c=l+1,o=0;v>o;o++)t[f+o]=d[c+o];if(1>=a){m=!0;break}}if(t[h--]=t[s--],0===--n){m=!0;break}p--}while(g>=rg||v>=rg);if(m)break;0>p&&(p=0),p+=2}if(u=p,1>u&&(u=1),1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];t[h]=d[l]}else{if(0===a)throw new Error;for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}}else for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}var l,h,u=rg,c=0,d=[];l=[],h=[],this.mergeRuns=n,this.forceMergeRuns=r,this.pushRun=i}function Ii(t,e,i,n){i||(i=0),n||(n=t.length);var r=n-i;if(!(2>r)){var a=0;if(ng>r)return a=_i(t,i,n,e),void wi(t,i,n,i+a,e);var o=new Mi(t,e),s=yi(r);do{if(a=_i(t,i,n,e),s>a){var l=r;l>s&&(l=s),wi(t,i,i+l,i+a,e),a=l}o.pushRun(i,a),o.mergeRuns(),r-=a,i+=a}while(0!==r);o.forceMergeRuns()}}function Ti(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function Ci(t,e,i){var n=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,a=a*i.height+i.y,o=o*i.height+i.y),n=isNaN(n)?0:n,r=isNaN(r)?1:r,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(n,a,r,o);return s}function Di(t,e,i){var n=i.width,r=i.height,a=Math.min(n,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*n+i.x,s=s*r+i.y,l*=a);var h=t.createRadialGradient(o,s,0,o,s,l);return h}function Ai(){return!1}function ki(t,e,i){var n=np(),r=e.getWidth(),a=e.getHeight(),o=n.style;return o&&(o.position="absolute",o.left=0,o.top=0,o.width=r+"px",o.height=a+"px",n.setAttribute("data-zr-dom-id",t)),n.width=r*i,n.height=a*i,n}function Pi(t){if("string"==typeof t){var e=yg.get(t);return e&&e.image}return t}function Li(t,e,i,n,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!i)return e;var a=yg.get(t),o={hostEl:i,cb:n,cbPayload:r};return a?(e=a.image,!Ei(e)&&a.pending.push(o)):(e=new Image,e.onload=e.onerror=Oi,yg.put(t,e.__cachedImgObj={image:e,pending:[o]}),e.src=e.__zrImageSrc=t),e}return t}return e}function Oi(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var i=t.pending[e],n=i.cb;n&&n(this,i.cbPayload),i.hostEl.dirty()}t.pending.length=0}function Ei(t){return t&&t.width&&t.height}function Bi(t,e){Mg[t]=e}function zi(t,e){e=e||Sg;var i=t+":"+e;if(_g[i])return _g[i];for(var n=(t+"").split("\n"),r=0,a=0,o=n.length;o>a;a++)r=Math.max(qi(n[a],e).width,r);return xg>wg&&(xg=0,_g={}),xg++,_g[i]=r,r}function Ri(t,e,i,n,r,a,o,s){return o?Fi(t,e,i,n,r,a,o,s):Ni(t,e,i,n,r,a,s)}function Ni(t,e,i,n,r,a,o){var s=Zi(t,e,r,a,o),l=zi(t,e);r&&(l+=r[1]+r[3]);var h=s.outerHeight,u=Vi(0,l,i),c=Hi(0,h,n),d=new mi(u,c,l,h);return d.lineHeight=s.lineHeight,d}function Fi(t,e,i,n,r,a,o,s){var l=$i(t,{rich:o,truncate:s,font:e,textAlign:i,textPadding:r,textLineHeight:a}),h=l.outerWidth,u=l.outerHeight,c=Vi(0,h,i),d=Hi(0,u,n);return new mi(c,d,h,u)}function Vi(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function Hi(t,e,i){return"middle"===i?t-=e/2:"bottom"===i&&(t-=e),t}function Wi(t,e,i){var n=e.x,r=e.y,a=e.height,o=e.width,s=a/2,l="left",h="top";switch(t){case"left":n-=i,r+=s,l="right",h="middle";break;case"right":n+=i+o,r+=s,h="middle";break;case"top":n+=o/2,r-=i,l="center",h="bottom";break;case"bottom":n+=o/2,r+=a+i,l="center";break;case"inside":n+=o/2,r+=s,l="center",h="middle";break;case"insideLeft":n+=i,r+=s,h="middle";break;case"insideRight":n+=o-i,r+=s,l="right",h="middle";break;case"insideTop":n+=o/2,r+=i,l="center";break;case"insideBottom":n+=o/2,r+=a-i,l="center",h="bottom";break;case"insideTopLeft":n+=i,r+=i;break;case"insideTopRight":n+=o-i,r+=i,l="right";break;case"insideBottomLeft":n+=i,r+=a-i,h="bottom";break;case"insideBottomRight":n+=o-i,r+=a-i,l="right",h="bottom"}return{x:n,y:r,textAlign:l,textVerticalAlign:h}}function Gi(t,e,i,n,r){if(!e)return"";var a=(t+"").split("\n");r=Ui(e,i,n,r);for(var o=0,s=a.length;s>o;o++)a[o]=Xi(a[o],r);return a.join("\n")}function Ui(t,e,i,n){n=o({},n),n.font=e;var i=A(i,"...");n.maxIterations=A(n.maxIterations,2);var r=n.minChar=A(n.minChar,0);n.cnCharWidth=zi("国",e);var a=n.ascCharWidth=zi("a",e);n.placeholder=A(n.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;r>l&&s>=a;l++)s-=a;var h=zi(i,e);return h>s&&(i="",h=0),s=t-h,n.ellipsis=i,n.ellipsisWidth=h,n.contentWidth=s,n.containerWidth=t,n}function Xi(t,e){var i=e.containerWidth,n=e.font,r=e.contentWidth;if(!i)return"";var a=zi(t,n);if(i>=a)return t;for(var o=0;;o++){if(r>=a||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?ji(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,s),a=zi(t,n)}return""===t&&(t=e.placeholder),t}function ji(t,e,i,n){for(var r=0,a=0,o=t.length;o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?i:n}return a}function Yi(t){return zi("国",t)}function qi(t,e){return Mg.measureText(t,e)}function Zi(t,e,i,n,r){null!=t&&(t+="");var a=A(n,Yi(e)),o=t?t.split("\n"):[],s=o.length*a,l=s;if(i&&(l+=i[0]+i[2]),t&&r){var h=r.outerHeight,u=r.outerWidth;if(null!=h&&l>h)t="",o=[];else if(null!=u)for(var c=Ui(u-(i?i[1]+i[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),d=0,f=o.length;f>d;d++)o[d]=Xi(o[d],c)}return{lines:o,height:s,outerHeight:l,lineHeight:a}}function $i(t,e){var i={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return i;for(var n,r=bg.lastIndex=0;null!=(n=bg.exec(t));){var a=n.index;a>r&&Ki(i,t.substring(r,a)),Ki(i,n[2],n[1]),r=bg.lastIndex}r<t.length&&Ki(i,t.substring(r,t.length));var o=i.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f&&(f-=u[0]+u[2]));for(var p=0;p<o.length;p++){for(var g=o[p],v=0,m=0,y=0;y<g.tokens.length;y++){var _=g.tokens[y],x=_.styleName&&e.rich[_.styleName]||{},w=_.textPadding=x.textPadding,b=_.font=x.font||e.font,S=_.textHeight=A(x.textHeight,Yi(b));if(w&&(S+=w[0]+w[2]),_.height=S,_.lineHeight=k(x.textLineHeight,e.textLineHeight,S),_.textAlign=x&&x.textAlign||e.textAlign,_.textVerticalAlign=x&&x.textVerticalAlign||"middle",null!=f&&s+_.lineHeight>f)return{lines:[],width:0,height:0};_.textWidth=zi(_.text,b);var M=x.textWidth,I=null==M||"auto"===M;if("string"==typeof M&&"%"===M.charAt(M.length-1))_.percentWidth=M,h.push(_),M=0;else{if(I){M=_.textWidth;var T=x.textBackgroundColor,C=T&&T.image;C&&(C=Pi(C),Ei(C)&&(M=Math.max(M,C.width*S/C.height)))}var D=w?w[1]+w[3]:0;M+=D;var P=null!=d?d-m:null;null!=P&&M>P&&(!I||D>P?(_.text="",_.textWidth=M=0):(_.text=Gi(_.text,P-D,b,c.ellipsis,{minChar:c.minChar}),_.textWidth=zi(_.text,b),M=_.textWidth+D))}m+=_.width=M,x&&(v=Math.max(v,_.lineHeight))}g.width=m,g.lineHeight=v,s+=v,l=Math.max(l,m)}i.outerWidth=i.width=A(e.textWidth,l),i.outerHeight=i.height=A(e.textHeight,s),u&&(i.outerWidth+=u[1]+u[3],i.outerHeight+=u[0]+u[2]);for(var p=0;p<h.length;p++){var _=h[p],L=_.percentWidth;_.width=parseInt(L,10)/100*l}return i}function Ki(t,e,i){for(var n=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:i,text:s,isLineHolder:!s&&!n};if(o)a.push({tokens:[l]});else{var h=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||n)&&h.push(l)}}}function Qi(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&E(e)||t.textFont||t.font}function Ji(t,e){var i,n,r,a,o=e.x,s=e.y,l=e.width,h=e.height,u=e.r;0>l&&(o+=l,l=-l),0>h&&(s+=h,h=-h),"number"==typeof u?i=n=r=a=u:u instanceof Array?1===u.length?i=n=r=a=u[0]:2===u.length?(i=r=u[0],n=a=u[1]):3===u.length?(i=u[0],n=a=u[1],r=u[2]):(i=u[0],n=u[1],r=u[2],a=u[3]):i=n=r=a=0;var c;i+n>l&&(c=i+n,i*=l/c,n*=l/c),r+a>l&&(c=r+a,r*=l/c,a*=l/c),n+r>h&&(c=n+r,n*=h/c,r*=h/c),i+a>h&&(c=i+a,i*=h/c,a*=h/c),t.moveTo(o+i,s),t.lineTo(o+l-n,s),0!==n&&t.arc(o+l-n,s+n,n,-Math.PI/2,0),t.lineTo(o+l,s+h-r),0!==r&&t.arc(o+l-r,s+h-r,r,0,Math.PI/2),t.lineTo(o+a,s+h),0!==a&&t.arc(o+a,s+h-a,a,Math.PI/2,Math.PI),t.lineTo(o,s+i),0!==i&&t.arc(o+i,s+i,i,Math.PI,1.5*Math.PI)}function tn(t){return en(t),f(t.rich,en),t}function en(t){if(t){t.font=Qi(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||Tg[e]?e:"left";var i=t.textVerticalAlign||t.textBaseline;"center"===i&&(i="middle"),t.textVerticalAlign=null==i||Cg[i]?i:"top";var n=t.textPadding;n&&(t.textPadding=L(t.textPadding))}}function nn(t,e,i,n,r,a){n.rich?an(t,e,i,n,r,a):rn(t,e,i,n,r,a)}function rn(t,e,i,n,r,a){var o,s=hn(n),l=!1,h=e.__attrCachedBy===lg.PLAIN_TEXT;a!==hg?(a&&(o=a.style,l=!s&&h&&o),e.__attrCachedBy=s?lg.NONE:lg.PLAIN_TEXT):h&&(e.__attrCachedBy=lg.NONE);var u=n.font||Ig;l&&u===(o.font||Ig)||(e.font=u);var c=t.__computedFont;t.__styleFont!==u&&(t.__styleFont=u,c=t.__computedFont=e.font);var d=n.textPadding,f=n.textLineHeight,p=t.__textCotentBlock;(!p||t.__dirtyText)&&(p=t.__textCotentBlock=Zi(i,c,d,f,n.truncate));var g=p.outerHeight,v=p.lines,m=p.lineHeight,y=dn(g,n,r),_=y.baseX,x=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;sn(e,n,r,_,x);var S=Hi(x,g,b),M=_,I=S;if(s||d){var T=zi(i,c),C=T;d&&(C+=d[1]+d[3]);var D=Vi(_,C,w);s&&un(t,e,n,D,S,C,g),d&&(M=mn(_,w,d),I+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=n.opacity||1;for(var A=0;A<Dg.length;A++){var k=Dg[A],P=k[0],L=k[1],O=n[P];l&&O===o[P]||(e[L]=sg(e,L,O||k[2]))}I+=m/2;var E=n.textStrokeWidth,B=l?o.textStrokeWidth:null,z=!l||E!==B,R=!l||z||n.textStroke!==o.textStroke,N=pn(n.textStroke,E),F=gn(n.textFill);if(N&&(z&&(e.lineWidth=E),R&&(e.strokeStyle=N)),F&&(l&&n.textFill===o.textFill||(e.fillStyle=F)),1===v.length)N&&e.strokeText(v[0],M,I),F&&e.fillText(v[0],M,I);else for(var A=0;A<v.length;A++)N&&e.strokeText(v[A],M,I),F&&e.fillText(v[A],M,I),I+=m}function an(t,e,i,n,r,a){a!==hg&&(e.__attrCachedBy=lg.NONE);var o=t.__textCotentBlock;(!o||t.__dirtyText)&&(o=t.__textCotentBlock=$i(i,n)),on(t,e,o,n,r)}function on(t,e,i,n,r){var a=i.width,o=i.outerWidth,s=i.outerHeight,l=n.textPadding,h=dn(s,n,r),u=h.baseX,c=h.baseY,d=h.textAlign,f=h.textVerticalAlign;sn(e,n,r,u,c);var p=Vi(u,o,d),g=Hi(c,s,f),v=p,m=g;l&&(v+=l[3],m+=l[0]);var y=v+a;hn(n)&&un(t,e,n,p,g,o,s);for(var _=0;_<i.lines.length;_++){for(var x,w=i.lines[_],b=w.tokens,S=b.length,M=w.lineHeight,I=w.width,T=0,C=v,D=y,A=S-1;S>T&&(x=b[T],!x.textAlign||"left"===x.textAlign);)ln(t,e,x,n,M,m,C,"left"),I-=x.width,C+=x.width,T++;for(;A>=0&&(x=b[A],"right"===x.textAlign);)ln(t,e,x,n,M,m,D,"right"),I-=x.width,D-=x.width,A--;for(C+=(a-(C-v)-(y-D)-I)/2;A>=T;)x=b[T],ln(t,e,x,n,M,m,C+x.width/2,"center"),C+=x.width,T++;m+=M}}function sn(t,e,i,n,r){if(i&&e.textRotation){var a=e.textOrigin;"center"===a?(n=i.width/2+i.x,r=i.height/2+i.y):a&&(n=a[0]+i.x,r=a[1]+i.y),t.translate(n,r),t.rotate(-e.textRotation),t.translate(-n,-r)}}function ln(t,e,i,n,r,a,o,s){var l=n.rich[i.styleName]||{};l.text=i.text;var h=i.textVerticalAlign,u=a+r/2;"top"===h?u=a+i.height/2:"bottom"===h&&(u=a+r-i.height/2),!i.isLineHolder&&hn(l)&&un(t,e,l,"right"===s?o-i.width:"center"===s?o-i.width/2:o,u-i.height/2,i.width,i.height);var c=i.textPadding;c&&(o=mn(o,s,c),u-=i.height/2-c[2]-i.textHeight/2),fn(e,"shadowBlur",k(l.textShadowBlur,n.textShadowBlur,0)),fn(e,"shadowColor",l.textShadowColor||n.textShadowColor||"transparent"),fn(e,"shadowOffsetX",k(l.textShadowOffsetX,n.textShadowOffsetX,0)),fn(e,"shadowOffsetY",k(l.textShadowOffsetY,n.textShadowOffsetY,0)),fn(e,"textAlign",s),fn(e,"textBaseline","middle"),fn(e,"font",i.font||Ig);var d=pn(l.textStroke||n.textStroke,p),f=gn(l.textFill||n.textFill),p=A(l.textStrokeWidth,n.textStrokeWidth);d&&(fn(e,"lineWidth",p),fn(e,"strokeStyle",d),e.strokeText(i.text,o,u)),f&&(fn(e,"fillStyle",f),e.fillText(i.text,o,u))}function hn(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function un(t,e,i,n,r,a,o){var s=i.textBackgroundColor,l=i.textBorderWidth,h=i.textBorderColor,u=b(s);if(fn(e,"shadowBlur",i.textBoxShadowBlur||0),fn(e,"shadowColor",i.textBoxShadowColor||"transparent"),fn(e,"shadowOffsetX",i.textBoxShadowOffsetX||0),fn(e,"shadowOffsetY",i.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var c=i.textBorderRadius;c?Ji(e,{x:n,y:r,width:a,height:o,r:c}):e.rect(n,r,a,o),e.closePath()}if(u)if(fn(e,"fillStyle",s),null!=i.fillOpacity){var d=e.globalAlpha;e.globalAlpha=i.fillOpacity*i.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(S(s)){var f=s.image;f=Li(f,null,t,cn,s),f&&Ei(f)&&e.drawImage(f,n,r,a,o)}if(l&&h)if(fn(e,"lineWidth",l),fn(e,"strokeStyle",h),null!=i.strokeOpacity){var d=e.globalAlpha;e.globalAlpha=i.strokeOpacity*i.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function cn(t,e){e.image=t}function dn(t,e,i){var n=e.x||0,r=e.y||0,a=e.textAlign,o=e.textVerticalAlign;if(i){var s=e.textPosition;if(s instanceof Array)n=i.x+vn(s[0],i.width),r=i.y+vn(s[1],i.height);else{var l=Wi(s,i,e.textDistance);n=l.x,r=l.y,a=a||l.textAlign,o=o||l.textVerticalAlign}var h=e.textOffset;h&&(n+=h[0],r+=h[1])}return{baseX:n,baseY:r,textAlign:a,textVerticalAlign:o}}function fn(t,e,i){return t[e]=sg(t,e,i),t[e]}function pn(t,e){return null==t||0>=e||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t
}function gn(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function vn(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function mn(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}function yn(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function _n(t){t=t||{},Qp.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new cg(t.style,this),this._rect=null,this.__clipPaths=[]}function xn(t){_n.call(this,t)}function wn(t){return parseInt(t,10)}function bn(t){return t?t.__builtin__?!0:"function"!=typeof t.resize||"function"!=typeof t.refresh?!1:!0:!1}function Sn(t,e,i){return Bg.copy(t.getBoundingRect()),t.transform&&Bg.applyTransform(t.transform),zg.width=e,zg.height=i,!Bg.intersect(zg)}function Mn(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var i=0;i<t.length;i++)if(t[i]!==e[i])return!0}function In(t,e){for(var i=0;i<t.length;i++){var n=t[i];n.setTransform(e),e.beginPath(),n.buildPath(e,n.shape),e.clip(),n.restoreTransform(e)}}function Tn(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}function Cn(t){return"mousewheel"===t&&jf.browser.firefox?"DOMMouseScroll":t}function Dn(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function An(t){var e=t.pointerType;return"pen"===e||"touch"===e}function kn(t){function e(t,e){return function(){return e._touching?void 0:t.apply(e,arguments)}}f(Hg,function(e){t._handlers[e]=y(Ug[e],t)}),f(Gg,function(e){t._handlers[e]=y(Ug[e],t)}),f(Vg,function(i){t._handlers[i]=e(Ug[i],t)})}function Pn(t){function e(e,i){f(e,function(e){ve(t,Cn(e),i._handlers[e])},i)}pp.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._handlers={},kn(this),jf.pointerEventsSupported?e(Gg,this):(jf.touchEventsSupported&&e(Hg,this),e(Vg,this))}function Ln(t,e){var i=new $g(Uf(),t,e);return qg[i.id]=i,i}function On(t){if(t)t.dispose();else{for(var e in qg)qg.hasOwnProperty(e)&&qg[e].dispose();qg={}}return this}function En(t){return qg[t]}function Bn(t,e){Yg[t]=e}function zn(t){delete qg[t]}function Rn(t){return t instanceof Array?t:null==t?[]:[t]}function Nn(t,e,i){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var n=0,r=i.length;r>n;n++){var a=i[n];!t.emphasis[e].hasOwnProperty(a)&&t[e].hasOwnProperty(a)&&(t.emphasis[e][a]=t[e][a])}}}function Fn(t){return!Jg(t)||tv(t)||t instanceof Date?t:t.value}function Vn(t){return Jg(t)&&!(t instanceof Array)}function Hn(t,e){e=(e||[]).slice();var i=p(t||[],function(t){return{exist:t}});return Qg(e,function(t,n){if(Jg(t)){for(var r=0;r<i.length;r++)if(!i[r].option&&null!=t.id&&i[r].exist.id===t.id+"")return i[r].option=t,void(e[n]=null);for(var r=0;r<i.length;r++){var a=i[r].exist;if(!(i[r].option||null!=a.id&&null!=t.id||null==t.name||Un(t)||Un(a)||a.name!==t.name+""))return i[r].option=t,void(e[n]=null)}}}),Qg(e,function(t){if(Jg(t)){for(var e=0;e<i.length;e++){var n=i[e].exist;if(!i[e].option&&!Un(n)&&null==t.id){i[e].option=t;break}}e>=i.length&&i.push({option:t})}}),i}function Wn(t){var e=N();Qg(t,function(t){var i=t.exist;i&&e.set(i.id,t)}),Qg(t,function(t){var i=t.option;O(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),Qg(t,function(t,i){var n=t.exist,r=t.option,a=t.keyInfo;if(Jg(r)){if(a.name=null!=r.name?r.name+"":n?n.name:ev+i,n)a.id=n.id;else if(null!=r.id)a.id=r.id+"";else{var o=0;do a.id="\x00"+a.name+"\x00"+o++;while(e.get(a.id))}e.set(a.id,t)}})}function Gn(t){var e=t.name;return!(!e||!e.indexOf(ev))}function Un(t){return Jg(t)&&t.id&&0===(t.id+"").indexOf("\x00_ec_\x00")}function Xn(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?x(e.dataIndex)?p(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?x(e.name)?p(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function jn(){var t="__\x00ec_inner_"+nv++ +"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}function Yn(t,e,i){if(b(e)){var n={};n[e+"Index"]=0,e=n}var r=i&&i.defaultMainType;!r||qn(e,r+"Index")||qn(e,r+"Id")||qn(e,r+"Name")||(e[r+"Index"]=0);var a={};return Qg(e,function(n,r){var n=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(a[r]=n);var o=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=o[1],l=(o[2]||"").toLowerCase();if(!(!s||!l||null==n||"index"===l&&"none"===n||i&&i.includeMainTypes&&h(i.includeMainTypes,s)<0)){var u={mainType:s};("index"!==l||"all"!==n)&&(u[l]=n);var c=t.queryComponents(u);a[s+"Models"]=c,a[s+"Model"]=c[0]}}),a}function qn(t,e){return t&&t.hasOwnProperty(e)}function Zn(t,e,i){t.setAttribute?t.setAttribute(e,i):t[e]=i}function $n(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function Kn(t){return"auto"===t?jf.domSupported?"html":"richText":t||"html"}function Qn(t){var e={main:"",sub:""};return t&&(t=t.split(rv),e.main=t[0]||"",e.sub=t[1]||""),e}function Jn(t){O(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function tr(t){t.$constructor=t,t.extend=function(t){var e=this,i=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return o(i.prototype,t),i.extend=this.extend,i.superCall=ir,i.superApply=nr,u(i,this),i.superClass=e,i}}function er(t){var e=["__\x00is_clz",ov++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function ir(t,e){var i=P(arguments,2);return this.superClass.prototype[e].apply(t,i)}function nr(t,e,i){return this.superClass.prototype[e].apply(t,i)}function rr(t,e){function i(t){var e=n[t.main];return e&&e[av]||(e=n[t.main]={},e[av]=!0),e}e=e||{};var n={};if(t.registerClass=function(t,e){if(e)if(Jn(e),e=Qn(e),e.sub){if(e.sub!==av){var r=i(e);r[e.sub]=t}}else n[e.main]=t;return t},t.getClass=function(t,e,i){var r=n[t];if(r&&r[av]&&(r=e?r[e]:null),i&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=Qn(t);var e=[],i=n[t.main];return i&&i[av]?f(i,function(t,i){i!==av&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=Qn(t),!!n[t.main]},t.getAllClassMainTypes=function(){var t=[];return f(n,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=Qn(t);var e=n[t.main];return e&&e[av]},t.parseClassType=Qn,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var i=r.call(this,e);return t.registerClass(i,e.type)})}return t}function ar(t){return t>-pv&&pv>t}function or(t){return t>pv||-pv>t}function sr(t,e,i,n,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*n+3*a*i)}function lr(t,e,i,n,r){var a=1-r;return 3*(((e-t)*a+2*(i-e)*r)*a+(n-i)*r*r)}function hr(t,e,i,n,r,a){var o=n+3*(e-i)-t,s=3*(i-2*e+t),l=3*(e-t),h=t-r,u=s*s-3*o*l,c=s*l-9*o*h,d=l*l-3*s*h,f=0;if(ar(u)&&ar(c))if(ar(s))a[0]=0;else{var p=-l/s;p>=0&&1>=p&&(a[f++]=p)}else{var g=c*c-4*u*d;if(ar(g)){var v=c/u,p=-s/o+v,m=-v/2;p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m)}else if(g>0){var y=fv(g),_=u*s+1.5*o*(-c+y),x=u*s+1.5*o*(-c-y);_=0>_?-dv(-_,mv):dv(_,mv),x=0>x?-dv(-x,mv):dv(x,mv);var p=(-s-(_+x))/(3*o);p>=0&&1>=p&&(a[f++]=p)}else{var w=(2*u*s-3*o*c)/(2*fv(u*u*u)),b=Math.acos(w)/3,S=fv(u),M=Math.cos(b),p=(-s-2*S*M)/(3*o),m=(-s+S*(M+vv*Math.sin(b)))/(3*o),I=(-s+S*(M-vv*Math.sin(b)))/(3*o);p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m),I>=0&&1>=I&&(a[f++]=I)}}return f}function ur(t,e,i,n,r){var a=6*i-12*e+6*t,o=9*e+3*n-3*t-9*i,s=3*e-3*t,l=0;if(ar(o)){if(or(a)){var h=-s/a;h>=0&&1>=h&&(r[l++]=h)}}else{var u=a*a-4*o*s;if(ar(u))r[0]=-a/(2*o);else if(u>0){var c=fv(u),h=(-a+c)/(2*o),d=(-a-c)/(2*o);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function cr(t,e,i,n,r,a){var o=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,h=(s-o)*r+o,u=(l-s)*r+s,c=(u-h)*r+h;a[0]=t,a[1]=o,a[2]=h,a[3]=c,a[4]=c,a[5]=u,a[6]=l,a[7]=n}function dr(t,e,i,n,r,a,o,s,l,h,u){var c,d,f,p,g,v=.005,m=1/0;yv[0]=l,yv[1]=h;for(var y=0;1>y;y+=.05)_v[0]=sr(t,i,r,o,y),_v[1]=sr(e,n,a,s,y),p=cp(yv,_v),m>p&&(c=y,m=p);m=1/0;for(var _=0;32>_&&!(gv>v);_++)d=c-v,f=c+v,_v[0]=sr(t,i,r,o,d),_v[1]=sr(e,n,a,s,d),p=cp(_v,yv),d>=0&&m>p?(c=d,m=p):(xv[0]=sr(t,i,r,o,f),xv[1]=sr(e,n,a,s,f),g=cp(xv,yv),1>=f&&m>g?(c=f,m=g):v*=.5);return u&&(u[0]=sr(t,i,r,o,c),u[1]=sr(e,n,a,s,c)),fv(m)}function fr(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function pr(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function gr(t,e,i,n,r){var a=t-2*e+i,o=2*(e-t),s=t-n,l=0;if(ar(a)){if(or(o)){var h=-s/o;h>=0&&1>=h&&(r[l++]=h)}}else{var u=o*o-4*a*s;if(ar(u)){var h=-o/(2*a);h>=0&&1>=h&&(r[l++]=h)}else if(u>0){var c=fv(u),h=(-o+c)/(2*a),d=(-o-c)/(2*a);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function vr(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function mr(t,e,i,n,r){var a=(e-t)*n+t,o=(i-e)*n+e,s=(o-a)*n+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=i}function yr(t,e,i,n,r,a,o,s,l){var h,u=.005,c=1/0;yv[0]=o,yv[1]=s;for(var d=0;1>d;d+=.05){_v[0]=fr(t,i,r,d),_v[1]=fr(e,n,a,d);var f=cp(yv,_v);c>f&&(h=d,c=f)}c=1/0;for(var p=0;32>p&&!(gv>u);p++){var g=h-u,v=h+u;_v[0]=fr(t,i,r,g),_v[1]=fr(e,n,a,g);var f=cp(_v,yv);if(g>=0&&c>f)h=g,c=f;else{xv[0]=fr(t,i,r,v),xv[1]=fr(e,n,a,v);var m=cp(xv,yv);1>=v&&c>m?(h=v,c=m):u*=.5}}return l&&(l[0]=fr(t,i,r,h),l[1]=fr(e,n,a,h)),fv(c)}function _r(t,e,i){if(0!==t.length){var n,r=t[0],a=r[0],o=r[0],s=r[1],l=r[1];for(n=1;n<t.length;n++)r=t[n],a=wv(a,r[0]),o=bv(o,r[0]),s=wv(s,r[1]),l=bv(l,r[1]);e[0]=a,e[1]=s,i[0]=o,i[1]=l}}function xr(t,e,i,n,r,a){r[0]=wv(t,i),r[1]=wv(e,n),a[0]=bv(t,i),a[1]=bv(e,n)}function wr(t,e,i,n,r,a,o,s,l,h){var u,c=ur,d=sr,f=c(t,i,r,o,Av);for(l[0]=1/0,l[1]=1/0,h[0]=-1/0,h[1]=-1/0,u=0;f>u;u++){var p=d(t,i,r,o,Av[u]);l[0]=wv(p,l[0]),h[0]=bv(p,h[0])}for(f=c(e,n,a,s,kv),u=0;f>u;u++){var g=d(e,n,a,s,kv[u]);l[1]=wv(g,l[1]),h[1]=bv(g,h[1])}l[0]=wv(t,l[0]),h[0]=bv(t,h[0]),l[0]=wv(o,l[0]),h[0]=bv(o,h[0]),l[1]=wv(e,l[1]),h[1]=bv(e,h[1]),l[1]=wv(s,l[1]),h[1]=bv(s,h[1])}function br(t,e,i,n,r,a,o,s){var l=vr,h=fr,u=bv(wv(l(t,i,r),1),0),c=bv(wv(l(e,n,a),1),0),d=h(t,i,r,u),f=h(e,n,a,c);o[0]=wv(t,r,d),o[1]=wv(e,a,f),s[0]=bv(t,r,d),s[1]=bv(e,a,f)}function Sr(t,e,i,n,r,a,o,s,l){var h=oe,u=se,c=Math.abs(r-a);if(1e-4>c%Iv&&c>1e-4)return s[0]=t-i,s[1]=e-n,l[0]=t+i,void(l[1]=e+n);if(Tv[0]=Mv(r)*i+t,Tv[1]=Sv(r)*n+e,Cv[0]=Mv(a)*i+t,Cv[1]=Sv(a)*n+e,h(s,Tv,Cv),u(l,Tv,Cv),r%=Iv,0>r&&(r+=Iv),a%=Iv,0>a&&(a+=Iv),r>a&&!o?a+=Iv:a>r&&o&&(r+=Iv),o){var d=a;a=r,r=d}for(var f=0;a>f;f+=Math.PI/2)f>r&&(Dv[0]=Mv(f)*i+t,Dv[1]=Sv(f)*n+e,h(s,Dv,s),u(l,Dv,l))}function Mr(t,e,i,n,r,a,o){if(0===r)return!1;var s=r,l=0,h=t;if(o>e+s&&o>n+s||e-s>o&&n-s>o||a>t+s&&a>i+s||t-s>a&&i-s>a)return!1;if(t===i)return Math.abs(a-t)<=s/2;l=(e-n)/(t-i),h=(t*n-i*e)/(t-i);var u=l*a-o+h,c=u*u/(l*l+1);return s/2*s/2>=c}function Ir(t,e,i,n,r,a,o,s,l,h,u){if(0===l)return!1;var c=l;if(u>e+c&&u>n+c&&u>a+c&&u>s+c||e-c>u&&n-c>u&&a-c>u&&s-c>u||h>t+c&&h>i+c&&h>r+c&&h>o+c||t-c>h&&i-c>h&&r-c>h&&o-c>h)return!1;var d=dr(t,e,i,n,r,a,o,s,h,u,null);return c/2>=d}function Tr(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;if(l>e+h&&l>n+h&&l>a+h||e-h>l&&n-h>l&&a-h>l||s>t+h&&s>i+h&&s>r+h||t-h>s&&i-h>s&&r-h>s)return!1;var u=yr(t,e,i,n,r,a,s,l,null);return h/2>=u}function Cr(t){return t%=Uv,0>t&&(t+=Uv),t}function Dr(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;s-=t,l-=e;var u=Math.sqrt(s*s+l*l);if(u-h>i||i>u+h)return!1;if(Math.abs(n-r)%Xv<1e-4)return!0;if(a){var c=n;n=Cr(r),r=Cr(c)}else n=Cr(n),r=Cr(r);n>r&&(r+=Xv);var d=Math.atan2(l,s);return 0>d&&(d+=Xv),d>=n&&r>=d||d+Xv>=n&&r>=d+Xv}function Ar(t,e,i,n,r,a){if(a>e&&a>n||e>a&&n>a)return 0;if(n===e)return 0;var o=e>n?1:-1,s=(a-e)/(n-e);(1===s||0===s)&&(o=e>n?.5:-.5);var l=s*(i-t)+t;return l===r?1/0:l>r?o:0}function kr(t,e){return Math.abs(t-e)<qv}function Pr(){var t=$v[0];$v[0]=$v[1],$v[1]=t}function Lr(t,e,i,n,r,a,o,s,l,h){if(h>e&&h>n&&h>a&&h>s||e>h&&n>h&&a>h&&s>h)return 0;var u=hr(e,n,a,s,h,Zv);if(0===u)return 0;for(var c,d,f=0,p=-1,g=0;u>g;g++){var v=Zv[g],m=0===v||1===v?.5:1,y=sr(t,i,r,o,v);l>y||(0>p&&(p=ur(e,n,a,s,$v),$v[1]<$v[0]&&p>1&&Pr(),c=sr(e,n,a,s,$v[0]),p>1&&(d=sr(e,n,a,s,$v[1]))),f+=2===p?v<$v[0]?e>c?m:-m:v<$v[1]?c>d?m:-m:d>s?m:-m:v<$v[0]?e>c?m:-m:c>s?m:-m)}return f}function Or(t,e,i,n,r,a,o,s){if(s>e&&s>n&&s>a||e>s&&n>s&&a>s)return 0;var l=gr(e,n,a,s,Zv);if(0===l)return 0;var h=vr(e,n,a);if(h>=0&&1>=h){for(var u=0,c=fr(e,n,a,h),d=0;l>d;d++){var f=0===Zv[d]||1===Zv[d]?.5:1,p=fr(t,i,r,Zv[d]);o>p||(u+=Zv[d]<h?e>c?f:-f:c>a?f:-f)}return u}var f=0===Zv[0]||1===Zv[0]?.5:1,p=fr(t,i,r,Zv[0]);return o>p?0:e>a?f:-f}function Er(t,e,i,n,r,a,o,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);Zv[0]=-l,Zv[1]=l;var h=Math.abs(n-r);if(1e-4>h)return 0;if(1e-4>h%Yv){n=0,r=Yv;var u=a?1:-1;return o>=Zv[0]+t&&o<=Zv[1]+t?u:0}if(a){var l=n;n=Cr(r),r=Cr(l)}else n=Cr(n),r=Cr(r);n>r&&(r+=Yv);for(var c=0,d=0;2>d;d++){var f=Zv[d];if(f+t>o){var p=Math.atan2(s,f),u=a?1:-1;0>p&&(p=Yv+p),(p>=n&&r>=p||p+Yv>=n&&r>=p+Yv)&&(p>Math.PI/2&&p<1.5*Math.PI&&(u=-u),c+=u)}}return c}function Br(t,e,i,n,r){for(var a=0,o=0,s=0,l=0,h=0,u=0;u<t.length;){var c=t[u++];switch(c===jv.M&&u>1&&(i||(a+=Ar(o,s,l,h,n,r))),1===u&&(o=t[u],s=t[u+1],l=o,h=s),c){case jv.M:l=t[u++],h=t[u++],o=l,s=h;break;case jv.L:if(i){if(Mr(o,s,t[u],t[u+1],e,n,r))return!0}else a+=Ar(o,s,t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case jv.C:if(i){if(Ir(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=Lr(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case jv.Q:if(i){if(Tr(o,s,t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=Or(o,s,t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case jv.A:var d=t[u++],f=t[u++],p=t[u++],g=t[u++],v=t[u++],m=t[u++];u+=1;var y=1-t[u++],_=Math.cos(v)*p+d,x=Math.sin(v)*g+f;u>1?a+=Ar(o,s,_,x,n,r):(l=_,h=x);var w=(n-d)*g/p+d;if(i){if(Dr(d,f,g,v,v+m,y,e,w,r))return!0}else a+=Er(d,f,g,v,v+m,y,w,r);o=Math.cos(v+m)*p+d,s=Math.sin(v+m)*g+f;break;case jv.R:l=o=t[u++],h=s=t[u++];var b=t[u++],S=t[u++],_=l+b,x=h+S;if(i){if(Mr(l,h,_,h,e,n,r)||Mr(_,h,_,x,e,n,r)||Mr(_,x,l,x,e,n,r)||Mr(l,x,l,h,e,n,r))return!0}else a+=Ar(_,h,_,x,n,r),a+=Ar(l,x,l,h,n,r);break;case jv.Z:if(i){if(Mr(o,s,l,h,e,n,r))return!0}else a+=Ar(o,s,l,h,n,r);o=l,s=h}}return i||kr(s,h)||(a+=Ar(o,s,l,h,n,r)||0),0!==a}function zr(t,e,i){return Br(t,0,!1,e,i)}function Rr(t,e,i,n){return Br(t,e,!0,i,n)}function Nr(t){_n.call(this,t),this.path=null}function Fr(t,e,i,n,r,a,o,s,l,h,u){var c=l*(lm/180),d=sm(c)*(t-i)/2+om(c)*(e-n)/2,f=-1*om(c)*(t-i)/2+sm(c)*(e-n)/2,p=d*d/(o*o)+f*f/(s*s);p>1&&(o*=am(p),s*=am(p));var g=(r===a?-1:1)*am((o*o*s*s-o*o*f*f-s*s*d*d)/(o*o*f*f+s*s*d*d))||0,v=g*o*f/s,m=g*-s*d/o,y=(t+i)/2+sm(c)*v-om(c)*m,_=(e+n)/2+om(c)*v+sm(c)*m,x=cm([1,0],[(d-v)/o,(f-m)/s]),w=[(d-v)/o,(f-m)/s],b=[(-1*d-v)/o,(-1*f-m)/s],S=cm(w,b);um(w,b)<=-1&&(S=lm),um(w,b)>=1&&(S=0),0===a&&S>0&&(S-=2*lm),1===a&&0>S&&(S+=2*lm),u.addData(h,y,_,o,s,x,S,c,a)}function Vr(t){if(!t)return new Gv;for(var e,i=0,n=0,r=i,a=n,o=new Gv,s=Gv.CMD,l=t.match(dm),h=0;h<l.length;h++){for(var u,c=l[h],d=c.charAt(0),f=c.match(fm)||[],p=f.length,g=0;p>g;g++)f[g]=parseFloat(f[g]);for(var v=0;p>v;){var m,y,_,x,w,b,S,M=i,I=n;switch(d){case"l":i+=f[v++],n+=f[v++],u=s.L,o.addData(u,i,n);break;case"L":i=f[v++],n=f[v++],u=s.L,o.addData(u,i,n);break;case"m":i+=f[v++],n+=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="l";break;case"M":i=f[v++],n=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="L";break;case"h":i+=f[v++],u=s.L,o.addData(u,i,n);break;case"H":i=f[v++],u=s.L,o.addData(u,i,n);break;case"v":n+=f[v++],u=s.L,o.addData(u,i,n);break;case"V":n=f[v++],u=s.L,o.addData(u,i,n);break;case"C":u=s.C,o.addData(u,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),i=f[v-2],n=f[v-1];break;case"c":u=s.C,o.addData(u,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n),i+=f[v-2],n+=f[v-1];break;case"S":m=i,y=n;var T=o.len(),C=o.data;e===s.C&&(m+=i-C[T-4],y+=n-C[T-3]),u=s.C,M=f[v++],I=f[v++],i=f[v++],n=f[v++],o.addData(u,m,y,M,I,i,n);break;case"s":m=i,y=n;var T=o.len(),C=o.data;e===s.C&&(m+=i-C[T-4],y+=n-C[T-3]),u=s.C,M=i+f[v++],I=n+f[v++],i+=f[v++],n+=f[v++],o.addData(u,m,y,M,I,i,n);break;case"Q":M=f[v++],I=f[v++],i=f[v++],n=f[v++],u=s.Q,o.addData(u,M,I,i,n);break;case"q":M=f[v++]+i,I=f[v++]+n,i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,M,I,i,n);break;case"T":m=i,y=n;var T=o.len(),C=o.data;e===s.Q&&(m+=i-C[T-4],y+=n-C[T-3]),i=f[v++],n=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"t":m=i,y=n;var T=o.len(),C=o.data;e===s.Q&&(m+=i-C[T-4],y+=n-C[T-3]),i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"A":_=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],M=i,I=n,i=f[v++],n=f[v++],u=s.A,Fr(M,I,i,n,b,S,_,x,w,u,o);break;case"a":_=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],M=i,I=n,i+=f[v++],n+=f[v++],u=s.A,Fr(M,I,i,n,b,S,_,x,w,u,o)}}("z"===d||"Z"===d)&&(u=s.Z,o.addData(u),i=r,n=a),e=u}return o.toStatic(),o}function Hr(t,e){var i=Vr(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(i.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;i.rebuildPath(e)}},e.applyTransform=function(t){rm(i,t),this.dirty(!0)},e}function Wr(t,e){return new Nr(Hr(t,e))}function Gr(t,e){return Nr.extend(Hr(t,e))}function Ur(t,e){for(var i=[],n=t.length,r=0;n>r;r++){var a=t[r];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),i.push(a.path)}var o=new Nr(e);return o.createPathProxy(),o.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},o}function Xr(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function jr(t,e,i){var n=e.points,r=e.smooth;if(n&&n.length>=2){if(r&&"spline"!==r){var a=wm(n,r,i,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;(i?o:o-1)>s;s++){var l=a[2*s],h=a[2*s+1],u=n[(s+1)%o];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}}else{"spline"===r&&(n=xm(n,i)),t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;c>s;s++)t.lineTo(n[s][0],n[s][1])}i&&t.closePath()}}function Yr(t,e,i){var n=i&&i.lineWidth;if(e&&n){var r=e.x1,a=e.x2,o=e.y1,s=e.y2;Mm(2*r)===Mm(2*a)?t.x1=t.x2=Zr(r,n,!0):(t.x1=r,t.x2=a),Mm(2*o)===Mm(2*s)?t.y1=t.y2=Zr(o,n,!0):(t.y1=o,t.y2=s)}}function qr(t,e,i){var n=i&&i.lineWidth;if(e&&n){var r=e.x,a=e.y,o=e.width,s=e.height;t.x=Zr(r,n,!0),t.y=Zr(a,n,!0),t.width=Math.max(Zr(r+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Zr(a+s,n,!1)-t.y,0===s?0:1)}}function Zr(t,e,i){var n=Mm(2*t);return(n+Mm(e))%2===0?n/2:(n+(i?1:-1))/2}function $r(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?lr:sr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?lr:sr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?pr:fr)(t.x1,t.cpx1,t.x2,e),(i?pr:fr)(t.y1,t.cpy1,t.y2,e)]}function Kr(t){_n.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}function Qr(t){return Nr.extend(t)}function Jr(t,e){return Gr(t,e)}function ta(t,e,i,n){var r=Wr(t,e);return i&&("center"===n&&(i=ia(i,r.getBoundingRect())),na(r,i)),r}function ea(t,e,i){var n=new xn({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===i){var r={width:t.width,height:t.height};n.setStyle(ia(e,r))}}});return n}function ia(t,e){var i,n=e.width/e.height,r=t.height*n;r<=t.width?i=t.height:(r=t.width,i=r/n);var a=t.x+t.width/2,o=t.y+t.height/2;return{x:a-r/2,y:o-i/2,width:r,height:i}}function na(t,e){if(t.applyTransform){var i=t.getBoundingRect(),n=i.calculateTransform(e);t.applyTransform(n)}}function ra(t){var e=t.shape,i=t.style.lineWidth;return Rm(2*e.x1)===Rm(2*e.x2)&&(e.x1=e.x2=oa(e.x1,i,!0)),Rm(2*e.y1)===Rm(2*e.y2)&&(e.y1=e.y2=oa(e.y1,i,!0)),t}function aa(t){var e=t.shape,i=t.style.lineWidth,n=e.x,r=e.y,a=e.width,o=e.height;return e.x=oa(e.x,i,!0),e.y=oa(e.y,i,!0),e.width=Math.max(oa(n+a,i,!1)-e.x,0===a?0:1),e.height=Math.max(oa(r+o,i,!1)-e.y,0===o?0:1),t}function oa(t,e,i){var n=Rm(2*t);return(n+Rm(e))%2===0?n/2:(n+(i?1:-1))/2}function sa(t){return null!=t&&"none"!==t}function la(t){if("string"!=typeof t)return t;var e=Gm.get(t);return e||(e=qe(t,-.1),1e4>Um&&(Gm.set(t,e),Um++)),e}function ha(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(!e)return void(t.__cachedNormalStl=t.__cachedNormalZ2=null);var i=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var n=t.style;for(var r in e)null!=e[r]&&(i[r]=n[r]);i.fill=n.fill,i.stroke=n.stroke}}function ua(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var i=t.useHoverLayer;t.__highlighted=i?"layer":"plain";var n=t.__zr;if(n||!i){var r=t,a=t.style;i&&(r=n.addHover(t),a=r.style),ka(a),i||ha(r),a.extendFrom(e),ca(a,e,"fill"),ca(a,e,"stroke"),Aa(a),i||(t.dirty(!1),t.z2+=Hm)}}}function ca(t,e,i){!sa(e[i])&&sa(t[i])&&(t[i]=la(t[i]))}function da(t){var e=t.__highlighted;if(e)if(t.__highlighted=!1,"layer"===e)t.__zr&&t.__zr.removeHover(t);else if(e){var i=t.style,n=t.__cachedNormalStl;n&&(ka(i),t.setStyle(n),Aa(i));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===Hm&&(t.z2=r)}}function fa(t,e){t.isGroup?t.traverse(function(t){!t.isGroup&&e(t)}):e(t)}function pa(t,e){e=t.__hoverStl=e!==!1&&(e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,da(t),ua(t))}function ga(t){return t&&t.__isEmphasisEntered}function va(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasisEntered&&fa(this,ua)}function ma(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasisEntered&&fa(this,da)}function ya(){this.__isEmphasisEntered=!0,fa(this,ua)}function _a(){this.__isEmphasisEntered=!1,fa(this,da)}function xa(t,e,i){t.isGroup?t.traverse(function(t){!t.isGroup&&pa(t,t.hoverStyle||e)}):pa(t,t.hoverStyle||e),wa(t,i)}function wa(t,e){var i=e===!1;if(t.__hoverSilentOnTouch=null!=e&&e.hoverSilentOnTouch,!i||t.__hoverStyleTrigger){var n=i?"off":"on";t[n]("mouseover",va)[n]("mouseout",ma),t[n]("emphasis",ya)[n]("normal",_a),t.__hoverStyleTrigger=!i}}function ba(t,e,i,n,r,a,o){r=r||Vm;var s,l=r.labelFetcher,h=r.labelDataIndex,u=r.labelDimIndex,c=i.getShallow("show"),d=n.getShallow("show");(c||d)&&(l&&(s=l.getFormattedLabel(h,"normal",null,u)),null==s&&(s=w(r.defaultText)?r.defaultText(h,r):r.defaultText));var f=c?s:null,p=d?A(l?l.getFormattedLabel(h,"emphasis",null,u):null,s):null;(null!=f||null!=p)&&(Sa(t,i,a,r),Sa(e,n,o,r,!0)),t.text=f,e.text=p}function Sa(t,e,i,n,r){return Ia(t,e,n,r),i&&o(t,i),t}function Ma(t,e,i){var n,r={isRectText:!0};i===!1?n=!0:r.autoColor=i,Ia(t,e,r,n)}function Ia(t,e,i,n){if(i=i||Vm,i.isRectText){var r=e.getShallow("position")||(n?null:"inside");"outside"===r&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var a=e.getShallow("rotate");null!=a&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=A(e.getShallow("distance"),n?null:5)}var o,s=e.ecModel,l=s&&s.option.textStyle,h=Ta(e);if(h){o={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);Ca(o[u]={},c,l,i,n)}}return t.rich=o,Ca(t,e,l,i,n,!0),i.forceRich&&!i.textStyle&&(i.textStyle={}),t}function Ta(t){for(var e;t&&t!==t.ecModel;){var i=(t.option||Vm).rich;if(i){e=e||{};for(var n in i)i.hasOwnProperty(n)&&(e[n]=1)}t=t.parentModel}return e}function Ca(t,e,i,n,r,a){i=!r&&i||Vm,t.textFill=Da(e.getShallow("color"),n)||i.color,t.textStroke=Da(e.getShallow("textBorderColor"),n)||i.textBorderColor,t.textStrokeWidth=A(e.getShallow("textBorderWidth"),i.textBorderWidth),t.insideRawTextPosition=t.textPosition,r||(a&&(t.insideRollbackOpt=n,Aa(t)),null==t.textFill&&(t.textFill=n.autoColor)),t.fontStyle=e.getShallow("fontStyle")||i.fontStyle,t.fontWeight=e.getShallow("fontWeight")||i.fontWeight,t.fontSize=e.getShallow("fontSize")||i.fontSize,t.fontFamily=e.getShallow("fontFamily")||i.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),a&&n.disableBox||(t.textBackgroundColor=Da(e.getShallow("backgroundColor"),n),t.textPadding=e.getShallow("padding"),t.textBorderColor=Da(e.getShallow("borderColor"),n),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||i.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||i.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||i.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||i.textShadowOffsetY}function Da(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Aa(t){var e=t.insideRollbackOpt;if(e&&null==t.textFill){var i,n=e.useInsideStyle,r=t.insideRawTextPosition,a=e.autoColor;n!==!1&&(n===!0||e.isRectText&&r&&"string"==typeof r&&r.indexOf("inside")>=0)?(i={textFill:null,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=a,null==t.textStrokeWidth&&(t.textStrokeWidth=2))):null!=a&&(i={textFill:null},t.textFill=a),i&&(t.insideRollback=i)}}function ka(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function Pa(t,e){var i=e||e.getModel("textStyle");return E([t.fontStyle||i&&i.getShallow("fontStyle")||"",t.fontWeight||i&&i.getShallow("fontWeight")||"",(t.fontSize||i&&i.getShallow("fontSize")||12)+"px",t.fontFamily||i&&i.getShallow("fontFamily")||"sans-serif"].join(" "))}function La(t,e,i,n,r,a){"function"==typeof r&&(a=r,r=null);var o=n&&n.isAnimationEnabled();if(o){var s=t?"Update":"",l=n.getShallow("animationDuration"+s),h=n.getShallow("animationEasing"+s),u=n.getShallow("animationDelay"+s);"function"==typeof u&&(u=u(r,n.getAnimationDelayParams?n.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(i,l,u||0,h,a,!!a):(e.stopAnimation(),e.attr(i),a&&a())}else e.stopAnimation(),e.attr(i),a&&a()}function Oa(t,e,i,n,r){La(!0,t,e,i,n,r)}function Ea(t,e,i,n,r){La(!1,t,e,i,n,r)}function Ba(t,e){for(var i=Ie([]);t&&t!==e;)Ce(i,t.getLocalTransform(),i),t=t.parent;return i}function za(t,e,i){return e&&!d(e)&&(e=Cp.getLocalTransform(e)),i&&(e=Pe([],e)),ae([],t,e)}function Ra(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-n:"right"===t?n:0,"top"===t?-r:"bottom"===t?r:0];return a=za(a,e,i),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function Na(t,e,i){function n(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:G(t.position),rotation:t.rotation};return t.shape&&(e.shape=o({},t.shape)),e}if(t&&e){var a=n(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),Oa(t,n,i,t.dataIndex)}}})}}function Fa(t,e){return p(t,function(t){var i=t[0];i=Nm(i,e.x),i=Fm(i,e.x+e.width);var n=t[1];return n=Nm(n,e.y),n=Fm(n,e.y+e.height),[i,n]})}function Va(t,e){var i=Nm(t.x,e.x),n=Fm(t.x+t.width,e.x+e.width),r=Nm(t.y,e.y),a=Fm(t.y+t.height,e.y+e.height);return n>=i&&a>=r?{x:i,y:r,width:n-i,height:a-r}:void 0}function Ha(t,e,i){e=o({rectHover:!0},e);var n=e.style={strokeNoScale:!0};return i=i||{x:-1,y:-1,width:2,height:2},t?0===t.indexOf("image://")?(n.image=t.slice(8),s(n,i),new xn(e)):ta(t.replace("path://",""),e,i,"center"):void 0}function Wa(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}function Ga(t,e,i){for(var n=0;n<e.length&&(!e[n]||(t=t&&"object"==typeof t?t[e[n]]:null,null!=t));n++);return null==t&&i&&(t=i.get(e)),t}function Ua(t,e){var i=Km(t).getParent;return i?i.call(t,e):t.parentModel}function Xa(t){return[t||"",Qm++,Math.random().toFixed(5)].join("_")}function ja(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=Qn(t),e[t.main]=i},t.determineSubType=function(i,n){var r=n.type;if(!r){var a=Qn(i).main;t.hasSubTypes(i)&&e[a]&&(r=e[a](n))}return r},t}function Ya(t,e){function i(t){var i={},a=[];return f(t,function(o){var s=n(i,o),l=s.originalDeps=e(o),u=r(l,t);s.entryCount=u.length,0===s.entryCount&&a.push(o),f(u,function(t){h(s.predecessor,t)<0&&s.predecessor.push(t);var e=n(i,t);h(e.successor,t)<0&&e.successor.push(o)})}),{graph:i,noEntryList:a}}function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var i=[];return f(t,function(t){h(e,t)>=0&&i.push(t)}),i}t.topologicalTravel=function(t,e,n,r){function a(t){l[t].entryCount--,0===l[t].entryCount&&h.push(t)}function o(t){u[t]=!0,a(t)}if(t.length){var s=i(e),l=s.graph,h=s.noEntryList,u={};for(f(t,function(t){u[t]=!0});h.length;){var c=h.pop(),d=l[c],p=!!u[c];p&&(n.call(r,c,d.originalDeps.slice()),delete u[c]),f(d.successor,p?o:a)}f(u,function(){throw new Error("Circle dependency may exists")})}}}function qa(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function Za(t,e,i,n){var r=e[1]-e[0],a=i[1]-i[0];if(0===r)return 0===a?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*a+i[0]}function $a(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?qa(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?0/0:+t}function Ka(t,e,i){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),i?t:+t}function Qa(t){return t.sort(function(t,e){return t-e}),t}function Ja(t){if(t=+t,isNaN(t))return 0;for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i}function to(t){var e=t.toString(),i=e.indexOf("e");if(i>0){var n=+e.slice(i+1);return 0>n?-n:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}function eo(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),a=Math.round(i(Math.abs(e[1]-e[0]))/n),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20}function io(t,e,i){if(!t[e])return 0;var n=g(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return 0;for(var r=Math.pow(10,i),a=p(t,function(t){return(isNaN(t)?0:t)/n*r*100}),o=100*r,s=p(a,function(t){return Math.floor(t)}),l=g(s,function(t,e){return t+e},0),h=p(a,function(t,e){return t-s[e]});o>l;){for(var u=Number.NEGATIVE_INFINITY,c=null,d=0,f=h.length;f>d;++d)h[d]>u&&(u=h[d],c=d);++s[c],h[c]=0,++l}return s[e]/r}function no(t){var e=2*Math.PI;return(t%e+e)%e}function ro(t){return t>-Jm&&Jm>t}function ao(t){if(t instanceof Date)return t;if("string"==typeof t){var e=ey.exec(t);if(!e)return new Date(0/0);if(e[8]){var i=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(i-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,i,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return new Date(null==t?0/0:Math.round(t))}function oo(t){return Math.pow(10,so(t))}function so(t){return Math.floor(Math.log(t)/Math.LN10)}function lo(t,e){var i,n=so(t),r=Math.pow(10,n),a=t/r;return i=e?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,t=i*r,n>=-20?+t.toFixed(0>n?-n:0):t}function ho(t,e){var i=(t.length-1)*e+1,n=Math.floor(i),r=+t[n-1],a=i-n;return a?r+a*(t[n]-r):r}function uo(t){function e(t,i,n){return t.interval[n]<i.interval[n]||t.interval[n]===i.interval[n]&&(t.close[n]-i.close[n]===(n?-1:1)||!n&&e(t,i,1))}t.sort(function(t,i){return e(t,i,0)?-1:1});for(var i=-1/0,n=1,r=0;r<t.length;){for(var a=t[r].interval,o=t[r].close,s=0;2>s;s++)a[s]<=i&&(a[s]=i,o[s]=s?1:1-n),i=a[s],n=o[s];a[0]===a[1]&&o[0]*o[1]!==1?t.splice(r,1):r++}return t}function co(t){return t-parseFloat(t)>=0}function fo(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function po(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t
}function go(t){return null==t?"":(t+"").replace(ry,function(t,e){return ay[e]})}function vo(t,e,i){x(e)||(e=[e]);var n=e.length;if(!n)return"";for(var r=e[0].$vars||[],a=0;a<r.length;a++){var o=oy[a];t=t.replace(sy(o),sy(o,0))}for(var s=0;n>s;s++)for(var l=0;l<r.length;l++){var h=e[s][r[l]];t=t.replace(sy(oy[l],s),i?go(h):h)}return t}function mo(t,e,i){return f(e,function(e,n){t=t.replace("{"+n+"}",i?go(e):e)}),t}function yo(t,e){t=b(t)?{color:t,extraCssText:e}:t||{};var i=t.color,n=t.type,e=t.extraCssText,r=t.renderMode||"html",a=t.markerId||"X";return i?"html"===r?"subItem"===n?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+go(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+go(i)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+a+"|}  ",style:{color:i}}:""}function _o(t,e){return t+="","0000".substr(0,e-t.length)+t}function xo(t,e,i){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=ao(e),r=i?"UTC":"",a=n["get"+r+"FullYear"](),o=n["get"+r+"Month"]()+1,s=n["get"+r+"Date"](),l=n["get"+r+"Hours"](),h=n["get"+r+"Minutes"](),u=n["get"+r+"Seconds"](),c=n["get"+r+"Milliseconds"]();return t=t.replace("MM",_o(o,2)).replace("M",o).replace("yyyy",a).replace("yy",a%100).replace("dd",_o(s,2)).replace("d",s).replace("hh",_o(l,2)).replace("h",l).replace("mm",_o(h,2)).replace("m",h).replace("ss",_o(u,2)).replace("s",u).replace("SSS",_o(c,3))}function wo(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function bo(t){return Ri(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)}function So(t,e,i,n,r,a,o,s){return Ri(t,e,i,n,r,s,a,o)}function Mo(t,e,i,n,r){var a=0,o=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,h){var u,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(h+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);u=a+v,u>n||l.newline?(a=0,u=v,o+=s+i,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);c=o+m,c>r||l.newline?(a+=s+i,o=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=u+i:o=c+i)})}function Io(t,e,i){i=ny(i||0);var n=e.width,r=e.height,a=$a(t.left,n),o=$a(t.top,r),s=$a(t.right,n),l=$a(t.bottom,r),h=$a(t.width,n),u=$a(t.height,r),c=i[2]+i[0],d=i[1]+i[3],f=t.aspect;switch(isNaN(h)&&(h=n-s-d-a),isNaN(u)&&(u=r-l-c-o),null!=f&&(isNaN(h)&&isNaN(u)&&(f>n/r?h=.8*n:u=.8*r),isNaN(h)&&(h=f*u),isNaN(u)&&(u=h/f)),isNaN(a)&&(a=n-s-h-d),isNaN(o)&&(o=r-l-u-c),t.left||t.right){case"center":a=n/2-h/2-i[3];break;case"right":a=n-h-d}switch(t.top||t.bottom){case"middle":case"center":o=r/2-u/2-i[0];break;case"bottom":o=r-u-c}a=a||0,o=o||0,isNaN(h)&&(h=n-d-a-(s||0)),isNaN(u)&&(u=r-c-o-(l||0));var p=new mi(a+i[3],o+i[0],h,u);return p.margin=i,p}function To(t,e,i){function n(i,n){var o={},l=0,h={},u=0,c=2;if(uy(i,function(e){h[e]=t[e]}),uy(i,function(t){r(e,t)&&(o[t]=h[t]=e[t]),a(o,t)&&l++,a(h,t)&&u++}),s[n])return a(e,i[1])?h[i[2]]=null:a(e,i[2])&&(h[i[1]]=null),h;if(u!==c&&l){if(l>=c)return o;for(var d=0;d<i.length;d++){var f=i[d];if(!r(o,f)&&r(t,f)){o[f]=t[f];break}}return o}return h}function r(t,e){return t.hasOwnProperty(e)}function a(t,e){return null!=t[e]&&"auto"!==t[e]}function o(t,e,i){uy(t,function(t){e[t]=i[t]})}!S(i)&&(i={});var s=i.ignoreSize;!x(s)&&(s=[s,s]);var l=n(dy[0],0),h=n(dy[1],1);o(dy[0],t,l),o(dy[1],t,h)}function Co(t){return Do({},t)}function Do(t,e){return e&&t&&uy(cy,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t}function Ao(t){var e=[];return f(vy.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=p(e,function(t){return Qn(t).main}),"dataset"!==t&&h(e,"dataset")<=0&&e.unshift("dataset"),e}function ko(t,e){for(var i=t.length,n=0;i>n;n++)if(t[n].length>e)return t[n];return t[i-1]}function Po(t){var e=t.get("coordinateSystem"),i={coordSysName:e,coordSysDims:[],axisMap:N(),categoryAxisMap:N()},n=wy[e];return n?(n(t,i,i.axisMap,i.categoryAxisMap),i):void 0}function Lo(t){return"category"===t.get("type")}function Oo(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===Iy?{}:[]),this.sourceFormat=t.sourceFormat||Ty,this.seriesLayoutBy=t.seriesLayoutBy||Dy,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&N(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}function Eo(t){var e=t.option.source,i=Ty;if(I(e))i=Cy;else if(x(e)){0===e.length&&(i=Sy);for(var n=0,r=e.length;r>n;n++){var a=e[n];if(null!=a){if(x(a)){i=Sy;break}if(S(a)){i=My;break}}}}else if(S(e)){for(var o in e)if(e.hasOwnProperty(o)&&d(e[o])){i=Iy;break}}else if(null!=e)throw new Error("Invalid data");ky(t).sourceFormat=i}function Bo(t){return ky(t).source}function zo(t){ky(t).datasetMap=N()}function Ro(t){var e=t.option,i=e.data,n=I(i)?Cy:by,r=!1,a=e.seriesLayoutBy,o=e.sourceHeader,s=e.dimensions,l=Go(t);if(l){var h=l.option;i=h.source,n=ky(l).sourceFormat,r=!0,a=a||h.seriesLayoutBy,null==o&&(o=h.sourceHeader),s=s||h.dimensions}var u=No(i,n,a,o,s),c=e.encode;!c&&l&&(c=Wo(t,l,i,n,a,u)),ky(t).source=new Oo({data:i,fromDataset:r,seriesLayoutBy:a,sourceFormat:n,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectCount:u.dimensionsDetectCount,encodeDefine:c})}function No(t,e,i,n,r){if(!t)return{dimensionsDefine:Fo(r)};var a,o,s;if(e===Sy)"auto"===n||null==n?Vo(function(t){null!=t&&"-"!==t&&(b(t)?null==o&&(o=1):o=0)},i,t,10):o=n?1:0,r||1!==o||(r=[],Vo(function(t,e){r[e]=null!=t?t:""},i,t)),a=r?r.length:i===Ay?t.length:t[0]?t[0].length:null;else if(e===My)r||(r=Ho(t),s=!0);else if(e===Iy)r||(r=[],s=!0,f(t,function(t,e){r.push(e)}));else if(e===by){var l=Fn(t[0]);a=x(l)&&l.length||1}var h;return s&&f(r,function(t,e){"name"===(S(t)?t.name:t)&&(h=e)}),{startIndex:o,dimensionsDefine:Fo(r),dimensionsDetectCount:a,potentialNameDimIndex:h}}function Fo(t){if(t){var e=N();return p(t,function(t){if(t=o({},S(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=e.get(t.name);return i?t.name+="-"+i.count++:e.set(t.name,{count:1}),t})}}function Vo(t,e,i,n){if(null==n&&(n=1/0),e===Ay)for(var r=0;r<i.length&&n>r;r++)t(i[r]?i[r][0]:null,r);else for(var a=i[0]||[],r=0;r<a.length&&n>r;r++)t(a[r],r)}function Ho(t){for(var e,i=0;i<t.length&&!(e=t[i++]););if(e){var n=[];return f(e,function(t,e){n.push(e)}),n}}function Wo(t,e,i,n,r,a){var o=Po(t),s={},l=[],h=[],u=t.subType,c=N(["pie","map","funnel"]),d=N(["line","bar","pictorialBar","scatter","effectScatter","candlestick","boxplot"]);if(o&&null!=d.get(u)){var p=t.ecModel,g=ky(p).datasetMap,v=e.uid+"_"+r,m=g.get(v)||g.set(v,{categoryWayDim:1,valueWayDim:0});f(o.coordSysDims,function(t){if(null==o.firstCategoryDimIndex){var e=m.valueWayDim++;s[t]=e,h.push(e)}else if(o.categoryAxisMap.get(t))s[t]=0,l.push(0);else{var e=m.categoryWayDim++;s[t]=e,h.push(e)}})}else if(null!=c.get(u)){for(var y,_=0;5>_&&null==y;_++)Xo(i,n,r,a.dimensionsDefine,a.startIndex,_)||(y=_);if(null!=y){s.value=y;var x=a.potentialNameDimIndex||Math.max(y-1,0);h.push(x),l.push(x)}}return l.length&&(s.itemName=l),h.length&&(s.seriesName=h),s}function Go(t){var e=t.option,i=e.data;return i?void 0:t.ecModel.getComponent("dataset",e.datasetIndex||0)}function Uo(t,e){return Xo(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function Xo(t,e,i,n,r,a){function o(t){return null!=t&&isFinite(t)&&""!==t?!1:b(t)&&"-"!==t?!0:void 0}var s,l=5;if(I(t))return!1;var h;if(n&&(h=n[a],h=S(h)?h.name:h),e===Sy)if(i===Ay){for(var u=t[a],c=0;c<(u||[]).length&&l>c;c++)if(null!=(s=o(u[r+c])))return s}else for(var c=0;c<t.length&&l>c;c++){var d=t[r+c];if(d&&null!=(s=o(d[a])))return s}else if(e===My){if(!h)return;for(var c=0;c<t.length&&l>c;c++){var f=t[c];if(f&&null!=(s=o(f[h])))return s}}else if(e===Iy){if(!h)return;var u=t[h];if(!u||I(u))return!1;for(var c=0;c<u.length&&l>c;c++)if(null!=(s=o(u[c])))return s}else if(e===by)for(var c=0;c<t.length&&l>c;c++){var f=t[c],p=Fn(f);if(!x(p))return!1;if(null!=(s=o(p[a])))return s}return!1}function jo(t,e){if(e){var i=e.seiresIndex,n=e.seriesId,r=e.seriesName;return null!=i&&t.componentIndex!==i||null!=n&&t.id!==n||null!=r&&t.name!==r}}function Yo(t,e){var i=t.color&&!t.colorLayer;f(e,function(e,a){"colorLayer"===a&&i||vy.hasClass(a)||("object"==typeof e?t[a]=t[a]?r(t[a],e,!1):n(e):null==t[a]&&(t[a]=e))})}function qo(t){t=t,this.option={},this.option[Py]=1,this._componentsMap=N({series:[]}),this._seriesIndices,this._seriesIndicesMap,Yo(t,this._theme.option),r(t,yy,!1),this.mergeOption(t)}function Zo(t,e){x(e)||(e=e?[e]:[]);var i={};return f(e,function(e){i[e]=(t.get(e)||[]).slice()}),i}function $o(t,e,i){var n=e.type?e.type:i?i.subType:vy.determineSubType(t,e);return n}function Ko(t,e){t._seriesIndicesMap=N(t._seriesIndices=p(e,function(t){return t.componentIndex})||[])}function Qo(t,e){return e.hasOwnProperty("subType")?v(t,function(t){return t.subType===e.subType}):t}function Jo(t){f(Oy,function(e){this[e]=y(t[e],t)},this)}function ts(){this._coordinateSystems=[]}function es(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function is(t,e,i){var n,r,a=[],o=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;By(l,function(t){t&&t.option&&(t.query?o.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=s),By([r].concat(a).concat(p(o,function(t){return t.option})),function(t){By(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:a,mediaDefault:n,mediaList:o}}function ns(t,e,i){var n={width:e,height:i,aspectratio:e/i},r=!0;return f(t,function(t,e){var i=e.match(Fy);if(i&&i[1]&&i[2]){var a=i[1],o=i[2].toLowerCase();rs(n[o],t,a)||(r=!1)}}),r}function rs(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function as(t,e){return t.join(",")===e.join(",")}function os(t,e){e=e||{},By(e,function(e,i){if(null!=e){var n=t[i];if(vy.hasClass(i)){e=Rn(e),n=Rn(n);var r=Hn(n,e);t[i]=Ry(r,function(t){return t.option&&t.exist?Ny(t.exist,t.option,!0):t.exist||t.option})}else t[i]=Ny(n,e,!0)}})}function ss(t){var e=t&&t.itemStyle;if(e)for(var i=0,n=Wy.length;n>i;i++){var a=Wy[i],o=e.normal,s=e.emphasis;o&&o[a]&&(t[a]=t[a]||{},t[a].normal?r(t[a].normal,o[a]):t[a].normal=o[a],o[a]=null),s&&s[a]&&(t[a]=t[a]||{},t[a].emphasis?r(t[a].emphasis,s[a]):t[a].emphasis=s[a],s[a]=null)}}function ls(t,e,i){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var n=t[e].normal,r=t[e].emphasis;n&&(i?(t[e].normal=t[e].emphasis=null,s(t[e],n)):t[e]=n),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function hs(t){ls(t,"itemStyle"),ls(t,"lineStyle"),ls(t,"areaStyle"),ls(t,"label"),ls(t,"labelLine"),ls(t,"upperLabel"),ls(t,"edgeLabel")}function us(t,e){var i=Hy(t)&&t[e],n=Hy(i)&&i.textStyle;if(n)for(var r=0,a=iv.length;a>r;r++){var e=iv[r];n.hasOwnProperty(e)&&(i[e]=n[e])}}function cs(t){t&&(hs(t),us(t,"label"),t.emphasis&&us(t.emphasis,"label"))}function ds(t){if(Hy(t)){ss(t),hs(t),us(t,"label"),us(t,"upperLabel"),us(t,"edgeLabel"),t.emphasis&&(us(t.emphasis,"label"),us(t.emphasis,"upperLabel"),us(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(ss(e),cs(e));var i=t.markLine;i&&(ss(i),cs(i));var n=t.markArea;n&&cs(n);var r=t.data;if("graph"===t.type){r=r||t.nodes;var a=t.links||t.edges;if(a&&!I(a))for(var o=0;o<a.length;o++)cs(a[o]);f(t.categories,function(t){hs(t)})}if(r&&!I(r))for(var o=0;o<r.length;o++)cs(r[o]);var e=t.markPoint;if(e&&e.data)for(var s=e.data,o=0;o<s.length;o++)cs(s[o]);var i=t.markLine;if(i&&i.data)for(var l=i.data,o=0;o<l.length;o++)x(l[o])?(cs(l[o][0]),cs(l[o][1])):cs(l[o]);"gauge"===t.type?(us(t,"axisLabel"),us(t,"title"),us(t,"detail")):"treemap"===t.type?(ls(t.breadcrumb,"itemStyle"),f(t.levels,function(t){hs(t)})):"tree"===t.type&&hs(t.leaves)}}function fs(t){return x(t)?t:t?[t]:[]}function ps(t){return(x(t)?t[0]:t)||{}}function gs(t,e){e=e.split(",");for(var i=t,n=0;n<e.length&&(i=i&&i[e[n]],null!=i);n++);return i}function vs(t,e,i,n){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(n||null==a[e[o]])&&(a[e[o]]=i)}function ms(t){f(Uy,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function ys(t){f(t,function(e,i){var n=[],r=[0/0,0/0],a=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=o.map(a,function(a,l,h){var u=o.get(e.stackedDimension,h);if(isNaN(u))return r;var c,d;s?d=o.getRawIndex(h):c=o.get(e.stackedByDimension,h);for(var f=0/0,p=i-1;p>=0;p--){var g=t[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var v=g.data.getByRawIndex(g.stackResultDimension,d);if(u>=0&&v>0||0>=u&&0>v){u+=v,f=v;break}}}return n[0]=u,n[1]=f,n});o.hostModel.setData(l),e.data=l})}function _s(t,e){Oo.isInstance(t)||(t=Oo.seriesDataToSource(t)),this._source=t;var i=this._data=t.data,n=t.sourceFormat;n===Cy&&(this._offset=0,this._dimSize=e,this._data=i);var r=Zy[n===Sy?n+"_"+t.seriesLayoutBy:n];o(this,r)}function xs(){return this._data.length}function ws(t){return this._data[t]}function bs(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Ss(t,e,i){return null!=i?t[i]:t}function Ms(t,e,i,n){return Is(t[n],this._dimensionInfos[e])}function Is(t,e){var i=e&&e.type;if("ordinal"===i){var n=e&&e.ordinalMeta;return n?n.parseAndCollect(t):t}return"time"===i&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+ao(t)),null==t||""===t?0/0:+t}function Ts(t,e,i){if(t){var n=t.getRawDataItem(e);if(null!=n){var r,a,o=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(i);return s&&(r=s.name,a=s.index),$y[o](n,e,a,r)}}}function Cs(t,e,i){if(t){var n=t.getProvider().getSource().sourceFormat;if(n===by||n===My){var r=t.getRawDataItem(e);return n!==by||S(r)||(r=null),r?r[i]:void 0}}}function Ds(t){return new As(t)}function As(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}function ks(t,e,i,n,r,a){e_.reset(i,n,r,a),t._callingProgress=e,t._callingProgress({start:i,end:n,count:n-i,next:e_.next},t.context)}function Ps(t,e){t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null;var i,n;!e&&t._reset&&(i=t._reset(t.context),i&&i.progress&&(n=i.forceFirstProgress,i=i.progress),x(i)&&!i.length&&(i=null)),t._progress=i,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),n}function Ls(t){var e=t.name;Gn(t)||(t.name=Os(t)||e)}function Os(t){var e=t.getRawData(),i=e.mapDimension("seriesName",!0),n=[];return f(i,function(t){var i=e.getDimensionInfo(t);i.displayName&&n.push(i.displayName)}),n.join(" ")}function Es(t){return t.model.getRawData().count()}function Bs(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),zs}function zs(t,e){t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Rs(t,e){f(t.CHANGABLE_METHODS,function(i){t.wrapMethod(i,_(Ns,e))})}function Ns(t){var e=Fs(t);e&&e.setOutputEnd(this.count())}function Fs(t){var e=(t.ecModel||{}).scheduler,i=e&&e.getPipeline(t.uid);if(i){var n=i.currentTask;if(n){var r=n.agentStubMap;r&&(n=r.get(t.uid))}return n}}function Vs(){this.group=new ig,this.uid=Xa("viewChart"),this.renderTask=Ds({plan:Gs,reset:Us}),this.renderTask.context={view:this}}function Hs(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var i=0;i<t.childCount();i++)Hs(t.childAt(i),e)}function Ws(t,e,i){var n=Xn(t,e);null!=n?f(Rn(n),function(e){Hs(t.getItemGraphicEl(e),i)}):t.eachItemGraphicEl(function(t){Hs(t,i)})}function Gs(t){return l_(t.model)}function Us(t){var e=t.model,i=t.ecModel,n=t.api,r=t.payload,a=e.pipelineContext.progressiveRender,o=t.view,s=r&&s_(r).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return"render"!==l&&o[l](e,i,n,r),u_[l]}function Xs(t,e,i){function n(){u=(new Date).getTime(),c=null,t.apply(o,s||[])}var r,a,o,s,l,h=0,u=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),o=this,s=arguments;var t=l||e,d=l||i;l=null,a=r-(d?h:u)-t,clearTimeout(c),d?c=setTimeout(n,t):a>=0?n():c=setTimeout(n,-a),h=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function js(t,e,i,n){var r=t[e];if(r){var a=r[c_]||r,o=r[f_],s=r[d_];if(s!==i||o!==n){if(null==i||!n)return t[e]=a;r=t[e]=Xs(a,i,"debounce"===n),r[c_]=a,r[f_]=n,r[d_]=i}return r}}function Ys(t,e,i,n){this.ecInstance=t,this.api=e,this.unfinished;var i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice();this._allHandlers=i.concat(n),this._stageTaskMap=N()}function qs(t,e,i,n,r){function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}r=r||{};var o;f(e,function(e){if(!r.visualType||r.visualType===e.visualType){var s=t._stageTaskMap.get(e.uid),l=s.seriesTaskMap,h=s.overallTask;if(h){var u,c=h.agentStubMap;c.each(function(t){a(r,t)&&(t.dirty(),u=!0)}),u&&h.dirty(),x_(h,n);var d=t.getPerformArgs(h,r.block);c.each(function(t){t.perform(d)}),o|=h.perform(d)}else l&&l.each(function(s){a(r,s)&&s.dirty();var l=t.getPerformArgs(s,r.block);l.skip=!e.performRawSeries&&i.isSeriesFiltered(s.context.model),x_(s,n),o|=s.perform(l)})}}),t.unfinished|=o}function Zs(t,e,i,n,r){function a(i){var a=i.uid,s=o.get(a)||o.set(a,Ds({plan:el,reset:il,count:rl}));s.context={model:i,ecModel:n,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},al(t,i,s)}var o=i.seriesTaskMap||(i.seriesTaskMap=N()),s=e.seriesType,l=e.getTargetSeries;e.createOnAllSeries?n.eachRawSeries(a):s?n.eachRawSeriesByType(s,a):l&&l(n,r).each(a);var h=t._pipelineMap;o.each(function(t,e){h.get(e)||(t.dispose(),o.removeKey(e))})}function $s(t,e,i,n,r){function a(e){var i=e.uid,n=s.get(i);n||(n=s.set(i,Ds({reset:Qs,onDirty:tl})),o.dirty()),n.context={model:e,overallProgress:u,modifyOutputEnd:c},n.agent=o,n.__block=u,al(t,e,n)}var o=i.overallTask=i.overallTask||Ds({reset:Ks});o.context={ecModel:n,api:r,overallReset:e.overallReset,scheduler:t};var s=o.agentStubMap=o.agentStubMap||N(),l=e.seriesType,h=e.getTargetSeries,u=!0,c=e.modifyOutputEnd;l?n.eachRawSeriesByType(l,a):h?h(n,r).each(a):(u=!1,f(n.getSeries(),a));var d=t._pipelineMap;s.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),s.removeKey(e))})}function Ks(t){t.overallReset(t.ecModel,t.api,t.payload)}function Qs(t){return t.overallProgress&&Js}function Js(){this.agent.dirty(),this.getDownstream().dirty()}function tl(){this.agent&&this.agent.dirty()}function el(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function il(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=Rn(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?p(e,function(t,e){return nl(e)}):w_}function nl(t){return function(e,i){var n=i.data,r=i.resetDefines[t];if(r&&r.dataEach)for(var a=e.start;a<e.end;a++)r.dataEach(n,a);else r&&r.progress&&r.progress(e,n)}}function rl(t){return t.data.count()}function al(t,e,i){var n=e.uid,r=t._pipelineMap.get(n);!r.head&&(r.head=i),r.tail&&r.tail.pipe(i),r.tail=i,i.__idxInPipeline=r.count++,i.__pipeline=r}function ol(t){b_=null;try{t(S_,M_)}catch(e){}return b_}function sl(t,e){for(var i in e.prototype)t[i]=V}function ll(t){if(b(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}function hl(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}function ul(t,e){for(var i=t.firstChild;i;){if(1===i.nodeType){var n=i.getAttribute("offset");n=n.indexOf("%")>0?parseInt(n,10)/100:n?parseFloat(n):0;var r=i.getAttribute("stop-color")||"#000000";e.addColorStop(n,r)}i=i.nextSibling}}function cl(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),s(e.__inheritedStyle,t.__inheritedStyle))}function dl(t){for(var e=E(t).split(L_),i=[],n=0;n<e.length;n+=2){var r=parseFloat(e[n]),a=parseFloat(e[n+1]);i.push([r,a])}return i}function fl(t,e,i,n){var r=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(gl(t,e),o(r,vl(t)),!n))for(var s in B_)if(B_.hasOwnProperty(s)){var l=t.getAttribute(s);null!=l&&(r[B_[s]]=l)}var h=a?"textFill":"fill",u=a?"textStroke":"stroke";e.style=e.style||new cg;var c=e.style;null!=r.fill&&c.set(h,pl(r.fill,i)),null!=r.stroke&&c.set(u,pl(r.stroke,i)),f(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=r[t]&&c.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),f(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&c.set(t,r[t])}),r.lineDash&&(e.style.lineDash=E(r.lineDash).split(L_)),c[u]&&"none"!==c[u]&&(e[u]=!0),e.__inheritedStyle=r}function pl(t,e){var i=e&&t&&t.match(z_);if(i){var n=E(i[1]),r=e[n];return r}return t}function gl(t,e){var i=t.getAttribute("transform");if(i){i=i.replace(/,/g," ");var n=null,r=[];i.replace(R_,function(t,e,i){r.push(e,i)});for(var a=r.length-1;a>0;a-=2){var o=r[a],s=r[a-1];switch(n=n||Me(),s){case"translate":o=E(o).split(L_),De(n,n,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=E(o).split(L_),ke(n,n,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=E(o).split(L_),Ae(n,n,parseFloat(o[0]));break;case"skew":o=E(o).split(L_),console.warn("Skew transform is not supported yet");break;case"matrix":var o=E(o).split(L_);n[0]=parseFloat(o[0]),n[1]=parseFloat(o[1]),n[2]=parseFloat(o[2]),n[3]=parseFloat(o[3]),n[4]=parseFloat(o[4]),n[5]=parseFloat(o[5])}}e.setLocalTransform(n)}}function vl(t){var e=t.getAttribute("style"),i={};if(!e)return i;var n={};N_.lastIndex=0;for(var r;null!=(r=N_.exec(e));)n[r[1]]=r[2];for(var a in B_)B_.hasOwnProperty(a)&&null!=n[a]&&(i[B_[a]]=n[a]);return i}function ml(t,e,i){var n=e/t.width,r=i/t.height,a=Math.min(n,r),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+i/2];return{scale:o,position:s}}function yl(t){return function(e,i,n){e=e&&e.toLowerCase(),pp.prototype[t].call(this,e,i,n)}}function _l(){pp.call(this)}function xl(t,e,i){function r(t,e){return t.__prio-e.__prio}i=i||{},"string"==typeof e&&(e=vx[e]),this.id,this.group,this._dom=t;var a="canvas",o=this._zr=Ln(t,{renderer:i.renderer||a,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height});this._throttledZrFlush=Xs(y(o.flush,o),17);var e=n(e);e&&jy(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new ts;var s=this._api=Nl(this);Ii(gx,r),Ii(dx,r),this._scheduler=new Ys(this,s,dx,gx),pp.call(this,this._ecEventProcessor=new Fl),this._messageCenter=new _l,this._initEvents(),this.resize=y(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),Dl(o,this),B(this)}function wl(t,e,i){var n,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=Yn(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(n=s[t](r,e,i)))return n}}function bl(t){var e=t._model,i=t._scheduler;i.restorePipelines(e),i.prepareStageTasks(),Al(t,"component",e,i),Al(t,"chart",e,i),i.plan()}function Sl(t,e,i,n,r){function a(n){n&&n.__alive&&n[e]&&n[e](n.__model,o,t._api,i)}var o=t._model;if(!n)return void G_(t._componentsViews.concat(t._chartsViews),a);var s={};s[n+"Id"]=i[n+"Id"],s[n+"Index"]=i[n+"Index"],s[n+"Name"]=i[n+"Name"];var l={mainType:n,query:s};r&&(l.subType=r);var h=i.excludeSeriesId;null!=h&&(h=N(Rn(h))),o&&o.eachComponent(l,function(e){h&&null!=h.get(e.id)||a(t["series"===n?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function Ml(t,e){var i=t._chartsMap,n=t._scheduler;e.eachSeries(function(t){n.updateStreamModes(t,i[t.__viewId])})}function Il(t,e){var i=t.type,n=t.escapeConnect,r=ux[i],a=r.actionInfo,l=(a.update||"update").split(":"),h=l.pop();l=null!=l[0]&&j_(l[0]),this[rx]=!0;var u=[t],c=!1;t.batch&&(c=!0,u=p(t.batch,function(e){return e=s(o({},e),t),e.batch=null,e}));var d,f=[],g="highlight"===i||"downplay"===i;G_(u,function(t){d=r.action(t,this._model,this._api),d=d||o({},t),d.type=a.event||d.type,f.push(d),g?Sl(this,h,t,"series"):l&&Sl(this,h,t,l.main,l.sub)},this),"none"===h||g||l||(this[ax]?(bl(this),lx.update.call(this,t),this[ax]=!1):lx[h].call(this,t)),d=c?{type:a.event||i,escapeConnect:n,batch:f}:f[0],this[rx]=!1,!e&&this._messageCenter.trigger(d.type,d)}function Tl(t){for(var e=this._pendingActions;e.length;){var i=e.shift();Il.call(this,i,t)}}function Cl(t){!t&&this.trigger("updated")}function Dl(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[ax]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function Al(t,e,i,n){function r(t){var e="_ec_"+t.id+"_"+t.type,r=s[e];if(!r){var u=j_(t.type),c=a?r_.getClass(u.main,u.sub):Vs.getClass(u.sub);r=new c,r.init(i,h),s[e]=r,o.push(r),l.add(r.group)}t.__viewId=r.__id=e,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!a&&n.prepareView(r,t,i,h)}for(var a="component"===e,o=a?t._componentsViews:t._chartsViews,s=a?t._componentsMap:t._chartsMap,l=t._zr,h=t._api,u=0;u<o.length;u++)o[u].__alive=!1;a?i.eachComponent(function(t,e){"series"!==t&&r(e)}):i.eachSeries(r);for(var u=0;u<o.length;){var c=o[u];c.__alive?u++:(!a&&c.renderTask.dispose(),l.remove(c.group),c.dispose(i,h),o.splice(u,1),delete s[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function kl(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Pl(t,e,i,n){Ll(t,e,i,n),G_(t._chartsViews,function(t){t.__alive=!1}),Ol(t,e,i,n),G_(t._chartsViews,function(t){t.__alive||t.remove(e,i)})}function Ll(t,e,i,n,r){G_(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,i,n),Rl(r,t)})}function Ol(t,e,i,n,r){var a,o=t._scheduler;e.eachSeries(function(e){var i=t._chartsMap[e.__viewId];i.__alive=!0;var s=i.renderTask;o.updatePayload(s,n),r&&r.get(e.uid)&&s.dirty(),a|=s.perform(o.getPerformArgs(s)),i.group.silent=!!e.get("silent"),Rl(e,i),zl(e,i)}),o.unfinished|=a,Bl(t._zr,e),v_(t._zr.dom,e)}function El(t,e){G_(px,function(i){i(t,e)})}function Bl(t,e){var i=t.storage,n=0;i.traverse(function(t){t.isGroup||n++}),n>e.get("hoverLayerThreshold")&&!jf.node&&i.traverse(function(t){t.isGroup||(t.useHoverLayer=!0)})}function zl(t,e){var i=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==i&&t.setStyle("blend",i),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",i)})})}function Rl(t,e){var i=t.get("z"),n=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t.zlevel=n))})}function Nl(t){var e=t._coordSysMgr;return o(new Jo(t),{getCoordinateSystems:y(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var i=e.__ecComponentInfo;if(null!=i)return t._model.getComponent(i.mainType,i.index);e=e.parent}}})}function Fl(){this.eventInfo}function Vl(t){function e(t,e){for(var i=0;i<t.length;i++){var n=t[i];n[a]=e}}var i=0,n=1,r=2,a="__connectUpdateStatus";G_(cx,function(o,s){t._messageCenter.on(s,function(o){if(_x[t.group]&&t[a]!==i){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];G_(yx,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,i),G_(l,function(t){t[a]!==n&&t.dispatchAction(s)}),e(l,r)}})})}function Hl(t,e,i){var n=Xl(t);if(n)return n;var r=new xl(t,e,i);return r.id="ec_"+xx++,yx[r.id]=r,Zn(t,Sx,r.id),Vl(r),r}function Wl(t){if(x(t)){var e=t;t=null,G_(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+bx++,G_(e,function(e){e.group=t})}return _x[t]=!0,t}function Gl(t){_x[t]=!1}function Ul(t){"string"==typeof t?t=yx[t]:t instanceof xl||(t=Xl(t)),t instanceof xl&&!t.isDisposed()&&t.dispose()}function Xl(t){return yx[$n(t,Sx)]}function jl(t){return yx[t]}function Yl(t,e){vx[t]=e}function ql(t){fx.push(t)}function Zl(t,e){ih(dx,t,e,$_)}function $l(t){px.push(t)}function Kl(t,e,i){"function"==typeof e&&(i=e,e="");var n=X_(t)?t.type:[t,t={event:e}][0];t.event=(t.event||n).toLowerCase(),e=t.event,W_(ox.test(n)&&ox.test(e)),ux[n]||(ux[n]={action:i,actionInfo:t}),cx[e]=n}function Ql(t,e){ts.register(t,e)}function Jl(t){var e=ts.get(t);return e?e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice():void 0}function th(t,e){ih(gx,t,e,Q_,"layout")}function eh(t,e){ih(gx,t,e,tx,"visual")}function ih(t,e,i,n,r){(U_(e)||X_(e))&&(i=e,e=n);var a=Ys.wrapStageHandler(i,r);return a.__prio=e,a.__raw=i,t.push(a),a}function nh(t,e){mx[t]=e}function rh(t){return vy.extend(t)}function ah(t){return r_.extend(t)}function oh(t){return n_.extend(t)}function sh(t){return Vs.extend(t)}function lh(t){i("createCanvas",t)}function hh(t,e,i){V_.registerMap(t,e,i)}function uh(t){var e=V_.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}function ch(t){return t}function dh(t,e,i,n,r){this._old=t,this._new=e,this._oldKeyGetter=i||ch,this._newKeyGetter=n||ch,this.context=r}function fh(t,e,i,n,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[n](t[a],a),s=e[o];null==s?(i.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}function ph(t){var e={},i=e.encode={},n=N(),r=[],a=[];f(t.dimensions,function(e){var o=t.getDimensionInfo(e),s=o.coordDim;if(s){var l=i[s];i.hasOwnProperty(s)||(l=i[s]=[]),l[o.coordDimIndex]=e,o.isExtraCoord||(n.set(s,1),vh(o.type)&&(r[0]=e)),o.defaultTooltip&&a.push(e)}Tx.each(function(t,e){var n=i[e];i.hasOwnProperty(e)||(n=i[e]=[]);var r=o.otherDims[e];null!=r&&r!==!1&&(n[r]=o.name)})});var o=[],s={};n.each(function(t,e){var n=i[e];s[e]=n[0],o=o.concat(n)}),e.dataDimsOnCoord=o,e.encodeFirstDimNotExtra=s;var l=i.label;l&&l.length&&(r=l.slice());var h=i.tooltip;return h&&h.length?a=h.slice():a.length||(a=r.slice()),i.defaultedLabel=r,i.defaultedTooltip=a,e}function gh(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function vh(t){return!("ordinal"===t||"time"===t)}function mh(t){return t._rawCount>65535?Lx:Ex}function yh(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function _h(t,e){f(Bx.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,f(zx,function(i){t[i]=n(e[i])}),t._calculationInfo=o(e._calculationInfo)}function xh(t,e,i,n,r){var a=Px[e.type],o=n-1,s=e.name,l=t[s][o];if(l&&l.length<i){for(var h=new a(Math.min(r-o*i,i)),u=0;u<l.length;u++)h[u]=l[u];t[s][o]=h}for(var c=n*i;r>c;c+=i)t[s].push(new a(Math.min(r-c,i)))}function wh(t){var e=t._invertedIndicesMap;f(e,function(i,n){var r=t._dimensionInfos[n],a=r.ordinalMeta;if(a){i=e[n]=new Ox(a.categories.length);for(var o=0;o<i.length;o++)i[o]=Ax;for(var o=0;o<t._count;o++)i[t.get(n,o)]=o}})}function bh(t,e,i){var n;if(null!=e){var r=t._chunkSize,a=Math.floor(i/r),o=i%r,s=t.dimensions[e],l=t._storage[s][a];if(l){n=l[o];var h=t._dimensionInfos[s].ordinalMeta;h&&h.categories.length&&(n=h.categories[n])}}return n}function Sh(t){return t}function Mh(t){return t<this._count&&t>=0?this._indices[t]:-1}function Ih(t,e){var i=t._idList[e];return null==i&&(i=bh(t,t._idDimIdx,e)),null==i&&(i=kx+e),i}function Th(t){return x(t)||(t=[t]),t}function Ch(t,e){var i=t.dimensions,n=new Rx(p(i,t.getDimensionInfo,t),t.hostModel);_h(n,t);for(var r=n._storage={},a=t._storage,o=0;o<i.length;o++){var s=i[o];a[s]&&(h(e,s)>=0?(r[s]=Dh(a[s]),n._rawExtent[s]=Ah(),n._extent[s]=null):r[s]=a[s])}return n}function Dh(t){for(var e=new Array(t.length),i=0;i<t.length;i++)e[i]=yh(t[i]);return e}function Ah(){return[1/0,-1/0]}function kh(t,e,i){function r(t,e,i){null!=Tx.get(e)?t.otherDims[e]=i:(t.coordDim=e,t.coordDimIndex=i,u.set(e,!0))}Oo.isInstance(e)||(e=Oo.seriesDataToSource(e)),i=i||{},t=(t||[]).slice();for(var a=(i.dimsDef||[]).slice(),l=N(i.encodeDef),h=N(),u=N(),c=[],d=Ph(e,t,a,i.dimCount),p=0;d>p;p++){var g=a[p]=o({},S(a[p])?a[p]:{name:a[p]}),v=g.name,m=c[p]={otherDims:{}};null!=v&&null==h.get(v)&&(m.name=m.displayName=v,h.set(v,p)),null!=g.type&&(m.type=g.type),null!=g.displayName&&(m.displayName=g.displayName)
}l.each(function(t,e){if(t=Rn(t).slice(),1===t.length&&t[0]<0)return void l.set(e,!1);var i=l.set(e,[]);f(t,function(t,n){b(t)&&(t=h.get(t)),null!=t&&d>t&&(i[n]=t,r(c[t],e,n))})});var y=0;f(t,function(t){var e,t,i,a;if(b(t))e=t,t={};else{e=t.name;var o=t.ordinalMeta;t.ordinalMeta=null,t=n(t),t.ordinalMeta=o,i=t.dimsDef,a=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var h=l.get(e);if(h!==!1){var h=Rn(h);if(!h.length)for(var u=0;u<(i&&i.length||1);u++){for(;y<c.length&&null!=c[y].coordDim;)y++;y<c.length&&h.push(y++)}f(h,function(n,o){var l=c[n];if(r(s(l,t),e,o),null==l.name&&i){var h=i[o];!S(h)&&(h={name:h}),l.name=l.displayName=h.name,l.defaultTooltip=h.defaultTooltip}a&&s(l.otherDims,a)})}});var _=i.generateCoord,x=i.generateCoordCount,w=null!=x;x=_?x||1:0;for(var M=_||"value",I=0;d>I;I++){var m=c[I]=c[I]||{},T=m.coordDim;null==T&&(m.coordDim=Lh(M,u,w),m.coordDimIndex=0,(!_||0>=x)&&(m.isExtraCoord=!0),x--),null==m.name&&(m.name=Lh(m.coordDim,h)),null==m.type&&Uo(e,I,m.name)&&(m.type="ordinal")}return c}function Ph(t,e,i,n){var r=Math.max(t.dimensionsDetectCount||1,e.length,i.length,n||0);return f(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function Lh(t,e,i){if(i||null!=e.get(t)){for(var n=0;null!=e.get(t+n);)n++;t+=n}return e.set(t,!0),t}function Oh(t,e,i){i=i||{};var n,r,a,o,s=i.byIndex,l=i.stackedCoordDimension,h=!(!t||!t.get("stack"));if(f(e,function(t,i){b(t)&&(e[i]=t={name:t}),h&&!t.isExtraCoord&&(s||n||!t.ordinalMeta||(n=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||n||(s=!0),r){a="__\x00ecstackresult",o="__\x00ecstackedover",n&&(n.createInvertedIndices=!0);var u=r.coordDim,c=r.type,d=0;f(e,function(t){t.coordDim===u&&d++}),e.push({name:a,coordDim:u,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,e.push({name:o,coordDim:o,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:n&&n.name,isStackedByIndex:s,stackedOverDimension:o,stackResultDimension:a}}function Eh(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Bh(t,e){return Eh(t,e)?t.getCalculationInfo("stackResultDimension"):e}function zh(t,e,i){i=i||{},Oo.isInstance(t)||(t=Oo.seriesDataToSource(t));var n,r=e.get("coordinateSystem"),a=ts.get(r),o=Po(e);o&&(n=p(o.coordSysDims,function(t){var e={name:t},i=o.axisMap.get(t);if(i){var n=i.get("type");e.type=gh(n)}return e})),n||(n=a&&(a.getDimensionsInfo?a.getDimensionsInfo():a.dimensions.slice())||["x","y"]);var s,l,h=Vx(t,{coordDimensions:n,generateCoord:i.generateCoord});o&&f(h,function(t,e){var i=t.coordDim,n=o.categoryAxisMap.get(i);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(h[s].otherDims.itemName=0);var u=Oh(e,h),c=new Rx(h,e);c.setCalculationInfo(u);var d=null!=s&&Rh(t)?function(t,e,i,n){return n===s?i:this.defaultDimValueGetter(t,e,i,n)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function Rh(t){if(t.sourceFormat===by){var e=Nh(t.data||[]);return null!=e&&!x(Fn(e))}}function Nh(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function Fh(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function Vh(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}function Hh(t){return t._map||(t._map=N(t.categories))}function Wh(t){return S(t)&&null!=t.value?t.value:t+""}function Gh(t,e,i,n){var r={},a=t[1]-t[0],o=r.interval=lo(a/e,!0);null!=i&&i>o&&(o=r.interval=i),null!=n&&o>n&&(o=r.interval=n);var s=r.intervalPrecision=Uh(o),l=r.niceTickExtent=[Ux(Math.ceil(t[0]/o)*o,s),Ux(Math.floor(t[1]/o)*o,s)];return jh(l,t),r}function Uh(t){return to(t)+2}function Xh(t,e,i){t[e]=Math.max(Math.min(t[e],i[1]),i[0])}function jh(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),Xh(t,0,e),Xh(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function Yh(t,e,i,n){var r=[];if(!t)return r;var a=1e4;e[0]<i[0]&&r.push(e[0]);for(var o=i[0];o<=i[1]&&(r.push(o),o=Ux(o+t,n),o!==r[r.length-1]);)if(r.length>a)return[];return e[1]>(r.length?r[r.length-1]:i[1])&&r.push(e[1]),r}function qh(t){return t.get("stack")||Yx+t.seriesIndex}function Zh(t){return t.dim+t.index}function $h(t,e){var i=[];return e.eachSeriesByType(t,function(t){eu(t)&&!iu(t)&&i.push(t)}),i}function Kh(t){var e=[];return f(t,function(t){var i=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),a=r.getExtent(),o="category"===r.type?r.getBandWidth():Math.abs(a[1]-a[0])/i.count(),s=$a(t.get("barWidth"),o),l=$a(t.get("barMaxWidth"),o),h=t.get("barGap"),u=t.get("barCategoryGap");e.push({bandWidth:o,barWidth:s,barMaxWidth:l,barGap:h,barCategoryGap:u,axisKey:Zh(r),stackId:qh(t)})}),Qh(e)}function Qh(t){var e={};f(t,function(t){var i=t.axisKey,n=t.bandWidth,r=e[i]||{bandWidth:n,remainedWidth:n,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},a=r.stacks;e[i]=r;var o=t.stackId;a[o]||r.autoWidthCount++,a[o]=a[o]||{width:0,maxWidth:0};var s=t.barWidth;s&&!a[o].width&&(a[o].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(a[o].maxWidth=l);var h=t.barGap;null!=h&&(r.gap=h);var u=t.barCategoryGap;null!=u&&(r.categoryGap=u)});var i={};return f(e,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,a=$a(t.categoryGap,r),o=$a(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-a)/(l+(l-1)*o);h=Math.max(h,0),f(n,function(t){var e=t.maxWidth;e&&h>e&&(e=Math.min(e,s),t.width&&(e=Math.min(e,t.width)),s-=e,t.width=e,l--)}),h=(s-a)/(l+(l-1)*o),h=Math.max(h,0);var u,c=0;f(n,function(t){t.width||(t.width=h),u=t,c+=t.width*(1+o)}),u&&(c-=u.width*o);var d=-c/2;f(n,function(t,n){i[e][n]=i[e][n]||{offset:d,width:t.width},d+=t.width*(1+o)})}),i}function Jh(t,e,i){if(t&&e){var n=t[Zh(e)];return null!=n&&null!=i&&(n=n[qh(i)]),n}}function tu(t,e){var i=$h(t,e),n=Kh(i),r={};f(i,function(t){var e=t.getData(),i=t.coordinateSystem,a=i.getBaseAxis(),o=qh(t),s=n[Zh(a)][o],l=s.offset,h=s.width,u=i.getOtherAxis(a),c=t.get("barMinHeight")||0;r[o]=r[o]||[],e.setLayout({offset:l,size:h});for(var d=e.mapDimension(u.dim),f=e.mapDimension(a.dim),p=Eh(e,d),g=u.isHorizontal(),v=nu(a,u,p),m=0,y=e.count();y>m;m++){var _=e.get(d,m),x=e.get(f,m);if(!isNaN(_)){var w=_>=0?"p":"n",b=v;p&&(r[o][x]||(r[o][x]={p:v,n:v}),b=r[o][x][w]);var S,M,I,T;if(g){var C=i.dataToPoint([_,x]);S=b,M=C[1]+l,I=C[0]-v,T=h,Math.abs(I)<c&&(I=(0>I?-1:1)*c),p&&(r[o][x][w]+=I)}else{var C=i.dataToPoint([x,_]);S=C[0]+l,M=b,I=h,T=C[1]-v,Math.abs(T)<c&&(T=(0>=T?-1:1)*c),p&&(r[o][x][w]+=T)}e.setItemLayout(m,{x:S,y:M,width:I,height:T})}}},this)}function eu(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function iu(t){return t.pipelineContext&&t.pipelineContext.large}function nu(t,e){var i,n,r=e.getGlobalExtent();r[0]>r[1]?(i=r[1],n=r[0]):(i=r[0],n=r[1]);var a=e.toGlobalCoord(e.dataToCoord(0));return i>a&&(a=i),a>n&&(a=n),a}function ru(t,e){return uw(t,hw(e))}function au(t,e){var i,n,r,a=t.type,o=e.getMin(),s=e.getMax(),l=null!=o,h=null!=s,u=t.getExtent();"ordinal"===a?i=e.getCategories().length:(n=e.get("boundaryGap"),x(n)||(n=[n||0,n||0]),"boolean"==typeof n[0]&&(n=[0,0]),n[0]=$a(n[0],1),n[1]=$a(n[1],1),r=u[1]-u[0]||Math.abs(u[0])),null==o&&(o="ordinal"===a?i?0:0/0:u[0]-n[0]*r),null==s&&(s="ordinal"===a?i?i-1:0/0:u[1]+n[1]*r),"dataMin"===o?o=u[0]:"function"==typeof o&&(o=o({min:u[0],max:u[1]})),"dataMax"===s?s=u[1]:"function"==typeof s&&(s=s({min:u[0],max:u[1]})),(null==o||!isFinite(o))&&(o=0/0),(null==s||!isFinite(s))&&(s=0/0),t.setBlank(C(o)||C(s)||"ordinal"===a&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(o>0&&s>0&&!l&&(o=0),0>o&&0>s&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===a){var d,p=$h("bar",c);if(f(p,function(t){d|=t.getBaseAxis()===e.axis}),d){var g=Kh(p),v=ou(o,s,e,g);o=v.min,s=v.max}}return[o,s]}function ou(t,e,i,n){var r=i.axis.getExtent(),a=r[1]-r[0],o=Jh(n,i.axis);if(void 0===o)return{min:t,max:e};var s=1/0;f(o,function(t){s=Math.min(t.offset,s)});var l=-1/0;f(o,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var h=s+l,u=e-t,c=1-(s+l)/a,d=u/c-u;return e+=d*(l/h),t-=d*(s/h),{min:t,max:e}}function su(t,e){var i=au(t,e),n=null!=e.getMin(),r=null!=e.getMax(),a=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:a,fixMin:n,fixMax:r,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function lu(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Gx(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new jx;default:return(Fh.getClass(e)||jx).create(t)}}function hu(t){var e=t.scale.getExtent(),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)}function uu(t){var e=t.getLabelModel().get("formatter"),i="category"===t.type?t.scale.getExtent()[0]:null;return"string"==typeof e?e=function(e){return function(i){return i=t.scale.getLabel(i),e.replace("{value}",null!=i?i:"")}}(e):"function"==typeof e?function(n,r){return null!=i&&(r=n-i),e(cu(t,n),r)}:function(e){return t.scale.getLabel(e)}}function cu(t,e){return"category"===t.type?t.scale.getLabel(e):e}function du(t){var e=t.model,i=t.scale;if(e.get("axisLabel.show")&&!i.isBlank()){var n,r,a="category"===t.type,o=i.getExtent();a?r=i.count():(n=i.getTicks(),r=n.length);var s,l=t.getLabelModel(),h=uu(t),u=1;r>40&&(u=Math.ceil(r/40));for(var c=0;r>c;c+=u){var d=n?n[c]:o[0]+c,f=h(d),p=l.getTextRect(f),g=fu(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function fu(t,e){var i=e*Math.PI/180,n=t.plain(),r=n.width,a=n.height,o=r*Math.cos(i)+a*Math.sin(i),s=r*Math.sin(i)+a*Math.cos(i),l=new mi(n.x,n.y,o,s);return l}function pu(t){var e=t.get("interval");return null==e?"auto":e}function gu(t){return"category"===t.type&&0===pu(t.getLabelModel())}function vu(t,e){if("image"!==this.type){var i=this.style,n=this.shape;n&&"line"===n.symbolType?i.stroke=t:this.__isEmptyBrush?(i.stroke=t,i.fill=e||"#fff"):(i.fill&&(i.fill=t),i.stroke&&(i.stroke=t)),this.dirty(!1)}}function mu(t,e,i,n,r,a,o){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?ea(t.slice(8),new mi(e,i,n,r),o?"center":"cover"):0===t.indexOf("path://")?ta(t.slice(7),{},new mi(e,i,n,r),o?"center":"cover"):new Mw({shape:{symbolType:t,x:e,y:i,width:n,height:r}}),l.__isEmptyBrush=s,l.setColor=vu,l.setColor(a),l}function yu(t){return zh(t.getSource(),t)}function _u(t,e){var i=e;Wa.isInstance(e)||(i=new Wa(e),c(i,vw));var n=lu(i);return n.setExtent(t[0],t[1]),su(n,i),n}function xu(t){c(t,vw)}function wu(t,e){return Math.abs(t-e)<Cw}function bu(t,e,i){var n=0,r=t[0];if(!r)return!1;for(var a=1;a<t.length;a++){var o=t[a];n+=Ar(r[0],r[1],o[0],o[1],e,i),r=o}var s=t[0];return wu(r[0],s[0])&&wu(r[1],s[1])||(n+=Ar(r[0],r[1],s[0],s[1],e,i)),0!==n}function Su(t,e,i){if(this.name=t,this.geometries=e,i)i=[i[0],i[1]];else{var n=this.getBoundingRect();i=[n.x+n.width/2,n.y+n.height/2]}this.center=i}function Mu(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var i=t.features,n=0;n<i.length;n++)for(var r=i[n],a=r.geometry,o=a.coordinates,s=a.encodeOffsets,l=0;l<o.length;l++){var h=o[l];if("Polygon"===a.type)o[l]=Iu(h,s[l],e);else if("MultiPolygon"===a.type)for(var u=0;u<h.length;u++){var c=h[u];h[u]=Iu(c,s[l][u],e)}}return t.UTF8Encoding=!1,t}function Iu(t,e,i){for(var n=[],r=e[0],a=e[1],o=0;o<t.length;o+=2){var s=t.charCodeAt(o)-64,l=t.charCodeAt(o+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),s+=r,l+=a,r=s,a=l,n.push([s/i,l/i])}return n}function Tu(t){return"category"===t.type?Du(t):Pu(t)}function Cu(t,e){return"category"===t.type?ku(t,e):{ticks:t.scale.getTicks()}}function Du(t){var e=t.getLabelModel(),i=Au(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:i.labelCategoryInterval}:i}function Au(t,e){var i=Lu(t,"labels"),n=pu(e),r=Ou(i,n);if(r)return r;var a,o;return w(n)?a=Fu(t,n):(o="auto"===n?Bu(t):n,a=Nu(t,o)),Eu(i,n,{labels:a,labelCategoryInterval:o})}function ku(t,e){var i=Lu(t,"ticks"),n=pu(e),r=Ou(i,n);if(r)return r;var a,o;if((!e.get("show")||t.scale.isBlank())&&(a=[]),w(n))a=Fu(t,n,!0);else if("auto"===n){var s=Au(t,t.getLabelModel());o=s.labelCategoryInterval,a=p(s.labels,function(t){return t.tickValue})}else o=n,a=Nu(t,o,!0);return Eu(i,n,{ticks:a,tickCategoryInterval:o})}function Pu(t){var e=t.scale.getTicks(),i=uu(t);return{labels:p(e,function(e,n){return{formattedLabel:i(e,n),rawLabel:t.scale.getLabel(e),tickValue:e}})}}function Lu(t,e){return Aw(t)[e]||(Aw(t)[e]=[])}function Ou(t,e){for(var i=0;i<t.length;i++)if(t[i].key===e)return t[i].value}function Eu(t,e,i){return t.push({key:e,value:i}),i}function Bu(t){var e=Aw(t).autoInterval;return null!=e?e:Aw(t).autoInterval=t.calculateCategoryInterval()}function zu(t){var e=Ru(t),i=uu(t),n=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,a=r.getExtent(),o=r.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],h=t.dataToCoord(l+1)-t.dataToCoord(l),u=Math.abs(h*Math.cos(n)),c=Math.abs(h*Math.sin(n)),d=0,f=0;l<=a[1];l+=s){var p=0,g=0,v=Ri(i(l),e.font,"center","top");p=1.3*v.width,g=1.3*v.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var m=d/u,y=f/c;isNaN(m)&&(m=1/0),isNaN(y)&&(y=1/0);var _=Math.max(0,Math.floor(Math.min(m,y))),x=Aw(t.model),w=x.lastAutoInterval,b=x.lastTickCount;return null!=w&&null!=b&&Math.abs(w-_)<=1&&Math.abs(b-o)<=1&&w>_?_=w:(x.lastTickCount=o,x.lastAutoInterval=_),_}function Ru(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function Nu(t,e,i){function n(t){l.push(i?t:{formattedLabel:r(t),rawLabel:a.getLabel(t),tickValue:t})}var r=uu(t),a=t.scale,o=a.getExtent(),s=t.getLabelModel(),l=[],h=Math.max((e||0)+1,1),u=o[0],c=a.count();0!==u&&h>1&&c/h>2&&(u=Math.round(Math.ceil(u/h)*h));var d=gu(t),f=s.get("showMinLabel")||d,p=s.get("showMaxLabel")||d;f&&u!==o[0]&&n(o[0]);for(var g=u;g<=o[1];g+=h)n(g);return p&&g!==o[1]&&n(o[1]),l}function Fu(t,e,i){var n=t.scale,r=uu(t),a=[];return f(n.getTicks(),function(t){var o=n.getLabel(t);e(t,o)&&a.push(i?t:{formattedLabel:r(t),rawLabel:o,tickValue:t})}),a}function Vu(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}function Hu(t,e,i,n,r){function a(t,e){return u?t>e:e>t}var o=e.length;if(t.onBand&&!n&&o){var s,l=t.getExtent();if(1===o)e[0].coord=l[0],s=e[1]={coord:l[0]};else{var h=e[1].coord-e[0].coord;f(e,function(t){t.coord-=h/2;var e=e||0;e%2>0&&(t.coord-=h/(2*(e+1)))}),s={coord:e[o-1].coord+h},e.push(s)}var u=l[0]>l[1];a(e[0].coord,l[0])&&(r?e[0].coord=l[0]:e.shift()),r&&a(l[0],e[0].coord)&&e.unshift({coord:l[0]}),a(l[1],s.coord)&&(r?s.coord=l[1]:e.pop()),r&&a(s.coord,l[1])&&e.push({coord:l[1]})}}function Wu(t){return this._axes[t]}function Gu(t){Bw.call(this,t)}function Uu(t,e){return e.type||(e.data?"category":"value")}function Xu(t,e){return t.getCoordSysModel()===e}function ju(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this.model=t}function Yu(t,e,i,n){function r(t){return t.dim+"_"+t.index}i.getAxesOnZeroOf=function(){return a?[a]:[]};var a,o=t[e],s=i.model,l=s.get("axisLine.onZero"),h=s.get("axisLine.onZeroAxisIndex");if(l){if(null!=h)qu(o[h])&&(a=o[h]);else for(var u in o)if(o.hasOwnProperty(u)&&qu(o[u])&&!n[r(o[u])]){a=o[u];break}a&&(n[r(a)]=!0)}}function qu(t){return t&&"category"!==t.type&&"time"!==t.type&&hu(t)}function Zu(t,e){var i=t.getExtent(),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function $u(t){return p(Uw,function(e){var i=t.getReferringComponents(e)[0];return i})}function Ku(t){return"cartesian2d"===t.get("coordinateSystem")}function Qu(t,e){var i=t.mapDimension("defaultedLabel",!0),n=i.length;if(1===n)return Ts(t,e,i[0]);if(n){for(var r=[],a=0;a<i.length;a++){var o=Ts(t,e,i[a]);r.push(o)}return r.join(" ")}}function Ju(t,e,i,n,r,a){var o=i.getModel("label"),s=i.getModel("emphasis.label");ba(t,e,o,s,{labelFetcher:r,labelDataIndex:a,defaultText:Qu(r.getData(),a),isRectText:!0,autoColor:n}),tc(t),tc(e)}function tc(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function ec(t,e,i){i.style.text=null,Oa(i,{shape:{width:0}},e,t,function(){i.parent&&i.parent.remove(i)})}function ic(t,e,i){i.style.text=null,Oa(i,{shape:{r:i.shape.r0}},e,t,function(){i.parent&&i.parent.remove(i)})}function nc(t,e,i,n,r,a,o,l){var h=e.getItemVisual(i,"color"),u=e.getItemVisual(i,"opacity"),c=n.getModel("itemStyle"),d=n.getModel("emphasis.itemStyle").getBarItemStyle();l||t.setShape("r",c.get("barBorderRadius")||0),t.useStyle(s({fill:h,opacity:u},c.getBarItemStyle()));var f=n.getShallow("cursor");f&&t.attr("cursor",f);var p=o?r.height>0?"bottom":"top":r.width>0?"left":"right";l||Ju(t.style,d,n,h,a,i,p),xa(t,d)}function rc(t,e){var i=t.get(qw)||0;return Math.min(i,Math.abs(e.width),Math.abs(e.height))}function ac(t,e,i){var n=t.getData(),r=[],a=n.getLayout("valueAxisHorizontal")?1:0;r[1-a]=n.getLayout("valueAxisStart");var o=new Kw({shape:{points:n.getLayout("largePoints")},incremental:!!i,__startPoint:r,__valueIdx:a});e.add(o),oc(o,t,n)}function oc(t,e,i){var n=i.getVisual("borderColor")||i.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=n,t.style.lineWidth=i.getLayout("barWidth")}function sc(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e}function lc(t,e,i,n){var r,a,o=no(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;return ro(o-Qw/2)?(a=l?"bottom":"top",r="center"):ro(o-1.5*Qw)?(a=l?"top":"bottom",r="center"):(a="middle",r=1.5*Qw>o&&o>Qw/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}function hc(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}function uc(t,e,i){if(!gu(t.axis)){var n=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],i=i||[];var a=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],h=i[0],u=i[1],c=i[i.length-1],d=i[i.length-2];n===!1?(cc(a),cc(h)):dc(a,o)&&(n?(cc(o),cc(u)):(cc(a),cc(h))),r===!1?(cc(s),cc(c)):dc(l,s)&&(r?(cc(l),cc(d)):(cc(s),cc(c)))}}function cc(t){t&&(t.ignore=!0)}function dc(t,e){var i=t&&t.getBoundingRect().clone(),n=e&&e.getBoundingRect().clone();if(i&&n){var r=Ie([]);return Ae(r,r,-t.rotation),i.applyTransform(Ce([],r,t.getLocalTransform())),n.applyTransform(Ce([],r,e.getLocalTransform())),i.intersect(n)}}function fc(t){return"middle"===t||"center"===t}function pc(t,e,i){var n=e.axis;if(e.get("axisTick.show")&&!n.scale.isBlank()){for(var r=e.getModel("axisTick"),a=r.getModel("lineStyle"),o=r.get("length"),l=n.getTicksCoords(),h=[],u=[],c=t._transform,d=[],f=0;f<l.length;f++){var p=l[f].coord;h[0]=p,h[1]=0,u[0]=p,u[1]=i.tickDirection*o,c&&(ae(h,h,c),ae(u,u,c));var g=new Dm(ra({anid:"tick_"+l[f].tickValue,shape:{x1:h[0],y1:h[1],x2:u[0],y2:u[1]},style:s(a.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),z2:2,silent:!0}));t.group.add(g),d.push(g)}return d}}function gc(t,e,i){var n=e.axis,r=D(i.axisLabelShow,e.get("axisLabel.show"));if(r&&!n.scale.isBlank()){var a=e.getModel("axisLabel"),o=a.get("margin"),s=n.getViewLabels(),l=(D(i.labelRotate,a.get("rotate"))||0)*Qw/180,h=eb(i.rotation,l,i.labelDirection),u=e.getCategories(!0),c=[],d=hc(e),p=e.get("triggerEvent");return f(s,function(r,s){var l=r.tickValue,f=r.formattedLabel,g=r.rawLabel,v=a;u&&u[l]&&u[l].textStyle&&(v=new Wa(u[l].textStyle,a,e.ecModel));var m=v.getTextColor()||e.get("axisLine.lineStyle.color"),y=n.dataToCoord(l),_=[y,i.labelOffset+i.labelDirection*o],x=new pm({anid:"label_"+l,position:_,rotation:h.rotation,silent:d,z2:10});Sa(x.style,v,{text:f,textAlign:v.getShallow("align",!0)||h.textAlign,textVerticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||h.textVerticalAlign,textFill:"function"==typeof m?m("category"===n.type?g:"value"===n.type?l+"":l,s):m}),p&&(x.eventData=sc(e),x.eventData.targetType="axisLabel",x.eventData.value=g),t._dumbGroup.add(x),x.updateTransform(),c.push(x),t.group.add(x),x.decomposeTransform()}),c}}function vc(t,e){var i={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return mc(i,t,e),i.seriesInvolved&&_c(i,t),i}function mc(t,e,i){var n=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),a=r.get("link",!0)||[],o=[];ib(i.getCoordinateSystems(),function(i){function s(n,s,l){var u=l.model.getModel("axisPointer",r),d=u.get("show");if(d&&("auto"!==d||n||Ic(u))){null==s&&(s=u.get("triggerTooltip")),u=n?yc(l,c,r,e,n,s):u;var f=u.get("snap"),p=Tc(l.model),g=s||f||"category"===l.type,v=t.axesInfo[p]={key:p,axis:l,coordSys:i,axisPointerModel:u,triggerTooltip:s,involveSeries:g,snap:f,useHandle:Ic(u),seriesModels:[]};h[p]=v,t.seriesInvolved|=g;var m=xc(a,l);if(null!=m){var y=o[m]||(o[m]={axesInfo:{}});y.axesInfo[p]=v,y.mapper=a[m].mapper,v.linkGroup=y}}}if(i.axisPointerEnabled){var l=Tc(i.model),h=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=i;var u=i.model,c=u.getModel("tooltip",n);if(ib(i.getAxes(),nb(s,!1,null)),i.getTooltipAxes&&n&&c.get("show")){var d="axis"===c.get("trigger"),f="cross"===c.get("axisPointer.type"),p=i.getTooltipAxes(c.get("axisPointer.axis"));(d||f)&&ib(p.baseAxes,nb(s,f?"cross":!0,d)),f&&ib(p.otherAxes,nb(s,"cross",!1))}}})}function yc(t,e,i,r,a,o){var l=e.getModel("axisPointer"),h={};ib(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){h[t]=n(l.get(t))}),h.snap="category"!==t.type&&!!o,"cross"===l.get("type")&&(h.type="line");var u=h.label||(h.label={});if(null==u.show&&(u.show=!1),"cross"===a){var c=l.get("label.show");if(u.show=null!=c?c:!0,!o){var d=h.lineStyle=l.get("crossStyle");d&&s(u,d.textStyle)}}return t.model.getModel("axisPointer",new Wa(h,i,r))}function _c(t,e){e.eachSeries(function(e){var i=e.coordinateSystem,n=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);i&&"none"!==n&&n!==!1&&"item"!==n&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&ib(t.coordSysAxesInfo[Tc(i.model)],function(t){var n=t.axis;i.getAxis(n.dim)===n&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function xc(t,e){for(var i=e.model,n=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(wc(a[n+"AxisId"],i.id)||wc(a[n+"AxisIndex"],i.componentIndex)||wc(a[n+"AxisName"],i.name))return r}}function wc(t,e){return"all"===t||x(t)&&h(t,e)>=0||t===e}function bc(t){var e=Sc(t);if(e){var i=e.axisPointerModel,n=e.axis.scale,r=i.option,a=i.get("status"),o=i.get("value");null!=o&&(o=n.parse(o));var s=Ic(i);null==a&&(r.status=s?"show":"hide");var l=n.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==o||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function Sc(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[Tc(t)]}function Mc(t){var e=Sc(t);return e&&e.axisPointerModel}function Ic(t){return!!t.get("handle.show")}function Tc(t){return t.type+"||"+t.id}function Cc(t,e,i,n,r,a){var o=rb.getAxisPointerClass(t.axisPointerClass);if(o){var s=Mc(e);s?(t._axisPointer||(t._axisPointer=new o)).render(e,s,n,a):Dc(t,n)}}function Dc(t,e,i){var n=t._axisPointer;n&&n.dispose(e,i),t._axisPointer=null}function Ac(t,e,i){i=i||{};var n=t.coordinateSystem,r=e.axis,a={},o=r.getAxesOnZeroOf()[0],s=r.position,l=o?"onZero":s,h=r.dim,u=n.getRect(),c=[u.x,u.x+u.width,u.y,u.y+u.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===h?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(o){var g=o.toGlobalCoord(o.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}a.position=["y"===h?p[d[l]]:c[0],"x"===h?p[d[l]]:c[3]],a.rotation=Math.PI/2*("x"===h?0:1);var v={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=v[s],a.labelOffset=o?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(a.tickDirection=-a.tickDirection),D(i.labelInside,e.get("axisLabel.inside"))&&(a.labelDirection=-a.labelDirection);var m=e.get("axisLabel.rotate");return a.labelRotate="top"===l?-m:m,a.z2=1,a}function kc(t,e,i){ig.call(this),this.updateData(t,e,i)}function Pc(t){return[t[0]/2,t[1]/2]}function Lc(t,e){this.parent.drift(t,e)}function Oc(){!ga(this)&&Bc.call(this)}function Ec(){!ga(this)&&zc.call(this)}function Bc(){if(!this.incremental&&!this.useHoverLayer){var t=this.__symbolOriginalScale,e=t[1]/t[0];this.animateTo({scale:[Math.max(1.1*t[0],t[0]+3),Math.max(1.1*t[1],t[1]+3*e)]},400,"elasticOut")}}function zc(){this.incremental||this.useHoverLayer||this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}function Rc(t){this.group=new ig,this._symbolCtor=t||kc}function Nc(t,e,i,n){return!(!e||isNaN(e[0])||isNaN(e[1])||n.isIgnore&&n.isIgnore(i)||n.clipShape&&!n.clipShape.contain(e[0],e[1])||"none"===t.getItemVisual(i,"symbol"))}function Fc(t){return null==t||S(t)||(t={isIgnore:t}),t||{}}function Vc(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function Hc(t,e,i){var n,r=t.getBaseAxis(),a=t.getOtherAxis(r),o=Wc(a,i),s=r.dim,l=a.dim,h=e.mapDimension(l),u=e.mapDimension(s),c="x"===l||"radius"===l?1:0,d=p(t.dimensions,function(t){return e.mapDimension(t)}),f=e.getCalculationInfo("stackResultDimension");return(n|=Eh(e,d[0]))&&(d[0]=f),(n|=Eh(e,d[1]))&&(d[1]=f),{dataDimsForPoint:d,valueStart:o,valueAxisDim:l,baseAxisDim:s,stacked:!!n,valueDim:h,baseDim:u,baseDataOffset:c,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Wc(t,e){var i=0,n=t.scale.getExtent();return"start"===e?i=n[0]:"end"===e?i=n[1]:n[0]>0?i=n[0]:n[1]<0&&(i=n[1]),i}function Gc(t,e,i,n){var r=0/0;t.stacked&&(r=i.get(i.getCalculationInfo("stackedOverDimension"),n)),isNaN(r)&&(r=t.valueStart);var a=t.baseDataOffset,o=[];return o[a]=i.get(t.baseDim,n),o[1-a]=r,e.dataToPoint(o)}function Uc(t,e){var i=[];return e.diff(t).add(function(t){i.push({cmd:"+",idx:t})}).update(function(t,e){i.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){i.push({cmd:"-",idx:t})}).execute(),i}function Xc(t){return isNaN(t[0])||isNaN(t[1])}function jc(t,e,i,n,r,a,o,s,l,h){return"none"!==h&&h?Yc.apply(this,arguments):qc.apply(this,arguments)}function Yc(t,e,i,n,r,a,o,s,l,h,u){for(var c=0,d=i,f=0;n>f;f++){var p=e[d];if(d>=r||0>d)break;if(Xc(p)){if(u){d+=a;continue}break}if(d===i)t[a>0?"moveTo":"lineTo"](p[0],p[1]);else if(l>0){var g=e[c],v="y"===h?1:0,m=(p[v]-g[v])*l;xb(bb,g),bb[v]=g[v]+m,xb(Sb,p),Sb[v]=p[v]-m,t.bezierCurveTo(bb[0],bb[1],Sb[0],Sb[1],p[0],p[1])}else t.lineTo(p[0],p[1]);c=d,d+=a}return f}function qc(t,e,i,n,r,a,o,s,l,h,u){for(var c=0,d=i,f=0;n>f;f++){var p=e[d];if(d>=r||0>d)break;if(Xc(p)){if(u){d+=a;continue}break}if(d===i)t[a>0?"moveTo":"lineTo"](p[0],p[1]),xb(bb,p);else if(l>0){var g=d+a,v=e[g];if(u)for(;v&&Xc(e[g]);)g+=a,v=e[g];var m=.5,y=e[c],v=e[g];if(!v||Xc(v))xb(Sb,p);else{Xc(v)&&!u&&(v=p),Y(wb,v,y);var _,x;if("x"===h||"y"===h){var w="x"===h?0:1;_=Math.abs(p[w]-y[w]),x=Math.abs(p[w]-v[w])}else _=up(p,y),x=up(p,v);m=x/(x+_),_b(Sb,p,wb,-l*(1-m))}mb(bb,bb,s),yb(bb,bb,o),mb(Sb,Sb,s),yb(Sb,Sb,o),t.bezierCurveTo(bb[0],bb[1],Sb[0],Sb[1],p[0],p[1]),_b(bb,p,wb,l*m)}else t.lineTo(p[0],p[1]);c=d,d+=a}return f}function Zc(t,e){var i=[1/0,1/0],n=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var a=t[r];a[0]<i[0]&&(i[0]=a[0]),a[1]<i[1]&&(i[1]=a[1]),a[0]>n[0]&&(n[0]=a[0]),a[1]>n[1]&&(n[1]=a[1])}return{min:e?i:n,max:e?n:i}}function $c(t,e){if(t.length===e.length){for(var i=0;i<t.length;i++){var n=t[i],r=e[i];if(n[0]!==r[0]||n[1]!==r[1])return}return!0}}function Kc(t){return"number"==typeof t?t:t?.5:0}function Qc(t){var e=t.getGlobalExtent();if(t.onBand){var i=t.getBandWidth()/2-1,n=e[1]>e[0]?1:-1;e[0]+=n*i,e[1]-=n*i}return e}function Jc(t,e,i){if(!i.valueDim)return[];for(var n=[],r=0,a=e.count();a>r;r++)n.push(Gc(i,t,e,r));return n}function td(t,e,i,n){var r=Qc(t.getAxis("x")),a=Qc(t.getAxis("y")),o=t.getBaseAxis().isHorizontal(),s=Math.min(r[0],r[1]),l=Math.min(a[0],a[1]),h=Math.max(r[0],r[1])-s,u=Math.max(a[0],a[1])-l;if(i)s-=.5,h+=.5,l-=.5,u+=.5;else{var c=n.get("lineStyle.width")||2,d=n.get("clipOverflow")?c/2:Math.max(h,u);o?(l-=d,u+=2*d):(s-=d,h+=2*d)}var f=new Tm({shape:{x:s,y:l,width:h,height:u}});return e&&(f.shape[o?"width":"height"]=0,Ea(f,{shape:{width:h,height:u}},n)),f}function ed(t,e,i,n){var r=t.getAngleAxis(),a=t.getRadiusAxis(),o=a.getExtent().slice();o[0]>o[1]&&o.reverse();var s=r.getExtent(),l=Math.PI/180;i&&(o[0]-=.5,o[1]+=.5);var h=new ym({shape:{cx:Ka(t.cx,1),cy:Ka(t.cy,1),r0:Ka(o[0],1),r:Ka(o[1],1),startAngle:-s[0]*l,endAngle:-s[1]*l,clockwise:r.inverse}});return e&&(h.shape.endAngle=-s[0]*l,Ea(h,{shape:{endAngle:-s[1]*l}},n)),h}function id(t,e,i,n){return"polar"===t.type?ed(t,e,i,n):td(t,e,i,n)}function nd(t,e,i){for(var n=e.getBaseAxis(),r="x"===n.dim||"radius"===n.dim?0:1,a=[],o=0;o<t.length-1;o++){var s=t[o+1],l=t[o];a.push(l);var h=[];switch(i){case"end":h[r]=s[r],h[1-r]=l[1-r],a.push(h);break;case"middle":var u=(l[r]+s[r])/2,c=[];h[r]=c[r]=u,h[1-r]=l[1-r],c[1-r]=s[1-r],a.push(h),a.push(c);break;default:h[r]=l[r],h[1-r]=s[1-r],a.push(h)}}return t[o]&&a.push(t[o]),a}function rd(t,e){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var n,r,a=i.length-1;a>=0;a--){var o=i[a].dimension,s=t.dimensions[o],l=t.getDimensionInfo(s);if(n=l&&l.coordDim,"x"===n||"y"===n){r=i[a];break}}if(r){var h=e.getAxis(n),u=p(r.stops,function(t){return{coord:h.toGlobalCoord(h.dataToCoord(t.value)),color:t.color}}),c=u.length,d=r.outerColors.slice();c&&u[0].coord>u[c-1].coord&&(u.reverse(),d.reverse());var g=10,v=u[0].coord-g,m=u[c-1].coord+g,y=m-v;if(.001>y)return"transparent";f(u,function(t){t.offset=(t.coord-v)/y}),u.push({offset:c?u[c-1].offset:.5,color:d[1]||"transparent"}),u.unshift({offset:c?u[0].offset:.5,color:d[0]||"transparent"});var _=new Em(0,0,0,0,u,!0);return _[n]=v,_[n+"2"]=m,_}}}function ad(t,e,i){var n=t.get("showAllSymbol"),r="auto"===n;if(!n||r){var a=i.getAxesByScale("ordinal")[0];if(a&&(!r||!od(a,e))){var o=e.mapDimension(a.dim),s={};return f(a.getViewLabels(),function(t){s[t.tickValue]=1}),function(t){return!s.hasOwnProperty(e.get(o,t))}}}}function od(t,e){var i=t.getExtent(),n=Math.abs(i[1]-i[0])/t.scale.count();isNaN(n)&&(n=0);for(var r=e.count(),a=Math.max(1,Math.round(r/5)),o=0;r>o;o+=a)if(1.5*kc.getSymbolSize(e,o)[t.isHorizontal()?1:0]>n)return!1;return!0}function sd(t,e,i,n){var r=e.getData(),a=this.dataIndex,o=r.getName(a),s=e.get("selectedOffset");n.dispatchAction({type:"pieToggleSelect",from:t,name:o,seriesId:e.id}),r.each(function(t){ld(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,i)})}function ld(t,e,i,n,r){var a=(e.startAngle+e.endAngle)/2,o=Math.cos(a),s=Math.sin(a),l=i?n:0,h=[o*l,s*l];r?t.animate().when(200,{position:h}).start("bounceOut"):t.attr("position",h)}function hd(t,e){function i(){a.ignore=a.hoverIgnore,o.ignore=o.hoverIgnore}function n(){a.ignore=a.normalIgnore,o.ignore=o.normalIgnore}ig.call(this);var r=new ym({z2:2}),a=new Sm,o=new pm;this.add(r),this.add(a),this.add(o),this.updateData(t,e,!0),this.on("emphasis",i).on("normal",n).on("mouseover",i).on("mouseout",n)}function ud(t,e,i,n,r,a,o){function s(e,i,n){for(var r=e;i>r;r++)if(t[r].y+=n,r>e&&i>r+1&&t[r+1].y>t[r].y+t[r].height)return void l(r,n/2);l(i-1,n/2)}function l(e,i){for(var n=e;n>=0&&(t[n].y-=i,!(n>0&&t[n].y>t[n-1].y+t[n-1].height));n--);}function h(t,e,i,n,r,a){for(var o=a>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t.length;l>s;s++){var h=Math.abs(t[s].y-n),u=t[s].len,c=t[s].len2,d=r+u>h?Math.sqrt((r+u+c)*(r+u+c)-h*h):Math.abs(t[s].x-i);
e&&d>=o&&(d=o-10),!e&&o>=d&&(d=o+10),t[s].x=i+d*a,o=d}}t.sort(function(t,e){return t.y-e.y});for(var u,c=0,d=t.length,f=[],p=[],g=0;d>g;g++)u=t[g].y-c,0>u&&s(g,d,-u,r),c=t[g].y+t[g].height;0>o-c&&l(d-1,c-o);for(var g=0;d>g;g++)t[g].y>=i?p.push(t[g]):f.push(t[g]);h(f,!1,e,i,n,r),h(p,!0,e,i,n,r)}function cd(t,e,i,n,r,a){for(var o=[],s=[],l=0;l<t.length;l++)dd(t[l])||(t[l].x<e?o.push(t[l]):s.push(t[l]));ud(s,e,i,n,1,r,a),ud(o,e,i,n,-1,r,a);for(var l=0;l<t.length;l++)if(!dd(t[l])){var h=t[l].linePoints;if(h){var u=h[1][0]-h[2][0];h[2][0]=t[l].x<e?t[l].x+3:t[l].x-3,h[1][1]=h[2][1]=t[l].y,h[1][0]=h[2][0]+u}}}function dd(t){return"center"===t.position}function fd(t,e,i){var n,r={},a="toggleSelected"===t;return i.eachComponent("legend",function(i){a&&null!=n?i[n?"select":"unSelect"](e.name):(i[t](e.name),n=i.isSelected(e.name));var o=i.getData();f(o,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var n=i.isSelected(e);r[e]=r.hasOwnProperty(e)?r[e]&&n:n}})}),{name:e.name,selected:r}}function pd(t,e){var i=ny(e.get("padding")),n=e.getItemStyle(["color","opacity"]);n.fill=e.get("backgroundColor");var t=new Tm({shape:{x:t.x-i[3],y:t.y-i[0],width:t.width+i[1]+i[3],height:t.height+i[0]+i[2],r:e.get("borderRadius")},style:n,silent:!0,z2:-1});return t}function gd(t,e){e.dispatchAction({type:"legendToggleSelect",name:t})}function vd(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:n})}function md(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:n})}function yd(t,e,i){var n=t.getOrient(),r=[1,1];r[n.index]=0,To(e,i,{type:"box",ignoreSize:r})}function _d(t,e,i,n,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e)){if(!t.involveSeries)return void i.showPointer(t,e);var s=xd(e,t),l=s.payloadBatch,h=s.snapToValue;l[0]&&null==r.seriesIndex&&o(r,l[0]),!n&&t.snap&&a.containData(h)&&null!=h&&(e=h),i.showPointer(t,e,l,r),i.showTooltip(t,s,h)}}function xd(t,e){var i=e.axis,n=i.dim,r=t,a=[],o=Number.MAX_VALUE,s=-1;return tS(e.seriesModels,function(e){var l,h,u=e.getData().mapDimension(n,!0);if(e.getAxisTooltipData){var c=e.getAxisTooltipData(u,t,i);h=c.dataIndices,l=c.nestestValue}else{if(h=e.getData().indicesOfNearest(u[0],t,"category"===i.type?.5:null),!h.length)return;l=e.getData().get(u[0],h[0])}if(null!=l&&isFinite(l)){var d=t-l,f=Math.abs(d);o>=f&&((o>f||d>=0&&0>s)&&(o=f,s=d,r=l,a.length=0),tS(h,function(t){a.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:a,snapToValue:r}}function wd(t,e,i,n){t[e.key]={value:i,payloadBatch:n}}function bd(t,e,i,n){var r=i.payloadBatch,a=e.axis,o=a.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,h=Tc(l),u=t.map[h];u||(u=t.map[h]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(u)),u.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:n,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Sd(t,e,i){var n=i.axesInfo=[];tS(e,function(e,i){var r=e.axisPointerModel.option,a=t[i];a?(!e.useHandle&&(r.status="show"),r.value=a.value,r.seriesDataIndices=(a.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&n.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function Md(t,e,i,n){if(Dd(e)||!t.list.length)return void n({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:i.tooltipOption,position:i.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function Id(t,e,i){var n=i.getZr(),r="axisPointerLastHighlights",a=iS(n)[r]||{},o=iS(n)[r]={};tS(t,function(t){var e=t.axisPointerModel.option;"show"===e.status&&tS(e.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;o[e]=t})});var s=[],l=[];f(a,function(t,e){!o[e]&&l.push(t)}),f(o,function(t,e){!a[e]&&s.push(t)}),l.length&&i.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&i.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function Td(t,e){for(var i=0;i<(t||[]).length;i++){var n=t[i];if(e.axis.dim===n.axisDim&&e.axis.model.componentIndex===n.axisIndex)return n}}function Cd(t){var e=t.axis.model,i={},n=i.axisDim=t.axis.dim;return i.axisIndex=i[n+"AxisIndex"]=e.componentIndex,i.axisName=i[n+"AxisName"]=e.name,i.axisId=i[n+"AxisId"]=e.id,i}function Dd(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function Ad(t,e,i){if(!jf.node){var n=e.getZr();rS(n).records||(rS(n).records={}),kd(n,e);var r=rS(n).records[t]||(rS(n).records[t]={});r.handler=i}}function kd(t,e){function i(i,n){t.on(i,function(i){var r=Ed(e);aS(rS(t).records,function(t){t&&n(t,i,r.dispatchAction)}),Pd(r.pendings,e)})}rS(t).initialized||(rS(t).initialized=!0,i("click",_(Od,"click")),i("mousemove",_(Od,"mousemove")),i("globalout",Ld))}function Pd(t,e){var i,n=t.showTip.length,r=t.hideTip.length;n?i=t.showTip[n-1]:r&&(i=t.hideTip[r-1]),i&&(i.dispatchAction=null,e.dispatchAction(i))}function Ld(t,e,i){t.handler("leave",null,i)}function Od(t,e,i,n){e.handler(t,i,n)}function Ed(t){var e={showTip:[],hideTip:[]},i=function(n){var r=e[n.type];r?r.push(n):(n.dispatchAction=i,t.dispatchAction(n))};return{dispatchAction:i,pendings:e}}function Bd(t,e){if(!jf.node){var i=e.getZr(),n=(rS(i).records||{})[t];n&&(rS(i).records[t]=null)}}function zd(){}function Rd(t,e,i,n){Nd(sS(i).lastProp,n)||(sS(i).lastProp=n,e?Oa(i,n,t):(i.stopAnimation(),i.attr(n)))}function Nd(t,e){if(S(t)&&S(e)){var i=!0;return f(e,function(e,n){i=i&&Nd(t[n],e)}),!!i}return t===e}function Fd(t,e){t[e.get("label.show")?"show":"hide"]()}function Vd(t){return{position:t.position.slice(),rotation:t.rotation||0}}function Hd(t,e,i){var n=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=r&&(t.zlevel=r),t.silent=i)})}function Wd(t){var e,i=t.get("type"),n=t.getModel(i+"Style");return"line"===i?(e=n.getLineStyle(),e.fill=null):"shadow"===i&&(e=n.getAreaStyle(),e.stroke=null),e}function Gd(t,e,i,n,r){var a=i.get("value"),o=Xd(a,e.axis,e.ecModel,i.get("seriesDataIndices"),{precision:i.get("label.precision"),formatter:i.get("label.formatter")}),s=i.getModel("label"),l=ny(s.get("padding")||0),h=s.getFont(),u=Ri(o,h),c=r.position,d=u.width+l[1]+l[3],f=u.height+l[0]+l[2],p=r.align;"right"===p&&(c[0]-=d),"center"===p&&(c[0]-=d/2);var g=r.verticalAlign;"bottom"===g&&(c[1]-=f),"middle"===g&&(c[1]-=f/2),Ud(c,d,f,n);var v=s.get("backgroundColor");v&&"auto"!==v||(v=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:d,height:f,r:s.get("borderRadius")},position:c.slice(),style:{text:o,textFont:h,textFill:s.getTextColor(),textPosition:"inside",fill:v,stroke:s.get("borderColor")||"transparent",lineWidth:s.get("borderWidth")||0,shadowBlur:s.get("shadowBlur"),shadowColor:s.get("shadowColor"),shadowOffsetX:s.get("shadowOffsetX"),shadowOffsetY:s.get("shadowOffsetY")},z2:10}}function Ud(t,e,i,n){var r=n.getWidth(),a=n.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+i,a)-i,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function Xd(t,e,i,n,r){t=e.scale.parse(t);var a=e.scale.getLabel(t,{precision:r.precision}),o=r.formatter;if(o){var s={value:cu(e,t),seriesData:[]};f(n,function(t){var e=i.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,r=e&&e.getDataParams(n);r&&s.seriesData.push(r)}),b(o)?a=o.replace("{value}",a):w(o)&&(a=o(s))}return a}function jd(t,e,i){var n=Me();return Ae(n,n,i.rotation),De(n,n,i.position),za([t.dataToCoord(e),(i.labelOffset||0)+(i.labelDirection||1)*(i.labelMargin||0)],n)}function Yd(t,e,i,n,r,a){var o=Jw.innerTextLayout(i.rotation,0,i.labelDirection);i.labelMargin=r.get("label.margin"),Gd(e,n,r,a,{position:jd(n.axis,t,i),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function qd(t,e,i){return i=i||0,{x1:t[i],y1:t[1-i],x2:e[i],y2:e[1-i]}}function Zd(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}function $d(t,e){var i={};return i[e.dim+"AxisIndex"]=e.index,t.getCartesian(i)}function Kd(t){return"x"===t.dim?0:1}function Qd(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return p(pS,function(t){return t+"transition:"+i}).join(";")}function Jd(t){var e=[],i=t.get("fontSize"),n=t.getTextColor();return n&&e.push("color:"+n),e.push("font:"+t.getFont()),i&&e.push("line-height:"+Math.round(3*i/2)+"px"),dS(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function tf(t){var e=[],i=t.get("transitionDuration"),n=t.get("backgroundColor"),r=t.getModel("textStyle"),a=t.get("padding");return i&&e.push(Qd(i)),n&&(jf.canvasSupported?e.push("background-Color:"+n):(e.push("background-Color:#"+Ze(n)),e.push("filter:alpha(opacity=70)"))),dS(["width","color","radius"],function(i){var n="border-"+i,r=fS(n),a=t.get(r);null!=a&&e.push(n+":"+a+("color"===i?"":"px"))}),e.push(Jd(r)),null!=a&&e.push("padding:"+ny(a).join("px ")+"px"),e.join(";")+";"}function ef(t,e){if(jf.wxa)return null;var i=document.createElement("div"),n=this._zr=e.getZr();this.el=i,this._x=e.getWidth()/2,this._y=e.getHeight()/2,t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var r=this;i.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(e){if(e=e||window.event,!r._enterable){var i=n.handler;ge(t,e,!0),i.dispatch("mousemove",e)}},i.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}function nf(t){this._zr=t.getZr(),this._show=!1,this._hideTimeout}function rf(t){for(var e=t.pop();t.length;){var i=t.pop();i&&(Wa.isInstance(i)&&(i=i.get("tooltip",!0)),"string"==typeof i&&(i={formatter:i}),e=new Wa(i,e,e.ecModel))}return e}function af(t,e){return t.dispatchAction||y(e.dispatchAction,e)}function of(t,e,i,n,r,a,o){var s=i.getOuterSize(),l=s.width,h=s.height;return null!=a&&(t+l+a>n?t-=l+a:t+=a),null!=o&&(e+h+o>r?e-=h+o:e+=o),[t,e]}function sf(t,e,i,n,r){var a=i.getOuterSize(),o=a.width,s=a.height;return t=Math.min(t+o,n)-o,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function lf(t,e,i){var n=i[0],r=i[1],a=5,o=0,s=0,l=e.width,h=e.height;switch(t){case"inside":o=e.x+l/2-n/2,s=e.y+h/2-r/2;break;case"top":o=e.x+l/2-n/2,s=e.y-r-a;break;case"bottom":o=e.x+l/2-n/2,s=e.y+h+a;break;case"left":o=e.x-n-a,s=e.y+h/2-r/2;break;case"right":o=e.x+l+a,s=e.y+h/2-r/2}return[o,s]}function hf(t){return"center"===t||"middle"===t}function uf(t){return xS(t)}function cf(){if(!SS&&MS){SS=!0;var t=MS.styleSheets;t.length<31?MS.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}function df(t){return parseInt(t,10)}function ff(t,e){cf(),this.root=t,this.storage=e;var i=document.createElement("div"),n=document.createElement("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",n.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=n,this._vmlViewport=i,this.resize();var r=e.delFromStorage,a=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(n)},e.addToStorage=function(t){t.onAdd&&t.onAdd(n),a.call(e,t)},this._firstPaint=!0}function pf(t){return function(){$p('In IE8.0 VML mode painter not support method "'+t+'"')}}function gf(t){return document.createElementNS(pM,t)}function vf(t){return yM(1e4*t)/1e4}function mf(t){return MM>t&&t>-MM}function yf(t,e){var i=e?t.textFill:t.fill;return null!=i&&i!==mM}function _f(t,e){var i=e?t.textStroke:t.stroke;return null!=i&&i!==mM}function xf(t,e){e&&wf(t,"transform","matrix("+vM.call(e,",")+")")}function wf(t,e,i){(!i||"linear"!==i.type&&"radial"!==i.type)&&t.setAttribute(e,i)}function bf(t,e,i){t.setAttributeNS("http://www.w3.org/1999/xlink",e,i)}function Sf(t,e,i,n){if(yf(e,i)){var r=i?e.textFill:e.fill;r="transparent"===r?mM:r,"none"!==t.getAttribute("clip-path")&&r===mM&&(r="rgba(0, 0, 0, 0.002)"),wf(t,"fill",r),wf(t,"fill-opacity",null!=e.fillOpacity?e.fillOpacity*e.opacity:e.opacity)}else wf(t,"fill",mM);if(_f(e,i)){var a=i?e.textStroke:e.stroke;a="transparent"===a?mM:a,wf(t,"stroke",a);var o=i?e.textStrokeWidth:e.lineWidth,s=!i&&e.strokeNoScale?n.getLineScale():1;wf(t,"stroke-width",o/s),wf(t,"paint-order",i?"stroke":"fill"),wf(t,"stroke-opacity",null!=e.strokeOpacity?e.strokeOpacity:e.opacity);var l=e.lineDash;l?(wf(t,"stroke-dasharray",e.lineDash.join(",")),wf(t,"stroke-dashoffset",yM(e.lineDashOffset||0))):wf(t,"stroke-dasharray",""),e.lineCap&&wf(t,"stroke-linecap",e.lineCap),e.lineJoin&&wf(t,"stroke-linejoin",e.lineJoin),e.miterLimit&&wf(t,"stroke-miterlimit",e.miterLimit)}else wf(t,"stroke",mM)}function Mf(t){for(var e=[],i=t.data,n=t.len(),r=0;n>r;){var a=i[r++],o="",s=0;switch(a){case gM.M:o="M",s=2;break;case gM.L:o="L",s=2;break;case gM.Q:o="Q",s=4;break;case gM.C:o="C",s=6;break;case gM.A:var l=i[r++],h=i[r++],u=i[r++],c=i[r++],d=i[r++],f=i[r++],p=i[r++],g=i[r++],v=Math.abs(f),m=mf(v-bM)&&!mf(v),y=!1;y=v>=bM?!0:mf(v)?!1:(f>-wM&&0>f||f>wM)==!!g;var _=vf(l+u*xM(d)),x=vf(h+c*_M(d));m&&(f=g?bM-1e-4:-bM+1e-4,y=!0,9===r&&e.push("M",_,x));var w=vf(l+u*xM(d+f)),b=vf(h+c*_M(d+f));e.push("A",vf(u),vf(c),yM(p*SM),+y,+g,w,b);break;case gM.Z:o="Z";break;case gM.R:var w=vf(i[r++]),b=vf(i[r++]),S=vf(i[r++]),M=vf(i[r++]);e.push("M",w,b,"L",w+S,b,"L",w+S,b+M,"L",w,b+M,"L",w,b)}o&&e.push(o);for(var I=0;s>I;I++)e.push(vf(i[r++]))}return e.join(" ")}function If(t){return"middle"===t?"middle":"bottom"===t?"after-edge":"hanging"}function Tf(){}function Cf(t,e){for(var i=0,n=e.length,r=0,a=0;n>i;i++){var o=e[i];if(o.removed){for(var s=[],l=a;l<a+o.count;l++)s.push(l);o.indices=s,a+=o.count}else{for(var s=[],l=r;l<r+o.count;l++)s.push(l);o.indices=s,r+=o.count,o.added||(a+=o.count)}}return e}function Df(t){return{newPos:t.newPos,components:t.components.slice(0)}}function Af(t,e,i,n,r){this._zrId=t,this._svgRoot=e,this._tagNames="string"==typeof i?[i]:i,this._markLabel=n,this._domName=r||"_dom",this.nextId=0}function kf(t,e){Af.call(this,t,e,["linearGradient","radialGradient"],"__gradient_in_use__")}function Pf(t,e){Af.call(this,t,e,"clipPath","__clippath_in_use__")}function Lf(t,e){Af.call(this,t,e,["filter"],"__filter_in_use__","_shadowDom")}function Of(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY||t.textShadowBlur||t.textShadowOffsetX||t.textShadowOffsetY)}function Ef(t){return parseInt(t,10)}function Bf(t){return t instanceof Nr?IM:t instanceof xn?TM:t instanceof pm?CM:IM}function zf(t,e){return e&&t&&e.parentNode!==t}function Rf(t,e,i){if(zf(t,e)&&i){var n=i.nextSibling;n?t.insertBefore(e,n):t.appendChild(e)}}function Nf(t,e){if(zf(t,e)){var i=t.firstChild;i?t.insertBefore(e,i):t.appendChild(e)}}function Ff(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)}function Vf(t){return t.__textSvgEl}function Hf(t){return t.__svgEl}function Wf(t){return function(){$p('In SVG mode painter not support method "'+t+'"')}}var Gf=2311,Uf=function(){return Gf++},Xf={};Xf="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:e(navigator.userAgent);var jf=Xf,Yf={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},qf={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},Zf=Object.prototype.toString,$f=Array.prototype,Kf=$f.forEach,Qf=$f.filter,Jf=$f.slice,tp=$f.map,ep=$f.reduce,ip={},np=function(){return ip.createCanvas()};ip.createCanvas=function(){return document.createElement("canvas")};var rp,ap="__ec_primitive__";R.prototype={constructor:R,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){void 0!==e&&(t=y(t,e));for(var i in this.data)this.data.hasOwnProperty(i)&&t(this.data[i],i)},removeKey:function(t){delete this.data[t]}};var op=(Object.freeze||Object)({$override:i,clone:n,merge:r,mergeAll:a,extend:o,defaults:s,createCanvas:np,getContext:l,indexOf:h,inherits:u,mixin:c,isArrayLike:d,each:f,map:p,reduce:g,filter:v,find:m,bind:y,curry:_,isArray:x,isFunction:w,isString:b,isObject:S,isBuiltInObject:M,isTypedArray:I,isDom:T,eqNaN:C,retrieve:D,retrieve2:A,retrieve3:k,slice:P,normalizeCssArray:L,assert:O,trim:E,setAsPrimitive:B,isPrimitive:z,createHashMap:N,concatArray:F,noop:V}),sp="undefined"==typeof Float32Array?Array:Float32Array,lp=q,hp=Z,up=ee,cp=ie,dp=(Object.freeze||Object)({create:H,copy:W,clone:G,set:U,add:X,scaleAndAdd:j,sub:Y,len:q,length:lp,lenSquare:Z,lengthSquare:hp,mul:$,div:K,dot:Q,scale:J,normalize:te,distance:ee,dist:up,distanceSquare:ie,distSquare:cp,negate:ne,lerp:re,applyTransform:ae,min:oe,max:se});le.prototype={constructor:le,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(he(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,a=n-this._y;this._x=i,this._y=n,e.drift(r,a,t),this.dispatchToElement(he(e,t),"drag",t.event);var o=this.findHover(i,n,e).target,s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(he(s,t),"dragleave",t.event),o&&o!==s&&this.dispatchToElement(he(o,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(he(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(he(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var fp=Array.prototype.slice,pp=function(t){this._$handlers={},this._$eventProcessor=t};pp.prototype={constructor:pp,one:function(t,e,i,n){return ce(this,t,e,i,n,!0)},on:function(t,e,i,n){return ce(this,t,e,i,n,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,a=i[t].length;a>r;r++)i[t][r].h!==e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t].length&&delete i[t]}else delete i[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>3&&(n=fp.call(n,1));for(var a=e.length,o=0;a>o;){var s=e[o];if(i&&i.filter&&null!=s.query&&!i.filter(t,s.query))o++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,n[1]);break;case 3:s.h.call(s.ctx,n[1],n[2]);break;default:s.h.apply(s.ctx,n)}s.one?(e.splice(o,1),a--):o++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>4&&(n=fp.call(n,1,n.length-1));for(var a=n[n.length-1],o=e.length,s=0;o>s;){var l=e[s];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(a);break;case 2:l.h.call(a,n[1]);break;case 3:l.h.call(a,n[1],n[2]);break;default:l.h.apply(a,n)}l.one?(e.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var gp="undefined"!=typeof window&&!!window.addEventListener,vp=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,mp=gp?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},yp=function(){this._track=[]};yp.prototype={constructor:yp,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,i){var n=t.touches;if(n){for(var r={points:[],touches:[],target:e,event:t},a=0,o=n.length;o>a;a++){var s=n[a],l=fe(i,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in _p)if(_p.hasOwnProperty(e)){var i=_p[e](this._track,t);if(i)return i}}};var _p={pinch:function(t,e){var i=t.length;if(i){var n=(t[i-1]||{}).points,r=(t[i-2]||{}).points||n;if(r&&r.length>1&&n&&n.length>1){var a=ye(n)/ye(r);!isFinite(a)&&(a=1),e.pinchScale=a;var o=_e(n);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},xp="silent";be.prototype.dispose=function(){};var wp=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],bp=function(t,e,i,n){pp.call(this),this.storage=t,this.painter=e,this.painterRoot=n,i=i||new be,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,le.call(this),this.setHandlerProxy(i)};bp.prototype={constructor:bp,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(f(wp,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,i=t.zrY,n=this._hovered,r=n.target;r&&!r.__zr&&(n=this.findHover(n.x,n.y),r=n.target);var a=this._hovered=this.findHover(e,i),o=a.target,s=this.proxy;s.setCursor&&s.setCursor(o?o.cursor:"default"),r&&o!==r&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(a,"mousemove",t),o&&o!==r&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,i=t.toElement||t.relatedTarget;do i=i&&i.parentNode;while(i&&9!==i.nodeType&&!(e=i===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(){this._hovered={}},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,i){t=t||{};var n=t.target;if(!n||!n.silent){for(var r="on"+e,a=xe(e,t,i);n&&(n[r]&&(a.cancelBubble=n[r].call(n,a)),n.trigger(e,a),n=n.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,i){for(var n=this.storage.getDisplayList(),r={x:t,y:e},a=n.length-1;a>=0;a--){var o;if(n[a]!==i&&!n[a].ignore&&(o=Se(n[a],t,e))&&(!r.topTarget&&(r.topTarget=n[a]),o!==xp)){r.target=n[a];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new yp);var i=this._gestureMgr;"start"===e&&i.clear();var n=i.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&i.clear(),n){var r=n.type;t.gestureEvent=r,this.dispatchToElement({target:n.target},r,n.event)}}},f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){bp.prototype[t]=function(e){var i=this.findHover(e.zrX,e.zrY),n=i.target;if("mousedown"===t)this._downEl=n,this._downPoint=[e.zrX,e.zrY],this._upEl=n;else if("mouseup"===t)this._upEl=n;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||up(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(i,t,e)}}),c(bp,pp),c(bp,le);var Sp="undefined"==typeof Float32Array?Array:Float32Array,Mp=(Object.freeze||Object)({create:Me,identity:Ie,copy:Te,mul:Ce,translate:De,rotate:Ae,scale:ke,invert:Pe,clone:Le}),Ip=Ie,Tp=5e-5,Cp=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},Dp=Cp.prototype;Dp.transform=null,Dp.needLocalTransform=function(){return Oe(this.rotation)||Oe(this.position[0])||Oe(this.position[1])||Oe(this.scale[0]-1)||Oe(this.scale[1]-1)};var Ap=[];Dp.updateTransform=function(){var t=this.parent,e=t&&t.transform,i=this.needLocalTransform(),n=this.transform;if(!i&&!e)return void(n&&Ip(n));n=n||Me(),i?this.getLocalTransform(n):Ip(n),e&&(i?Ce(n,t.transform,n):Te(n,t.transform)),this.transform=n;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(Ap);var a=Ap[0]<0?-1:1,o=Ap[1]<0?-1:1,s=((Ap[0]-a)*r+a)/Ap[0]||0,l=((Ap[1]-o)*r+o)/Ap[1]||0;n[0]*=s,n[1]*=s,n[2]*=l,n[3]*=l}this.invTransform=this.invTransform||Me(),Pe(this.invTransform,n)},Dp.getLocalTransform=function(t){return Cp.getLocalTransform(this,t)},Dp.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},Dp.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var kp=[],Pp=Me();Dp.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=this.position,r=this.scale;Oe(e-1)&&(e=Math.sqrt(e)),Oe(i-1)&&(i=Math.sqrt(i)),t[0]<0&&(e=-e),t[3]<0&&(i=-i),n[0]=t[4],n[1]=t[5],r[0]=e,r[1]=i,this.rotation=Math.atan2(-t[1]/i,t[0]/e)}},Dp.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(Ce(kp,t.invTransform,e),e=kp);var i=this.origin;i&&(i[0]||i[1])&&(Pp[4]=i[0],Pp[5]=i[1],Ce(kp,e,Pp),kp[4]-=i[0],kp[5]-=i[1],e=kp),this.setLocalTransform(e)}},Dp.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},Dp.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&ae(i,i,n),i},Dp.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&ae(i,i,n),i},Cp.getLocalTransform=function(t,e){e=e||[],Ip(e);var i=t.origin,n=t.scale||[1,1],r=t.rotation||0,a=t.position||[0,0];return i&&(e[4]-=i[0],e[5]-=i[1]),ke(e,e,n),r&&Ae(e,e,r),i&&(e[4]+=i[0],e[5]+=i[1]),e[4]+=a[0],e[5]+=a[1],e};var Lp={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-Lp.bounceOut(1-t)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return.5>t?.5*Lp.bounceIn(2*t):.5*Lp.bounceOut(2*t-1)+.5}};Ee.prototype={constructor:Ee,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var i=(t-this._startTime-this._pausedTime)/this._life;if(!(0>i)){i=Math.min(i,1);var n=this.easing,r="string"==typeof n?Lp[n]:n,a="function"==typeof r?r(i):i;return this.fire("frame",a),1===i?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var Op=function(){this.head=null,this.tail=null,this._len=0},Ep=Op.prototype;Ep.insert=function(t){var e=new Bp(t);return this.insertEntry(e),e},Ep.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Ep.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Ep.len=function(){return this._len},Ep.clear=function(){this.head=this.tail=null,this._len=0};var Bp=function(t){this.value=t,this.next,this.prev},zp=function(t){this._list=new Op,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},Rp=zp.prototype;Rp.put=function(t,e){var i=this._list,n=this._map,r=null;if(null==n[t]){var a=i.len(),o=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var s=i.head;i.remove(s),delete n[s.key],r=s.value,this._lastRemovedEntry=s}o?o.value=e:o=new Bp(e),o.key=t,i.insertEntry(o),n[t]=o}return r},Rp.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value):void 0},Rp.clear=function(){this._list.clear(),this._map={}};var Np={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},Fp=new zp(20),Vp=null,Hp=$e,Wp=Ke,Gp=(Object.freeze||Object)({parse:Xe,lift:qe,toHex:Ze,fastLerp:$e,fastMapToColor:Hp,lerp:Ke,mapToColor:Wp,modifyHSL:Qe,modifyAlpha:Je,stringify:ti}),Up=Array.prototype.slice,Xp=function(t,e,i,n){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||ei,this._setter=n||ii,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]
};Xp.prototype={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:ui(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,i=0;e>i;i++)t[i].call(this)},start:function(t,e){var i,n=this,r=0,a=function(){r--,r||n._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=fi(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),i=s)}if(i){var l=i.onframe;i.onframe=function(t,e){l(t,e);for(var i=0;i<n._onframeList.length;i++)n._onframeList[i](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this.animation,n=0;n<e.length;n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var jp=1;"undefined"!=typeof window&&(jp=Math.max(window.devicePixelRatio||1,1));var Yp=0,qp=jp,Zp=function(){};1===Yp?Zp=function(){for(var t in arguments)throw new Error(arguments[t])}:Yp>1&&(Zp=function(){for(var t in arguments)console.log(arguments[t])});var $p=Zp,Kp=function(){this.animators=[]};Kp.prototype={constructor:Kp,animate:function(t,e){var i,n=!1,r=this,a=this.__zr;if(t){var o=t.split("."),s=r;n="shape"===o[0];for(var l=0,u=o.length;u>l;l++)s&&(s=s[o[l]]);s&&(i=s)}else i=r;if(!i)return void $p('Property "'+t+'" is not existed in element '+r.id);var c=r.animators,d=new Xp(i,e);return d.during(function(){r.dirty(n)}).done(function(){c.splice(h(c,d),1)}),c.push(d),a&&a.animation.addAnimator(d),d},stopAnimation:function(t){for(var e=this.animators,i=e.length,n=0;i>n;n++)e[n].stop(t);return e.length=0,this},animateTo:function(t,e,i,n,r,a){pi(this,t,e,i,n,r,a)},animateFrom:function(t,e,i,n,r,a){pi(this,t,e,i,n,r,a,!0)}};var Qp=function(t){Cp.call(this,t),pp.call(this,t),Kp.call(this,t),this.id=t.id||Uf()};Qp.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(S(t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},c(Qp,Kp),c(Qp,Cp),c(Qp,pp);var Jp=ae,tg=Math.min,eg=Math.max;mi.prototype={constructor:mi,union:function(t){var e=tg(t.x,this.x),i=tg(t.y,this.y);this.width=eg(t.x+t.width,this.x+this.width)-e,this.height=eg(t.y+t.height,this.y+this.height)-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[],i=[],n=[];return function(r){if(r){t[0]=i[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=i[1]=this.y+this.height,Jp(t,t,r),Jp(e,e,r),Jp(i,i,r),Jp(n,n,r),this.x=tg(t[0],e[0],i[0],n[0]),this.y=tg(t[1],e[1],i[1],n[1]);var a=eg(t[0],e[0],i[0],n[0]),o=eg(t[1],e[1],i[1],n[1]);this.width=a-this.x,this.height=o-this.y}}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,n=t.height/e.height,r=Me();return De(r,r,[-e.x,-e.y]),ke(r,r,[i,n]),De(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof mi||(t=mi.create(t));var e=this,i=e.x,n=e.x+e.width,r=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,l=t.y,h=t.y+t.height;return!(o>n||i>s||l>a||r>h)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},clone:function(){return new mi(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},mi.create=function(t){return new mi(t.x,t.y,t.width,t.height)};var ig=function(t){t=t||{},Qp.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};ig.prototype={constructor:ig,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e.length;i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,n=i.indexOf(e);n>=0&&(i.splice(n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof ig&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var e=this.__zr,i=this.__storage,n=this._children,r=h(n,t);return 0>r?this:(n.splice(r,1),t.parent=null,i&&(i.delFromStorage(t),t instanceof ig&&t.delChildrenFromStorage(i)),e&&e.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i.length;e++)t=i[e],n&&(n.delFromStorage(t),t instanceof ig&&t.delChildrenFromStorage(n)),t.parent=null;return i.length=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i.length;n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children.length;i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.addToStorage(i),i instanceof ig&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.delFromStorage(i),i instanceof ig&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new mi(0,0,0,0),n=t||this._children,r=[],a=0;a<n.length;a++){var o=n[a];if(!o.ignore&&!o.invisible){var s=o.getBoundingRect(),l=o.getLocalTransform(r);l?(i.copy(s),i.applyTransform(l),e=e||i.clone(),e.union(i)):(e=e||s.clone(),e.union(s))}}return e||i}},u(ig,Qp);var ng=32,rg=7,ag=function(){this._roots=[],this._displayList=[],this._displayListLen=0};ag.prototype={constructor:ag,traverse:function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,r=e.length;r>n;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,jf.canvasSupported&&Ii(i,Ti)},_updateAndAddDisplayable:function(t,e,i){if(!t.ignore||i){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var n=t.clipPath;if(n){e=e?e.slice():[];for(var r=n,a=t;r;)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof ig&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var i=this._roots[e];i instanceof ig&&i.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,n=t.length;n>e;e++)this.delRoot(t[e]);else{var r=h(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof ig&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:Ti};var og={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},sg=function(t,e,i){return og.hasOwnProperty(e)?i*=t.dpr:i},lg={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},hg=9,ug=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],cg=function(t){this.extendFrom(t,!1)};cg.prototype={constructor:cg,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,i){var n=this,r=i&&i.style,a=!r||t.__attrCachedBy!==lg.STYLE_BIND;t.__attrCachedBy=lg.STYLE_BIND;for(var o=0;o<ug.length;o++){var s=ug[o],l=s[0];(a||n[l]!==r[l])&&(t[l]=sg(t,l,n[l]||s[1]))}if((a||n.fill!==r.fill)&&(t.fillStyle=n.fill),(a||n.stroke!==r.stroke)&&(t.strokeStyle=n.stroke),(a||n.opacity!==r.opacity)&&(t.globalAlpha=null==n.opacity?1:n.opacity),(a||n.blend!==r.blend)&&(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var h=n.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var i in t)!t.hasOwnProperty(i)||e!==!0&&(e===!1?this.hasOwnProperty(i):null==t[i])||(this[i]=t[i])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var n="radial"===e.type?Di:Ci,r=n(t,e,i),a=e.colorStops,o=0;o<a.length;o++)r.addColorStop(a[o].offset,a[o].color);return r}};for(var dg=cg.prototype,fg=0;fg<ug.length;fg++){var pg=ug[fg];pg[0]in dg||(dg[pg[0]]=pg[1])}cg.getGradient=dg.getGradient;var gg=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};gg.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var vg=function(t,e,i){var n;i=i||qp,"string"==typeof t?n=ki(t,e,i):S(t)&&(n=t,t=n.id),this.id=t,this.dom=n;var r=n.style;r&&(n.onselectstart=Ai,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=i};vg.prototype={constructor:vg,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=ki("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,a=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),n.width=t*i,n.height=e*i,a&&(a.width=t*i,a.height=e*i,1!==i&&this.ctxBack.scale(i,i))},clear:function(t,e){var i=this.dom,n=this.ctx,r=i.width,a=i.height,e=e||this.clearColor,o=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(o&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,r/l,a/l)),n.clearRect(0,0,r,a),e&&"transparent"!==e){var h;e.colorStops?(h=e.__canvasGradient||cg.getGradient(n,e,{x:0,y:0,width:r,height:a}),e.__canvasGradient=h):e.image&&(h=gg.prototype.getCanvasPattern.call(e,n)),n.save(),n.fillStyle=h||e,n.fillRect(0,0,r,a),n.restore()}if(o){var u=this.domBack;n.save(),n.globalAlpha=s,n.drawImage(u,0,0,r,a),n.restore()}}};var mg="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},yg=new zp(50),_g={},xg=0,wg=5e3,bg=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,Sg="12px sans-serif",Mg={};Mg.measureText=function(t,e){var i=l();return i.font=e||Sg,i.measureText(t)};var Ig=Sg,Tg={left:1,right:1,center:1},Cg={top:1,bottom:1,middle:1},Dg=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],Ag=new mi,kg=function(){};kg.prototype={constructor:kg,drawRectText:function(t,e){var i=this.style;e=i.textRect||e,this.__dirty&&tn(i,!0);var n=i.text;if(null!=n&&(n+=""),yn(n,i)){t.save();var r=this.transform;i.transformText?this.setTransform(t):r&&(Ag.copy(e),Ag.applyTransform(r),e=Ag),nn(this,t,n,i,e,hg),t.restore()}}},_n.prototype={constructor:_n,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();return n.contain(i[0],i[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?Qp.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new cg(t,this),this.dirty(!1),this}},u(_n,Qp),c(_n,kg),xn.prototype={constructor:xn,type:"image",brush:function(t,e){var i=this.style,n=i.image;i.bind(t,this,e);var r=this._image=Li(n,this._image,this,this.onload);if(r&&Ei(r)){var a=i.x||0,o=i.y||0,s=i.width,l=i.height,h=r.width/r.height;if(null==s&&null!=l?s=l*h:null==l&&null!=s?l=s/h:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),i.sWidth&&i.sHeight){var u=i.sx||0,c=i.sy||0;t.drawImage(r,u,c,i.sWidth,i.sHeight,a,o,s,l)}else if(i.sx&&i.sy){var u=i.sx,c=i.sy,d=s-u,f=l-c;t.drawImage(r,u,c,d,f,a,o,s,l)}else t.drawImage(r,a,o,s,l);null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new mi(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},u(xn,_n);var Pg=1e5,Lg=314159,Og=.01,Eg=.001,Bg=new mi(0,0,0,0),zg=new mi(0,0,0,0),Rg=function(t,e,i){this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=o({},i||{}),this.dpr=i.devicePixelRatio||qp,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,n){var l=t.width,h=t.height;null!=i.width&&(l=i.width),null!=i.height&&(h=i.height),this.dpr=i.devicePixelRatio||1,t.width=l*this.dpr,t.height=h*this.dpr,this._width=l,this._height=h;var u=new vg(t,this,this.dpr);u.__builtin__=!0,u.initContext(),s[Lg]=u,u.zlevel=Lg,a.push(Lg),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=Tn(this._width,this._height);t.appendChild(c)}this._hoverlayer=null,this._hoverElements=[]};Rg.prototype={constructor:Rg,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var n=0;n<i.length;n++){var r=i[n],a=this._layers[r];if(!a.__builtin__&&a.refresh){var o=0===n?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return i.__from=t,t.__hoverMir=i,e&&i.setStyle(e),this._hoverElements.push(i),i}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=h(i,e);n>=0&&i.splice(n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,i=this._hoverlayer;if(i&&i.clear(),e){Ii(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(Pg));var n={};i.ctx.save();for(var r=0;e>r;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,i,!0,n))):(t.splice(r,1),o.__hoverMir=null,e--)}i.ctx.restore()}},getHoverLayer:function(){return this.getLayer(Pg)},_paintList:function(t,e,i){if(this._redrawId===i){e=e||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!n){var r=this;mg(function(){r._paintList(t,e,i)})}}},_compositeManually:function(){var t=this.getLayer(Lg).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},_doPaintList:function(t,e){for(var i=[],n=0;n<this._zlevelList.length;n++){var r=this._zlevelList[n],a=this._layers[r];a.__builtin__&&a!==this._hoverlayer&&(a.__dirty||e)&&i.push(a)}for(var o=!0,s=0;s<i.length;s++){var a=i[s],l=a.ctx,h={};l.save();var u=e?a.__startIndex:a.__drawIndex,c=!e&&a.incremental&&Date.now,d=c&&Date.now(),p=a.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(a.__startIndex===a.__endIndex)a.clear(!1,p);else if(u===a.__startIndex){var g=t[u];g.incremental&&g.notClear&&!e||a.clear(!1,p)}-1===u&&(console.error("For some unknown reason. drawIndex is -1"),u=a.__startIndex);for(var v=u;v<a.__endIndex;v++){var m=t[v];if(this._doPaintEl(m,a,e,h),m.__dirty=m.__dirtyText=!1,c){var y=Date.now()-d;if(y>15)break}}a.__drawIndex=v,a.__drawIndex<a.__endIndex&&(o=!1),h.prevElClipPaths&&l.restore(),l.restore()}return jf.wxa&&f(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,i,n){var r=e.ctx,a=t.transform;if(!(!e.__dirty&&!i||t.invisible||0===t.style.opacity||a&&!a[0]&&!a[3]||t.culling&&Sn(t,this._width,this._height))){var o=t.__clipPaths;(!n.prevElClipPaths||Mn(o,n.prevElClipPaths))&&(n.prevElClipPaths&&(e.ctx.restore(),n.prevElClipPaths=null,n.prevEl=null),o&&(r.save(),In(o,r),n.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,n.prevEl||null),n.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Lg);var i=this._layers[t];return i||(i=new vg("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]&&r(i,this._layerConfig[t],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},insertLayer:function(t,e){var i=this._layers,n=this._zlevelList,r=n.length,a=null,o=-1,s=this._domRoot;if(i[t])return void $p("ZLevel "+t+" has been used already");if(!bn(e))return void $p("Layer of zlevel "+t+" is not valid");if(r>0&&t>n[0]){for(o=0;r-1>o&&!(n[o]<t&&n[o+1]>t);o++);a=i[n[o]]}if(n.splice(o+1,0,t),i[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r.length;n++)i=r[n],t.call(e,this._layers[i],i)},eachBuiltinLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t){t.__dirty=t.__used=!1}),this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}for(var r=null,a=0,i=0;i<t.length;i++){var o,n=t[i],s=n.zlevel;n.incremental?(o=this.getLayer(s+Eg,this._needsManuallyCompositing),o.incremental=!0,a=1):o=this.getLayer(s+(a>0?Og:0),this._needsManuallyCompositing),o.__builtin__||$p("ZLevel "+s+" has been used by unkown layer "+o.id),o!==r&&(o.__used=!0,o.__startIndex!==i&&(o.__dirty=!0),o.__startIndex=i,o.__drawIndex=o.incremental?-1:i,e(i),r=o),n.__dirty&&(o.__dirty=!0,o.incremental&&o.__drawIndex<0&&(o.__drawIndex=i))}e(i),this.eachBuiltinLayer(function(t){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?r(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+Og){var o=this._layers[a];r(o,i[t],!0)}}}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(h(i,t),1))},resize:function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);f(this._progressiveLayers,function(i){i.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(Lg).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Lg].dom;var e=new vg("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var l=o[s];this._doPaintEl(l,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[n]||wn(s[i])||wn(o.style[i]))-(wn(s[r])||0)-(wn(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,l=a.shadowOffsetY*e,h=a.hasStroke()?a.lineWidth:0,u=Math.max(h/2,-s+o),c=Math.max(h/2,s+o),d=Math.max(h/2,-l+o),f=Math.max(h/2,l+o),p=r.width+u+c,g=r.height+d+f;i.width=p*e,i.height=g*e,n.scale(e,e),n.clearRect(0,0,p,g),n.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(n);var m=xn,y=new m({style:{x:0,y:0,image:i}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}};var Ng=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,pp.call(this)};Ng.prototype={constructor:Ng,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),i=0;i<e.length;i++)this.addClip(e[i])},removeClip:function(t){var e=h(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e.length;i++)this.removeClip(e[i]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i.length,r=[],a=[],o=0;n>o;o++){var s=i[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(var o=0;n>o;)i[o]._needsRemove?(i[o]=i[n-1],i.pop(),n--):o++;n=r.length;for(var o=0;n>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(mg(t),!e._paused&&e._update())}var e=this;this._running=!0,mg(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var i=new Xp(t,e.loop,e.getter,e.setter);return this.addAnimator(i),i}},c(Ng,pp);var Fg=300,Vg=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Hg=["touchstart","touchend","touchmove"],Wg={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},Gg=p(Vg,function(t){var e=t.replace("mouse","pointer");return Wg[e]?e:t}),Ug={mousemove:function(t){t=ge(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=ge(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!==this.dom)for(;e&&9!==e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){t=ge(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,this.handler.processGesture(this,t,"start"),Ug.mousemove.call(this,t),Ug.mousedown.call(this,t),Dn(this)},touchmove:function(t){t=ge(this.dom,t),t.zrByTouch=!0,this.handler.processGesture(this,t,"change"),Ug.mousemove.call(this,t),Dn(this)},touchend:function(t){t=ge(this.dom,t),t.zrByTouch=!0,this.handler.processGesture(this,t,"end"),Ug.mouseup.call(this,t),+new Date-this._lastTouchMoment<Fg&&Ug.click.call(this,t),Dn(this)},pointerdown:function(t){Ug.mousedown.call(this,t)},pointermove:function(t){An(t)||Ug.mousemove.call(this,t)},pointerup:function(t){Ug.mouseup.call(this,t)},pointerout:function(t){An(t)||Ug.mouseout.call(this,t)}};f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){Ug[t]=function(e){e=ge(this.dom,e),this.trigger(t,e)}});var Xg=Pn.prototype;Xg.dispose=function(){for(var t=Vg.concat(Hg),e=0;e<t.length;e++){var i=t[e];me(this.dom,Cn(i),this._handlers[i])}},Xg.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},c(Pn,pp);var jg=!jf.canvasSupported,Yg={canvas:Rg},qg={},Zg="4.0.7",$g=function(t,e,i){i=i||{},this.dom=e,this.id=t;var n=this,r=new ag,a=i.renderer;if(jg){if(!Yg.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");a="vml"}else a&&Yg[a]||(a="canvas");var o=new Yg[a](e,r,i,t);this.storage=r,this.painter=o;var s=jf.node||jf.worker?null:new Pn(o.getViewportRoot());this.handler=new bp(r,o,s,o.root),this.animation=new Ng({stage:{update:y(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,h=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(n)},r.addToStorage=function(t){h.call(r,t),t.addSelfToZr(n)}};$g.prototype={constructor:$g,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var i=this.painter.addHover(t,e);return this.refreshHover(),i}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,zn(this.id)}};var Kg=(Object.freeze||Object)({version:Zg,init:Ln,dispose:On,getInstance:En,registerPainter:Bn}),Qg=f,Jg=S,tv=x,ev="series\x00",iv=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],nv=0,rv=".",av="___EC__COMPONENT__CONTAINER___",ov=0,sv=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);
return function(e,i,n){for(var r={},a=0;a<t.length;a++){var o=t[a][1];if(!(i&&h(i,o)>=0||n&&h(n,o)<0)){var s=e.getShallow(o);null!=s&&(r[t[a][0]]=s)}}return r}},lv=sv([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),hv={getLineStyle:function(t){var e=lv(this,t),i=this.getLineDash(e.lineWidth);return i&&(e.lineDash=i),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"===e||null==e?null:"dashed"===e?[n,n]:[i,i]}},uv=sv([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),cv={getAreaStyle:function(t,e){return uv(this,t,e)}},dv=Math.pow,fv=Math.sqrt,pv=1e-8,gv=1e-4,vv=fv(3),mv=1/3,yv=H(),_v=H(),xv=H(),wv=Math.min,bv=Math.max,Sv=Math.sin,Mv=Math.cos,Iv=2*Math.PI,Tv=H(),Cv=H(),Dv=H(),Av=[],kv=[],Pv={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Lv=[],Ov=[],Ev=[],Bv=[],zv=Math.min,Rv=Math.max,Nv=Math.cos,Fv=Math.sin,Vv=Math.sqrt,Hv=Math.abs,Wv="undefined"!=typeof Float32Array,Gv=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};Gv.prototype={constructor:Gv,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=Hv(1/qp/t)||0,this._uy=Hv(1/qp/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Pv.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=Hv(t-this._xi)>this._ux||Hv(e-this._yi)>this._uy||this._len<5;return this.addData(Pv.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,a){return this.addData(Pv.C,t,e,i,n,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,a):this._ctx.bezierCurveTo(t,e,i,n,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,i,n){return this.addData(Pv.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,a){return this.addData(Pv.A,t,e,i,i,n,r-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,a),this._xi=Nv(r)*i+t,this._yi=Fv(r)*i+e,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(Pv.R,t,e,i,n),this},closePath:function(){this.addData(Pv.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t.length;i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Wv||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();Wv&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[n++]=a[o];this._len=n},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var i=0;i<arguments.length;i++)e[this._len++]=arguments[i];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=Vv(u*u+c*c),f=l,p=h,g=o.length;for(u/=d,c/=d,0>a&&(a=r+a),a%=r,f-=a*u,p-=a*c;u>0&&t>=f||0>u&&f>=t||0===u&&(c>0&&e>=p||0>c&&p>=e);)n=this._dashIdx,i=o[n],f+=u*i,p+=c*i,this._dashIdx=(n+1)%g,u>0&&l>f||0>u&&f>l||c>0&&h>p||0>c&&p>h||s[n%2?"moveTo":"lineTo"](u>=0?zv(f,t):Rv(f,t),c>=0?zv(p,e):Rv(p,e));u=f-t,c=p-e,this._dashOffset=-Vv(u*u+c*c)},_dashedBezierTo:function(t,e,i,n,r,a){var o,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,v=this._yi,m=sr,y=0,_=this._dashIdx,x=f.length,w=0;for(0>d&&(d=c+d),d%=c,o=0;1>o;o+=.1)s=m(g,t,i,r,o+.1)-m(g,t,i,r,o),l=m(v,e,n,a,o+.1)-m(v,e,n,a,o),y+=Vv(s*s+l*l);for(;x>_&&(w+=f[_],!(w>d));_++);for(o=(w-d)/y;1>=o;)h=m(g,t,i,r,o),u=m(v,e,n,a,o),_%2?p.moveTo(h,u):p.lineTo(h,u),o+=f[_]/y,_=(_+1)%x;_%2!==0&&p.lineTo(r,a),s=r-h,l=a-u,this._dashOffset=-Vv(s*s+l*l)},_dashedQuadraticTo:function(t,e,i,n){var r=i,a=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Wv&&(this.data=new Float32Array(t)))},getBoundingRect:function(){Lv[0]=Lv[1]=Ev[0]=Ev[1]=Number.MAX_VALUE,Ov[0]=Ov[1]=Bv[0]=Bv[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,i=0,n=0,r=0,a=0;a<t.length;){var o=t[a++];switch(1===a&&(e=t[a],i=t[a+1],n=e,r=i),o){case Pv.M:n=t[a++],r=t[a++],e=n,i=r,Ev[0]=n,Ev[1]=r,Bv[0]=n,Bv[1]=r;break;case Pv.L:xr(e,i,t[a],t[a+1],Ev,Bv),e=t[a++],i=t[a++];break;case Pv.C:wr(e,i,t[a++],t[a++],t[a++],t[a++],t[a],t[a+1],Ev,Bv),e=t[a++],i=t[a++];break;case Pv.Q:br(e,i,t[a++],t[a++],t[a],t[a+1],Ev,Bv),e=t[a++],i=t[a++];break;case Pv.A:var s=t[a++],l=t[a++],h=t[a++],u=t[a++],c=t[a++],d=t[a++]+c;a+=1;var f=1-t[a++];1===a&&(n=Nv(c)*h+s,r=Fv(c)*u+l),Sr(s,l,h,u,c,d,f,Ev,Bv),e=Nv(d)*h+s,i=Fv(d)*u+l;break;case Pv.R:n=e=t[a++],r=i=t[a++];var p=t[a++],g=t[a++];xr(n,r,n+p,r+g,Ev,Bv);break;case Pv.Z:e=n,i=r}oe(Lv,Lv,Ev),se(Ov,Ov,Bv)}return 0===a&&(Lv[0]=Lv[1]=Ov[0]=Ov[1]=0),new mi(Lv[0],Lv[1],Ov[0]-Lv[0],Ov[1]-Lv[1])},rebuildPath:function(t){for(var e,i,n,r,a,o,s=this.data,l=this._ux,h=this._uy,u=this._len,c=0;u>c;){var d=s[c++];switch(1===c&&(n=s[c],r=s[c+1],e=n,i=r),d){case Pv.M:e=n=s[c++],i=r=s[c++],t.moveTo(n,r);break;case Pv.L:a=s[c++],o=s[c++],(Hv(a-n)>l||Hv(o-r)>h||c===u-1)&&(t.lineTo(a,o),n=a,r=o);break;case Pv.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case Pv.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case Pv.A:var f=s[c++],p=s[c++],g=s[c++],v=s[c++],m=s[c++],y=s[c++],_=s[c++],x=s[c++],w=g>v?g:v,b=g>v?1:g/v,S=g>v?v/g:1,M=Math.abs(g-v)>.001,I=m+y;M?(t.translate(f,p),t.rotate(_),t.scale(b,S),t.arc(0,0,w,m,I,1-x),t.scale(1/b,1/S),t.rotate(-_),t.translate(-f,-p)):t.arc(f,p,w,m,I,1-x),1===c&&(e=Nv(m)*g+f,i=Fv(m)*v+p),n=Nv(I)*g+f,r=Fv(I)*v+p;break;case Pv.R:e=n=s[c],i=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case Pv.Z:t.closePath(),n=e,r=i}}}},Gv.CMD=Pv;var Uv=2*Math.PI,Xv=2*Math.PI,jv=Gv.CMD,Yv=2*Math.PI,qv=1e-4,Zv=[-1,-1,-1],$v=[-1,-1],Kv=gg.prototype.getCanvasPattern,Qv=Math.abs,Jv=new Gv(!0);Nr.prototype={constructor:Nr,type:"path",__dirtyPath:!0,strokeContainThreshold:5,subPixelOptimize:!1,brush:function(t,e){var i=this.style,n=this.path||Jv,r=i.hasStroke(),a=i.hasFill(),o=i.fill,s=i.stroke,l=a&&!!o.colorStops,h=r&&!!s.colorStops,u=a&&!!o.image,c=r&&!!s.image;if(i.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=i.getGradient(t,o,d)),h&&(d=d||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:u&&(t.fillStyle=Kv.call(o,t)),h?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=Kv.call(s,t));var f=i.lineDash,p=i.lineDashOffset,g=!!t.setLineDash,v=this.getGlobalScale();if(n.setScale(v[0],v[1]),this.__dirtyPath||f&&!g&&r?(n.beginPath(t),f&&!g&&(n.setLineDash(f),n.setLineDashOffset(p)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a)if(null!=i.fillOpacity){var m=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,n.fill(t),t.globalAlpha=m}else n.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),r)if(null!=i.strokeOpacity){var m=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,n.stroke(t),t.globalAlpha=m}else n.stroke(t);f&&g&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(){},createPathProxy:function(){this.path=new Gv},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;n||(n=this.path=new Gv),this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){r.copy(t);var a=e.lineWidth,o=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),o>1e-10&&(r.width+=a/o,r.height+=a/o,r.x-=a/o/2,r.y-=a/o/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),r=this.style;if(t=i[0],e=i[1],n.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var o=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),Rr(a,o/s,t,e)))return!0}if(r.hasFill())return zr(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):_n.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(S(t))for(var n in t)t.hasOwnProperty(n)&&(i[n]=t[n]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&Qv(t[0]-1)>1e-10&&Qv(t[3]-1)>1e-10?Math.sqrt(Qv(t[0]*t[3]-t[2]*t[1])):1}},Nr.extend=function(t){var e=function(e){Nr.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var i=t.shape;if(i){this.shape=this.shape||{};var n=this.shape;for(var r in i)!n.hasOwnProperty(r)&&i.hasOwnProperty(r)&&(n[r]=i[r])}t.init&&t.init.call(this,e)};u(e,Nr);for(var i in t)"style"!==i&&"shape"!==i&&(e.prototype[i]=t[i]);return e},u(Nr,_n);var tm=Gv.CMD,em=[[],[],[]],im=Math.sqrt,nm=Math.atan2,rm=function(t,e){var i,n,r,a,o,s,l=t.data,h=tm.M,u=tm.C,c=tm.L,d=tm.R,f=tm.A,p=tm.Q;for(r=0,a=0;r<l.length;){switch(i=l[r++],a=r,n=0,i){case h:n=1;break;case c:n=1;break;case u:n=3;break;case p:n=2;break;case f:var g=e[4],v=e[5],m=im(e[0]*e[0]+e[1]*e[1]),y=im(e[2]*e[2]+e[3]*e[3]),_=nm(-e[1]/y,e[0]/m);l[r]*=m,l[r++]+=g,l[r]*=y,l[r++]+=v,l[r++]*=m,l[r++]*=y,l[r++]+=_,l[r++]+=_,r+=2,a=r;break;case d:s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1],s[0]+=l[r++],s[1]+=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}for(o=0;n>o;o++){var s=em[o];s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}}},am=Math.sqrt,om=Math.sin,sm=Math.cos,lm=Math.PI,hm=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},um=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(hm(t)*hm(e))},cm=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(um(t,e))},dm=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,fm=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,pm=function(t){_n.call(this,t)};pm.prototype={constructor:pm,type:"text",brush:function(t,e){var i=this.style;this.__dirty&&tn(i,!0),i.fill=i.stroke=i.shadowBlur=i.shadowColor=i.shadowOffsetX=i.shadowOffsetY=null;var n=i.text;return null!=n&&(n+=""),yn(n,i)?(this.setTransform(t),nn(this,t,n,i,null,e),void this.restoreTransform(t)):void(t.__attrCachedBy=lg.NONE)},getBoundingRect:function(){var t=this.style;if(this.__dirty&&tn(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var i=Ri(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(i.x+=t.x||0,i.y+=t.y||0,pn(t.textStroke,t.textStrokeWidth)){var n=t.textStrokeWidth;i.x-=n/2,i.y-=n/2,i.width+=n,i.height+=n}this._rect=i}return this._rect}},u(pm,_n);var gm=Nr.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),vm=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],mm=function(t){return jf.browser.ie&&jf.browser.version>=11?function(){var e,i=this.__clipPaths,n=this.style;if(i)for(var r=0;r<i.length;r++){var a=i[r],o=a&&a.shape,s=a&&a.type;if(o&&("sector"===s&&o.startAngle===o.endAngle||"rect"===s&&(!o.width||!o.height))){for(var l=0;l<vm.length;l++)vm[l][2]=n[vm[l][0]],n[vm[l][0]]=vm[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<vm.length;l++)n[vm[l][0]]=vm[l][2]}:t},ym=Nr.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:mm(Nr.prototype.brush),buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(o),u=Math.sin(o);t.moveTo(h*r+i,u*r+n),t.lineTo(h*a+i,u*a+n),t.arc(i,n,a,o,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,o,l),t.closePath()}}),_m=Nr.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}}),xm=function(t,e){for(var i=t.length,n=[],r=0,a=1;i>a;a++)r+=ee(t[a-1],t[a]);var o=r/2;o=i>o?i:o;for(var a=0;o>a;a++){var s,l,h,u=a/(o-1)*(e?i:i-1),c=Math.floor(u),d=u-c,f=t[c%i];e?(s=t[(c-1+i)%i],l=t[(c+1)%i],h=t[(c+2)%i]):(s=t[0===c?c:c-1],l=t[c>i-2?i-1:c+1],h=t[c>i-3?i-1:c+2]);var p=d*d,g=d*p;n.push([Xr(s[0],f[0],l[0],h[0],d,p,g),Xr(s[1],f[1],l[1],h[1],d,p,g)])}return n},wm=function(t,e,i,n){var r,a,o,s,l=[],h=[],u=[],c=[];if(n){o=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;f>d;d++)oe(o,o,t[d]),se(s,s,t[d]);oe(o,o,n[0]),se(s,s,n[1])}for(var d=0,f=t.length;f>d;d++){var p=t[d];if(i)r=t[d?d-1:f-1],a=t[(d+1)%f];else{if(0===d||d===f-1){l.push(G(t[d]));continue}r=t[d-1],a=t[d+1]}Y(h,a,r),J(h,h,e);var g=ee(p,r),v=ee(p,a),m=g+v;0!==m&&(g/=m,v/=m),J(u,h,-g),J(c,h,v);var y=X([],p,u),_=X([],p,c);n&&(se(y,y,o),oe(y,y,s),se(_,_,o),oe(_,_,s)),l.push(y),l.push(_)}return i&&l.push(l.shift()),l},bm=Nr.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){jr(t,e,!0)}}),Sm=Nr.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){jr(t,e,!1)}}),Mm=Math.round,Im={},Tm=Nr.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(qr(Im,e,this.style),i=Im.x,n=Im.y,r=Im.width,a=Im.height,Im.r=e.r,e=Im):(i=e.x,n=e.y,r=e.width,a=e.height),e.r?Ji(t,e):t.rect(i,n,r,a),t.closePath()}}),Cm={},Dm=Nr.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(Yr(Cm,e,this.style),i=Cm.x1,n=Cm.y1,r=Cm.x2,a=Cm.y2):(i=e.x1,n=e.y1,r=e.x2,a=e.y2);var o=e.percent;0!==o&&(t.moveTo(i,n),1>o&&(r=i*(1-o)+r*o,a=n*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),Am=[],km=Nr.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,a=e.y2,o=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,u=e.percent;0!==u&&(t.moveTo(i,n),null==l||null==h?(1>u&&(mr(i,o,r,u,Am),o=Am[1],r=Am[2],mr(n,s,a,u,Am),s=Am[1],a=Am[2]),t.quadraticCurveTo(o,s,r,a)):(1>u&&(cr(i,o,l,r,u,Am),o=Am[1],l=Am[2],r=Am[3],cr(n,s,h,a,u,Am),s=Am[1],h=Am[2],a=Am[3]),t.bezierCurveTo(o,s,l,h,r,a)))},pointAt:function(t){return $r(this.shape,t,!1)},tangentAt:function(t){var e=$r(this.shape,t,!0);return te(e,e)}}),Pm=Nr.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),h=Math.sin(a);t.moveTo(l*r+i,h*r+n),t.arc(i,n,r,a,o,!s)}}),Lm=Nr.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e.length;i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t.length;i++)t[i].path||t[i].createPathProxy(),t[i].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i.length;n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),Nr.prototype.getBoundingRect.call(this)}}),Om=function(t){this.colorStops=t||[]};Om.prototype={constructor:Om,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var Em=function(t,e,i,n,r,a){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==i?1:i,this.y2=null==n?0:n,this.type="linear",this.global=a||!1,Om.call(this,r)};Em.prototype={constructor:Em},u(Em,Om);var Bm=function(t,e,i,n,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==i?.5:i,this.type="radial",this.global=r||!1,Om.call(this,n)};Bm.prototype={constructor:Bm},u(Bm,Om),Kr.prototype.incremental=!0,Kr.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},Kr.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},Kr.prototype.addDisplayables=function(t,e){e=e||!1;for(var i=0;i<t.length;i++)this.addDisplayable(t[i],e)},Kr.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Kr.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},Kr.prototype.brush=function(t){for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,e===this._cursor?null:this._displayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=e;for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===e?null:this._temporaryDisplayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var zm=[];Kr.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new mi(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var i=this._displayables[e],n=i.getBoundingRect().clone();i.needLocalTransform()&&n.applyTransform(i.getLocalTransform(zm)),t.union(n)}this._rect=t}return this._rect},Kr.prototype.contain=function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();if(n.contain(i[0],i[1]))for(var r=0;r<this._displayables.length;r++){var a=this._displayables[r];if(a.contain(t,e))return!0}return!1},u(Kr,_n);var Rm=Math.round,Nm=Math.max,Fm=Math.min,Vm={},Hm=1,Wm=Ur,Gm=N(),Um=0,Xm=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:Hm,extendShape:Qr,extendPath:Jr,makePath:ta,makeImage:ea,mergePath:Wm,resizePath:na,subPixelOptimizeLine:ra,subPixelOptimizeRect:aa,subPixelOptimize:oa,setElementHoverStyle:pa,isInEmphasis:ga,setHoverStyle:xa,setAsHoverStyleTrigger:wa,setLabelStyle:ba,setTextStyle:Sa,setText:Ma,getFont:Pa,updateProps:Oa,initProps:Ea,getTransform:Ba,applyTransform:za,transformDirection:Ra,groupTransition:Na,clipPointsByRect:Fa,clipRectByRect:Va,createIcon:Ha,Group:ig,Image:xn,Text:pm,Circle:gm,Sector:ym,Ring:_m,Polygon:bm,Polyline:Sm,Rect:Tm,Line:Dm,BezierCurve:km,Arc:Pm,IncrementalDisplayable:Kr,CompoundPath:Lm,LinearGradient:Em,RadialGradient:Bm,BoundingRect:mi}),jm=["textStyle","color"],Ym={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(jm):null)},getFont:function(){return Pa({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return Ri(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},qm=sv([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),Zm={getItemStyle:function(t,e){var i=qm(this,t,e),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},$m=c,Km=jn();Wa.prototype={constructor:Wa,init:null,mergeOption:function(t){r(this.option,t,!0)},get:function(t,e){return null==t?this.option:Ga(this.option,this.parsePath(t),!e&&Ua(this,t))},getShallow:function(t,e){var i=this.option,n=null==i?i:i[t],r=!e&&Ua(this,t);return null==n&&r&&(n=r.getShallow(t)),n},getModel:function(t,e){var i,n=null==t?this.option:Ga(this.option,t=this.parsePath(t));return e=e||(i=Ua(this,t))&&i.getModel(t),new Wa(n,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(n(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){Km(this).getParent=t},isAnimationEnabled:function(){if(!jf.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},tr(Wa),er(Wa),$m(Wa,hv),$m(Wa,cv),$m(Wa,Ym),$m(Wa,Zm);var Qm=0,Jm=1e-4,ty=9007199254740991,ey=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,iy=(Object.freeze||Object)({linearMap:Za,parsePercent:$a,round:Ka,asc:Qa,getPrecision:Ja,getPrecisionSafe:to,getPixelPrecision:eo,getPercentWithPrecision:io,MAX_SAFE_INTEGER:ty,remRadian:no,isRadianAroundZero:ro,parseDate:ao,quantity:oo,nice:lo,quantile:ho,reformIntervals:uo,isNumeric:co}),ny=L,ry=/([&<>"'])/g,ay={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},oy=["a","b","c","d","e","f","g"],sy=function(t,e){return"{"+t+(null==e?"":e)+"}"},ly=Gi,hy=(Object.freeze||Object)({addCommas:fo,toCamelCase:po,normalizeCssArray:ny,encodeHTML:go,formatTpl:vo,formatTplSimple:mo,getTooltipMarker:yo,formatTime:xo,capitalFirst:wo,truncateText:ly,getTextBoundingRect:bo,getTextRect:So}),uy=f,cy=["left","right","top","bottom","width","height"],dy=[["width","left","right"],["height","top","bottom"]],fy=Mo,py=(_(Mo,"vertical"),_(Mo,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),gy=jn(),vy=Wa.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,i,n){Wa.call(this,t,e,i,n),this.uid=Xa("ec_cpt_model")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?Co(t):{},a=e.getTheme();r(t,a.get(this.mainType)),r(t,this.getDefaultOption()),i&&To(t,n,i)},mergeOption:function(t){r(this.option,t,!0);var e=this.layoutMode;e&&To(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){var t=gy(this);if(!t.defaultOption){for(var e=[],i=this.constructor;i;){var n=i.prototype.defaultOption;n&&e.push(n),i=i.superClass}for(var a={},o=e.length-1;o>=0;o--)a=r(a,e[o],!0);t.defaultOption=a}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});rr(vy,{registerWhenExtend:!0}),ja(vy),Ya(vy,Ao),c(vy,py);var my="";"undefined"!=typeof navigator&&(my=navigator.platform||"");var yy={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:my.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},_y=jn(),xy={clearColorPalette:function(){_y(this).colorIdx=0,_y(this).colorNameMap={}},getColorFromPalette:function(t,e,i){e=e||this;var n=_y(e),r=n.colorIdx||0,a=n.colorNameMap=n.colorNameMap||{};if(a.hasOwnProperty(t))return a[t];var o=Rn(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=i&&s?ko(s,i):o;if(l=l||o,l&&l.length){var h=l[r];return t&&(a[t]=h),n.colorIdx=(r+1)%l.length,h}}},wy={cartesian2d:function(t,e,i,n){var r=t.getReferringComponents("xAxis")[0],a=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],i.set("x",r),i.set("y",a),Lo(r)&&(n.set("x",r),e.firstCategoryDimIndex=0),Lo(a)&&(n.set("y",a),e.firstCategoryDimIndex=1)},singleAxis:function(t,e,i,n){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],i.set("single",r),Lo(r)&&(n.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,i,n){var r=t.getReferringComponents("polar")[0],a=r.findAxisModel("radiusAxis"),o=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],i.set("radius",a),i.set("angle",o),Lo(a)&&(n.set("radius",a),e.firstCategoryDimIndex=0),Lo(o)&&(n.set("angle",o),e.firstCategoryDimIndex=1)},geo:function(t,e){e.coordSysDims=["lng","lat"]},parallel:function(t,e,i,n){var r=t.ecModel,a=r.getComponent("parallel",t.get("parallelIndex")),o=e.coordSysDims=a.dimensions.slice();f(a.parallelAxisIndex,function(t,a){var s=r.getComponent("parallelAxis",t),l=o[a];i.set(l,s),Lo(s)&&null==e.firstCategoryDimIndex&&(n.set(l,s),e.firstCategoryDimIndex=a)})}},by="original",Sy="arrayRows",My="objectRows",Iy="keyedColumns",Ty="unknown",Cy="typedArray",Dy="column",Ay="row";Oo.seriesDataToSource=function(t){return new Oo({data:t,sourceFormat:I(t)?Cy:by,fromDataset:!1})},er(Oo);var ky=jn(),Py="\x00_ec_inner",Ly=Wa.extend({init:function(t,e,i,n){i=i||{},this.option=null,this._theme=new Wa(i),this._optionManager=n},setOption:function(t,e){O(!(Py in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,i=this._optionManager;if(!t||"recreate"===t){var n=i.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(n)):qo.call(this,n),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=i.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this,this._api);a.length&&f(a,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,n){var r=Rn(t[e]),s=Hn(a.get(e),r);Wn(s),f(s,function(t){var i=t.option;S(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=$o(e,i,t.exist))});var l=Zo(a,n);i[e]=[],a.set(e,[]),f(s,function(t,n){var r=t.exist,s=t.option;if(O(S(s)||r,"Empty component definition"),s){var h=vy.getClass(e,t.keyInfo.subType,!0);if(r&&r instanceof h)r.name=t.keyInfo.name,r.mergeOption(s,this),r.optionUpdated(s,!1);else{var u=o({dependentModels:l,componentIndex:n},t.keyInfo);r=new h(s,this,this,u),o(r,u),r.init(s,this,this,u),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);a.get(e)[n]=r,i[e][n]=r.option},this),"series"===e&&Ko(this,a.get("series"))}var i=this.option,a=this._componentsMap,s=[];zo(this),f(t,function(t,e){null!=t&&(vy.hasClass(e)?e&&s.push(e):i[e]=null==i[e]?n(t):r(i[e],t,!0))}),vy.topologicalTravel(s,vy.getAllClassMainTypes(),e,this),this._seriesIndicesMap=N(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=n(this.option);return f(t,function(e,i){if(vy.hasClass(i)){for(var e=Rn(e),n=e.length-1;n>=0;n--)Un(e[n])&&e.splice(n,1);t[i]=e}}),delete t[Py],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap.get(t);return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];var o;if(null!=i)x(i)||(i=[i]),o=v(p(i,function(t){return a[t]}),function(t){return!!t});else if(null!=n){var s=x(n);o=v(a,function(t){return s&&h(n,t.id)>=0||!s&&t.id===n})}else if(null!=r){var l=x(r);o=v(a,function(t){return l&&h(r,t.name)>=0||!l&&t.name===r})}else o=a.slice();return Qo(o,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return!t||null==t[e]&&null==t[i]&&null==t[n]?null:{mainType:r,index:t[e],id:t[i],name:t[n]}}function i(e){return t.filter?v(e,t.filter):e}var n=t.query,r=t.mainType,a=e(n),o=a?this.queryComponents(a):this._componentsMap.get(r);return i(Qo(o,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if("function"==typeof t)i=e,e=t,n.each(function(t,n){f(t,function(t,r){e.call(i,n,t,r)})});else if(b(t))f(n.get(t),e,i);else if(S(t)){var r=this.findComponents(t);f(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){f(this._seriesIndices,function(i){var n=this._componentsMap.get("series")[i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){f(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,i){f(this._seriesIndices,function(n){var r=this._componentsMap.get("series")[n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return f(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){var i=v(this._componentsMap.get("series"),t,e);Ko(this,i)},restoreData:function(t){var e=this._componentsMap;Ko(this,e.get("series"));var i=[];e.each(function(t,e){i.push(e)}),vy.topologicalTravel(i,vy.getAllClassMainTypes(),function(i){f(e.get(i),function(e){("series"!==i||!jo(e,t))&&e.restoreData()})})}});c(Ly,xy);var Oy=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],Ey={};ts.prototype={constructor:ts,create:function(t,e){var i=[];f(Ey,function(n){var r=n.create(t,e);i=i.concat(r||[])}),this._coordinateSystems=i},update:function(t,e){f(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},ts.register=function(t,e){Ey[t]=e},ts.get=function(t){return Ey[t]};var By=f,zy=n,Ry=p,Ny=r,Fy=/^(min|max)?(.+)$/;es.prototype={constructor:es,setOption:function(t,e){t&&f(Rn(t.series),function(t){t&&t.data&&I(t.data)&&B(t.data)}),t=zy(t,!0);var i=this._optionBackup,n=is.call(this,t,e,!i);this._newBaseOption=n.baseOption,i?(os(i.baseOption,n.baseOption),n.timelineOptions.length&&(i.timelineOptions=n.timelineOptions),n.mediaList.length&&(i.mediaList=n.mediaList),n.mediaDefault&&(i.mediaDefault=n.mediaDefault)):this._optionBackup=n
},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=Ry(e.timelineOptions,zy),this._mediaList=Ry(e.mediaList,zy),this._mediaDefault=zy(e.mediaDefault),this._currentMediaIndices=[],zy(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=zy(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),i=this._mediaList,n=this._mediaDefault,r=[],a=[];if(!i.length&&!n)return a;for(var o=0,s=i.length;s>o;o++)ns(i[o].query,t,e)&&r.push(o);return!r.length&&n&&(r=[-1]),r.length&&!as(r,this._currentMediaIndices)&&(a=Ry(r,function(t){return zy(-1===t?n.option:i[t].option)})),this._currentMediaIndices=r,a}};var Vy=f,Hy=S,Wy=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],Gy=function(t,e){Vy(fs(t.series),function(t){Hy(t)&&ds(t)});var i=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&i.push("valueAxis","categoryAxis","logAxis","timeAxis"),Vy(i,function(e){Vy(fs(t[e]),function(t){t&&(us(t,"axisLabel"),us(t.axisPointer,"label"))})}),Vy(fs(t.parallel),function(t){var e=t&&t.parallelAxisDefault;us(e,"axisLabel"),us(e&&e.axisPointer,"label")}),Vy(fs(t.calendar),function(t){ls(t,"itemStyle"),us(t,"dayLabel"),us(t,"monthLabel"),us(t,"yearLabel")}),Vy(fs(t.radar),function(t){us(t,"name")}),Vy(fs(t.geo),function(t){Hy(t)&&(cs(t),Vy(fs(t.regions),function(t){cs(t)}))}),Vy(fs(t.timeline),function(t){cs(t),ls(t,"label"),ls(t,"itemStyle"),ls(t,"controlStyle",!0);var e=t.data;x(e)&&f(e,function(t){S(t)&&(ls(t,"label"),ls(t,"itemStyle"))})}),Vy(fs(t.toolbox),function(t){ls(t,"iconStyle"),Vy(t.feature,function(t){ls(t,"iconStyle")})}),us(ps(t.axisPointer),"label"),us(ps(t.tooltip).axisPointer,"label")},Uy=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Xy=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],jy=function(t,e){Gy(t,e),t.series=Rn(t.series),f(t.series,function(t){if(S(t)){var e=t.type;if(("pie"===e||"gauge"===e)&&null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===e){var i=gs(t,"pointer.color");null!=i&&vs(t,"itemStyle.normal.color",i)}ms(t)}}),t.dataRange&&(t.visualMap=t.dataRange),f(Xy,function(e){var i=t[e];i&&(x(i)||(i=[i]),f(i,function(t){ms(t)}))})},Yy=function(t){var e=N();t.eachSeries(function(t){var i=t.get("stack");if(i){var n=e.get(i)||e.set(i,[]),r=t.getData(),a={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!a.stackedDimension||!a.isStackedByIndex&&!a.stackedByDimension)return;n.length&&r.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(a)}}),e.each(ys)},qy=_s.prototype;qy.pure=!1,qy.persistent=!0,qy.getSource=function(){return this._source};var Zy={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:bs},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],i=this._data,n=0;n<i.length;n++){var r=i[n];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:xs,getItem:ws,appendData:bs},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],i=this._source.dimensionsDefine,n=0;n<i.length;n++){var r=this._data[i[n].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;f(t,function(t,i){for(var n=e[i]||(e[i]=[]),r=0;r<(t||[]).length;r++)n.push(t[r])})}},original:{count:xs,getItem:ws,appendData:bs},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var i=this._dimSize*t,n=0;n<this._dimSize;n++)e[n]=this._data[i+n];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}},$y={arrayRows:Ss,objectRows:function(t,e,i,n){return null!=i?t[n]:t},keyedColumns:Ss,original:function(t,e,i){var n=Fn(t);return null!=i&&n instanceof Array?n[i]:n},typedArray:Ss},Ky={arrayRows:Ms,objectRows:function(t,e){return Is(t[e],this._dimensionInfos[e])},keyedColumns:Ms,original:function(t,e,i,n){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&Vn(t)&&(this.hasItemOption=!0),Is(r instanceof Array?r[n]:r,this._dimensionInfos[e])},typedArray:function(t,e,i,n){return t[n]}},Qy=/\{@(.+?)\}/g,Jy={getDataParams:function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),r=i.getRawIndex(t),a=i.getName(t),o=i.getRawDataItem(t),s=i.getItemVisual(t,"color"),l=this.ecModel.getComponent("tooltip"),h=l&&l.get("renderMode"),u=Kn(h),c=this.mainType,d="series"===c;return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:d?this.subType:null,seriesIndex:this.seriesIndex,seriesId:d?this.id:null,seriesName:d?this.name:null,name:a,dataIndex:r,data:o,dataType:e,value:n,color:s,marker:yo({color:s,renderMode:u}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,i,n,r){e=e||"normal";var a=this.getData(i),o=a.getItemModel(t),s=this.getDataParams(t,i);null!=n&&s.value instanceof Array&&(s.value=s.value[n]);var l=o.get("normal"===e?[r||"label","formatter"]:[e,r||"label","formatter"]);if("function"==typeof l)return s.status=e,l(s);if("string"==typeof l){var h=vo(l,s);return h.replace(Qy,function(e,i){var n=i.length;return"["===i.charAt(0)&&"]"===i.charAt(n-1)&&(i=+i.slice(1,n-1)),Ts(a,t,i)})}},getRawValue:function(t,e){return Ts(this.getData(e),t)},formatTooltip:function(){}},t_=As.prototype;t_.perform=function(t){function e(t){return!(t>=1)&&(t=1),t}var i=this._upstream,n=t&&t.skip;if(this._dirty&&i){var r=this.context;r.data=r.outputData=i.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!n&&(a=this._plan(this.context));var o=e(this._modBy),s=this._modDataCount||0,l=e(t&&t.modBy),h=t&&t.modDataCount||0;(o!==l||s!==h)&&(a="reset");var u;(this._dirty||"reset"===a)&&(this._dirty=!1,u=Ps(this,n)),this._modBy=l,this._modDataCount=h;var c=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!n&&(u||f>d)){var p=this._progress;if(x(p))for(var g=0;g<p.length;g++)ks(this,p[g],d,f,l,h);else ks(this,p,d,f,l,h)}this._dueIndex=f;var v=null!=this._settedOutputEnd?this._settedOutputEnd:f;this._outputDueEnd=v}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var e_=function(){function t(){return i>n?n++:null}function e(){var t=n%o*r+Math.ceil(n/o),e=n>=i?null:a>t?t:n;return n++,e}var i,n,r,a,o,s={reset:function(l,h,u,c){n=l,i=h,r=u,a=c,o=Math.ceil(a/r),s.next=r>1&&a>0?e:t}};return s}();t_.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t_.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t_.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t_.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t_.getUpstream=function(){return this._upstream},t_.getDownstream=function(){return this._downstream},t_.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var i_=jn(),n_=vy.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.color",layoutMode:null,init:function(t,e,i){this.seriesIndex=this.componentIndex,this.dataTask=Ds({count:Es,reset:Bs}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,i),Ro(this);var n=this.getInitialData(t,i);Rs(n,this),this.dataTask.context.data=n,i_(this).dataBeforeProcessed=n,Ls(this)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?Co(t):{},a=this.subType;vy.hasClass(a)&&(a+="Series"),r(t,e.getTheme().get(this.subType)),r(t,this.getDefaultOption()),Nn(t,"label",["show"]),this.fillDataTextStyle(t.data),i&&To(t,n,i)},mergeOption:function(t,e){t=r(this.option,t,!0),this.fillDataTextStyle(t.data);var i=this.layoutMode;i&&To(this.option,t,i),Ro(this);var n=this.getInitialData(t,e);Rs(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,i_(this).dataBeforeProcessed=n,Ls(this)},fillDataTextStyle:function(t){if(t&&!I(t))for(var e=["show"],i=0;i<t.length;i++)t[i]&&t[i].label&&Nn(t[i],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=Fs(this);if(e){var i=e.context.data;return null==t?i:i.getLinkedData(t)}return i_(this).data},setData:function(t){var e=Fs(this);if(e){var i=e.context;i.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),i.outputData=t,e!==this.dataTask&&(i.data=t)}i_(this).data=t},getSource:function(){return Bo(this)},getRawData:function(){return i_(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,i,n){function r(i){function r(t,i){var r=c.getDimensionInfo(i);if(r&&r.otherDims.tooltip!==!1){var d=r.type,f="sub"+o.seriesIndex+"at"+u,p=yo({color:y,type:"subItem",renderMode:n,markerId:f}),g="string"==typeof p?p:p.content,v=(a?g+go(r.displayName||"-")+": ":"")+go("ordinal"===d?t+"":"time"===d?e?"":xo("yyyy/MM/dd hh:mm:ss",t):fo(t));v&&s.push(v),l&&(h[f]=y,++u)}}var a=g(i,function(t,e,i){var n=c.getDimensionInfo(i);return t|=n&&n.tooltip!==!1&&null!=n.displayName},0),s=[];d.length?f(d,function(e){r(Ts(c,t,e),e)}):f(i,r);var p=a?l?"\n":"<br/>":"",v=p+s.join(p||", ");return{renderMode:n,content:v,style:h}}function a(t){return{renderMode:n,content:go(fo(t)),style:h}}var o=this;n=n||"html";var s="html"===n?"<br/>":"\n",l="richText"===n,h={},u=0,c=this.getData(),d=c.mapDimension("defaultedTooltip",!0),p=d.length,v=this.getRawValue(t),m=x(v),y=c.getItemVisual(t,"color");S(y)&&y.colorStops&&(y=(y.colorStops[0]||{}).color),y=y||"transparent";var _=p>1||m&&!p?r(v):a(p?Ts(c,t,d[0]):m?v[0]:v),w=_.content,b=o.seriesIndex+"at"+u,M=yo({color:y,type:"item",renderMode:n,markerId:b});h[b]=y,++u;var I=c.getName(t),T=this.name;Gn(this)||(T=""),T=T?go(T)+(e?": ":s):"";var C="string"==typeof M?M:M.content,D=e?C+T+w:T+C+(I?go(I)+": "+w:w);return{html:D,markers:h}},isAnimationEnabled:function(){if(jf.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,i){var n=this.ecModel,r=xy.getColorFromPalette.call(this,t,e,i);return r||(r=n.getColorFromPalette(t,e,i)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});c(n_,Jy),c(n_,xy);var r_=function(){this.group=new ig,this.uid=Xa("viewComponent")};r_.prototype={constructor:r_,init:function(){},render:function(){},dispose:function(){},filterForExposedEvent:null};var a_=r_.prototype;a_.updateView=a_.updateLayout=a_.updateVisual=function(){},tr(r_),rr(r_,{registerWhenExtend:!0});var o_=function(){var t=jn();return function(e){var i=t(e),n=e.pipelineContext,r=i.large,a=i.progressiveRender,o=i.large=n.large,s=i.progressiveRender=n.progressiveRender;return!!(r^o||a^s)&&"reset"}},s_=jn(),l_=o_();Vs.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,n){Ws(t.getData(),n,"emphasis")},downplay:function(t,e,i,n){Ws(t.getData(),n,"normal")},remove:function(){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var h_=Vs.prototype;h_.updateView=h_.updateLayout=h_.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},tr(Vs,["dispose"]),rr(Vs,{registerWhenExtend:!0}),Vs.markUpdateMethod=function(t,e){s_(t).updateMethod=e};var u_={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},c_="\x00__throttleOriginMethod",d_="\x00__throttleRate",f_="\x00__throttleType",p_={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i=t.getData(),n=(t.visualColorAccessPath||"itemStyle.color").split("."),r=t.get(n)||t.getColorFromPalette(t.name,null,e.getSeriesCount());if(i.setVisual("color",r),!e.isSeriesFiltered(t)){"function"!=typeof r||r instanceof Om||i.each(function(e){i.setItemVisual(e,"color",r(t.getDataParams(e)))});var a=function(t,e){var i=t.getItemModel(e),r=i.get(n,!0);null!=r&&t.setItemVisual(e,"color",r)};return{dataEach:i.hasItemOption?a:null}}}},g_={toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},v_=function(t,e){function i(t,e){if("string"!=typeof t)return t;var i=t;return f(e,function(t,e){i=i.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),i}function n(t){var e=o.get(t);if(null==e){for(var i=t.split("."),n=g_.aria,r=0;r<i.length;++r)n=n[i[r]];return n}return e}function r(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function a(t){return g_.series.typeNames[t]||"自定义图"}var o=e.getModel("aria");if(o.get("show")){if(o.get("description"))return void t.setAttribute("aria-label",o.get("description"));var s=0;e.eachSeries(function(){++s},this);var l,h=o.get("data.maxCount")||10,u=o.get("series.maxCount")||10,c=Math.min(s,u);if(!(1>s)){var d=r();l=d?i(n("general.withTitle"),{title:d}):n("general.withoutTitle");var p=[],g=s>1?"series.multiple.prefix":"series.single.prefix";l+=i(n(g),{seriesCount:s}),e.eachSeries(function(t,e){if(c>e){var r,o=t.get("name"),l="series."+(s>1?"multiple":"single")+".";r=n(o?l+"withName":l+"withoutName"),r=i(r,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:a(t.subType)});var u=t.getData();window.data=u,r+=u.count()>h?i(n("data.partialData"),{displayCnt:h}):n("data.allData");for(var d=[],f=0;f<u.count();f++)if(h>f){var g=u.getName(f),v=Ts(u,f);d.push(i(n(g?"data.withName":"data.withoutName"),{name:g,value:v}))}r+=d.join(n("data.separator.middle"))+n("data.separator.end"),p.push(r)}}),l+=p.join(n("series.multiple.separator.middle"))+n("series.multiple.separator.end"),t.setAttribute("aria-label",l)}}},m_=Math.PI,y_=function(t,e){e=e||{},s(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var i=new Tm({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),n=new Pm({shape:{startAngle:-m_/2,endAngle:-m_/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),r=new Tm({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});n.animateShape(!0).when(1e3,{endAngle:3*m_/2}).start("circularInOut"),n.animateShape(!0).when(1e3,{startAngle:3*m_/2}).delay(300).start("circularInOut");var a=new ig;return a.add(n),a.add(r),a.add(i),a.resize=function(){var e=t.getWidth()/2,a=t.getHeight()/2;n.setShape({cx:e,cy:a});var o=n.shape.r;r.setShape({x:e-o,y:a-o,width:2*o,height:2*o}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},a.resize(),a},__=Ys.prototype;__.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},__.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,r=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,a=r?i.step:null,o=n&&n.modDataCount,s=null!=o?Math.ceil(o/a):null;return{step:a,modBy:s,modDataCount:o}}},__.getPipeline=function(t){return this._pipelineMap.get(t)},__.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),r=n.count(),a=i.progressiveEnabled&&e.incrementalPrepareRender&&r>=i.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=i.context={progressiveRender:a,modDataCount:s,large:o}},__.restorePipelines=function(t){var e=this,i=e._pipelineMap=N();t.eachSeries(function(t){var n=t.getProgressive(),r=t.uid;i.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:n&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(n||700),count:0}),al(e,t,t.dataTask)})},__.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),i=this.api;f(this._allHandlers,function(n){var r=t.get(n.uid)||t.set(n.uid,[]);n.reset&&Zs(this,n,r,e,i),n.overallReset&&$s(this,n,r,e,i)},this)},__.prepareView=function(t,e,i,n){var r=t.renderTask,a=r.context;a.model=e,a.ecModel=i,a.api=n,r.__block=!t.incrementalPrepareRender,al(this,e,r)},__.performDataProcessorTasks=function(t,e){qs(this,this._dataProcessorHandlers,t,e,{block:!0})},__.performVisualTasks=function(t,e,i){qs(this,this._visualHandlers,t,e,i)},__.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},__.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var x_=__.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},w_=nl(0);Ys.wrapStageHandler=function(t,e){return w(t)&&(t={overallReset:t,seriesType:ol(t)}),t.uid=Xa("stageHandler"),e&&(t.visualType=e),t};var b_,S_={},M_={};sl(S_,Ly),sl(M_,Jo),S_.eachSeriesByType=S_.eachRawSeriesByType=function(t){b_=t},S_.eachComponent=function(t){"series"===t.mainType&&t.subType&&(b_=t.subType)};var I_=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],T_={color:I_,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],I_]},C_="#eee",D_=function(){return{axisLine:{lineStyle:{color:C_}},axisTick:{lineStyle:{color:C_}},axisLabel:{textStyle:{color:C_}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:C_}}}},A_=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],k_={color:A_,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:C_},crossStyle:{color:C_}}},legend:{textStyle:{color:C_}},textStyle:{color:C_},title:{textStyle:{color:C_}},toolbox:{iconStyle:{normal:{borderColor:C_}}},dataZoom:{textStyle:{color:C_}},visualMap:{textStyle:{color:C_}},timeline:{lineStyle:{color:C_},itemStyle:{normal:{color:A_[1]}},label:{normal:{textStyle:{color:C_}}},controlStyle:{normal:{color:C_,borderColor:C_}}},timeAxis:D_(),logAxis:D_(),valueAxis:D_(),categoryAxis:D_(),line:{symbol:"circle"},graph:{color:A_},gauge:{title:{textStyle:{color:C_}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};k_.categoryAxis.splitLine.show=!1,vy.extend({type:"dataset",defaultOption:{seriesLayoutBy:Dy,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){Eo(this)}}),r_.extend({type:"dataset"});var P_=Nr.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var i=.5522848,n=e.cx,r=e.cy,a=e.rx,o=e.ry,s=a*i,l=o*i;t.moveTo(n-a,r),t.bezierCurveTo(n-a,r-l,n-s,r-o,n,r-o),t.bezierCurveTo(n+s,r-o,n+a,r-l,n+a,r),t.bezierCurveTo(n+a,r+l,n+s,r+o,n,r+o),t.bezierCurveTo(n-s,r+o,n-a,r+l,n-a,r),t.closePath()}}),L_=/[\s,]+/;hl.prototype.parse=function(t,e){e=e||{};var i=ll(t);if(!i)throw new Error("Illegal svg");var n=new ig;this._root=n;var r=i.getAttribute("viewBox")||"",a=parseFloat(i.getAttribute("width")||e.width),o=parseFloat(i.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(o)&&(o=null),fl(i,n,null,!0);for(var s=i.firstChild;s;)this._parseNode(s,n),s=s.nextSibling;var l,h;if(r){var u=E(r).split(L_);u.length>=4&&(l={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])})}if(l&&null!=a&&null!=o&&(h=ml(l,a,o),!e.ignoreViewBox)){var c=n;n=new ig,n.add(c),c.scale=h.scale.slice(),c.position=h.position.slice()}return e.ignoreRootClip||null==a||null==o||n.setClipPath(new Tm({shape:{x:0,y:0,width:a,height:o}})),{root:n,width:a,height:o,viewBoxRect:l,viewBoxTransform:h}},hl.prototype._parseNode=function(t,e){var i=t.nodeName.toLowerCase();"defs"===i?this._isDefine=!0:"text"===i&&(this._isText=!0);var n;if(this._isDefine){var r=E_[i];if(r){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{var r=O_[i];r&&(n=r.call(this,t,e),e.add(n))}for(var s=t.firstChild;s;)1===s.nodeType&&this._parseNode(s,n),3===s.nodeType&&this._isText&&this._parseText(s,n),s=s.nextSibling;"defs"===i?this._isDefine=!1:"text"===i&&(this._isText=!1)},hl.prototype._parseText=function(t,e){if(1===t.nodeType){var i=t.getAttribute("dx")||0,n=t.getAttribute("dy")||0;this._textX+=parseFloat(i),this._textY+=parseFloat(n)}var r=new pm({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});cl(e,r),fl(t,r,this._defs);var a=r.style.fontSize;a&&9>a&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=a/9,r.scale[1]*=a/9);var o=r.getBoundingRect();return this._textX+=o.width,e.add(r),r};var O_={g:function(t,e){var i=new ig;return cl(e,i),fl(t,i,this._defs),i},rect:function(t,e){var i=new Tm;return cl(e,i),fl(t,i,this._defs),i.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),i},circle:function(t,e){var i=new gm;return cl(e,i),fl(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),i},line:function(t,e){var i=new Dm;return cl(e,i),fl(t,i,this._defs),i.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),i},ellipse:function(t,e){var i=new P_;return cl(e,i),fl(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),i},polygon:function(t,e){var i=t.getAttribute("points");i&&(i=dl(i));var n=new bm({shape:{points:i||[]}});return cl(e,n),fl(t,n,this._defs),n},polyline:function(t,e){var i=new Nr;cl(e,i),fl(t,i,this._defs);var n=t.getAttribute("points");n&&(n=dl(n));var r=new Sm({shape:{points:n||[]}});return r},image:function(t,e){var i=new xn;return cl(e,i),fl(t,i,this._defs),i.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),i},text:function(t,e){var i=t.getAttribute("x")||0,n=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0;this._textX=parseFloat(i)+parseFloat(r),this._textY=parseFloat(n)+parseFloat(a);var o=new ig;return cl(e,o),fl(t,o,this._defs),o},tspan:function(t,e){var i=t.getAttribute("x"),n=t.getAttribute("y");null!=i&&(this._textX=parseFloat(i)),null!=n&&(this._textY=parseFloat(n));var r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0,o=new ig;return cl(e,o),fl(t,o,this._defs),this._textX+=r,this._textY+=a,o},path:function(t,e){var i=t.getAttribute("d")||"",n=Wr(i);return cl(e,n),fl(t,n,this._defs),n}},E_={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),i=parseInt(t.getAttribute("y1")||0,10),n=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),a=new Em(e,i,n,r);return ul(t,a),a},radialgradient:function(){}},B_={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"},z_=/url\(\s*#(.*?)\)/,R_=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g,N_=/([^\s:;]+)\s*:\s*([^:;]+)/g,F_=N(),V_={registerMap:function(t,e,i){var n;return x(e)?n=e:e.svg?n=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(i=e.specialAreas,e=e.geoJson),n=[{type:"geoJSON",source:e,specialAreas:i}]),f(n,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var i=H_[e];i(t)}),F_.set(t,n)},retrieveMap:function(t){return F_.get(t)}},H_={geoJSON:function(t){var e=t.source;t.geoJSON=b(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=ll(t.source)}},W_=O,G_=f,U_=w,X_=S,j_=vy.parseClassType,Y_="4.2.1",q_={zrender:"4.0.6"},Z_=1,$_=1e3,K_=5e3,Q_=1e3,J_=2e3,tx=3e3,ex=4e3,ix=5e3,nx={PROCESSOR:{FILTER:$_,STATISTIC:K_},VISUAL:{LAYOUT:Q_,GLOBAL:J_,CHART:tx,COMPONENT:ex,BRUSH:ix}},rx="__flagInMainProcess",ax="__optionUpdated",ox=/^[a-zA-Z0-9_]+$/;_l.prototype.on=yl("on"),_l.prototype.off=yl("off"),_l.prototype.one=yl("one"),c(_l,pp);var sx=xl.prototype;sx._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[ax]){var e=this[ax].silent;this[rx]=!0,bl(this),lx.update.call(this),this[rx]=!1,this[ax]=!1,Tl.call(this,e),Cl.call(this,e)}else if(t.unfinished){var i=Z_,n=this._model,r=this._api;t.unfinished=!1;do{var a=+new Date;t.performSeriesTasks(n),t.performDataProcessorTasks(n),Ml(this,n),t.performVisualTasks(n),Ol(this,this._model,r,"remain"),i-=+new Date-a}while(i>0&&t.unfinished);t.unfinished||this._zr.flush()}}},sx.getDom=function(){return this._dom},sx.getZr=function(){return this._zr},sx.setOption=function(t,e,i){var n;if(X_(e)&&(i=e.lazyUpdate,n=e.silent,e=e.notMerge),this[rx]=!0,!this._model||e){var r=new es(this._api),a=this._theme,o=this._model=new Ly(null,null,a,r);o.scheduler=this._scheduler,o.init(null,null,a,r)}this._model.setOption(t,fx),i?(this[ax]={silent:n},this[rx]=!1):(bl(this),lx.update.call(this),this._zr.flush(),this[ax]=!1,this[rx]=!1,Tl.call(this,n),Cl.call(this,n))},sx.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},sx.getModel=function(){return this._model},sx.getOption=function(){return this._model&&this._model.getOption()},sx.getWidth=function(){return this._zr.getWidth()},sx.getHeight=function(){return this._zr.getHeight()},sx.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},sx.getRenderedCanvas=function(t){if(jf.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},sx.getSvgDataUrl=function(){if(jf.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return f(e,function(t){t.stopAnimation(!0)}),t.painter.pathToDataUrl()}},sx.getDataURL=function(t){t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;G_(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(n.push(e),e.group.ignore=!0)})});var a="svg"===this._zr.painter.getType()?this.getSvgDataUrl():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return G_(n,function(t){t.group.ignore=!1}),a},sx.getConnectedDataURL=function(t){if(jf.canvasSupported){var e=this.group,i=Math.min,r=Math.max,a=1/0;if(_x[e]){var o=a,s=a,l=-a,h=-a,u=[],c=t&&t.pixelRatio||1;f(yx,function(a){if(a.group===e){var c=a.getRenderedCanvas(n(t)),d=a.getDom().getBoundingClientRect();o=i(d.left,o),s=i(d.top,s),l=r(d.right,l),h=r(d.bottom,h),u.push({dom:c,left:d.left,top:d.top})}}),o*=c,s*=c,l*=c,h*=c;var d=l-o,p=h-s,g=np();g.width=d,g.height=p;var v=Ln(g);return G_(u,function(t){var e=new xn({style:{x:t.left*c-o,y:t.top*c-s,image:t.dom}});v.add(e)}),v.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},sx.convertToPixel=_(wl,"convertToPixel"),sx.convertFromPixel=_(wl,"convertFromPixel"),sx.containPixel=function(t,e){var i,n=this._model;return t=Yn(n,t),f(t,function(t,n){n.indexOf("Models")>=0&&f(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)i|=!!r.containPoint(e);else if("seriesModels"===n){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(i|=a.containPoint(e,t))}},this)},this),!!i},sx.getVisual=function(t,e){var i=this._model;t=Yn(i,t,{defaultMainType:"series"});var n=t.seriesModel,r=n.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},sx.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},sx.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var lx={prepareAndUpdate:function(t){bl(this),lx.update.call(this,t)},update:function(t){var e=this._model,i=this._api,n=this._zr,r=this._coordSysMgr,a=this._scheduler;if(e){a.restoreData(e,t),a.performSeriesTasks(e),r.create(e,i),a.performDataProcessorTasks(e,t),Ml(this,e),r.update(e,i),kl(e),a.performVisualTasks(e,t),Pl(this,e,i,t);var o=e.get("backgroundColor")||"transparent";if(jf.canvasSupported)n.setBackgroundColor(o);else{var s=Xe(o);o=ti(s,"rgb"),0===s[3]&&(o="transparent")}El(e,i)}},updateTransform:function(t){var e=this._model,i=this,n=this._api;if(e){var r=[];e.eachComponent(function(a,o){var s=i.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(o,e,n,t);l&&l.update&&r.push(s)}else r.push(s)});var a=N();e.eachSeries(function(r){var o=i._chartsMap[r.__viewId];if(o.updateTransform){var s=o.updateTransform(r,e,n,t);s&&s.update&&a.set(r.uid,1)}else a.set(r.uid,1)}),kl(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:a}),Ol(i,e,n,t,a),El(e,this._api)}},updateView:function(t){var e=this._model;e&&(Vs.markUpdateMethod(t,"updateView"),kl(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Pl(this,this._model,this._api,t),El(e,this._api))},updateVisual:function(t){lx.update.call(this,t)},updateLayout:function(t){lx.update.call(this,t)}};sx.resize=function(t){this._zr.resize(t);
var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var i=e.resetOption("media"),n=t&&t.silent;this[rx]=!0,i&&bl(this),lx.update.call(this),this[rx]=!1,Tl.call(this,n),Cl.call(this,n)}},sx.showLoading=function(t,e){if(X_(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),mx[t]){var i=mx[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)}},sx.hideLoading=function(){this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},sx.makeActionFromEvent=function(t){var e=o({},t);return e.type=cx[t.type],e},sx.dispatchAction=function(t,e){if(X_(e)||(e={silent:!!e}),ux[t.type]&&this._model){if(this[rx])return void this._pendingActions.push(t);Il.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&jf.browser.weChat&&this._throttledZrFlush(),Tl.call(this,e.silent),Cl.call(this,e.silent)}},sx.appendData=function(t){var e=t.seriesIndex,i=this.getModel(),n=i.getSeriesByIndex(e);n.appendData(t),this._scheduler.unfinished=!0},sx.on=yl("on"),sx.off=yl("off"),sx.one=yl("one");var hx=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];sx._initEvents=function(){G_(hx,function(t){var e=function(e){var i,n=this.getModel(),r=e.target,a="globalout"===t;if(a)i={};else if(r&&null!=r.dataIndex){var s=r.dataModel||n.getSeriesByIndex(r.seriesIndex);i=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(i=o({},r.eventData));if(i){var l=i.componentType,h=i.componentIndex;("markLine"===l||"markPoint"===l||"markArea"===l)&&(l="series",h=i.seriesIndex);var u=l&&null!=h&&n.getComponent(l,h),c=u&&this["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];i.event=e,i.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:i,model:u,view:c},this.trigger(t,i)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)},this),G_(cx,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},sx.isDisposed=function(){return this._disposed},sx.clear=function(){this.setOption({series:[]},!0)},sx.dispose=function(){if(!this._disposed){this._disposed=!0,Zn(this.getDom(),Sx,"");var t=this._api,e=this._model;G_(this._componentsViews,function(i){i.dispose(e,t)}),G_(this._chartsViews,function(i){i.dispose(e,t)}),this._zr.dispose(),delete yx[this.id]}},c(xl,pp),Fl.prototype={constructor:Fl,normalizeQuery:function(t){var e={},i={},n={};if(b(t)){var r=j_(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var a=["Index","Name","Id"],o={name:1,dataIndex:1,dataType:1};f(t,function(t,r){for(var s=!1,l=0;l<a.length;l++){var h=a[l],u=r.lastIndexOf(h);if(u>0&&u===r.length-h.length){var c=r.slice(0,u);"data"!==c&&(e.mainType=c,e[h.toLowerCase()]=t,s=!0)}}o.hasOwnProperty(r)&&(i[r]=t,s=!0),s||(n[r]=t)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},filter:function(t,e){function i(t,e,i,n){return null==t[i]||e[n||i]===t[i]}var n=this.eventInfo;if(!n)return!0;var r=n.targetEl,a=n.packedEvent,o=n.model,s=n.view;if(!o||!s)return!0;var l=e.cptQuery,h=e.dataQuery;return i(l,o,"mainType")&&i(l,o,"subType")&&i(l,o,"index","componentIndex")&&i(l,o,"name")&&i(l,o,"id")&&i(h,a,"name")&&i(h,a,"dataIndex")&&i(h,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,a))},afterTrigger:function(){this.eventInfo=null}};var ux={},cx={},dx=[],fx=[],px=[],gx=[],vx={},mx={},yx={},_x={},xx=new Date-0,bx=new Date-0,Sx="_echarts_instance_",Mx=Gl;eh(J_,p_),ql(jy),Zl(K_,Yy),nh("default",y_),Kl({type:"highlight",event:"highlight",update:"highlight"},V),Kl({type:"downplay",event:"downplay",update:"downplay"},V),Yl("light",T_),Yl("dark",k_);var Ix={};dh.prototype={constructor:dh,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,i=this._new,n={},r={},a=[],o=[];for(fh(e,n,a,"_oldKeyGetter",this),fh(i,r,o,"_newKeyGetter",this),t=0;t<e.length;t++){var s=a[t],l=r[s];if(null!=l){var h=l.length;h?(1===h&&(r[s]=null),l=l.unshift()):r[s]=null,this._update&&this._update(l,t)}else this._remove&&this._remove(t)}for(var t=0;t<o.length;t++){var s=o[t];if(r.hasOwnProperty(s)){var l=r[s];if(null==l)continue;if(l.length)for(var u=0,h=l.length;h>u;u++)this._add&&this._add(l[u]);else this._add&&this._add(l)}}}};var Tx=N(["tooltip","label","itemName","itemId","seriesName"]),Cx=S,Dx="undefined",Ax=-1,kx="e\x00\x00",Px={"float":typeof Float64Array===Dx?Array:Float64Array,"int":typeof Int32Array===Dx?Array:Int32Array,ordinal:Array,number:Array,time:Array},Lx=typeof Uint32Array===Dx?Array:Uint32Array,Ox=typeof Int32Array===Dx?Array:Int32Array,Ex=typeof Uint16Array===Dx?Array:Uint16Array,Bx=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],zx=["_extent","_approximateExtent","_rawExtent"],Rx=function(t,e){t=t||["x","y"];for(var i={},n=[],r={},a=0;a<t.length;a++){var o=t[a];b(o)&&(o={name:o});var s=o.name;o.type=o.type||"float",o.coordDim||(o.coordDim=s,o.coordDimIndex=0),o.otherDims=o.otherDims||{},n.push(s),i[s]=o,o.index=a,o.createInvertedIndices&&(r[s]=[])}this.dimensions=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=ph(this),this._invertedIndicesMap=r,this._calculationInfo={}},Nx=Rx.prototype;Nx.type="list",Nx.hasItemOption=!0,Nx.getDimension=function(t){return isNaN(t)||(t=this.dimensions[t]||t),t},Nx.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},Nx.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},Nx.mapDimension=function(t,e){var i=this._dimensionsSummary;if(null==e)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return e===!0?(n||[]).slice():n&&n[e]},Nx.initData=function(t,e,i){var n=Oo.isInstance(t)||d(t);n&&(t=new _s(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},i||(this.hasItemOption=!1),this.defaultDimValueGetter=Ky[this._rawData.getSource().sourceFormat],this._dimValueGetter=i=i||this.defaultDimValueGetter,this._dimValueGetterArrayRows=Ky.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},Nx.getProvider=function(){return this._rawData},Nx.appendData=function(t){var e=this._rawData,i=this.count();e.appendData(t);var n=e.count();e.persistent||(n+=i),this._initDataFromProvider(i,n)},Nx.appendValues=function(t,e){for(var i=this._chunkSize,n=this._storage,r=this.dimensions,a=r.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),h=this._chunkCount,u=0;a>u;u++){var c=r[u];o[c]||(o[c]=Ah()),n[c]||(n[c]=[]),xh(n,this._dimensionInfos[c],i,h,l),this._chunkCount=n[c].length}for(var d=new Array(a),f=s;l>f;f++){for(var p=f-s,g=Math.floor(f/i),v=f%i,m=0;a>m;m++){var c=r[m],y=this._dimValueGetterArrayRows(t[p]||d,c,p,m);n[c][g][v]=y;var _=o[c];y<_[0]&&(_[0]=y),y>_[1]&&(_[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},wh(this)},Nx._initDataFromProvider=function(t,e){if(!(t>=e)){for(var i,n=this._chunkSize,r=this._rawData,a=this._storage,o=this.dimensions,s=o.length,l=this._dimensionInfos,h=this._nameList,u=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;s>p;p++){var g=o[p];c[g]||(c[g]=Ah());var v=l[g];0===v.otherDims.itemName&&(i=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),a[g]||(a[g]=[]),xh(a,v,n,f,e),this._chunkCount=a[g].length}for(var m=new Array(s),y=t;e>y;y++){m=r.getItem(y,m);for(var _=Math.floor(y/n),x=y%n,w=0;s>w;w++){var g=o[w],b=a[g][_],S=this._dimValueGetter(m,g,y,w);b[x]=S;var M=c[g];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var I=h[y];if(m&&null==I)if(null!=m.name)h[y]=I=m.name;else if(null!=i){var T=o[i],C=a[T][_];if(C){I=C[x];var D=l[T].ordinalMeta;D&&D.categories.length&&(I=D.categories[I])}}var A=null==m?null:m.id;null==A&&null!=I&&(d[I]=d[I]||0,A=I,d[I]>0&&(A+="__ec__"+d[I]),d[I]++),null!=A&&(u[y]=A)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},wh(this)}},Nx.count=function(){return this._count},Nx.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var r=0;n>r;r++)t[r]=e[r]}else t=new i(e.buffer,0,n)}else for(var i=mh(this),t=new i(this.count()),r=0;r<t.length;r++)t[r]=r;return t},Nx.get=function(t,e){if(!(e>=0&&e<this._count))return 0/0;var i=this._storage;if(!i[t])return 0/0;e=this.getRawIndex(e);var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[t][n],o=a[r];return o},Nx.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return 0/0;var i=this._storage[t];if(!i)return 0/0;var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[n];return a[r]},Nx._getFast=function(t,e){var i=Math.floor(e/this._chunkSize),n=e%this._chunkSize,r=this._storage[t][i];return r[n]},Nx.getValues=function(t,e){var i=[];x(t)||(e=t,t=this.dimensions);for(var n=0,r=t.length;r>n;n++)i.push(this.get(t[n],e));return i},Nx.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,i=this._dimensionInfos,n=0,r=e.length;r>n;n++)if("ordinal"!==i[e[n]].type&&isNaN(this.get(e[n],t)))return!1;return!0},Nx.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],i=Ah();if(!e)return i;var n,r=this.count(),a=!this._indices;if(a)return this._rawExtent[t].slice();if(n=this._extent[t])return n.slice();n=i;for(var o=n[0],s=n[1],l=0;r>l;l++){var h=this._getFast(t,this.getRawIndex(l));o>h&&(o=h),h>s&&(s=h)}return n=[o,s],this._extent[t]=n,n},Nx.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},Nx.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},Nx.getCalculationInfo=function(t){return this._calculationInfo[t]},Nx.setCalculationInfo=function(t,e){Cx(t)?o(this._calculationInfo,t):this._calculationInfo[t]=e},Nx.getSum=function(t){var e=this._storage[t],i=0;if(e)for(var n=0,r=this.count();r>n;n++){var a=this.get(t,n);isNaN(a)||(i+=a)}return i},Nx.getMedian=function(t){var e=[];this.each(t,function(t){isNaN(t)||e.push(t)});var i=[].concat(e).sort(function(t,e){return t-e}),n=this.count();return 0===n?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},Nx.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i[e];return null==n||isNaN(n)?Ax:n},Nx.indexOfName=function(t){for(var e=0,i=this.count();i>e;e++)if(this.getName(e)===t)return e;return-1},Nx.indexOfRawIndex=function(t){if(!this._indices)return t;if(t>=this._rawCount||0>t)return-1;var e=this._indices,i=e[t];if(null!=i&&i<this._count&&i===t)return t;for(var n=0,r=this._count-1;r>=n;){var a=(n+r)/2|0;if(e[a]<t)n=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},Nx.indicesOfNearest=function(t,e,i){var n=this._storage,r=n[t],a=[];if(!r)return a;null==i&&(i=1/0);for(var o=Number.MAX_VALUE,s=-1,l=0,h=this.count();h>l;l++){var u=e-this.get(t,l),c=Math.abs(u);i>=u&&o>=c&&((o>c||u>=0&&0>s)&&(o=c,s=u,a.length=0),a.push(l))}return a},Nx.getRawIndex=Sh,Nx.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],i=0;i<this.dimensions.length;i++){var n=this.dimensions[i];e.push(this.get(n,t))}return e},Nx.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||bh(this,this._nameDimIdx,e)||""},Nx.getId=function(t){return Ih(this,this.getRawIndex(t))},Nx.each=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(Th(t),this.getDimension,this);for(var r=t.length,a=0;a<this.count();a++)switch(r){case 0:e.call(i,a);break;case 1:e.call(i,this.get(t[0],a),a);break;case 2:e.call(i,this.get(t[0],a),this.get(t[1],a),a);break;default:for(var o=0,s=[];r>o;o++)s[o]=this.get(t[o],a);s[o]=a,e.apply(i,s)}}},Nx.filterSelf=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(Th(t),this.getDimension,this);for(var r=this.count(),a=mh(this),o=new a(r),s=[],l=t.length,h=0,u=t[0],c=0;r>c;c++){var d,f=this.getRawIndex(c);if(0===l)d=e.call(i,c);else if(1===l){var g=this._getFast(u,f);d=e.call(i,g,c)}else{for(var v=0;l>v;v++)s[v]=this._getFast(u,f);s[v]=c,d=e.apply(i,s)}d&&(o[h++]=f)}return r>h&&(this._indices=o),this._count=h,this._extent={},this.getRawIndex=this._indices?Mh:Sh,this}},Nx.selectRange=function(t){if(this._count){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);var n=e.length;if(n){var r=this.count(),a=mh(this),o=new a(r),s=0,l=e[0],h=t[l][0],u=t[l][1],c=!1;if(!this._indices){var d=0;if(1===n){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m];(y>=h&&u>=y||isNaN(y))&&(o[s++]=d),d++}c=!0}else if(2===n){for(var f=this._storage[l],_=this._storage[e[1]],x=t[e[1]][0],w=t[e[1]][1],p=0;p<this._chunkCount;p++)for(var g=f[p],b=_[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m],S=b[m];(y>=h&&u>=y||isNaN(y))&&(S>=x&&w>=S||isNaN(S))&&(o[s++]=d),d++}c=!0}}if(!c)if(1===n)for(var m=0;r>m;m++){var M=this.getRawIndex(m),y=this._getFast(l,M);(y>=h&&u>=y||isNaN(y))&&(o[s++]=M)}else for(var m=0;r>m;m++){for(var I=!0,M=this.getRawIndex(m),p=0;n>p;p++){var T=e[p],y=this._getFast(i,M);(y<t[T][0]||y>t[T][1])&&(I=!1)}I&&(o[s++]=this.getRawIndex(m))}return r>s&&(this._indices=o),this._count=s,this._extent={},this.getRawIndex=this._indices?Mh:Sh,this}}},Nx.mapArray=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i),r},Nx.map=function(t,e,i,n){i=i||n||this,t=p(Th(t),this.getDimension,this);var r=Ch(this,t);r._indices=this._indices,r.getRawIndex=r._indices?Mh:Sh;for(var a=r._storage,o=[],s=this._chunkSize,l=t.length,h=this.count(),u=[],c=r._rawExtent,d=0;h>d;d++){for(var f=0;l>f;f++)u[f]=this.get(t[f],d);u[l]=d;var g=e&&e.apply(i,u);if(null!=g){"object"!=typeof g&&(o[0]=g,g=o);for(var v=this.getRawIndex(d),m=Math.floor(v/s),y=v%s,_=0;_<g.length;_++){var x=t[_],w=g[_],b=c[x],S=a[x];S&&(S[m][y]=w),w<b[0]&&(b[0]=w),w>b[1]&&(b[1]=w)}}}return r},Nx.downSample=function(t,e,i,n){for(var r=Ch(this,[t]),a=r._storage,o=[],s=Math.floor(1/e),l=a[t],h=this.count(),u=this._chunkSize,c=r._rawExtent[t],d=new(mh(this))(h),f=0,p=0;h>p;p+=s){s>h-p&&(s=h-p,o.length=s);for(var g=0;s>g;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/u),y=v%u;o[g]=l[m][y]}var _=i(o),x=this.getRawIndex(Math.min(p+n(o,_)||0,h-1)),w=Math.floor(x/u),b=x%u;l[w][b]=_,_<c[0]&&(c[0]=_),_>c[1]&&(c[1]=_),d[f++]=x}return r._count=f,r._indices=d,r.getRawIndex=Mh,r},Nx.getItemModel=function(t){var e=this.hostModel;return new Wa(this.getRawDataItem(t),e,e&&e.ecModel)},Nx.diff=function(t){var e=this;return new dh(t?t.getIndices():[],this.getIndices(),function(e){return Ih(t,e)},function(t){return Ih(e,t)})},Nx.getVisual=function(t){var e=this._visual;return e&&e[t]},Nx.setVisual=function(t,e){if(Cx(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},Nx.setLayout=function(t,e){if(Cx(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},Nx.getLayout=function(t){return this._layout[t]},Nx.getItemLayout=function(t){return this._itemLayouts[t]},Nx.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?o(this._itemLayouts[t]||{},e):e},Nx.clearItemLayouts=function(){this._itemLayouts.length=0},Nx.getItemVisual=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},Nx.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=n,Cx(e))for(var a in e)e.hasOwnProperty(a)&&(n[a]=e[a],r[a]=!0);else n[e]=i,r[e]=!0},Nx.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var Fx=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};Nx.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e.traverse(Fx,e)),this._graphicEls[t]=e},Nx.getItemGraphicEl=function(t){return this._graphicEls[t]},Nx.eachItemGraphicEl=function(t,e){f(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},Nx.cloneShallow=function(t){if(!t){var e=p(this.dimensions,this.getDimensionInfo,this);t=new Rx(e,this.hostModel)}if(t._storage=this._storage,_h(t,this),this._indices){var i=this._indices.constructor;t._indices=new i(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?Mh:Sh,t},Nx.wrapMethod=function(t,e){var i=this[t];"function"==typeof i&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(P(arguments)))})},Nx.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],Nx.CHANGABLE_METHODS=["filterSelf","selectRange"];var Vx=function(t,e){return e=e||{},kh(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})};Fh.prototype.parse=function(t){return t},Fh.prototype.getSetting=function(t){return this._setting[t]},Fh.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},Fh.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},Fh.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},Fh.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Fh.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Fh.prototype.getExtent=function(){return this._extent.slice()},Fh.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},Fh.prototype.isBlank=function(){return this._isBlank},Fh.prototype.setBlank=function(t){this._isBlank=t},Fh.prototype.getLabel=null,tr(Fh),rr(Fh,{registerWhenExtend:!0}),Vh.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&p(i,Wh);return new Vh({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})};var Hx=Vh.prototype;Hx.getOrdinal=function(t){return Hh(this).get(t)},Hx.parseAndCollect=function(t){var e,i=this._needCollect;if("string"!=typeof t&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=Hh(this);return e=n.get(t),null==e&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=0/0),e};var Wx=Fh.prototype,Gx=Fh.extend({type:"ordinal",init:function(t,e){(!t||x(t))&&(t=new Vh({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),Wx.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return Wx.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(Wx.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this.isBlank()?void 0:this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:V,niceExtent:V});Gx.create=function(){return new Gx};var Ux=Ka,Xx=Ka,jx=Fh.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),jx.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Uh(t)},getTicks:function(){return Yh(this._interval,this._extent,this._niceExtent,this._intervalPrecision)},getLabel:function(t,e){if(null==t)return"";var i=e&&e.precision;return null==i?i=to(t)||0:"auto"===i&&(i=this._intervalPrecision),t=Xx(t,i,!0),fo(t)},niceTicks:function(t,e,i){t=t||5;var n=this._extent,r=n[1]-n[0];if(isFinite(r)){0>r&&(r=-r,n.reverse());var a=Gh(n,t,e,i);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var i=e[0];t.fixMax?e[0]-=i/2:(e[1]+=i/2,e[0]-=i/2)}else e[1]=1;var n=e[1]-e[0];isFinite(n)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=Xx(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=Xx(Math.ceil(e[1]/r)*r))}});jx.create=function(){return new jx};var Yx="__ec_stack_",qx=.5,Zx="undefined"!=typeof Float32Array?Float32Array:Array,$x={seriesType:"bar",plan:o_(),reset:function(t){function e(t,e){for(var i,c=new Zx(2*t.count),d=[],f=[],p=0;null!=(i=t.next());)f[h]=e.get(o,i),f[1-h]=e.get(s,i),d=n.dataToPoint(f,null,d),c[p++]=d[0],c[p++]=d[1];e.setLayout({largePoints:c,barWidth:u,valueAxisStart:nu(r,a,!1),valueAxisHorizontal:l})}if(eu(t)&&iu(t)){var i=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),a=n.getOtherAxis(r),o=i.mapDimension(a.dim),s=i.mapDimension(r.dim),l=a.isHorizontal(),h=l?0:1,u=Jh(Kh([t]),r,t).width;return u>qx||(u=qx),{progress:e}}}},Kx=jx.prototype,Qx=Math.ceil,Jx=Math.floor,tw=1e3,ew=60*tw,iw=60*ew,nw=24*iw,rw=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][1]<e?i=r+1:n=r}return i},aw=jx.extend({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return xo(e[0],i,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=nw,e[1]+=nw),e[1]===-1/0&&1/0===e[0]){var i=new Date;e[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),e[0]=e[1]-nw}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var n=this._interval;t.fixMin||(e[0]=Ka(Jx(e[0]/n)*n)),t.fixMax||(e[1]=Ka(Qx(e[1]/n)*n))},niceTicks:function(t,e,i){t=t||10;var n=this._extent,r=n[1]-n[0],a=r/t;null!=e&&e>a&&(a=e),null!=i&&a>i&&(a=i);var o=ow.length,s=rw(ow,a,0,o),l=ow[Math.min(s,o-1)],h=l[1];if("year"===l[0]){var u=r/h,c=lo(u/t,!0);h*=c}var d=this.getSetting("useUTC")?0:60*new Date(+n[0]||+n[1]).getTimezoneOffset()*1e3,f=[Math.round(Qx((n[0]-d)/h)*h+d),Math.round(Jx((n[1]-d)/h)*h+d)];jh(f,n),this._stepLvl=l,this._interval=h,this._niceExtent=f},parse:function(t){return+ao(t)}});f(["contain","normalize"],function(t){aw.prototype[t]=function(e){return Kx[t].call(this,this.parse(e))}});var ow=[["hh:mm:ss",tw],["hh:mm:ss",5*tw],["hh:mm:ss",10*tw],["hh:mm:ss",15*tw],["hh:mm:ss",30*tw],["hh:mm\nMM-dd",ew],["hh:mm\nMM-dd",5*ew],["hh:mm\nMM-dd",10*ew],["hh:mm\nMM-dd",15*ew],["hh:mm\nMM-dd",30*ew],["hh:mm\nMM-dd",iw],["hh:mm\nMM-dd",2*iw],["hh:mm\nMM-dd",6*iw],["hh:mm\nMM-dd",12*iw],["MM-dd\nyyyy",nw],["MM-dd\nyyyy",2*nw],["MM-dd\nyyyy",3*nw],["MM-dd\nyyyy",4*nw],["MM-dd\nyyyy",5*nw],["MM-dd\nyyyy",6*nw],["week",7*nw],["MM-dd\nyyyy",10*nw],["week",14*nw],["week",21*nw],["month",31*nw],["week",42*nw],["month",62*nw],["week",70*nw],["quarter",95*nw],["month",31*nw*4],["month",31*nw*5],["half-year",380*nw/2],["month",31*nw*8],["month",31*nw*10],["year",380*nw]];aw.create=function(t){return new aw({useUTC:t.ecModel.get("useUTC")})};var sw=Fh.prototype,lw=jx.prototype,hw=to,uw=Ka,cw=Math.floor,dw=Math.ceil,fw=Math.pow,pw=Math.log,gw=Fh.extend({type:"log",base:10,$constructor:function(){Fh.apply(this,arguments),this._originalScale=new jx},getTicks:function(){var t=this._originalScale,e=this._extent,i=t.getExtent();return p(lw.getTicks.call(this),function(n){var r=Ka(fw(this.base,n));return r=n===e[0]&&t.__fixMin?ru(r,i[0]):r,r=n===e[1]&&t.__fixMax?ru(r,i[1]):r},this)},getLabel:lw.getLabel,scale:function(t){return t=sw.scale.call(this,t),fw(this.base,t)},setExtent:function(t,e){var i=this.base;t=pw(t)/pw(i),e=pw(e)/pw(i),lw.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=sw.getExtent.call(this);e[0]=fw(t,e[0]),e[1]=fw(t,e[1]);var i=this._originalScale,n=i.getExtent();return i.__fixMin&&(e[0]=ru(e[0],n[0])),i.__fixMax&&(e[1]=ru(e[1],n[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=pw(t[0])/pw(e),t[1]=pw(t[1])/pw(e),sw.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var n=oo(i),r=t/i*n;for(.5>=r&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var a=[Ka(dw(e[0]/n)*n),Ka(cw(e[1]/n)*n)];this._interval=n,this._niceExtent=a}},niceExtent:function(t){lw.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});f(["contain","normalize"],function(t){gw.prototype[t]=function(e){return e=pw(e)/pw(this.base),sw[t].call(this,e)}}),gw.create=function(){return new gw};var vw={getMin:function(t){var e=this.option,i=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=i&&"dataMin"!==i&&"function"!=typeof i&&!C(i)&&(i=this.axis.scale.parse(i)),i},getMax:function(t){var e=this.option,i=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=i&&"dataMax"!==i&&"function"!=typeof i&&!C(i)&&(i=this.axis.scale.parse(i)),i},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},getCoordSysModel:V,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},mw=Qr({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n+a),t.lineTo(i-r,n+a),t.closePath()}}),yw=Qr({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n),t.lineTo(i,n+a),t.lineTo(i-r,n),t.closePath()}}),_w=Qr({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=n-a+o+s,h=Math.asin(s/o),u=Math.cos(h)*o,c=Math.sin(h),d=Math.cos(h),f=.6*o,p=.7*o;t.moveTo(i-u,l+s),t.arc(i,l,o,Math.PI-h,2*Math.PI+h),t.bezierCurveTo(i+u-c*f,l+s+d*f,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-u+c*f,l+s+d*f,i-u,l+s),t.closePath()}}),xw=Qr({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.height,n=e.width,r=e.x,a=e.y,o=n/3*2;t.moveTo(r,a),t.lineTo(r+o,a+i),t.lineTo(r,a+i/4*3),t.lineTo(r-o,a+i),t.lineTo(r,a),t.closePath()}}),ww={line:Dm,rect:Tm,roundRect:Tm,square:Tm,circle:gm,diamond:yw,pin:_w,arrow:xw,triangle:mw},bw={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var a=Math.min(i,n);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n}},Sw={};f(ww,function(t,e){Sw[e]=new t});var Mw=Qr({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign="center",t.textVerticalAlign="middle")},buildPath:function(t,e,i){var n=e.symbolType,r=Sw[n];"none"!==e.symbolType&&(r||(n="rect",r=Sw[n]),bw[n](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,i))}}),Iw={isDimensionStacked:Eh,enableDataStack:Oh,getStackedDimension:Bh},Tw=(Object.freeze||Object)({createList:yu,getLayoutRect:Io,dataStack:Iw,createScale:_u,mixinAxisModelCommonMethods:xu,completeDimensions:kh,createDimensions:Vx,createSymbol:mu}),Cw=1e-8;Su.prototype={constructor:Su,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,i=[e,e],n=[-e,-e],r=[],a=[],o=this.geometries,s=0;s<o.length;s++)if("polygon"===o[s].type){var l=o[s].exterior;_r(l,r,a),oe(i,i,r),se(n,n,a)}return 0===s&&(i[0]=i[1]=n[0]=n[1]=0),this._rect=new mi(i[0],i[1],n[0]-i[0],n[1]-i[1])},contain:function(t){var e=this.getBoundingRect(),i=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var n=0,r=i.length;r>n;n++)if("polygon"===i[n].type){var a=i[n].exterior,o=i[n].interiors;if(bu(a,t[0],t[1])){for(var s=0;s<(o?o.length:0);s++)if(bu(o[s]))continue t;return!0}}return!1},transformTo:function(t,e,i,n){var r=this.getBoundingRect(),a=r.width/r.height;i?n||(n=i/a):i=a*n;for(var o=new mi(t,e,i,n),s=r.calculateTransform(o),l=this.geometries,h=0;h<l.length;h++)if("polygon"===l[h].type){for(var u=l[h].exterior,c=l[h].interiors,d=0;d<u.length;d++)ae(u[d],u[d],s);for(var f=0;f<(c?c.length:0);f++)for(var d=0;d<c[f].length;d++)ae(c[f][d],c[f][d],s)}r=this._rect,r.copy(o),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new Su(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};var Dw=function(t){return Mu(t),p(v(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var e=t.properties,i=t.geometry,n=i.coordinates,r=[];"Polygon"===i.type&&r.push({type:"polygon",exterior:n[0],interiors:n.slice(1)}),"MultiPolygon"===i.type&&f(n,function(t){t[0]&&r.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var a=new Su(e.name,r,e.cp);return a.properties=e,a})},Aw=jn(),kw=[0,1],Pw=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1};Pw.prototype={constructor:Pw,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this.contain(this.dataToCoord(t))},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return eo(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&"ordinal"===n.type&&(i=i.slice(),Vu(i,n.count())),Za(t,kw,i,e)},coordToData:function(t,e){var i=this._extent,n=this.scale;this.onBand&&"ordinal"===n.type&&(i=i.slice(),Vu(i,n.count()));var r=Za(t,i,kw,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=Cu(this,e),n=i.ticks,r=p(n,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),a=e.get("alignWithLabel");return Hu(this,r,i.tickCategoryInterval,a,t.clamp),r},getViewLabels:function(){return Tu(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return zu(this)}};var Lw=Dw,Ow={};f(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){Ow[t]=op[t]});var Ew={};f(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){Ew[t]=Xm[t]
});var Bw=function(t){this._axes={},this._dimList=[],this.name=t||""};Bw.prototype={constructor:Bw,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return p(this._dimList,Wu,this)},getAxesByScale:function(t){return t=t.toLowerCase(),v(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i.length;r++){var a=i[r],o=this._axes[a];n[a]=o[e](t[a])}return n}},Gu.prototype={constructor:Gu,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,i){var n=this.getAxis("x"),r=this.getAxis("y");return i=i||[],i[0]=n.toGlobalCoord(n.dataToCoord(t[0])),i[1]=r.toGlobalCoord(r.dataToCoord(t[1])),i},clampData:function(t,e){var i=this.getAxis("x").scale,n=this.getAxis("y").scale,r=i.getExtent(),a=n.getExtent(),o=i.parse(t[0]),s=n.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),o),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(a[0],a[1]),s),Math.max(a[0],a[1])),e},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return e=e||[],e[0]=i.coordToData(i.toLocalCoord(t[0])),e[1]=n.coordToData(n.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},u(Gu,Bw);var zw=function(t,e,i,n,r){Pw.call(this,t,e,i),this.type=n||"value",this.position=r||"bottom"};zw.prototype={constructor:zw,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},u(zw,Pw);var Rw={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},Nw={};Nw.categoryAxis=r({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Rw),Nw.valueAxis=r({boundaryGap:[0,0],splitNumber:5},Rw),Nw.timeAxis=s({scale:!0,min:"dataMin",max:"dataMax"},Nw.valueAxis),Nw.logAxis=s({scale:!0,logBase:10},Nw.valueAxis);var Fw=["value","category","time","log"],Vw=function(t,e,i,n){f(Fw,function(o){e.extend({type:t+"Axis."+o,mergeDefaultAndTheme:function(e,n){var a=this.layoutMode,s=a?Co(e):{},l=n.getTheme();r(e,l.get(o+"Axis")),r(e,this.getDefaultOption()),e.type=i(t,e),a&&To(e,s,a)},optionUpdated:function(){var t=this.option;"category"===t.type&&(this.__ordinalMeta=Vh.createByAxisModel(this))},getCategories:function(t){var e=this.option;return"category"===e.type?t?e.data:this.__ordinalMeta.categories:void 0},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:a([{},Nw[o+"Axis"],n],!0)})}),vy.registerSubTypeDefaulter(t+"Axis",_(i,t))},Hw=vy.extend({type:"cartesian2dAxis",axis:null,init:function(){Hw.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){Hw.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){Hw.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});r(Hw.prototype,vw);var Ww={offset:0};Vw("x",Hw,Uu,Ww),Vw("y",Hw,Uu,Ww),vy.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var Gw=ju.prototype;Gw.type="grid",Gw.axisPointerEnabled=!0,Gw.getRect=function(){return this._rect},Gw.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model),f(i.x,function(t){su(t.scale,t.model)}),f(i.y,function(t){su(t.scale,t.model)});var n={};f(i.x,function(t){Yu(i,"y",t,n)}),f(i.y,function(t){Yu(i,"x",t,n)}),this.resize(this.model,e)},Gw.resize=function(t,e,i){function n(){f(a,function(t){var e=t.isHorizontal(),i=e?[0,r.width]:[0,r.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),Zu(t,e?r.x:r.y)})}var r=Io(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var a=this._axesList;n(),!i&&t.get("containLabel")&&(f(a,function(t){if(!t.model.get("axisLabel.inside")){var e=du(t);if(e){var i=t.isHorizontal()?"height":"width",n=t.model.get("axisLabel.margin");r[i]-=e[i]+n,"top"===t.position?r.y+=e.height+n:"left"===t.position&&(r.x+=e.width+n)}}}),n())},Gw.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},Gw.getAxes=function(){return this._axesList.slice()},Gw.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}S(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,r=this._coordsList;n<r.length;n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},Gw.getCartesians=function(){return this._coordsList.slice()},Gw.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},Gw.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},Gw._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)i=r.coordinateSystem,h(l,i)<0&&(i=null);else if(a&&o)i=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)n=this.getAxis("x",a.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(s){var u=s.coordinateSystem;u===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},Gw.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},Gw._initCartesian=function(t,e){function i(i){return function(o,s){if(Xu(o,t,e)){var l=o.get("position");"x"===i?"top"!==l&&"bottom"!==l&&(l="bottom",n[l]&&(l="top"===l?"bottom":"top")):"left"!==l&&"right"!==l&&(l="left",n[l]&&(l="left"===l?"right":"left")),n[l]=!0;var h=new zw(i,lu(o),[0,0],o.get("type"),l),u="category"===h.type;h.onBand=u&&o.get("boundaryGap"),h.inverse=o.get("inverse"),o.axis=h,h.model=o,h.grid=this,h.index=s,this._axesList.push(h),r[i][s]=h,a[i]++}}}var n={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},a={x:0,y:0};return e.eachComponent("xAxis",i("x"),this),e.eachComponent("yAxis",i("y"),this),a.x&&a.y?(this._axesMap=r,void f(r.x,function(e,i){f(r.y,function(n,r){var a="x"+i+"y"+r,o=new Gu(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(n)},this)},this)):(this._axesMap={},void(this._axesList=[]))},Gw._updateScale=function(t,e){function i(t,e){f(t.mapDimension(e.dim,!0),function(i){e.scale.unionExtentFromData(t,Bh(t,i))})}f(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(n){if(Ku(n)){var r=$u(n,t),a=r[0],o=r[1];if(!Xu(a,e,t)||!Xu(o,e,t))return;var s=this.getCartesian(a.componentIndex,o.componentIndex),l=n.getData(),h=s.getAxis("x"),u=s.getAxis("y");"list"===l.type&&(i(l,h,n),i(l,u,n))}},this)},Gw.getTooltipAxes=function(t){var e=[],i=[];return f(this.getCartesians(),function(n){var r=null!=t&&"auto"!==t?n.getAxis(t):n.getBaseAxis(),a=n.getOtherAxis(r);h(e,r)<0&&e.push(r),h(i,a)<0&&i.push(a)}),{baseAxes:e,otherAxes:i}};var Uw=["xAxis","yAxis"];ju.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,r){var a=new ju(n,t,e);a.name="grid_"+r,a.resize(n,e,!0),n.coordinateSystem=a,i.push(a)}),t.eachSeries(function(e){if(Ku(e)){var i=$u(e,t),n=i[0],r=i[1],a=n.getCoordSysModel(),o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(n.componentIndex,r.componentIndex)}}),i},ju.dimensions=ju.prototype.dimensions=Gu.prototype.dimensions,ts.register("cartesian2d",ju);var Xw=n_.extend({type:"series.__base_bar__",getInitialData:function(){return zh(this.getSource(),this)},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var i=e.dataToPoint(e.clampData(t)),n=this.getData(),r=n.getLayout("offset"),a=n.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return i[o]+=r+a/2,i}return[0/0,0/0]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});Xw.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return this.get("large")?this.get("progressive"):!1},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t}});var jw=sv([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Yw={getBarItemStyle:function(t){var e=jw(this,t);if(this.getBorderLineDash){var i=this.getBorderLineDash();i&&(e.lineDash=i)}return e}},qw=["itemStyle","barBorderWidth"];o(Wa.prototype,Yw),sh({type:"bar",render:function(t,e,i){this._updateDrawMode(t);var n=t.get("coordinateSystem");return("cartesian2d"===n||"polar"===n)&&(this._isLargeDraw?this._renderLarge(t,e,i):this._renderNormal(t,e,i)),this.group},incrementalPrepareRender:function(t){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t){var e,i=this.group,n=t.getData(),r=this._data,a=t.coordinateSystem,o=a.getBaseAxis();"cartesian2d"===a.type?e=o.isHorizontal():"polar"===a.type&&(e="angle"===o.dim);var s=t.isAnimationEnabled()?t:null;n.diff(r).add(function(r){if(n.hasValue(r)){var o=n.getItemModel(r),l=$w[a.type](n,r,o),h=Zw[a.type](n,r,o,l,e,s);n.setItemGraphicEl(r,h),i.add(h),nc(h,n,r,o,l,t,e,"polar"===a.type)}}).update(function(o,l){var h=r.getItemGraphicEl(l);if(!n.hasValue(o))return void i.remove(h);var u=n.getItemModel(o),c=$w[a.type](n,o,u);h?Oa(h,{shape:c},s,o):h=Zw[a.type](n,o,u,c,e,s,!0),n.setItemGraphicEl(o,h),i.add(h),nc(h,n,o,u,c,t,e,"polar"===a.type)}).remove(function(t){var e=r.getItemGraphicEl(t);"cartesian2d"===a.type?e&&ec(t,s,e):e&&ic(t,s,e)}).execute(),this._data=n},_renderLarge:function(t){this._clear(),ac(t,this.group)},_incrementalRenderLarge:function(t,e){ac(e,this.group,!0)},dispose:V,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,i=this._data;t&&t.get("animation")&&i&&!this._isLargeDraw?i.eachItemGraphicEl(function(e){"sector"===e.type?ic(e.dataIndex,t,e):ec(e.dataIndex,t,e)}):e.removeAll(),this._data=null}});var Zw={cartesian2d:function(t,e,i,n,r,a,s){var l=new Tm({shape:o({},n)});if(a){var h=l.shape,u=r?"height":"width",c={};h[u]=0,c[u]=n[u],Xm[s?"updateProps":"initProps"](l,{shape:c},a,e)}return l},polar:function(t,e,i,n,r,a,o){var l=n.startAngle<n.endAngle,h=new ym({shape:s({clockwise:l},n)});if(a){var u=h.shape,c=r?"r":"endAngle",d={};u[c]=r?0:n.startAngle,d[c]=n[c],Xm[o?"updateProps":"initProps"](h,{shape:d},a,e)}return h}},$w={cartesian2d:function(t,e,i){var n=t.getItemLayout(e),r=rc(i,n),a=n.width>0?1:-1,o=n.height>0?1:-1;return{x:n.x+a*r/2,y:n.y+o*r/2,width:n.width-a*r,height:n.height-o*r}},polar:function(t,e){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}},Kw=Nr.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var i=e.points,n=this.__startPoint,r=this.__valueIdx,a=0;a<i.length;a+=2)n[this.__valueIdx]=i[a+r],t.moveTo(n[0],n[1]),t.lineTo(i[a],i[a+1])}}),Qw=Math.PI,Jw=function(t,e){this.opt=e,this.axisModel=t,s(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new ig;var i=new ig({position:e.position.slice(),rotation:e.rotation});i.updateTransform(),this._transform=i.transform,this._dumbGroup=i};Jw.prototype={constructor:Jw,hasBuilder:function(t){return!!tb[t]},add:function(t){tb[t].call(this)},getGroup:function(){return this.group}};var tb={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis.getExtent(),n=this._transform,r=[i[0],0],a=[i[1],0];n&&(ae(r,r,n),ae(a,a,n));var s=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new Dm(ra({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})));var l=e.get("axisLine.symbol"),h=e.get("axisLine.symbolSize"),u=e.get("axisLine.symbolOffset")||0;if("number"==typeof u&&(u=[u,u]),null!=l){"string"==typeof l&&(l=[l,l]),("string"==typeof h||"number"==typeof h)&&(h=[h,h]);var c=h[0],d=h[1];f([{rotate:t.rotation+Math.PI/2,offset:u[0],r:0},{rotate:t.rotation-Math.PI/2,offset:u[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],function(e,i){if("none"!==l[i]&&null!=l[i]){var n=mu(l[i],-c/2,-d/2,c,d,s.stroke,!0),a=e.r+e.offset,o=[r[0]+a*Math.cos(t.rotation),r[1]-a*Math.sin(t.rotation)];n.attr({rotation:e.rotate,position:o,silent:!0,z2:11}),this.group.add(n)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,i=pc(this,t,e),n=gc(this,t,e);uc(t,n,i)},axisName:function(){var t=this.opt,e=this.axisModel,i=D(t.axisName,e.get("name"));if(i){var n,r=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,h=this.axisModel.axis.getExtent(),u=h[0]>h[1]?-1:1,c=["start"===r?h[0]-u*l:"end"===r?h[1]+u*l:(h[0]+h[1])/2,fc(r)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*Qw/180);var f;fc(r)?n=eb(t.rotation,null!=d?d:t.rotation,a):(n=lc(t,r,d||0,h),f=t.axisNameAvailableWidth,null!=f&&(f=Math.abs(f/Math.sin(n.rotation)),!isFinite(f)&&(f=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},v=g.ellipsis,m=D(t.nameTruncateMaxWidth,g.maxWidth,f),y=null!=v&&null!=m?ly(i,m,p,v,{minChar:2,placeholder:g.placeholder}):i,_=e.get("tooltip",!0),x=e.mainType,w={componentType:x,name:i,$vars:["name"]};w[x+"Index"]=e.componentIndex;var b=new pm({anid:"name",__fullText:i,__truncatedText:y,position:c,rotation:n.rotation,silent:hc(e),z2:1,tooltip:_&&_.show?o({content:i,formatter:function(){return i},formatterParams:w},_):null});Sa(b.style,s,{text:y,textFont:p,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:n.textAlign,textVerticalAlign:n.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=sc(e),b.eventData.targetType="axisName",b.eventData.name=i),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},eb=Jw.innerTextLayout=function(t,e,i){var n,r,a=no(e-t);return ro(a)?(r=i>0?"top":"bottom",n="center"):ro(a-Qw)?(r=i>0?"bottom":"top",n="center"):(r="middle",n=a>0&&Qw>a?i>0?"right":"left":i>0?"left":"right"),{rotation:a,textAlign:n,textVerticalAlign:r}},ib=f,nb=_,rb=ah({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,i,n){this.axisPointerClass&&bc(t),rb.superApply(this,"render",arguments),Cc(this,t,e,i,n,!0)},updateAxisPointer:function(t,e,i,n){Cc(this,t,e,i,n,!1)},remove:function(t,e){var i=this._axisPointer;i&&i.remove(e),rb.superApply(this,"remove",arguments)},dispose:function(t,e){Dc(this,e),rb.superApply(this,"dispose",arguments)}}),ab=[];rb.registerAxisPointerClass=function(t,e){ab[t]=e},rb.getAxisPointerClass=function(t){return t&&ab[t]};var ob=["axisLine","axisTickLabel","axisName"],sb=["splitArea","splitLine"],lb=rb.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,i,n){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new ig,this.group.add(this._axisGroup),t.get("show")){var a=t.getCoordSysModel(),o=Ac(a,t),s=new Jw(t,o);f(ob,s.add,s),this._axisGroup.add(s.getGroup()),f(sb,function(e){t.get(e+".show")&&this["_"+e](t,a)},this),Na(r,this._axisGroup,t),lb.superCall(this,"render",t,e,i,n)}},remove:function(){this._splitAreaColors=null},_splitLine:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitLine"),r=n.getModel("lineStyle"),a=r.get("color");a=x(a)?a:[a];for(var o=e.coordinateSystem.getRect(),l=i.isHorizontal(),h=0,u=i.getTicksCoords({tickModel:n}),c=[],d=[],f=r.getLineStyle(),p=0;p<u.length;p++){var g=i.toGlobalCoord(u[p].coord);l?(c[0]=g,c[1]=o.y,d[0]=g,d[1]=o.y+o.height):(c[0]=o.x,c[1]=g,d[0]=o.x+o.width,d[1]=g);var v=h++%a.length,m=u[p].tickValue;this._axisGroup.add(new Dm(ra({anid:null!=m?"line_"+u[p].tickValue:null,shape:{x1:c[0],y1:c[1],x2:d[0],y2:d[1]},style:s({stroke:a[v]},f),silent:!0})))}}},_splitArea:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitArea"),r=n.getModel("areaStyle"),a=r.get("color"),o=e.coordinateSystem.getRect(),l=i.getTicksCoords({tickModel:n,clamp:!0});if(l.length){var h=a.length,u=this._splitAreaColors,c=N(),d=0;if(u)for(var f=0;f<l.length;f++){var p=u.get(l[f].tickValue);if(null!=p){d=(p+(h-1)*f)%h;break}}var g=i.toGlobalCoord(l[0].coord),v=r.getAreaStyle();a=x(a)?a:[a];for(var f=1;f<l.length;f++){var m,y,_,w,b=i.toGlobalCoord(l[f].coord);i.isHorizontal()?(m=g,y=o.y,_=b-m,w=o.height,g=m+_):(m=o.x,y=g,_=o.width,w=b-y,g=y+w);var S=l[f-1].tickValue;null!=S&&c.set(S,d),this._axisGroup.add(new Tm({anid:null!=S?"area_"+S:null,shape:{x:m,y:y,width:_,height:w},style:s({fill:a[d]},v),silent:!0})),d=(d+1)%h}this._splitAreaColors=c}}}});lb.extend({type:"xAxis"}),lb.extend({type:"yAxis"}),ah({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new Tm({shape:t.coordinateSystem.getRect(),style:s({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),ql(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),th(_(tu,"bar")),th($x),eh({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}}),n_.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(){return zh(this.getSource(),this)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var hb=kc.prototype,ub=kc.getSymbolSize=function(t,e){var i=t.getItemVisual(e,"symbolSize");return i instanceof Array?i.slice():[+i,+i]};hb._createSymbol=function(t,e,i,n,r){this.removeAll();var a=e.getItemVisual(i,"color"),o=mu(t,-1,-1,2,2,a,r);o.attr({z2:100,culling:!0,scale:Pc(n)}),o.drift=Lc,this._symbolType=t,this.add(o)},hb.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},hb.getSymbolPath=function(){return this.childAt(0)},hb.getScale=function(){return this.childAt(0).scale},hb.highlight=function(){this.childAt(0).trigger("emphasis")},hb.downplay=function(){this.childAt(0).trigger("normal")},hb.setZ=function(t,e){var i=this.childAt(0);i.zlevel=t,i.z=e},hb.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},hb.updateData=function(t,e,i){this.silent=!1;var n=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,a=ub(t,e),o=n!==this._symbolType;if(o){var s=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(n,t,e,a,s)}else{var l=this.childAt(0);l.silent=!1,Oa(l,{scale:Pc(a)},r,e)}if(this._updateCommon(t,e,a,i),o){var l=this.childAt(0),h=i&&i.fadeIn,u={scale:l.scale.slice()};h&&(u.style={opacity:l.style.opacity}),l.scale=[0,0],h&&(l.style.opacity=0),Ea(l,u,r,e)}this._seriesModel=r};var cb=["itemStyle"],db=["emphasis","itemStyle"],fb=["label"],pb=["emphasis","label"];hb._updateCommon=function(t,e,i,n){function r(e){return b?t.getName(e):Qu(t,e)}var a=this.childAt(0),s=t.hostModel,l=t.getItemVisual(e,"color");"image"!==a.type&&a.useStyle({strokeNoScale:!0});var h=n&&n.itemStyle,u=n&&n.hoverItemStyle,c=n&&n.symbolRotate,d=n&&n.symbolOffset,f=n&&n.labelModel,p=n&&n.hoverLabelModel,g=n&&n.hoverAnimation,v=n&&n.cursorStyle;if(!n||t.hasItemOption){var m=n&&n.itemModel?n.itemModel:t.getItemModel(e);h=m.getModel(cb).getItemStyle(["color"]),u=m.getModel(db).getItemStyle(),c=m.getShallow("symbolRotate"),d=m.getShallow("symbolOffset"),f=m.getModel(fb),p=m.getModel(pb),g=m.getShallow("hoverAnimation"),v=m.getShallow("cursor")}else u=o({},u);var y=a.style;a.attr("rotation",(c||0)*Math.PI/180||0),d&&a.attr("position",[$a(d[0],i[0]),$a(d[1],i[1])]),v&&a.attr("cursor",v),a.setColor(l,n&&n.symbolInnerColor),a.setStyle(h);var _=t.getItemVisual(e,"opacity");null!=_&&(y.opacity=_);var x=t.getItemVisual(e,"liftZ"),w=a.__z2Origin;null!=x?null==w&&(a.__z2Origin=a.z2,a.z2+=x):null!=w&&(a.z2=w,a.__z2Origin=null);var b=n&&n.useNameLabel;ba(y,u,f,p,{labelFetcher:s,labelDataIndex:e,defaultText:r,isRectText:!0,autoColor:l}),a.off("mouseover").off("mouseout").off("emphasis").off("normal"),a.hoverStyle=u,xa(a),a.__symbolOriginalScale=Pc(i),g&&s.isAnimationEnabled()&&a.on("mouseover",Oc).on("mouseout",Ec).on("emphasis",Bc).on("normal",zc)},hb.fadeOut=function(t,e){var i=this.childAt(0);this.silent=i.silent=!0,!(e&&e.keepLabel)&&(i.style.text=null),Oa(i,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},u(kc,ig);var gb=Rc.prototype;gb.updateData=function(t,e){e=Fc(e);var i=this.group,n=t.hostModel,r=this._data,a=this._symbolCtor,o=Vc(t);r||i.removeAll(),t.diff(r).add(function(n){var r=t.getItemLayout(n);if(Nc(t,r,n,e)){var s=new a(t,n,o);s.attr("position",r),t.setItemGraphicEl(n,s),i.add(s)}}).update(function(s,l){var h=r.getItemGraphicEl(l),u=t.getItemLayout(s);return Nc(t,u,s,e)?(h?(h.updateData(t,s,o),Oa(h,{position:u},n)):(h=new a(t,s),h.attr("position",u)),i.add(h),void t.setItemGraphicEl(s,h)):void i.remove(h)}).remove(function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut(function(){i.remove(e)})}).execute(),this._data=t},gb.isPersistent=function(){return!0},gb.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,i){var n=t.getItemLayout(i);e.attr("position",n)})},gb.incrementalPrepareUpdate=function(t){this._seriesScope=Vc(t),this._data=null,this.group.removeAll()},gb.incrementalUpdate=function(t,e,i){function n(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}i=Fc(i);for(var r=t.start;r<t.end;r++){var a=e.getItemLayout(r);if(Nc(e,a,r,i)){var o=new this._symbolCtor(e,r,this._seriesScope);o.traverse(n),o.attr("position",a),this.group.add(o),e.setItemGraphicEl(r,o)}}},gb.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var vb=function(t,e,i,n,r,a,o,s){for(var l=Uc(t,e),h=[],u=[],c=[],d=[],f=[],p=[],g=[],v=Hc(r,e,o),m=Hc(a,t,s),y=0;y<l.length;y++){var _=l[y],x=!0;switch(_.cmd){case"=":var w=t.getItemLayout(_.idx),b=e.getItemLayout(_.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=b.slice()),h.push(w),u.push(b),c.push(i[_.idx]),d.push(n[_.idx1]),g.push(e.getRawIndex(_.idx1));break;case"+":var S=_.idx;h.push(r.dataToPoint([e.get(v.dataDimsForPoint[0],S),e.get(v.dataDimsForPoint[1],S)])),u.push(e.getItemLayout(S).slice()),c.push(Gc(v,r,e,S)),d.push(n[S]),g.push(e.getRawIndex(S));break;case"-":var S=_.idx,M=t.getRawIndex(S);M!==S?(h.push(t.getItemLayout(S)),u.push(a.dataToPoint([t.get(m.dataDimsForPoint[0],S),t.get(m.dataDimsForPoint[1],S)])),c.push(i[S]),d.push(Gc(m,a,t,S)),g.push(M)):x=!1}x&&(f.push(_),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});for(var I=[],T=[],C=[],D=[],A=[],y=0;y<p.length;y++){var S=p[y];I[y]=h[S],T[y]=u[S],C[y]=c[S],D[y]=d[S],A[y]=f[S]}return{current:I,next:T,stackedOnCurrent:C,stackedOnNext:D,status:A}},mb=oe,yb=se,_b=j,xb=W,wb=[],bb=[],Sb=[],Mb=Nr.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:mm(Nr.prototype.brush),buildPath:function(t,e){var i=e.points,n=0,r=i.length,a=Zc(i,e.smoothConstraint);if(e.connectNulls){for(;r>0&&Xc(i[r-1]);r--);for(;r>n&&Xc(i[n]);n++);}for(;r>n;)n+=jc(t,i,n,r,r,1,a.min,a.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),Ib=Nr.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:mm(Nr.prototype.brush),buildPath:function(t,e){var i=e.points,n=e.stackedOnPoints,r=0,a=i.length,o=e.smoothMonotone,s=Zc(i,e.smoothConstraint),l=Zc(n,e.smoothConstraint);if(e.connectNulls){for(;a>0&&Xc(i[a-1]);a--);for(;a>r&&Xc(i[r]);r++);}for(;a>r;){var h=jc(t,i,r,a,a,1,s.min,s.max,e.smooth,o,e.connectNulls);jc(t,n,r+h-1,h,a,-1,l.min,l.max,e.stackedOnSmooth,o,e.connectNulls),r+=h+1,t.closePath()}}});Vs.extend({type:"line",init:function(){var t=new ig,e=new Rc;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,i){var n=t.coordinateSystem,r=this.group,a=t.getData(),o=t.getModel("lineStyle"),l=t.getModel("areaStyle"),h=a.mapArray(a.getItemLayout),u="polar"===n.type,c=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,g=this._lineGroup,v=t.get("animation"),m=!l.isEmpty(),y=l.get("origin"),_=Hc(n,a,y),x=Jc(n,a,_),w=t.get("showSymbol"),b=w&&!u&&ad(t,a,n),S=this._data;S&&S.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),S.setItemGraphicEl(e,null))}),w||d.remove(),r.add(g);var M=!u&&t.get("step");f&&c.type===n.type&&M===this._step?(m&&!p?p=this._newPolygon(h,x,n,v):p&&!m&&(g.remove(p),p=this._polygon=null),g.setClipPath(id(n,!1,!1,t)),w&&d.updateData(a,{isIgnore:b,clipShape:id(n,!1,!0,t)}),a.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),$c(this._stackedOnPoints,x)&&$c(this._points,h)||(v?this._updateAnimation(a,x,n,i,M,y):(M&&(h=nd(h,n,M),x=nd(x,n,M)),f.setShape({points:h}),p&&p.setShape({points:h,stackedOnPoints:x})))):(w&&d.updateData(a,{isIgnore:b,clipShape:id(n,!1,!0,t)}),M&&(h=nd(h,n,M),x=nd(x,n,M)),f=this._newPolyline(h,n,v),m&&(p=this._newPolygon(h,x,n,v)),g.setClipPath(id(n,!0,!1,t)));var I=rd(a,n)||a.getVisual("color");f.useStyle(s(o.getLineStyle(),{fill:"none",stroke:I,lineJoin:"bevel"}));var T=t.get("smooth");if(T=Kc(t.get("smooth")),f.setShape({smooth:T,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p){var C=a.getCalculationInfo("stackedOnSeries"),D=0;p.useStyle(s(l.getAreaStyle(),{fill:I,opacity:.7,lineJoin:"bevel"})),C&&(D=Kc(C.get("smooth"))),p.setShape({smooth:T,stackedOnSmooth:D,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=a,this._coordSys=n,this._stackedOnPoints=x,this._points=h,this._step=M,this._valueOrigin=y},dispose:function(){},highlight:function(t,e,i,n){var r=t.getData(),a=Xn(r,n);if(!(a instanceof Array)&&null!=a&&a>=0){var o=r.getItemGraphicEl(a);if(!o){var s=r.getItemLayout(a);if(!s)return;o=new kc(r,a),o.position=s,o.setZ(t.get("zlevel"),t.get("z")),o.ignore=isNaN(s[0])||isNaN(s[1]),o.__temp=!0,r.setItemGraphicEl(a,o),o.stopSymbolAnimation(!0),this.group.add(o)}o.highlight()}else Vs.prototype.highlight.call(this,t,e,i,n)},downplay:function(t,e,i,n){var r=t.getData(),a=Xn(r,n);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group.remove(o)):o.downplay())}else Vs.prototype.downplay.call(this,t,e,i,n)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new Mb({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new Ib({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(i),this._polygon=i,i},_updateAnimation:function(t,e,i,n,r,a){var o=this._polyline,s=this._polygon,l=t.hostModel,h=vb(this._data,t,this._stackedOnPoints,e,this._coordSys,i,this._valueOrigin,a),u=h.current,c=h.stackedOnCurrent,d=h.next,f=h.stackedOnNext;r&&(u=nd(h.current,i,r),c=nd(h.stackedOnCurrent,i,r),d=nd(h.next,i,r),f=nd(h.stackedOnNext,i,r)),o.shape.__points=h.current,o.shape.points=u,Oa(o,{shape:{points:d}},l),s&&(s.setShape({points:u,stackedOnPoints:c}),Oa(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p=[],g=h.status,v=0;v<g.length;v++){var m=g[v].cmd;if("="===m){var y=t.getItemGraphicEl(g[v].idx1);y&&p.push({el:y,ptIdx:v})}}o.animators&&o.animators.length&&o.animators[0].during(function(){for(var t=0;t<p.length;t++){var e=p[t].el;e.attr("position",o.shape.__points[p[t].ptIdx])}})},remove:function(){var t=this.group,e=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),e&&e.eachItemGraphicEl(function(i,n){i.__temp&&(t.remove(i),e.setItemGraphicEl(n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});var Tb=function(t,e,i){return{seriesType:t,performRawSeries:!0,reset:function(t,n){function r(e,i){if("function"==typeof s){var n=t.getRawValue(i),r=t.getDataParams(i);e.setItemVisual(i,"symbolSize",s(n,r))}if(e.hasItemOption){var a=e.getItemModel(i),o=a.getShallow("symbol",!0),l=a.getShallow("symbolSize",!0),h=a.getShallow("symbolKeepAspect",!0);null!=o&&e.setItemVisual(i,"symbol",o),null!=l&&e.setItemVisual(i,"symbolSize",l),null!=h&&e.setItemVisual(i,"symbolKeepAspect",h)}}var a=t.getData(),o=t.get("symbol")||e,s=t.get("symbolSize"),l=t.get("symbolKeepAspect");if(a.setVisual({legendSymbol:i||o,symbol:o,symbolSize:s,symbolKeepAspect:l}),!n.isSeriesFiltered(t)){var h="function"==typeof s;return{dataEach:a.hasItemOption||h?r:null}}}}},Cb=function(t){return{seriesType:t,plan:o_(),reset:function(t){function e(t,e){for(var i=t.end-t.start,r=a&&new Float32Array(i*s),l=t.start,h=0,u=[],c=[];l<t.end;l++){var d;if(1===s){var f=e.get(o[0],l);d=!isNaN(f)&&n.dataToPoint(f,null,c)}else{var f=u[0]=e.get(o[0],l),p=u[1]=e.get(o[1],l);d=!isNaN(f)&&!isNaN(p)&&n.dataToPoint(u,null,c)}a?(r[h++]=d?d[0]:0/0,r[h++]=d?d[1]:0/0):e.setItemLayout(l,d&&d.slice()||[0/0,0/0])}a&&e.setLayout("symbolPoints",r)}var i=t.getData(),n=t.coordinateSystem,r=t.pipelineContext,a=r.large;if(n){var o=p(n.dimensions,function(t){return i.mapDimension(t)}).slice(0,2),s=o.length,l=i.getCalculationInfo("stackResultDimension");return Eh(i,o[0])&&(o[0]=l),Eh(i,o[1])&&(o[1]=l),s&&{progress:e}}}}},Db={average:function(t){for(var e=0,i=0,n=0;n<t.length;n++)isNaN(t[n])||(e+=t[n],i++);return 0===i?0/0:e/i},sum:function(t){for(var e=0,i=0;i<t.length;i++)e+=t[i]||0;return e},max:function(t){for(var e=-1/0,i=0;i<t.length;i++)t[i]>e&&(e=t[i]);return isFinite(e)?e:0/0},min:function(t){for(var e=1/0,i=0;i<t.length;i++)t[i]<e&&(e=t[i]);return isFinite(e)?e:0/0},nearest:function(t){return t[0]}},Ab=function(t){return Math.round(t.length/2)},kb=function(t){return{seriesType:t,modifyOutputEnd:!0,reset:function(t){var e=t.getData(),i=t.get("sampling"),n=t.coordinateSystem;if("cartesian2d"===n.type&&i){var r=n.getBaseAxis(),a=n.getOtherAxis(r),o=r.getExtent(),s=o[1]-o[0],l=Math.round(e.count()/s);
if(l>1){var h;"string"==typeof i?h=Db[i]:"function"==typeof i&&(h=i),h&&t.setData(e.downSample(e.mapDimension(a.dim),1/l,h,Ab))}}}}};eh(Tb("line","circle","line")),th(Cb("line")),Zl(nx.PROCESSOR.STATISTIC,kb("line"));var Pb=function(t,e,i){e=x(e)&&{coordDimensions:e}||o({},e);var n=t.getSource(),r=Vx(n,e),a=new Rx(r,t);return a.initData(n,i),a},Lb={updateSelectedMap:function(t){this._targetList=x(t)?t.slice():[],this._selectTargetMap=g(t||[],function(t,e){return t.set(e.name,e),t},N())},select:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t),n=this.get("selectedMode");"single"===n&&this._selectTargetMap.each(function(t){t.selected=!1}),i&&(i.selected=!0)},unSelect:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);i&&(i.selected=!1)},toggleSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return null!=i?(this[i.selected?"unSelect":"select"](t,e),i.selected):void 0},isSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return i&&i.selected}},Ob=oh({type:"series.pie",init:function(t){Ob.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()},this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){Ob.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(){return Pb(this,["value"])},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),i=[],n=0,r=t.count();r>n;n++)i.push({name:t.getName(n),value:t.get(e,n),selected:Cs(t,n,"selected")});return i},getDataParams:function(t){var e=this.getData(),i=Ob.superCall(this,"getDataParams",t),n=[];return e.each(e.mapDimension("value"),function(t){n.push(t)}),i.percent=io(n,t,e.hostModel.get("percentPrecision")),i.$vars.push("percent"),i},_defaultLabelLine:function(t){Nn(t,"labelLine",["show"]);var e=t.labelLine,i=t.emphasis.labelLine;e.show=e.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,label:{rotate:!1,show:!0,position:"outer"},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationEasing:"cubicOut"}});c(Ob,Lb);var Eb=hd.prototype;Eb.updateData=function(t,e,i){function n(){a.stopAnimation(!0),a.animateTo({shape:{r:u.r+l.get("hoverOffset")}},300,"elasticOut")}function r(){a.stopAnimation(!0),a.animateTo({shape:{r:u.r}},300,"elasticOut")}var a=this.childAt(0),l=t.hostModel,h=t.getItemModel(e),u=t.getItemLayout(e),c=o({},u);if(c.label=null,i){a.setShape(c);var d=l.getShallow("animationType");"scale"===d?(a.shape.r=u.r0,Ea(a,{shape:{r:u.r}},l,e)):(a.shape.endAngle=u.startAngle,Oa(a,{shape:{endAngle:u.endAngle}},l,e))}else Oa(a,{shape:c},l,e);var f=t.getItemVisual(e,"color");a.useStyle(s({lineJoin:"bevel",fill:f},h.getModel("itemStyle").getItemStyle())),a.hoverStyle=h.getModel("emphasis.itemStyle").getItemStyle();var p=h.getShallow("cursor");p&&a.attr("cursor",p),ld(this,t.getItemLayout(e),l.isSelected(null,e),l.get("selectedOffset"),l.get("animation")),a.off("mouseover").off("mouseout").off("emphasis").off("normal"),h.get("hoverAnimation")&&l.isAnimationEnabled()&&a.on("mouseover",n).on("mouseout",r).on("emphasis",n).on("normal",r),this._updateLabel(t,e),xa(this)},Eb._updateLabel=function(t,e){var i=this.childAt(1),n=this.childAt(2),r=t.hostModel,a=t.getItemModel(e),o=t.getItemLayout(e),s=o.label,l=t.getItemVisual(e,"color");Oa(i,{shape:{points:s.linePoints||[[s.x,s.y],[s.x,s.y],[s.x,s.y]]}},r,e),Oa(n,{style:{x:s.x,y:s.y}},r,e),n.attr({rotation:s.rotation,origin:[s.x,s.y],z2:10});var h=a.getModel("label"),u=a.getModel("emphasis.label"),c=a.getModel("labelLine"),d=a.getModel("emphasis.labelLine"),l=t.getItemVisual(e,"color");ba(n.style,n.hoverStyle={},h,u,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:t.getName(e),autoColor:l,useInsideStyle:!!s.inside},{textAlign:s.textAlign,textVerticalAlign:s.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),n.ignore=n.normalIgnore=!h.get("show"),n.hoverIgnore=!u.get("show"),i.ignore=i.normalIgnore=!c.get("show"),i.hoverIgnore=!d.get("show"),i.setStyle({stroke:l,opacity:t.getItemVisual(e,"opacity")}),i.setStyle(c.getModel("lineStyle").getLineStyle()),i.hoverStyle=d.getModel("lineStyle").getLineStyle();var f=c.get("smooth");f&&f===!0&&(f=.4),i.setShape({smooth:f})},u(hd,ig);var Bb=(Vs.extend({type:"pie",init:function(){var t=new ig;this._sectorGroup=t},render:function(t,e,i,n){if(!n||n.from!==this.uid){var r=t.getData(),a=this._data,o=this.group,s=e.get("animation"),l=!a,h=t.get("animationType"),u=_(sd,this.uid,t,s,i),c=t.get("selectedMode");if(r.diff(a).add(function(t){var e=new hd(r,t);l&&"scale"!==h&&e.eachChild(function(t){t.stopAnimation(!0)}),c&&e.on("click",u),r.setItemGraphicEl(t,e),o.add(e)}).update(function(t,e){var i=a.getItemGraphicEl(e);i.updateData(r,t),i.off("click"),c&&i.on("click",u),o.add(i),r.setItemGraphicEl(t,i)}).remove(function(t){var e=a.getItemGraphicEl(t);o.remove(e)}).execute(),s&&l&&r.count()>0&&"scale"!==h){var d=r.getItemLayout(0),f=Math.max(i.getWidth(),i.getHeight())/2,p=y(o.removeClipPath,o);o.setClipPath(this._createClipPath(d.cx,d.cy,f,d.startAngle,d.clockwise,p,t))}else o.removeClipPath();this._data=r}},dispose:function(){},_createClipPath:function(t,e,i,n,r,a,o){var s=new ym({shape:{cx:t,cy:e,r0:0,r:i,startAngle:n,endAngle:n,clockwise:r}});return Ea(s,{shape:{endAngle:n+(r?1:-1)*Math.PI*2}},o,a),s},containPoint:function(t,e){var i=e.getData(),n=i.getItemLayout(0);if(n){var r=t[0]-n.cx,a=t[1]-n.cy,o=Math.sqrt(r*r+a*a);return o<=n.r&&o>=n.r0}}}),function(t,e){f(e,function(e){e.update="updateView",Kl(e,function(i,n){var r={};return n.eachComponent({mainType:"series",subType:t,query:i},function(t){t[e.method]&&t[e.method](i.name,i.dataIndex);var n=t.getData();n.each(function(e){var i=n.getName(e);r[i]=t.isSelected(i)||!1})}),{name:i.name,selected:r}})})}),zb=function(t){return{getTargetSeries:function(e){var i={},n=N();return e.eachSeriesByType(t,function(t){t.__paletteScope=i,n.set(t.uid,t)}),n},reset:function(t){var e=t.getRawData(),i={},n=t.getData();n.each(function(t){var e=n.getRawIndex(t);i[e]=t}),e.each(function(r){var a=i[r],o=null!=a&&n.getItemVisual(a,"color",!0);if(o)e.setItemVisual(r,"color",o);else{var s=e.getItemModel(r),l=s.get("itemStyle.color")||t.getColorFromPalette(e.getName(r)||r+"",t.__paletteScope,e.count());e.setItemVisual(r,"color",l),null!=a&&n.setItemVisual(a,"color",l)}})}}},Rb=function(t,e,i,n){var r,a,o=t.getData(),s=[],l=!1;o.each(function(i){var n,h,u,c,d=o.getItemLayout(i),f=o.getItemModel(i),p=f.getModel("label"),g=p.get("position")||f.get("emphasis.label.position"),v=f.getModel("labelLine"),m=v.get("length"),y=v.get("length2"),_=(d.startAngle+d.endAngle)/2,x=Math.cos(_),w=Math.sin(_);r=d.cx,a=d.cy;var b="inside"===g||"inner"===g;if("center"===g)n=d.cx,h=d.cy,c="center";else{var S=(b?(d.r+d.r0)/2*x:d.r*x)+r,M=(b?(d.r+d.r0)/2*w:d.r*w)+a;if(n=S+3*x,h=M+3*w,!b){var I=S+x*(m+e-d.r),T=M+w*(m+e-d.r),C=I+(0>x?-1:1)*y,D=T;n=C+(0>x?-5:5),h=D,u=[[S,M],[I,T],[C,D]]}c=b?"center":x>0?"left":"right"}var A=p.getFont(),k=p.get("rotate")?0>x?-_+Math.PI:-_:0,P=t.getFormattedLabel(i,"normal")||o.getName(i),L=Ri(P,A,c,"top");l=!!k,d.label={x:n,y:h,position:g,height:L.height,len:m,len2:y,linePoints:u,textAlign:c,verticalAlign:"middle",rotation:k,inside:b},b||s.push(d.label)}),!l&&t.get("avoidLabelOverlap")&&cd(s,r,a,e,i,n)},Nb=2*Math.PI,Fb=Math.PI/180,Vb=function(t,e,i){e.eachSeriesByType(t,function(t){var e=t.getData(),n=e.mapDimension("value"),r=t.get("center"),a=t.get("radius");x(a)||(a=[0,a]),x(r)||(r=[r,r]);var o=i.getWidth(),s=i.getHeight(),l=Math.min(o,s),h=$a(r[0],o),u=$a(r[1],s),c=$a(a[0],l/2),d=$a(a[1],l/2),f=-t.get("startAngle")*Fb,p=t.get("minAngle")*Fb,g=0;e.each(n,function(t){!isNaN(t)&&g++});var v=e.getSum(n),m=Math.PI/(v||g)*2,y=t.get("clockwise"),_=t.get("roseType"),w=t.get("stillShowZeroSum"),b=e.getDataExtent(n);b[0]=0;var S=Nb,M=0,I=f,T=y?1:-1;if(e.each(n,function(t,i){var n;if(isNaN(t))return void e.setItemLayout(i,{angle:0/0,startAngle:0/0,endAngle:0/0,clockwise:y,cx:h,cy:u,r0:c,r:_?0/0:d});n="area"!==_?0===v&&w?m:t*m:Nb/g,p>n?(n=p,S-=p):M+=t;var r=I+T*n;e.setItemLayout(i,{angle:n,startAngle:I,endAngle:r,clockwise:y,cx:h,cy:u,r0:c,r:_?Za(t,b,[c,d]):d}),I=r}),Nb>S&&g)if(.001>=S){var C=Nb/g;e.each(n,function(t,i){if(!isNaN(t)){var n=e.getItemLayout(i);n.angle=C,n.startAngle=f+T*i*C,n.endAngle=f+T*(i+1)*C}})}else m=S/M,I=f,e.each(n,function(t,i){if(!isNaN(t)){var n=e.getItemLayout(i),r=n.angle===p?p:t*m;n.startAngle=I,n.endAngle=I+T*r,I+=T*r}});Rb(t,d,o,s)})},Hb=function(t){return{seriesType:t,reset:function(t,e){var i=e.findComponents({mainType:"legend"});if(i&&i.length){var n=t.getData();n.filterSelf(function(t){for(var e=n.getName(t),r=0;r<i.length;r++)if(!i[r].isSelected(e))return!1;return!0})}}}};Bb("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),eh(zb("pie")),th(_(Vb,"pie")),Zl(Hb("pie")),rh({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),ah({type:"title",render:function(t,e,i){if(this.group.removeAll(),t.get("show")){var n=this.group,r=t.getModel("textStyle"),a=t.getModel("subtextStyle"),o=t.get("textAlign"),s=t.get("textBaseline"),l=new pm({style:Sa({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),h=l.getBoundingRect(),u=t.get("subtext"),c=new pm({style:Sa({},a,{text:u,textFill:a.getTextColor(),y:h.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0);l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){window.open(d,"_"+t.get("target"))}),f&&c.on("click",function(){window.open(f,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,n.add(l),u&&n.add(c);var g=n.getBoundingRect(),v=t.getBoxLayoutParams();v.width=g.width,v.height=g.height;var m=Io(v,{width:i.getWidth(),height:i.getHeight()},t.get("padding"));o||(o=t.get("left")||t.get("right"),"middle"===o&&(o="center"),"right"===o?m.x+=m.width:"center"===o&&(m.x+=m.width/2)),s||(s=t.get("top")||t.get("bottom"),"center"===s&&(s="middle"),"bottom"===s?m.y+=m.height:"middle"===s&&(m.y+=m.height/2),s=s||"top"),n.attr("position",[m.x,m.y]);var y={textAlign:o,textVerticalAlign:s};l.setStyle(y),c.setStyle(y),g=n.getBoundingRect();var _=m.margin,x=t.getItemStyle(["color","opacity"]);x.fill=t.get("backgroundColor");var w=new Tm({shape:{x:g.x-_[3],y:g.y-_[0],width:g.width+_[1]+_[3],height:g.height+_[0]+_[2],r:t.get("borderRadius")},style:x,silent:!0});aa(w),n.add(w)}}});var Wb=rh({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{}},mergeOption:function(t){Wb.superCall(this,"mergeOption",t)},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,i=0;i<t.length;i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=[],i=[];t.eachRawSeries(function(n){var r=n.name;i.push(r);var a;if(n.legendDataProvider){var o=n.legendDataProvider(),s=o.mapArray(o.getName);t.isSeriesFiltered(n)||(i=i.concat(s)),s.length?e=e.concat(s):a=!0}else a=!0;a&&Gn(n)&&e.push(n.name)}),this._availableNames=i;var n=this.get("data")||e,r=p(n,function(t){return("string"==typeof t||"number"==typeof t)&&(t={name:t}),new Wa(t,this,this.ecModel)},this);this._data=r},getData:function(){return this._data},select:function(t){var e=this.option.selected,i=this.get("selectedMode");if("single"===i){var n=this._data;f(n,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&h(this._availableNames,t)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}});Kl("legendToggleSelect","legendselectchanged",_(fd,"toggleSelected")),Kl("legendSelect","legendselected",_(fd,"select")),Kl("legendUnSelect","legendunselected",_(fd,"unSelect"));var Gb=_,Ub=f,Xb=ig,jb=ah({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new Xb),this._backgroundEl,this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},render:function(t,e,i){var n=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===t.get("orient")?"right":"left"),this.renderInner(r,t,e,i);var a=t.getBoxLayoutParams(),o={width:i.getWidth(),height:i.getHeight()},l=t.get("padding"),h=Io(a,o,l),u=this.layoutInner(t,r,h,n),c=Io(s({width:u.width,height:u.height},a),o,l);this.group.attr("position",[c.x-u.x,c.y-u.y]),this.group.add(this._backgroundEl=pd(u,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl)},renderInner:function(t,e,i,n){var r=this.getContentGroup(),a=N(),o=e.get("selectedMode"),s=[];i.eachRawSeries(function(t){!t.get("legendHoverLink")&&s.push(t.id)}),Ub(e.getData(),function(l,h){var u=l.get("name");if(!this.newlineDisabled&&(""===u||"\n"===u))return void r.add(new Xb({newline:!0}));var c=i.getSeriesByName(u)[0];if(!a.get(u))if(c){var d=c.getData(),f=d.getVisual("color");"function"==typeof f&&(f=f(c.getDataParams(0)));var p=d.getVisual("legendSymbol")||"roundRect",g=d.getVisual("symbol"),v=this._createItem(u,h,l,e,p,g,t,f,o);v.on("click",Gb(gd,u,n)).on("mouseover",Gb(vd,c.name,null,n,s)).on("mouseout",Gb(md,c.name,null,n,s)),a.set(u,!0)}else i.eachRawSeries(function(i){if(!a.get(u)&&i.legendDataProvider){var r=i.legendDataProvider(),c=r.indexOfName(u);if(0>c)return;var d=r.getItemVisual(c,"color"),f="roundRect",p=this._createItem(u,h,l,e,f,null,t,d,o);p.on("click",Gb(gd,u,n)).on("mouseover",Gb(vd,null,u,n,s)).on("mouseout",Gb(md,null,u,n,s)),a.set(u,!0)}},this)},this)},_createItem:function(t,e,i,n,r,a,s,l,h){var u=n.get("itemWidth"),c=n.get("itemHeight"),d=n.get("inactiveColor"),f=n.get("symbolKeepAspect"),p=n.isSelected(t),g=new Xb,v=i.getModel("textStyle"),m=i.get("icon"),y=i.getModel("tooltip"),_=y.parentModel;if(r=m||r,g.add(mu(r,0,0,u,c,p?l:d,null==f?!0:f)),!m&&a&&(a!==r||"none"===a)){var x=.8*c;"none"===a&&(a="circle"),g.add(mu(a,(u-x)/2,(c-x)/2,x,x,p?l:d,null==f?!0:f))}var w="left"===s?u+5:-5,b=s,S=n.get("formatter"),M=t;"string"==typeof S&&S?M=S.replace("{name}",null!=t?t:""):"function"==typeof S&&(M=S(t)),g.add(new pm({style:Sa({},v,{text:M,x:w,y:c/2,textFill:p?v.getTextColor():d,textAlign:b,textVerticalAlign:"middle"})}));var I=new Tm({shape:g.getBoundingRect(),invisible:!0,tooltip:y.get("show")?o({content:t,formatter:_.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:n.componentIndex,name:t,$vars:["name"]}},y.option):null});return g.add(I),g.eachChild(function(t){t.silent=!0}),I.silent=!h,this.getContentGroup().add(g),xa(g),g.__legendDataIndex=e,g},layoutInner:function(t,e,i){var n=this.getContentGroup();fy(t.get("orient"),n,t.get("itemGap"),i.width,i.height);var r=n.getBoundingRect();return n.attr("position",[-r.x,-r.y]),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}}),Yb=function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var i=0;i<e.length;i++)if(!e[i].isSelected(t.name))return!1;return!0})};Zl(Yb),vy.registerSubTypeDefaulter("legend",function(){return"plain"});var qb=Wb.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,i,n){var r=Co(t);qb.superCall(this,"init",t,e,i,n),yd(this,t,r)},mergeOption:function(t,e){qb.superCall(this,"mergeOption",t,e),yd(this,this.option,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}}}),Zb=ig,$b=["width","height"],Kb=["x","y"],Qb=jb.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){Qb.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new Zb),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Zb),this._showController},resetInner:function(){Qb.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,i,n){function r(t,i){var r=t+"DataIndex",l=Ha(e.get("pageIcons",!0)[e.getOrient().name][i],{onclick:y(a._pageGo,a,r,e,n)},{x:-s[0]/2,y:-s[1]/2,width:s[0],height:s[1]});l.name=t,o.add(l)}var a=this;Qb.superCall(this,"renderInner",t,e,i,n);var o=this._controllerGroup,s=e.get("pageIconSize",!0);x(s)||(s=[s,s]),r("pagePrev",0);var l=e.getModel("pageTextStyle");o.add(new pm({name:"pageText",style:{textFill:l.getTextColor(),font:l.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),r("pageNext",1)},layoutInner:function(t,e,i,n){var r=this.getContentGroup(),a=this._containerGroup,o=this._controllerGroup,s=t.getOrient().index,l=$b[s],h=$b[1-s],u=Kb[1-s];fy(t.get("orient"),r,t.get("itemGap"),s?i.width:null,s?null:i.height),fy("horizontal",o,t.get("pageButtonItemGap",!0));var c=r.getBoundingRect(),d=o.getBoundingRect(),f=this._showController=c[l]>i[l],p=[-c.x,-c.y];n||(p[s]=r.position[s]);var g=[0,0],v=[-d.x,-d.y],m=A(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(f){var y=t.get("pageButtonPosition",!0);"end"===y?v[s]+=i[l]-d[l]:g[s]+=d[l]+m}v[1-s]+=c[h]/2-d[h]/2,r.attr("position",p),a.attr("position",g),o.attr("position",v);var _=this.group.getBoundingRect(),_={x:0,y:0};if(_[l]=f?i[l]:c[l],_[h]=Math.max(c[h],d[h]),_[u]=Math.min(0,d[u]+v[1-s]),a.__rectSize=i[l],f){var x={x:0,y:0};x[l]=Math.max(i[l]-d[l]-m,0),x[h]=_[h],a.setClipPath(new Tm({shape:x})),a.__rectSize=x[l]}else o.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var w=this._getPageInfo(t);return null!=w.pageIndex&&Oa(r,{position:w.contentPosition},f?t:!1),this._updatePageInfoView(t,w),_},_pageGo:function(t,e,i){var n=this._getPageInfo(e)[t];null!=n&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:e.id})},_updatePageInfoView:function(t,e){var i=this._controllerGroup;f(["pagePrev","pageNext"],function(n){var r=null!=e[n+"DataIndex"],a=i.childOfName(n);a&&(a.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),a.cursor=r?"pointer":"default")});var n=i.childOfName("pageText"),r=t.get("pageFormatter"),a=e.pageIndex,o=null!=a?a+1:0,s=e.pageCount;n&&r&&n.setStyle("text",b(r)?r.replace("{current}",o).replace("{total}",s):r({current:o,total:s}))},_getPageInfo:function(t){function e(t){if(t){var e=t.getBoundingRect(),i=e[l]+t.position[o];return{s:i,e:i+e[s],i:t.__legendDataIndex}}}function i(t,e){return t.e>=e&&t.s<=e+a}var n=t.get("scrollDataIndex",!0),r=this.getContentGroup(),a=this._containerGroup.__rectSize,o=t.getOrient().index,s=$b[o],l=Kb[o],h=this._findTargetItemIndex(n),u=r.children(),c=u[h],d=u.length,f=d?1:0,p={contentPosition:r.position.slice(),pageCount:f,pageIndex:f-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!c)return p;var g=e(c);p.contentPosition[o]=-g.s;for(var v=h+1,m=g,y=g,_=null;d>=v;++v)_=e(u[v]),(!_&&y.e>m.s+a||_&&!i(_,m.s))&&(m=y.i>m.i?y:_,m&&(null==p.pageNextDataIndex&&(p.pageNextDataIndex=m.i),++p.pageCount)),y=_;for(var v=h-1,m=g,y=g,_=null;v>=-1;--v)_=e(u[v]),_&&i(y,_.s)||!(m.i<y.i)||(y=m,null==p.pagePrevDataIndex&&(p.pagePrevDataIndex=m.i),++p.pageCount,++p.pageIndex),m=_;return p},_findTargetItemIndex:function(t){var e,i=this.getContentGroup();return this._showController?i.eachChild(function(i,n){i.__legendDataIndex===t&&(e=n)}):e=0,e}});Kl("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;null!=i&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(i)})});var Jb=function(t,e){var i,n=[],r=t.seriesIndex;if(null==r||!(i=e.getSeriesByIndex(r)))return{point:[]};var a=i.getData(),o=Xn(a,t);if(null==o||0>o||x(o))return{point:[]};var s=a.getItemGraphicEl(o),l=i.coordinateSystem;if(i.getTooltipPosition)n=i.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)n=l.dataToPoint(a.getValues(p(l.dimensions,function(t){return a.mapDimension(t)}),o,!0))||[];else if(s){var h=s.getBoundingRect().clone();h.applyTransform(s.transform),n=[h.x+h.width/2,h.y+h.height/2]}return{point:n,el:s}},tS=f,eS=_,iS=jn(),nS=function(t,e,i){var n=t.currTrigger,r=[t.x,t.y],a=t,o=t.dispatchAction||y(i.dispatchAction,i),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Dd(r)&&(r=Jb({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},e).point);var l=Dd(r),h=a.axesInfo,u=s.axesInfo,c="leave"===n||Dd(r),d={},f={},p={list:[],map:{}},g={showPointer:eS(wd,f),showTooltip:eS(bd,p)};tS(s.coordSysMap,function(t,e){var i=l||t.containPoint(r);tS(s.coordSysAxesInfo[e],function(t){var e=t.axis,n=Td(h,t);if(!c&&i&&(!h||n)){var a=n&&n.value;null!=a||l||(a=e.pointToData(r)),null!=a&&_d(t,a,g,!1,d)}})});var v={};return tS(u,function(t,e){var i=t.linkGroup;i&&!f[e]&&tS(i.axesInfo,function(e,n){var r=f[n];if(e!==t&&r){var a=r.value;i.mapper&&(a=t.axis.scale.parse(i.mapper(a,Cd(e),Cd(t)))),v[t.key]=a}})}),tS(v,function(t,e){_d(u[e],t,g,!0,d)}),Sd(f,u,d),Md(p,r,t,o),Id(u,o,i),d}},rS=(rh({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),jn()),aS=f,oS=ah({type:"axisPointer",render:function(t,e,i){var n=e.getComponent("tooltip"),r=t.get("triggerOn")||n&&n.get("triggerOn")||"mousemove|click";Ad("axisPointer",i,function(t,e,i){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&i({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){Bd(e.getZr(),"axisPointer"),oS.superApply(this._model,"remove",arguments)},dispose:function(t,e){Bd("axisPointer",e),oS.superApply(this._model,"dispose",arguments)}}),sS=jn(),lS=n,hS=y;zd.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,i,n){var r=e.get("value"),a=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,n||this._lastValue!==r||this._lastStatus!==a){this._lastValue=r,this._lastStatus=a;var o=this._group,s=this._handle;if(!a||"hide"===a)return o&&o.hide(),void(s&&s.hide());o&&o.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,i);var h=l.graphicKey;h!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=h;var u=this._moveAnimation=this.determineAnimation(t,e);if(o){var c=_(Rd,e,u);this.updatePointerEl(o,l,c,e),this.updateLabelEl(o,l,c,e)}else o=this._group=new ig,this.createPointerEl(o,l,t,e),this.createLabelEl(o,l,t,e),i.getZr().add(o);Hd(o,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var i=e.get("animation"),n=t.axis,r="category"===n.type,a=e.get("snap");if(!a&&!r)return!1;if("auto"===i||null==i){var o=this.animationThreshold;if(r&&n.getBandWidth()>o)return!0;if(a){var s=Sc(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/s>o}return!1}return i===!0},makeElOption:function(){},createPointerEl:function(t,e){var i=e.pointer;if(i){var n=sS(t).pointerEl=new Xm[i.type](lS(e.pointer));t.add(n)}},createLabelEl:function(t,e,i,n){if(e.label){var r=sS(t).labelEl=new Tm(lS(e.label));t.add(r),Fd(r,n)}},updatePointerEl:function(t,e,i){var n=sS(t).pointerEl;n&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,i,n){var r=sS(t).labelEl;r&&(r.setStyle(e.label.style),i(r,{shape:e.label.shape,position:e.label.position}),Fd(r,n))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,r=e.getModel("handle"),a=e.get("status");if(!r.get("show")||!a||"hide"===a)return n&&i.remove(n),void(this._handle=null);var o;this._handle||(o=!0,n=this._handle=Ha(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){mp(t.event)},onmousedown:hS(this._onHandleDragMove,this,0,0),drift:hS(this._onHandleDragMove,this),ondragend:hS(this._onHandleDragEnd,this)}),i.add(n)),Hd(n,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];n.setStyle(r.getItemStyle(null,s));var l=r.get("size");x(l)||(l=[l,l]),n.attr("scale",[l[0]/2,l[1]/2]),js(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)}},_moveHandleToValue:function(t,e){Rd(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Vd(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(Vd(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(Vd(n)),sS(i).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},_onHandleDragEnd:function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}},zd.prototype.constructor=zd,tr(zd);var uS=zd.extend({makeElOption:function(t,e,i,n,r){var a=i.axis,o=a.grid,s=n.get("type"),l=$d(o,a).getOtherAxis(a).getGlobalExtent(),h=a.toGlobalCoord(a.dataToCoord(e,!0));if(s&&"none"!==s){var u=Wd(n),c=cS[s](a,h,l,u);c.style=u,t.graphicKey=c.type,t.pointer=c}var d=Ac(o.model,i);Yd(e,t,d,i,n,r)},getHandleTransform:function(t,e,i){var n=Ac(e.axis.grid.model,e,{labelInside:!1});return n.labelMargin=i.get("handle.margin"),{position:jd(e.axis,t,n),rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,i){var n=i.axis,r=n.grid,a=n.getGlobalExtent(!0),o=$d(r,n).getOtherAxis(n).getGlobalExtent(),s="x"===n.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(a[1],l[s]),l[s]=Math.max(a[0],l[s]);var h=(o[1]+o[0])/2,u=[h,h];u[s]=l[s];var c=[{verticalAlign:"middle"},{align:"center"}];return{position:l,rotation:t.rotation,cursorPoint:u,tooltipOption:c[s]}}}),cS={line:function(t,e,i,n){var r=qd([e,i[0]],[e,i[1]],Kd(t));return ra({shape:r,style:n}),{type:"Line",shape:r}},shadow:function(t,e,i){var n=Math.max(1,t.getBandWidth()),r=i[1]-i[0];return{type:"Rect",shape:Zd([e-n/2,i[0]],[n,r],Kd(t))}}};rb.registerAxisPointerClass("CartesianAxisPointer",uS),ql(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!x(e)&&(t.axisPointer.link=[e])}}),Zl(nx.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=vc(t,e)}),Kl({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},nS),rh({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var dS=f,fS=po,pS=["","-webkit-","-moz-","-o-"],gS="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";ef.prototype={constructor:ef,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),i=t.style;"absolute"!==i.position&&"absolute"!==e.position&&(i.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=gS+tf(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var i,n=this._zr;n&&n.painter&&(i=n.painter.getViewportRootOffset())&&(t+=i.offsetLeft,e+=i.offsetTop);var r=this.el.style;r.left=t+"px",r.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(this.el);i&&(t+=parseInt(i.paddingLeft,10)+parseInt(i.paddingRight,10)+parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),e+=parseInt(i.paddingTop,10)+parseInt(i.paddingBottom,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:t,height:e}}},nf.prototype={constructor:nf,_enterable:!0,update:function(){},show:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0
},setContent:function(t,e,i){this.el&&this._zr.remove(this.el);for(var n={},r=t,a="{marker",o="|}",s=r.indexOf(a);s>=0;){var l=r.indexOf(o),h=r.substr(s+a.length,l-s-a.length);n["marker"+h]=h.indexOf("sub")>-1?{textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[h],textOffset:[3,0]}:{textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[h]},r=r.substr(l+1),s=r.indexOf("{marker")}this.el=new pm({style:{rich:n,text:t,textLineHeight:20,textBackgroundColor:i.get("backgroundColor"),textBorderRadius:i.get("borderRadius"),textFill:i.get("textStyle.color"),textPadding:i.get("padding")},z:i.get("z")}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){this.el&&this.el.attr("position",[t,e])},hide:function(){this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){return this.getSize()}};var vS=y,mS=f,yS=$a,_S=new Tm({shape:{x:-1,y:-1,width:2,height:2}});ah({type:"tooltip",init:function(t,e){if(!jf.node){var i=t.getComponent("tooltip"),n=i.get("renderMode");this._renderMode=Kn(n);var r;"html"===this._renderMode?(r=new ef(e.getDom(),e),this._newLine="<br/>"):(r=new nf(e),this._newLine="\n"),this._tooltipContent=r}},render:function(t,e,i){if(!jf.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var n=this._tooltipContent;n.update(),n.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");Ad("itemTooltip",this._api,vS(function(t,i,n){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(i,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,i=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var n=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){n.manuallyShowTip(t,e,i,{x:n._lastX,y:n._lastY})})}},manuallyShowTip:function(t,e,i,n){if(n.from!==this.uid&&!jf.node){var r=af(n,i);this._ticket="";var a=n.dataByCoordSys;if(n.tooltip&&null!=n.x&&null!=n.y){var o=_S;o.position=[n.x,n.y],o.update(),o.tooltip=n.tooltip,this._tryShow({offsetX:n.x,offsetY:n.y,target:o},r)}else if(a)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,event:{},dataByCoordSys:n.dataByCoordSys,tooltipOption:n.tooltipOption},r);else if(null!=n.seriesIndex){if(this._manuallyAxisShowTip(t,e,i,n))return;var s=Jb(n,e),l=s.point[0],h=s.point[1];null!=l&&null!=h&&this._tryShow({offsetX:l,offsetY:h,position:n.position,target:s.el,event:{}},r)}else null!=n.x&&null!=n.y&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target,event:{}},r))}},manuallyHideTip:function(t,e,i,n){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,n.from!==this.uid&&this._hide(af(n,i))},_manuallyAxisShowTip:function(t,e,i,n){var r=n.seriesIndex,a=n.dataIndex,o=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=a&&null!=o){var s=e.getSeriesByIndex(r);if(s){var l=s.getData(),t=rf([l.getItemModel(a),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return i.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:a,position:n.position}),!0}}},_tryShow:function(t,e){var i=t.target,n=this._tooltipModel;if(n){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var i=t.get("showDelay");e=y(e,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(e,i):e()},_showAxisTooltip:function(t,e){var i=this._ecModel,n=this._tooltipModel,a=[e.offsetX,e.offsetY],o=[],s=[],l=rf([e.tooltipOption,n]),h=this._renderMode,u=this._newLine,c={};mS(t,function(t){mS(t.dataByAxis,function(t){var e=i.getComponent(t.axisDim+"Axis",t.axisIndex),n=t.value,a=[];if(e&&null!=n){var l=Xd(n,e.axis,i,t.seriesDataIndices,t.valueLabelOpt);f(t.seriesDataIndices,function(o){var u=i.getSeriesByIndex(o.seriesIndex),d=o.dataIndexInside,f=u&&u.getDataParams(d);if(f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=cu(e.axis,n),f.axisValueLabel=l,f){s.push(f);var p,g=u.formatTooltip(d,!0,null,h);if(S(g)){p=g.html;var v=g.markers;r(c,v)}else p=g;a.push(p)}});var d=l;o.push("html"!==h?a.join(u):(d?go(d)+u:"")+a.join(u))}})},this),o.reverse(),o=o.join(this._newLine+this._newLine);var d=e.position;this._showOrMove(l,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(l,d,a[0],a[1],this._tooltipContent,s):this._showTooltipContent(l,o,s,Math.random(),a[0],a[1],d,void 0,c)})},_showSeriesItemTooltip:function(t,e,i){var n=this._ecModel,r=e.seriesIndex,a=n.getSeriesByIndex(r),o=e.dataModel||a,s=e.dataIndex,l=e.dataType,h=o.getData(),u=rf([h.getItemModel(s),o,a&&(a.coordinateSystem||{}).model,this._tooltipModel]),c=u.get("trigger");if(null==c||"item"===c){var d,f,p=o.getDataParams(s,l),g=o.formatTooltip(s,!1,l,this._renderMode);S(g)?(d=g.html,f=g.markers):(d=g,f=null);var v="item_"+o.name+"_"+s;this._showOrMove(u,function(){this._showTooltipContent(u,d,p,v,t.offsetX,t.offsetY,t.position,t.target,f)}),i({type:"showTip",dataIndexInside:s,dataIndex:h.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,i){var n=e.tooltip;if("string"==typeof n){var r=n;n={content:r,formatter:r}}var a=new Wa(n,this._tooltipModel,this._ecModel),o=a.get("content"),s=Math.random();this._showOrMove(a,function(){this._showTooltipContent(a,o,a.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),i({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,i,n,r,a,o,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var h=this._tooltipContent,u=t.get("formatter");o=o||t.get("position");var c=e;if(u&&"string"==typeof u)c=vo(u,i,!0);else if("function"==typeof u){var d=vS(function(e,n){e===this._ticket&&(h.setContent(n,l,t),this._updatePosition(t,o,r,a,h,i,s))},this);this._ticket=n,c=u(i,n,d)}h.setContent(c,l,t),h.show(t),this._updatePosition(t,o,r,a,h,i,s)}},_updatePosition:function(t,e,i,n,r,a,o){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var h=r.getSize(),u=t.get("align"),c=t.get("verticalAlign"),d=o&&o.getBoundingRect().clone();if(o&&d.applyTransform(o.transform),"function"==typeof e&&(e=e([i,n],a,r.el,d,{viewSize:[s,l],contentSize:h.slice()})),x(e))i=yS(e[0],s),n=yS(e[1],l);else if(S(e)){e.width=h[0],e.height=h[1];var f=Io(e,{width:s,height:l});i=f.x,n=f.y,u=null,c=null}else if("string"==typeof e&&o){var p=lf(e,d,h);i=p[0],n=p[1]}else{var p=of(i,n,r,s,l,u?null:20,c?null:20);i=p[0],n=p[1]}if(u&&(i-=hf(u)?h[0]/2:"right"===u?h[0]:0),c&&(n-=hf(c)?h[1]/2:"bottom"===c?h[1]:0),t.get("confine")){var p=sf(i,n,r,s,l);i=p[0],n=p[1]}r.moveTo(i,n)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,i=!!e&&e.length===t.length;return i&&mS(e,function(e,n){var r=e.dataByAxis||{},a=t[n]||{},o=a.dataByAxis||[];i&=r.length===o.length,i&&mS(r,function(t,e){var n=o[e]||{},r=t.seriesDataIndices||[],a=n.seriesDataIndices||[];i&=t.value===n.value&&t.axisType===n.axisType&&t.axisId===n.axisId&&r.length===a.length,i&&mS(r,function(t,e){var n=a[e];i&=t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})})}),this._lastDataByCoordSys=t,!!i},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){jf.node||(this._tooltipContent.hide(),Bd("itemTooltip",e))}}),Kl({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),Kl({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var xS,wS="urn:schemas-microsoft-com:vml",bS="undefined"==typeof window?null:window,SS=!1,MS=bS&&bS.document;if(MS&&!jf.canvasSupported)try{!MS.namespaces.zrvml&&MS.namespaces.add("zrvml",wS),xS=function(t){return MS.createElement("<zrvml:"+t+' class="zrvml">')}}catch(IS){xS=function(t){return MS.createElement("<"+t+' xmlns="'+wS+'" class="zrvml">')}}var TS=Gv.CMD,CS=Math.round,DS=Math.sqrt,AS=Math.abs,kS=Math.cos,PS=Math.sin,LS=Math.max;if(!jf.canvasSupported){var OS=",",ES="progid:DXImageTransform.Microsoft",BS=21600,zS=BS/2,RS=1e5,NS=1e3,FS=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=BS+","+BS,t.coordorigin="0,0"},VS=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},HS=function(t,e,i){return"rgb("+[t,e,i].join(",")+")"},WS=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},GS=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},US=function(t,e,i){return(parseFloat(t)||0)*RS+(parseFloat(e)||0)*NS+i},XS=function(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t},jS=function(t,e,i){var n=Xe(e);i=+i,isNaN(i)&&(i=1),n&&(t.color=HS(n[0],n[1],n[2]),t.opacity=i*n[3])},YS=function(t){var e=Xe(t);return[HS(e[0],e[1],e[2]),e[3]]},qS=function(t,e,i){var n=e.fill;if(null!=n)if(n instanceof Om){var r,a=0,o=[0,0],s=0,l=1,h=i.getBoundingRect(),u=h.width,c=h.height;if("linear"===n.type){r="gradient";var d=i.transform,f=[n.x*u,n.y*c],p=[n.x2*u,n.y2*c];d&&(ae(f,f,d),ae(p,p,d));var g=p[0]-f[0],v=p[1]-f[1];a=180*Math.atan2(g,v)/Math.PI,0>a&&(a+=360),1e-6>a&&(a=0)}else{r="gradientradial";var f=[n.x*u,n.y*c],d=i.transform,m=i.scale,y=u,_=c;o=[(f[0]-h.x)/y,(f[1]-h.y)/_],d&&ae(f,f,d),y/=m[0]*BS,_/=m[1]*BS;var x=LS(y,_);s=0/x,l=2*n.r/x-s}var w=n.colorStops.slice();w.sort(function(t,e){return t.offset-e.offset});for(var b=w.length,S=[],M=[],I=0;b>I;I++){var T=w[I],C=YS(T.color);M.push(T.offset*l+s+" "+C[0]),(0===I||I===b-1)&&S.push(C)}if(b>=2){var D=S[0][0],A=S[1][0],k=S[0][1]*e.opacity,P=S[1][1]*e.opacity;t.type=r,t.method="none",t.focus="100%",t.angle=a,t.color=D,t.color2=A,t.colors=M.join(","),t.opacity=P,t.opacity2=k}"radial"===r&&(t.focusposition=o.join(","))}else jS(t,n,e.opacity)},ZS=function(t,e){null!=e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof Om||jS(t,e.stroke,e.opacity)},$S=function(t,e,i,n){var r="fill"===e,a=t.getElementsByTagName(e)[0];null!=i[e]&&"none"!==i[e]&&(r||!r&&i.lineWidth)?(t[r?"filled":"stroked"]="true",i[e]instanceof Om&&GS(t,a),a||(a=uf(e)),r?qS(a,i,n):ZS(a,i),WS(t,a)):(t[r?"filled":"stroked"]="false",GS(t,a))},KS=[[],[],[]],QS=function(t,e){var i,n,r,a,o,s,l=TS.M,h=TS.C,u=TS.L,c=TS.A,d=TS.Q,f=[],p=t.data,g=t.len();for(a=0;g>a;){switch(r=p[a++],n="",i=0,r){case l:n=" m ",i=1,o=p[a++],s=p[a++],KS[0][0]=o,KS[0][1]=s;break;case u:n=" l ",i=1,o=p[a++],s=p[a++],KS[0][0]=o,KS[0][1]=s;break;case d:case h:n=" c ",i=3;var v,m,y=p[a++],_=p[a++],x=p[a++],w=p[a++];r===d?(v=x,m=w,x=(x+2*y)/3,w=(w+2*_)/3,y=(o+2*y)/3,_=(s+2*_)/3):(v=p[a++],m=p[a++]),KS[0][0]=y,KS[0][1]=_,KS[1][0]=x,KS[1][1]=w,KS[2][0]=v,KS[2][1]=m,o=v,s=m;break;case c:var b=0,S=0,M=1,I=1,T=0;e&&(b=e[4],S=e[5],M=DS(e[0]*e[0]+e[1]*e[1]),I=DS(e[2]*e[2]+e[3]*e[3]),T=Math.atan2(-e[1]/I,e[0]/M));var C=p[a++],D=p[a++],A=p[a++],k=p[a++],P=p[a++]+T,L=p[a++]+P+T;a++;var O=p[a++],E=C+kS(P)*A,B=D+PS(P)*k,y=C+kS(L)*A,_=D+PS(L)*k,z=O?" wa ":" at ";Math.abs(E-y)<1e-4&&(Math.abs(L-P)>.01?O&&(E+=270/BS):Math.abs(B-D)<1e-4?O&&C>E||!O&&E>C?_-=270/BS:_+=270/BS:O&&D>B||!O&&B>D?y+=270/BS:y-=270/BS),f.push(z,CS(((C-A)*M+b)*BS-zS),OS,CS(((D-k)*I+S)*BS-zS),OS,CS(((C+A)*M+b)*BS-zS),OS,CS(((D+k)*I+S)*BS-zS),OS,CS((E*M+b)*BS-zS),OS,CS((B*I+S)*BS-zS),OS,CS((y*M+b)*BS-zS),OS,CS((_*I+S)*BS-zS)),o=y,s=_;break;case TS.R:var R=KS[0],N=KS[1];R[0]=p[a++],R[1]=p[a++],N[0]=R[0]+p[a++],N[1]=R[1]+p[a++],e&&(ae(R,R,e),ae(N,N,e)),R[0]=CS(R[0]*BS-zS),N[0]=CS(N[0]*BS-zS),R[1]=CS(R[1]*BS-zS),N[1]=CS(N[1]*BS-zS),f.push(" m ",R[0],OS,R[1]," l ",N[0],OS,R[1]," l ",N[0],OS,N[1]," l ",R[0],OS,N[1]);break;case TS.Z:f.push(" x ")}if(i>0){f.push(n);for(var F=0;i>F;F++){var V=KS[F];e&&ae(V,V,e),f.push(CS(V[0]*BS-zS),OS,CS(V[1]*BS-zS),i-1>F?OS:"")}}}return f.join("")};Nr.prototype.brushVML=function(t){var e=this.style,i=this._vmlEl;i||(i=uf("shape"),FS(i),this._vmlEl=i),$S(i,"fill",e,this),$S(i,"stroke",e,this);var n=this.transform,r=null!=n,a=i.getElementsByTagName("stroke")[0];if(a){var o=e.lineWidth;if(r&&!e.strokeNoScale){var s=n[0]*n[3]-n[1]*n[2];o*=DS(AS(s))}a.weight=o+"px"}var l=this.path||(this.path=new Gv);this.__dirtyPath&&(l.beginPath(),l.subPixelOptimize=!1,this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),i.path=QS(l,this.transform),i.style.zIndex=US(this.zlevel,this.z,this.z2),WS(t,i),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},Nr.prototype.onRemove=function(t){GS(t,this._vmlEl),this.removeRectText(t)},Nr.prototype.onAdd=function(t){WS(t,this._vmlEl),this.appendRectText(t)};var JS=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};xn.prototype.brushVML=function(t){var e,i,n=this.style,r=n.image;if(JS(r)){var a=r.src;if(a===this._imageSrc)e=this._imageWidth,i=this._imageHeight;else{var o=r.runtimeStyle,s=o.width,l=o.height;o.width="auto",o.height="auto",e=r.width,i=r.height,o.width=s,o.height=l,this._imageSrc=a,this._imageWidth=e,this._imageHeight=i}r=a}else r===this._imageSrc&&(e=this._imageWidth,i=this._imageHeight);if(r){var h=n.x||0,u=n.y||0,c=n.width,d=n.height,f=n.sWidth,p=n.sHeight,g=n.sx||0,v=n.sy||0,m=f&&p,y=this._vmlEl;y||(y=MS.createElement("div"),FS(y),this._vmlEl=y);var _,x=y.style,w=!1,b=1,S=1;if(this.transform&&(_=this.transform,b=DS(_[0]*_[0]+_[1]*_[1]),S=DS(_[2]*_[2]+_[3]*_[3]),w=_[1]||_[2]),w){var M=[h,u],I=[h+c,u],T=[h,u+d],C=[h+c,u+d];ae(M,M,_),ae(I,I,_),ae(T,T,_),ae(C,C,_);var D=LS(M[0],I[0],T[0],C[0]),A=LS(M[1],I[1],T[1],C[1]),k=[];k.push("M11=",_[0]/b,OS,"M12=",_[2]/S,OS,"M21=",_[1]/b,OS,"M22=",_[3]/S,OS,"Dx=",CS(h*b+_[4]),OS,"Dy=",CS(u*S+_[5])),x.padding="0 "+CS(D)+"px "+CS(A)+"px 0",x.filter=ES+".Matrix("+k.join("")+", SizingMethod=clip)"}else _&&(h=h*b+_[4],u=u*S+_[5]),x.filter="",x.left=CS(h)+"px",x.top=CS(u)+"px";var P=this._imageEl,L=this._cropEl;P||(P=MS.createElement("div"),this._imageEl=P);var O=P.style;if(m){if(e&&i)O.width=CS(b*e*c/f)+"px",O.height=CS(S*i*d/p)+"px";else{var E=new Image,B=this;E.onload=function(){E.onload=null,e=E.width,i=E.height,O.width=CS(b*e*c/f)+"px",O.height=CS(S*i*d/p)+"px",B._imageWidth=e,B._imageHeight=i,B._imageSrc=r},E.src=r}L||(L=MS.createElement("div"),L.style.overflow="hidden",this._cropEl=L);var z=L.style;z.width=CS((c+g*c/f)*b),z.height=CS((d+v*d/p)*S),z.filter=ES+".Matrix(Dx="+-g*c/f*b+",Dy="+-v*d/p*S+")",L.parentNode||y.appendChild(L),P.parentNode!==L&&L.appendChild(P)}else O.width=CS(b*c)+"px",O.height=CS(S*d)+"px",y.appendChild(P),L&&L.parentNode&&(y.removeChild(L),this._cropEl=null);var R="",N=n.opacity;1>N&&(R+=".Alpha(opacity="+CS(100*N)+") "),R+=ES+".AlphaImageLoader(src="+r+", SizingMethod=scale)",O.filter=R,y.style.zIndex=US(this.zlevel,this.z,this.z2),WS(t,y),null!=n.text&&this.drawRectText(t,this.getBoundingRect())}},xn.prototype.onRemove=function(t){GS(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},xn.prototype.onAdd=function(t){WS(t,this._vmlEl),this.appendRectText(t)};var tM,eM="normal",iM={},nM=0,rM=100,aM=document.createElement("div"),oM=function(t){var e=iM[t];if(!e){nM>rM&&(nM=0,iM={});var i,n=aM.style;try{n.font=t,i=n.fontFamily.split(",")[0]}catch(r){}e={style:n.fontStyle||eM,variant:n.fontVariant||eM,weight:n.fontWeight||eM,size:0|parseFloat(n.fontSize||12),family:i||"Microsoft YaHei"},iM[t]=e,nM++}return e};Bi("measureText",function(t,e){var i=MS;tM||(tM=i.createElement("div"),tM.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",MS.body.appendChild(tM));try{tM.style.font=e}catch(n){}return tM.innerHTML="",tM.appendChild(i.createTextNode(t)),{width:tM.offsetWidth}});for(var sM=new mi,lM=function(t,e,i,n){var r=this.style;this.__dirty&&tn(r,!0);var a=r.text;if(null!=a&&(a+=""),a){if(r.rich){var o=$i(a,r);a=[];for(var s=0;s<o.lines.length;s++){for(var l=o.lines[s].tokens,h=[],u=0;u<l.length;u++)h.push(l[u].text);a.push(h.join(""))}a=a.join("\n")}var c,d,f=r.textAlign,p=r.textVerticalAlign,g=oM(r.font),v=g.style+" "+g.variant+" "+g.weight+" "+g.size+'px "'+g.family+'"';i=i||Ri(a,v,f,p,r.textPadding,r.textLineHeight);var m=this.transform;if(m&&!n&&(sM.copy(e),sM.applyTransform(m),e=sM),n)c=e.x,d=e.y;else{var y=r.textPosition,_=r.textDistance;if(y instanceof Array)c=e.x+XS(y[0],e.width),d=e.y+XS(y[1],e.height),f=f||"left";else{var x=Wi(y,e,_);c=x.x,d=x.y,f=f||x.textAlign,p=p||x.textVerticalAlign}}c=Vi(c,i.width,f),d=Hi(d,i.height,p),d+=i.height/2;var w,b,S,M=uf,I=this._textVmlEl;I?(S=I.firstChild,w=S.nextSibling,b=w.nextSibling):(I=M("line"),w=M("path"),b=M("textpath"),S=M("skew"),b.style["v-text-align"]="left",FS(I),w.textpathok=!0,b.on=!0,I.from="0 0",I.to="1000 0.05",WS(I,S),WS(I,w),WS(I,b),this._textVmlEl=I);var T=[c,d],C=I.style;m&&n?(ae(T,T,m),S.on=!0,S.matrix=m[0].toFixed(3)+OS+m[2].toFixed(3)+OS+m[1].toFixed(3)+OS+m[3].toFixed(3)+",0,0",S.offset=(CS(T[0])||0)+","+(CS(T[1])||0),S.origin="0 0",C.left="0px",C.top="0px"):(S.on=!1,C.left=CS(c)+"px",C.top=CS(d)+"px"),b.string=VS(a);try{b.style.font=v}catch(D){}$S(I,"fill",{fill:r.textFill,opacity:r.opacity},this),$S(I,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash},this),I.style.zIndex=US(this.zlevel,this.z,this.z2),WS(t,I)}},hM=function(t){GS(t,this._textVmlEl),this._textVmlEl=null},uM=function(t){WS(t,this._textVmlEl)},cM=[kg,_n,xn,Nr,pm],dM=0;dM<cM.length;dM++){var fM=cM[dM].prototype;fM.drawRectText=lM,fM.removeRectText=hM,fM.appendRectText=uM}pm.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},pm.prototype.onRemove=function(t){this.removeRectText(t)},pm.prototype.onAdd=function(t){this.appendRectText(t)}}ff.prototype={constructor:ff,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,i=0;i<t.length;i++){var n=t[i];n.invisible||n.ignore?(n.__alreadyNotVisible||n.onRemove(e),n.__alreadyNotVisible=!0):(n.__alreadyNotVisible&&n.onAdd(e),n.__alreadyNotVisible=!1,n.__dirty&&(n.beforeBrush&&n.beforeBrush(),(n.brushVML||n.brush).call(n,e),n.afterBrush&&n.afterBrush())),n.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!==t||this._height!==e){this._width=t,this._height=e;var i=this._vmlViewport.style;i.width=t+"px",i.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||df(e.width))-df(e.paddingLeft)-df(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||df(e.height))-df(e.paddingTop)-df(e.paddingBottom)|0}},f(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){ff.prototype[t]=pf(t)}),Bn("vml",ff);var pM="http://www.w3.org/2000/svg",gM=Gv.CMD,vM=Array.prototype.join,mM="none",yM=Math.round,_M=Math.sin,xM=Math.cos,wM=Math.PI,bM=2*Math.PI,SM=180/wM,MM=1e-4,IM={};IM.brush=function(t){var e=t.style,i=t.__svgEl;i||(i=gf("path"),t.__svgEl=i),t.path||t.createPathProxy();var n=t.path;if(t.__dirtyPath){n.beginPath(),n.subPixelOptimize=!1,t.buildPath(n,t.shape),t.__dirtyPath=!1;var r=Mf(n);r.indexOf("NaN")<0&&wf(i,"d",r)}Sf(i,e,!1,t),xf(i,t.transform),null!=e.text&&AM(t,t.getBoundingRect())};var TM={};TM.brush=function(t){var e=t.style,i=e.image;if(i instanceof HTMLImageElement){var n=i.src;i=n}if(i){var r=e.x||0,a=e.y||0,o=e.width,s=e.height,l=t.__svgEl;l||(l=gf("image"),t.__svgEl=l),i!==t.__imageSrc&&(bf(l,"href",i),t.__imageSrc=i),wf(l,"width",o),wf(l,"height",s),wf(l,"x",r),wf(l,"y",a),xf(l,t.transform),null!=e.text&&AM(t,t.getBoundingRect())}};var CM={},DM=new mi,AM=function(t,e,i){var n=t.style;t.__dirty&&tn(n,!0);var r=n.text;if(null!=r){r+="";var a=t.__textSvgEl;a||(a=gf("text"),t.__textSvgEl=a);var o,s,l=n.textPosition,h=n.textDistance,u=n.textAlign||"left";"number"==typeof n.fontSize&&(n.fontSize+="px");var c=n.font||[n.fontStyle||"",n.fontWeight||"",n.fontSize||"",n.fontFamily||""].join(" ")||Sg,d=If(n.textVerticalAlign);i=Ri(r,c,u,d,n.textPadding,n.textLineHeight);var f=i.lineHeight;if(l instanceof Array)o=e.x+l[0],s=e.y+l[1];else{var p=Wi(l,e,h);o=p.x,s=p.y,d=If(p.textVerticalAlign),u=p.textAlign}wf(a,"alignment-baseline",d),c&&(a.style.font=c);var g=n.textPadding;if(wf(a,"x",o),wf(a,"y",s),Sf(a,n,!0,t),t instanceof pm||t.style.transformText)xf(a,t.transform);else{if(t.transform)DM.copy(e),DM.applyTransform(t.transform),e=DM;else{var v=t.transformCoordToGlobal(e.x,e.y);e.x=v[0],e.y=v[1],t.transform=Ie(Me())}var m=n.textOrigin;"center"===m?(o=i.width/2+o,s=i.height/2+s):m&&(o=m[0]+o,s=m[1]+s);var y=-n.textRotation||0,_=Me();Ae(_,_,y);var v=[t.transform[4],t.transform[5]];De(_,_,v),xf(a,_)}var x=r.split("\n"),w=x.length,b=u;"left"===b?(b="start",g&&(o+=g[3])):"right"===b?(b="end",g&&(o-=g[1])):"center"===b&&(b="middle",g&&(o+=(g[3]-g[1])/2));var S=0;if("after-edge"===d?(S=-i.height+f,g&&(S-=g[2])):"middle"===d?(S=(-i.height+f)/2,g&&(s+=(g[0]-g[2])/2)):g&&(S+=g[0]),t.__text!==r||t.__textFont!==c){var M=t.__tspanList||[];t.__tspanList=M;for(var I=0;w>I;I++){var T=M[I];T?T.innerHTML="":(T=M[I]=gf("tspan"),a.appendChild(T),wf(T,"alignment-baseline",d),wf(T,"text-anchor",b)),wf(T,"x",o),wf(T,"y",s+I*f+S),T.appendChild(document.createTextNode(x[I]))}for(;I<M.length;I++)a.removeChild(M[I]);M.length=w,t.__text=r,t.__textFont=c}else if(t.__tspanList.length)for(var C=t.__tspanList.length,I=0;C>I;++I){var T=t.__tspanList[I];T&&(wf(T,"x",o),wf(T,"y",s+I*f+S))}}};CM.drawRectText=AM,CM.brush=function(t){var e=t.style;null!=e.text&&(e.textPosition=[0,0],AM(t,{x:e.x||0,y:e.y||0,width:0,height:0},t.getBoundingRect()))},Tf.prototype={diff:function(t,e,i){function n(){for(var i=-1*s;s>=i;i+=2){var n,l=h[i-1],u=h[i+1],c=(u?u.newPos:0)-i;l&&(h[i-1]=void 0);var d=l&&l.newPos+1<a,f=u&&c>=0&&o>c;if(d||f){if(!d||f&&l.newPos<u.newPos?(n=Df(u),r.pushComponent(n.components,void 0,!0)):(n=l,n.newPos++,r.pushComponent(n.components,!0,void 0)),c=r.extractCommon(n,e,t,i),n.newPos+1>=a&&c+1>=o)return Cf(r,n.components,e,t);h[i]=n}else h[i]=void 0}s++}i||(i=function(t,e){return t===e}),this.equals=i;var r=this;t=t.slice(),e=e.slice();var a=e.length,o=t.length,s=1,l=a+o,h=[{newPos:-1,components:[]}],u=this.extractCommon(h[0],e,t,0);if(h[0].newPos+1>=a&&u+1>=o){for(var c=[],d=0;d<e.length;d++)c.push(d);return[{indices:c,count:e.length}]}for(;l>=s;){var f=n();if(f)return f}},pushComponent:function(t,e,i){var n=t[t.length-1];n&&n.added===e&&n.removed===i?t[t.length-1]={count:n.count+1,added:e,removed:i}:t.push({count:1,added:e,removed:i})},extractCommon:function(t,e,i,n){for(var r=e.length,a=i.length,o=t.newPos,s=o-n,l=0;r>o+1&&a>s+1&&this.equals(e[o+1],i[s+1]);)o++,s++,l++;return l&&t.components.push({count:l}),t.newPos=o,s},tokenize:function(t){return t.slice()},join:function(t){return t.slice()}};var kM=new Tf,PM=function(t,e,i){return kM.diff(t,e,i)},LM="0",OM="1";Af.prototype.createElement=gf,Af.prototype.getDefs=function(t){var e=this._svgRoot,i=this._svgRoot.getElementsByTagName("defs");return 0===i.length?t?(i=e.insertBefore(this.createElement("defs"),e.firstChild),i.contains||(i.contains=function(t){var e=i.children;if(!e)return!1;for(var n=e.length-1;n>=0;--n)if(e[n]===t)return!0;return!1}),i):null:i[0]},Af.prototype.update=function(t,e){if(t){var i=this.getDefs(!1);if(t[this._domName]&&i.contains(t[this._domName]))"function"==typeof e&&e(t);else{var n=this.add(t);n&&(t[this._domName]=n)}}},Af.prototype.addDom=function(t){var e=this.getDefs(!0);e.appendChild(t)},Af.prototype.removeDom=function(t){var e=this.getDefs(!1);e&&t[this._domName]&&(e.removeChild(t[this._domName]),t[this._domName]=null)},Af.prototype.getDoms=function(){var t=this.getDefs(!1);if(!t)return[];var e=[];return f(this._tagNames,function(i){var n=t.getElementsByTagName(i);e=e.concat([].slice.call(n))}),e},Af.prototype.markAllUnused=function(){var t=this.getDoms(),e=this;f(t,function(t){t[e._markLabel]=LM})},Af.prototype.markUsed=function(t){t&&(t[this._markLabel]=OM)},Af.prototype.removeUnused=function(){var t=this.getDefs(!1);if(t){var e=this.getDoms(),i=this;f(e,function(e){e[i._markLabel]!==OM&&t.removeChild(e)})}},Af.prototype.getSvgProxy=function(t){return t instanceof Nr?IM:t instanceof xn?TM:t instanceof pm?CM:IM},Af.prototype.getTextSvgElement=function(t){return t.__textSvgEl},Af.prototype.getSvgElement=function(t){return t.__svgEl},u(kf,Af),kf.prototype.addWithoutUpdate=function(t,e){if(e&&e.style){var i=this;f(["fill","stroke"],function(n){if(e.style[n]&&("linear"===e.style[n].type||"radial"===e.style[n].type)){var r,a=e.style[n],o=i.getDefs(!0);a._dom?(r=a._dom,o.contains(a._dom)||i.addDom(r)):r=i.add(a),i.markUsed(e);var s=r.getAttribute("id");t.setAttribute(n,"url(#"+s+")")}})}},kf.prototype.add=function(t){var e;if("linear"===t.type)e=this.createElement("linearGradient");else{if("radial"!==t.type)return $p("Illegal gradient type."),null;e=this.createElement("radialGradient")}return t.id=t.id||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-gradient-"+t.id),this.updateDom(t,e),this.addDom(e),e},kf.prototype.update=function(t){var e=this;Af.prototype.update.call(this,t,function(){var i=t.type,n=t._dom.tagName;"linear"===i&&"linearGradient"===n||"radial"===i&&"radialGradient"===n?e.updateDom(t,t._dom):(e.removeDom(t),e.add(t))})},kf.prototype.updateDom=function(t,e){if("linear"===t.type)e.setAttribute("x1",t.x),e.setAttribute("y1",t.y),e.setAttribute("x2",t.x2),e.setAttribute("y2",t.y2);else{if("radial"!==t.type)return void $p("Illegal gradient type.");e.setAttribute("cx",t.x),e.setAttribute("cy",t.y),e.setAttribute("r",t.r)}t.global?e.setAttribute("gradientUnits","userSpaceOnUse"):e.setAttribute("gradientUnits","objectBoundingBox"),e.innerHTML="";for(var i=t.colorStops,n=0,r=i.length;r>n;++n){var a=this.createElement("stop");a.setAttribute("offset",100*i[n].offset+"%");var o=i[n].color;if(o.indexOf(!1)){var s=Xe(o)[3],l=Ze(o);a.setAttribute("stop-color","#"+l),a.setAttribute("stop-opacity",s)}else a.setAttribute("stop-color",i[n].color);e.appendChild(a)}t._dom=e},kf.prototype.markUsed=function(t){if(t.style){var e=t.style.fill;e&&e._dom&&Af.prototype.markUsed.call(this,e._dom),e=t.style.stroke,e&&e._dom&&Af.prototype.markUsed.call(this,e._dom)}},u(Pf,Af),Pf.prototype.update=function(t){var e=this.getSvgElement(t);e&&this.updateDom(e,t.__clipPaths,!1);var i=this.getTextSvgElement(t);i&&this.updateDom(i,t.__clipPaths,!0),this.markUsed(t)},Pf.prototype.updateDom=function(t,e,i){if(e&&e.length>0){var n,r,a=this.getDefs(!0),o=e[0],s=i?"_textDom":"_dom";o[s]?(r=o[s].getAttribute("id"),n=o[s],a.contains(n)||a.appendChild(n)):(r="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,n=this.createElement("clipPath"),n.setAttribute("id",r),a.appendChild(n),o[s]=n);var l=this.getSvgProxy(o);if(o.transform&&o.parent.invTransform&&!i){var h=Array.prototype.slice.call(o.transform);Ce(o.transform,o.parent.invTransform,o.transform),l.brush(o),o.transform=h}else l.brush(o);var u=this.getSvgElement(o);n.innerHTML="",n.appendChild(u.cloneNode()),t.setAttribute("clip-path","url(#"+r+")"),e.length>1&&this.updateDom(n,e.slice(1),i)}else t&&t.setAttribute("clip-path","none")},Pf.prototype.markUsed=function(t){var e=this;t.__clipPaths&&t.__clipPaths.length>0&&f(t.__clipPaths,function(t){t._dom&&Af.prototype.markUsed.call(e,t._dom),t._textDom&&Af.prototype.markUsed.call(e,t._textDom)})},u(Lf,Af),Lf.prototype.addWithoutUpdate=function(t,e){if(e&&Of(e.style)){var i,n=e.style;if(n._shadowDom){i=n._shadowDom;var r=this.getDefs(!0);r.contains(n._shadowDom)||this.addDom(i)}else i=this.add(e);this.markUsed(e);var a=i.getAttribute("id");t.style.filter="url(#"+a+")"}},Lf.prototype.add=function(t){var e=this.createElement("filter"),i=t.style;return i._shadowDomId=i._shadowDomId||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-shadow-"+i._shadowDomId),this.updateDom(t,e),this.addDom(e),e},Lf.prototype.update=function(t,e){var i=e.style;if(Of(i)){var n=this;Af.prototype.update.call(this,e,function(t){n.updateDom(e,t._shadowDom)})}else this.remove(t,i)},Lf.prototype.remove=function(t,e){null!=e._shadowDomId&&(this.removeDom(e),t.style.filter="")},Lf.prototype.updateDom=function(t,e){var i=e.getElementsByTagName("feDropShadow");i=0===i.length?this.createElement("feDropShadow"):i[0];var n,r,a,o,s=t.style,l=t.scale?t.scale[0]||1:1,h=t.scale?t.scale[1]||1:1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)n=s.shadowOffsetX||0,r=s.shadowOffsetY||0,a=s.shadowBlur,o=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(e,s);n=s.textShadowOffsetX||0,r=s.textShadowOffsetY||0,a=s.textShadowBlur,o=s.textShadowColor}i.setAttribute("dx",n/l),i.setAttribute("dy",r/h),i.setAttribute("flood-color",o);var u=a/2/l,c=a/2/h,d=u+" "+c;i.setAttribute("stdDeviation",d),e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width",Math.ceil(a/2*200)+"%"),e.setAttribute("height",Math.ceil(a/2*200)+"%"),e.appendChild(i),s._shadowDom=e},Lf.prototype.markUsed=function(t){var e=t.style;e&&e._shadowDom&&Af.prototype.markUsed.call(this,e._shadowDom)};var EM=function(t,e,i,n){this.root=t,this.storage=e,this._opts=i=o({},i||{});var r=gf("svg");r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("version","1.1"),r.setAttribute("baseProfile","full"),r.style.cssText="user-select:none;position:absolute;left:0;top:0;",this.gradientManager=new kf(n,r),this.clipPathManager=new Pf(n,r),this.shadowManager=new Lf(n,r);var a=document.createElement("div");a.style.cssText="overflow:hidden;position:relative",this._svgRoot=r,this._viewport=a,t.appendChild(a),a.appendChild(r),this.resize(i.width,i.height),this._visibleList=[]};EM.prototype={constructor:EM,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(){var t=this.storage.getDisplayList(!0);this._paintList(t)},setBackgroundColor:function(t){this._viewport.style.background=t},_paintList:function(t){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();var e,i=this._svgRoot,n=this._visibleList,r=t.length,a=[];for(e=0;r>e;e++){var o=t[e],s=Bf(o),l=Hf(o)||Vf(o);o.invisible||(o.__dirty&&(s&&s.brush(o),this.clipPathManager.update(o),o.style&&(this.gradientManager.update(o.style.fill),this.gradientManager.update(o.style.stroke),this.shadowManager.update(l,o)),o.__dirty=!1),a.push(o))
}var h,u=PM(n,a);for(e=0;e<u.length;e++){var c=u[e];if(c.removed)for(var d=0;d<c.count;d++){var o=n[c.indices[d]],l=Hf(o),f=Vf(o);Ff(i,l),Ff(i,f)}}for(e=0;e<u.length;e++){var c=u[e];if(c.added)for(var d=0;d<c.count;d++){var o=a[c.indices[d]],l=Hf(o),f=Vf(o);h?Rf(i,l,h):Nf(i,l),l?Rf(i,f,l):h?Rf(i,f,h):Nf(i,f),Rf(i,f,l),h=f||l||h,this.gradientManager.addWithoutUpdate(l,o),this.shadowManager.addWithoutUpdate(h,o),this.clipPathManager.markUsed(o)}else if(!c.removed)for(var d=0;d<c.count;d++){var o=a[c.indices[d]];h=l=Vf(o)||Hf(o)||h,this.gradientManager.markUsed(o),this.gradientManager.addWithoutUpdate(l,o),this.shadowManager.markUsed(o),this.shadowManager.addWithoutUpdate(l,o),this.clipPathManager.markUsed(o)}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=a},_getDefs:function(t){var e=this._svgRoot,i=this._svgRoot.getElementsByTagName("defs");if(0===i.length){if(t){var i=e.insertBefore(gf("defs"),e.firstChild);return i.contains||(i.contains=function(t){var e=i.children;if(!e)return!1;for(var n=e.length-1;n>=0;--n)if(e[n]===t)return!0;return!1}),i}return null}return i[0]},resize:function(t,e){var i=this._viewport;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!==t||this._height!==e){this._width=t,this._height=e;var r=i.style;r.width=t+"px",r.height=e+"px";var a=this._svgRoot;a.setAttribute("width",t),a.setAttribute("height",e)}},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[n]||Ef(s[i])||Ef(o.style[i]))-(Ef(s[r])||0)-(Ef(s[a])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},pathToDataUrl:function(){this.refresh();var t=this._svgRoot.outerHTML;return"data:image/svg+xml;charset=UTF-8,"+t}},f(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){EM.prototype[t]=Wf(t)}),Bn("svg",EM),t.version=Y_,t.dependencies=q_,t.PRIORITY=nx,t.init=Hl,t.connect=Wl,t.disConnect=Gl,t.disconnect=Mx,t.dispose=Ul,t.getInstanceByDom=Xl,t.getInstanceById=jl,t.registerTheme=Yl,t.registerPreprocessor=ql,t.registerProcessor=Zl,t.registerPostUpdate=$l,t.registerAction=Kl,t.registerCoordinateSystem=Ql,t.getCoordinateSystemDimensions=Jl,t.registerLayout=th,t.registerVisual=eh,t.registerLoading=nh,t.extendComponentModel=rh,t.extendComponentView=ah,t.extendSeriesModel=oh,t.extendChartView=sh,t.setCanvasCreator=lh,t.registerMap=hh,t.getMap=uh,t.dataTool=Ix,t.zrender=Kg,t.number=iy,t.format=hy,t.throttle=Xs,t.helper=Tw,t.matrix=Mp,t.vector=dp,t.color=Gp,t.parseGeoJSON=Dw,t.parseGeoJson=Lw,t.util=Ow,t.graphic=Ew,t.List=Rx,t.Model=Wa,t.Axis=Pw,t.env=jf});