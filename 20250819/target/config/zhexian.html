<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ECharts数据统计图</title>
</head>
<body>
<!-- 为 ECharts 准备一个具备大小（宽高）的 DOM -->
<div id="main" style="width:950px; height:660px;"></div>
<script type="text/javascript" src="jquery-3.1.1.min.js"></script>
<script type="text/javascript" src="echarts.min.js"></script>
<script type="text/javascript">
    var myChart = echarts.init(document.getElementById('main'));
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '折线图',
            textStyle: {
                fontSize: 14,
                color: '#1e85e6'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                lineStyle: { color: 'transparent' },
            },
        },
        calculable: true,
        grid: {
            top: "16%",
            bottom: "20%",
            left: "10%",
            right: "3%"
        },
        xAxis: {
            type: 'category',
            boundaryGap: ['10%', '10%'],
            axisLine: {
                lineStyle: { color: '#e6e9f1' }
            },
            axisLabel: {
                color: '#203e66',
                length: 7,
                formatter: function(val) {
                    var str = val.split(' ');
                    return str.join('\n')
                }
            },
            data: ['2019-09-20', '2019-09-19', '2019-09-18', '2019-09-17', '2019-09-16']
        },
        yAxis: [{
            type: 'value',
            axisTick: { show: false },//坐标轴刻度
            axisLine: {
                lineStyle: { color: '#e6e9f1' }
            },
            axisLabel: {
                show: true,
                textStyle: {
                    color: '#203e66',
                },
                formatter: function(val) {
                    val = val + '%';
                    return val;
                }
    },
    splitLine: {
        lineStyle: { color: '#e6e9f1' }
    }
    }],
    series: [{
        name: 'test',
        type: 'line',
        smooth: true,
        itemStyle: {
            normal: {
                color: 'rgb(99,223,246)',
            },
            emphasis: {
                borderColor: 'red',
            }
        },
        data: []
    }]
    };

    myChart.setOption(option,true);

    //核心方法
    function getImage(data){
        option.series[0].data = data.y;
        option.xAxis.data = data.x;
        option.title.text = data.content;
        myChart.setOption(option,true);
    }

    function returnEcharts(){
//        return myChart.getDataURL().replace("data:image/png;base64,","");
        var imgData = getFullCanvasDataURL(document.getElementById('main'));
        myChart.clear();
        return imgData;
    }

    function getFullCanvasDataURL(el) {
        //将第一个画布作为基准。
        var baseCanvas = $(el).find("canvas").first()[0];
        if (!baseCanvas) {
            return false;
        };
        var width = el.width;
        var height = el.height;
        var ctx = baseCanvas.getContext("2d");
        //遍历，将后续的画布添加到在第一个上
        $(el).find("canvas").each(function(i, canvasObj) {
            if (i > 0) {
                var canvasTmp = $(canvasObj)[0];
                ctx.drawImage(canvasTmp, 0, 0, width+100, height);
            }
        });
        //获取base64位的url
        return baseCanvas.toDataURL('image/png',0.5).replace("data:image/png;base64,","");
    }

</script>
</body>
</html>