{"logstash": {"keys": "ywxtes.conf", "path": "bin", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "elasticsearch-cloud-shield": {"keys": "/dist/elasticsearch-cloud-shield/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "elasticsearch-cloud-shield-data": {"keys": "/dist/elasticsearch-cloud-shield-data/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "LogstashSyncService": {"keys": "inf-,Main,logstash", "path": "", "win_script": "", "lin_script": "sh start_sync.sh", "process": "1", "monitor": "0"}, "SuspectDomainDetect": {"keys": "'ImportData\\|DynamicDomainDetect\\|StaticDomainDetect\\|OverLengthDetect\\|IntranetDomainDetect'", "path": "bin", "win_script": "", "lin_script": "sh start_all.sh", "process": "5", "monitor": "0"}, "CertificateMiningService": {"keys": "'ImportBlack\\|ImportCrl\\|SSLDetect'", "path": "bin", "win_script": "", "lin_script": "sh start_all.sh", "process": "3", "monitor": "0"}, "elasticsearch-cloud-shield-data1": {"keys": "/dist/elasticsearch-cloud-shield-data1/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "elasticsearch-cloud-shield-data2": {"keys": "/dist/elasticsearch-cloud-shield-data2/lib,bootstrap", "path": "deploy", "win_script": "start.bat", "lin_script": "sh start.sh", "process": "1", "monitor": "0"}, "CodisMaster": {"keys": "codis", "path": "/usr/local/codis", "win_script": "", "lin_script": "sh start_master.sh", "process": "6", "monitor": "0"}, "CodisSlaver": {"keys": "codis", "path": "/usr/local/codis", "win_script": "", "lin_script": "sh start_slave.sh", "process": "4", "monitor": "0"}}