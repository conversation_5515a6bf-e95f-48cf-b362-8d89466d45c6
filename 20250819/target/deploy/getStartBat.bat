@echo off
set fileName=start.bat
set fileName_sh=start.sh
set serverName=MaintainServer
set thisBatName=%~n0.bat
set serviceJarName=%1
set mainClass=com.maintain.server.MaintainServerApplication

echo cd .. > target\deploy\%fileName%
echo cd .. > target\deploy\%fileName_sh%
echo title  %serverName%  >> target\deploy\%fileName%
set /p="java -Dservice-jar=%serviceJarName% -Xms8g -Xmx8g -Xmn6g  -Djava.library.path=.\java-libs -classpath ">>target\deploy\%fileName%<nul
set /p=".\libs\%serviceJarName%;">>target\deploy\%fileName%<nul
set /p="java -classpath ">>target\deploy\%fileName_sh%<nul
set /p="./libs/%serviceJarName%:">>target\deploy\%fileName_sh%<nul
set /p=".\java-libs\*;">>target\deploy\%fileName%<nul
set /p="./java-libs/*:">>target\deploy\%fileName_sh%<nul
set /p=" -server -Djava.ext.dirs="%%JAVA_HOME%%\jre\lib\ext" ">>target\deploy\%fileName%<nul
set /p=" -Djava.ext.dirs="$JAVA_HOME/jre/lib/ext" ">>target\deploy\%fileName_sh%<nul
set /p="%mainClass% ">>target\deploy\%fileName%<nul
set /p="%mainClass% ">>target\deploy\%fileName_sh%<nul
set /p="%%1=%%2,./deploy/icepool.conf">>target\deploy\%fileName%
set /p="%%1=%%2,./deploy/icepool.conf">>target\deploy\%fileName_sh%
echo.>> target\deploy\%fileName%
echo.>> target\deploy\%fileName_sh%
echo pause >> target\deploy\%fileName%
echo pause >> target\deploy\%fileName_sh%