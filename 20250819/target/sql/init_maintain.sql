/*
Navicat MySQL Data Transfer

Source Server         : ***************-root
Source Server Version : 50718
Source Host           : ***************:3306
Source Database       : maintain-2.0

Target Server Type    : MYSQL
Target Server Version : 50718
File Encoding         : 65001

Date: 2020-04-22 10:32:18
*/


USE `guard`;

DELIMITER ??
DROP procedure if EXISTS dev_ap_change??
CREATE PROCEDURE dev_ap_change()
BEGIN
IF NOT EXISTS(select * from information_schema.`COLUMNS`  where TABLE_SCHEMA = 'guard' AND TABLE_NAME = 'dev_ap' and COLUMN_NAME = 'NOTE') THEN 
	ALTER TABLE dev_ap ADD COLUMN NOTE VARCHAR(255);
ELSE
	ALTER TABLE dev_ap MODIFY COLUMN NOTE VARCHAR(255);
END IF;
END ??
DELIMITER ;
call dev_ap_change;

CREATE DATABASE IF NOT EXISTS `maintain-2.0` DEFAULT CHARACTER SET utf8;

USE `maintain-2.0`;

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for agent
-- ----------------------------
DROP TABLE IF EXISTS `agent`;
CREATE TABLE `agent` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `VERSION` varchar(10) NOT NULL,
  `IP` varchar(64) NOT NULL,
  `OS` tinyint(1) NOT NULL COMMENT '0=windows，1=linux',
  `PID` int(5) DEFAULT '0',
  `ROLE` varchar(255) NOT NULL DEFAULT '未知',
  `NAME` varchar(255) NOT NULL COMMENT 'SSH账号',
  `PSWD` varchar(255) NOT NULL COMMENT 'SSH密码',
  `ROOT_PSWD` varchar(255) DEFAULT NULL,
  `PORT` int(5) NOT NULL,
  `AUTO_NTP` tinyint(1) DEFAULT '0',
  `AUTO_WATCHDOG` tinyint(1) DEFAULT '0',
  `CREATE_TIME` datetime DEFAULT NULL,
  `LAST_HEARTBEAT_TIME` datetime DEFAULT NULL COMMENT '心跳更新时间',
  `STATUS` tinyint(1) DEFAULT '0',
  `OPERATE_TYPE` tinyint(1) DEFAULT '0',
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `PROGRAM_STATUS` tinyint(1) DEFAULT '1',
  `NOTE` varchar(255) DEFAULT NULL,
  `NTP_STATUS` int(10) DEFAULT NULL COMMENT 'ntp服务状态；0异常，1正常',
  `EXISTS_VPN` tinyint(1) DEFAULT '0' COMMENT '是否配置了VPN:0没有,1有配置',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for analysis
-- ----------------------------
DROP TABLE IF EXISTS `analysis`;
CREATE TABLE `analysis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) NOT NULL,
  `status` tinyint(1) DEFAULT NULL,
  `process` int(11) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2892 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for business_monitor
-- ----------------------------
DROP TABLE IF EXISTS `business_monitor`;
CREATE TABLE `business_monitor` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `IP` varchar(64) NOT NULL,
  `SOFTWARE_NAME` varchar(255) NOT NULL COMMENT '程序名称',
  `MODULE` varchar(255) NOT NULL COMMENT '模块名',
  `CONTENT` text NOT NULL COMMENT '业务内容',
  `RESULT` varchar(255) DEFAULT '' COMMENT '结果',
  `DESCRIPTION` varchar(255) DEFAULT '' COMMENT '说明/描述/原因',
  `START_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '本次监控起始时间',
  `END_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '本次监控起始时间',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `VERSION` varchar(10) DEFAULT '1.0',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1198435 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for cdh_statistics
-- ----------------------------
DROP TABLE IF EXISTS `cdh_statistics`;
CREATE TABLE `cdh_statistics` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `INCRESE_DAY` varchar(10) DEFAULT NULL,
  `TOTAL_SPACE` bigint(20) DEFAULT NULL,
  `USED_SPACE` bigint(20) DEFAULT NULL,
  `FREE_SPACE` bigint(20) DEFAULT NULL,
  `HBASE_TABLE_INCREASE` text COMMENT 'json string',
  `HBASE_TABLE_SUM` text COMMENT 'json string',
  `UPDATE_TIME` datetime DEFAULT NULL,
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `uk_name` (`INCRESE_DAY`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for `deploy_task`
-- ----------------------------
DROP TABLE IF EXISTS `deploy_task`;
CREATE TABLE `deploy_task` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(255) NOT NULL COMMENT '程序名称',
  `IP` varchar(255) NOT NULL COMMENT 'IP',
  `PRODUCT` varchar(255) NOT NULL COMMENT '产品名称-版本号',
  `REMARK` varchar(1000) DEFAULT '' COMMENT '备注信息',
  `STATUS` tinyint(1) NOT NULL COMMENT '0未开始 1完成比对 2完成获取部署任务列表 3完成修改公共 4完成上传程序 5完成配置替换 6部署失败 7部署成功',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `MODIFY_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `SOURCE_PATH` varchar(255) NOT NULL,
  `TARGET_PATH` varchar(255) NOT NULL,
  `NEED_DELETE_FILES` varchar(255) DEFAULT NULL,
  `IS_FULL_PACKAGE` varchar(255) DEFAULT NULL,
  `IGNORE_BACKUP` varchar(255) DEFAULT NULL,
  `OLD_NAME` varchar(255) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2790 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for dynamic_static_monitor
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_static_monitor`;
CREATE TABLE `dynamic_static_monitor` (
  `id` bigint(100) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `monitor_time` datetime DEFAULT NULL COMMENT '监控时间',
  `his_eml_not_analysis` bigint(100) DEFAULT '0' COMMENT '历史跨站检测未完成数量',
  `eml_not_analysis` bigint(100) DEFAULT '0' COMMENT '当天邮件跨站检测未完成数量',
  `his_eml_analysis` bigint(100) DEFAULT '0' COMMENT '历史邮件跨站检测未完成数量',
  `eml_analysis` bigint(100) DEFAULT '0' COMMENT '当天邮件跨站检测完成数量',
  `analysis_type` int(10) DEFAULT NULL COMMENT '分析类型(0:动态分析，1:静态分析)',
  `his_success_num` bigint(100) DEFAULT '0' COMMENT '历史分析成功数量',
  `success_num` bigint(100) DEFAULT '0' COMMENT '当天分析成功数量',
  `his_fail_num` bigint(100) DEFAULT '0' COMMENT '历史分析失败数量',
  `fail_num` bigint(100) DEFAULT '0' COMMENT '当天分析失败数量',
  `his_not_analysis_num` bigint(100) DEFAULT '0' COMMENT '历史未分析数量',
  `not_analysis_num` bigint(100) DEFAULT '0' COMMENT '当天未分析数量',
  `fail_analysis_threshold` varchar(10) DEFAULT NULL COMMENT '告警阈值(分析失败)',
  `analysis_threshold` bigint(100) DEFAULT '0' COMMENT '告警阈值(分析能力)',
  `status` int(10) DEFAULT '0' COMMENT '告警状态(0：未告警，1：告警)',
  `desc` varchar(255) DEFAULT NULL COMMENT '告警描述信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2391 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for webmalware_monitor
-- ----------------------------
DROP TABLE IF EXISTS `webmalware_monitor`;
CREATE TABLE `webmalware_monitor` (
  `id` bigint(100) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `monitor_time` datetime DEFAULT NULL COMMENT '监控时间',
  `suspicious_num` bigint(100) DEFAULT '0' COMMENT '可疑数量',
  `danger_num` bigint(100) DEFAULT '0' COMMENT '危险数量',
  `high_risk_num` bigint(100) DEFAULT '0' COMMENT '高危数量',
  `feature_num` bigint(100) DEFAULT '0' COMMENT '特征检测数量',
  `static_num` bigint(100) DEFAULT '0' COMMENT '静态检测数量',
  `dynamic_num` bigint(100) DEFAULT '0' COMMENT '动态检测数量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3068 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for dynamic_static_threshold
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_static_threshold`;
CREATE TABLE `dynamic_static_threshold` (
  `id` int(100) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `analysis_threshold` bigint(100) NOT NULL DEFAULT '1500' COMMENT '日均分析标准值',
  `comment` varchar(255) DEFAULT NULL COMMENT '备注',
  `creator` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for es_cluster_state
-- ----------------------------
DROP TABLE IF EXISTS `es_cluster_state`;
CREATE TABLE `es_cluster_state` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '数据入库时间',
  `CLUSTER_STATE` varchar(10) COLLATE utf8_bin NOT NULL COMMENT '集群状态颜色',
  `CLUSTER_NAME` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '集群名称',
  `NODE_NUMBER` int(4) NOT NULL DEFAULT '0' COMMENT '当前集群节点数量',
  `NODE_NAME_LIST` text CHARACTER SET utf8 NOT NULL COMMENT '当前节点名称集合',
  `INDEX_NUMBER` int(7) NOT NULL DEFAULT '0' COMMENT '索引数量',
  `CLOSE_INDEX_NUMBER` int(7) NOT NULL DEFAULT '0' COMMENT '关闭索引数量',
  `OPEN_INDEX_NUMBER` int(7) NOT NULL DEFAULT '0' COMMENT '打开索引数量',
  `CHARDS_NUMBER` int(7) NOT NULL DEFAULT '0' COMMENT '分片数量',
  `DOCS_NUMBER` bigint(20) NOT NULL DEFAULT '0' COMMENT '文档总数量',
  `DOCS_DELETE_NUMBER` bigint(20) NOT NULL DEFAULT '0' COMMENT '文档删除总数量',
  `CLUSTER_DATA_SIZE_BYTES` bigint(20) NOT NULL DEFAULT '0' COMMENT '集群数据占用磁盘空间(单位:bytes)',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2039 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='es集群状态数据表';

-- ----------------------------
-- Table structure for es_delete_close_record
-- ----------------------------
DROP TABLE IF EXISTS `es_delete_close_record`;
CREATE TABLE `es_delete_close_record` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增长ID',
  `INDEX_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '删除或关闭索引的类型',
  `RECORD_TYPE` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'close' COMMENT '操作类型(delete,close,open)',
  `CREATE_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '该数据创建时间',
  `REMARK` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '无' COMMENT '操作备注',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=177 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='删除索引或者关闭索引的记录';

-- ----------------------------
-- Table structure for es_index_state
-- ----------------------------
DROP TABLE IF EXISTS `es_index_state`;
CREATE TABLE `es_index_state` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '表自增长ID',
  `HEALTH` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '索引状态(gren,red)',
  `STATUS` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '索引状态（open,close）',
  `INDEX` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '索引名称',
  `UUID` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '索引自成UUID',
  `PRI` int(10) NOT NULL DEFAULT '0' COMMENT '索引分片',
  `REP` int(10) NOT NULL DEFAULT '0' COMMENT '每个分片的副本数',
  `DOCS_COUNT` bigint(20) NOT NULL DEFAULT '0' COMMENT '索引文档数',
  `DOCS_DELETE` bigint(20) NOT NULL DEFAULT '0' COMMENT '索引删除文档数',
  `STORE_SIZE` bigint(20) NOT NULL DEFAULT '0' COMMENT '索引总占用空间(byte)',
  `PRI_STORE_SIZE` bigint(20) NOT NULL DEFAULT '0' COMMENT '索引分片占用空间(byte)',
  `EAL_CAP_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '最早捕获时间',
  `LAL_CAP_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '最近捕获时间',
  `EAL_CREATE_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '最早入库时间',
  `LAL_CREATE_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '最近入库时间',
  `CREATE_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '入库时间',
  `REMARE` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '备注',
  `STATIS_SUCCESS` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'true' COMMENT '索引统计是否完成',
  `SPLI_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '索引名称中的日期,如果是1970-01-01 00:00:00，说明索引名称中没有日期哦',
  PRIMARY KEY (`ID`,`INDEX`) USING BTREE,
  UNIQUE KEY `INDEX` (`INDEX`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=438488 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='索引状态表';

-- ----------------------------
-- Table structure for es_nodes_state
-- ----------------------------
DROP TABLE IF EXISTS `es_nodes_state`;
CREATE TABLE `es_nodes_state` (
  `CREATE_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '节点信息创建时间',
  `NODE_VERSION` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'xxx' COMMENT 'ES节点版本号',
  `ID` int(10) NOT NULL AUTO_INCREMENT COMMENT '自增长ID',
  `NODE_CREATE_TIME` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '节点创建时间',
  `NODE_ROLE` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '节点角色',
  `NODE_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '节点名称',
  `HOST_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '节点所在服务器的hostname',
  `HOST_ADDRESS` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '节点所在服务器的地址(ip)',
  `TCP_PORT` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '节点的TCP端口',
  `OS_NAME_AND_VERSION` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '节点所在服务器的系统名称和版本(名称和版本以分号隔开)',
  `JVM_VERSION` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT 'jvm版本号',
  `JVM_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT 'jvm名称',
  `NODE_PLUGINS` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '节点已安装插件',
  `DISK_TOTAL` bigint(20) NOT NULL DEFAULT '0' COMMENT '节点硬盘总容量',
  `DISK_AVAILABLE` bigint(20) NOT NULL DEFAULT '0' COMMENT '节点硬盘剩余容量',
  `MEM_TOTAL` bigint(20) NOT NULL DEFAULT '0' COMMENT '内存总容量(bit)',
  `MEM_FREE` bigint(20) NOT NULL DEFAULT '0' COMMENT '内容剩余容量(bit)',
  `NODE_CPU_PERCENT` int(3) NOT NULL DEFAULT '0' COMMENT '节点占用所在服务器cpu资源的百分比',
  `SYSTEM_PERCENT` double(10,2) NOT NULL DEFAULT '0.00' COMMENT '节点所在服务器cpu占用资源百分比',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10721 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for es_timing_task
-- ----------------------------
DROP TABLE IF EXISTS `es_timing_task`;
CREATE TABLE `es_timing_task`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增长ID',
  `NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'XXX' COMMENT '定时名称',
  `NUMBER` bigint(255) NOT NULL DEFAULT 0 COMMENT '定时数值(通常为天)',
  `POWER` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'true' COMMENT '开关',
  `REMARK` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '无' COMMENT '备注',
  PRIMARY KEY (`ID`, `NAME`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'es插件定时任务\r\n1.根据用户定时关闭索引\r\n2.根据用户定时删除索引' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for frontend_history
-- ----------------------------
DROP TABLE IF EXISTS `frontend_history`;
CREATE TABLE `frontend_history` (
  `ap_key` varchar(255) NOT NULL,
  `status` int(255) DEFAULT NULL,
  `mem_used` int(255) DEFAULT NULL,
  `flowspeed_convert` double(20,4) DEFAULT NULL,
  `soft_lost_ratio` double(255,5) DEFAULT NULL,
  `cpu_ava` varchar(255) DEFAULT NULL,
  `cpu_tip` varchar(255) DEFAULT NULL,
  `temp` double(255,0) DEFAULT NULL,
  `create_time` varchar(25) NOT NULL,
  PRIMARY KEY (`ap_key`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for frontend_transmit_history
-- ----------------------------
DROP TABLE IF EXISTS `frontend_transmit_history`;
CREATE TABLE `frontend_transmit_history` (
  `ap_key` varchar(255) NOT NULL,
  `num` int(11) DEFAULT NULL,
  `size` double(20,4) DEFAULT NULL,
  `create_time` varchar(25) NOT NULL,
  PRIMARY KEY (`ap_key`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for front_device
-- ----------------------------
DROP TABLE IF EXISTS `front_device`;
CREATE TABLE `front_device` (
  `IS_DELETE` tinyint(4) NOT NULL DEFAULT '0',
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `SOURCE_ID` varchar(50) NOT NULL,
  `STATUS` tinyint(1) DEFAULT NULL COMMENT '0=green，1=yellow，2=red',
  `DESCRIPTION` varchar(50) DEFAULT '',
  `VERSION` varchar(50) DEFAULT '',
  `DEVICE_ID` varchar(50) DEFAULT '',
  `AP_KEY` varchar(50) DEFAULT '' COMMENT '这是主键有问题吗？',
  `CPU_AVG` varchar(50) DEFAULT '',
  `USED_MEMORY` varchar(50) DEFAULT '',
  `TOTAL_MEMORY` varchar(50) DEFAULT '',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `REPORT_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `DEVICE_NAME` varchar(255) DEFAULT '',
  `SERIAL` char(8) DEFAULT NULL,
  `MONITOR_DIR_TOTAL` varchar(50) DEFAULT NULL,
  `MONITOR_DIR_FREE` varchar(50) DEFAULT NULL,
  `MONITOR_DIR_STATUS` tinyint(1) DEFAULT NULL,
  `DAY_FLOWSPEED_MAX` bigint(20) DEFAULT NULL,
  `DAY_FLOWSPEED_MIN` bigint(20) DEFAULT NULL,
  `DAY_FLOWSPEED_AVG` bigint(20) DEFAULT NULL,
  `HISTORY_FLOWSPEED_MAX` bigint(20) DEFAULT NULL,
  `HISTORY_FLOWSPEED_MIN` bigint(20) DEFAULT NULL,
  `HISTORY_FLOWSPEED_AVG` bigint(20) DEFAULT NULL,
  `NOTE` varchar(255) DEFAULT NULL,
  `soft_lost_ratio` double(10,4) DEFAULT NULL COMMENT '程序丢包率',
  `restart_count` int(11) DEFAULT NULL,
  `zcp_active_driver` varchar(255) DEFAULT NULL,
  `zcp_active_type` varchar(255) DEFAULT NULL,
  `cpu_tip` varchar(255) DEFAULT NULL,
  `temp` double(10,0) DEFAULT NULL,
  `file_num` int(11) DEFAULT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `wait_send_size` bigint(20) DEFAULT NULL,
  `flowspeed_convert` double(20,4) DEFAULT NULL,
  `transmit_status` int(1) DEFAULT NULL COMMENT '数据传输状态；0异常；1正常',
  `EX_NUMBER` int(255) DEFAULT '0',
  `plugin_active_list` text,
  `ignore_exception_info` text COMMENT '忽略异常信息',
  `exception_type` varchar(255) DEFAULT NULL COMMENT '异常类型：0-正常；1-回传异常；2-丢包异常；3-重启异常；4堆积异常；5-传输异常；',
  `ignore_exception_num` int(10) DEFAULT '0' COMMENT '已忽略异常数量',
  `is_display` tinyint(1) DEFAULT '1' COMMENT '0-不显示；1-显示',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `uk_source_id` (`SOURCE_ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61780 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for front_device_count
-- ----------------------------
DROP TABLE IF EXISTS `front_device_count`;
CREATE TABLE `front_device_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ap_key` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT '',
  `count` int(11) DEFAULT NULL,
  `monitor_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for front_status
-- ----------------------------
DROP TABLE IF EXISTS `front_status`;
CREATE TABLE `front_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status_wrong_count` int(10) DEFAULT NULL,
  `restart_count` int(10) DEFAULT NULL,
  `soft_lost_count` int(10) DEFAULT NULL,
  `transmit_wrong_count` int(10) DEFAULT NULL,
  `data_wait_send_count` int(10) DEFAULT NULL,
  `time` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for group_history_health
-- ----------------------------
DROP TABLE IF EXISTS `group_history_health`;
CREATE TABLE `group_history_health` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(50) NOT NULL,
  `STATUS` tinyint(1) NOT NULL COMMENT '0=green，1=yellow，2=red',
  `GROUP_ID` tinyint(2) NOT NULL,
  `DESCRIPTION` varchar(200) DEFAULT '',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `idx_group` (`GROUP_ID`,`CREATE_TIME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=168547 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for hardware_history_health
-- ----------------------------
DROP TABLE IF EXISTS `hardware_history_health`;
CREATE TABLE `hardware_history_health` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `AGENT_ID` int(32) NOT NULL,
  `STATUS` tinyint(1) NOT NULL COMMENT '1=green，2=yellow，3=red',
  `DESCRIPTION` varchar(50) DEFAULT '',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `CPU_PERCENT` varchar(255) DEFAULT NULL,
  `MEMORY_PERCENT` varchar(255) DEFAULT NULL,
  `USED_MEMORY` varchar(255) DEFAULT NULL,
  `USED_DISK` varchar(255) DEFAULT NULL,
  `DISK_PERCENT` varchar(255) DEFAULT NULL,
  `SEND_BYTE_PER_SECOND` int(11) NULL DEFAULT NULL COMMENT '每秒发送字节',
  `RECEIVE_BYTE_PER_SECOND` int(11) NULL DEFAULT NULL COMMENT '每秒接收字节',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `uk_history_health` (`AGENT_ID`,`STATUS`,`DESCRIPTION`,`CREATE_TIME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1763735 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for hardware_info
-- ----------------------------
DROP TABLE IF EXISTS `hardware_info`;
CREATE TABLE `hardware_info` (
  `AGENT_ID` int(32) NOT NULL,
  `FIREWALL_STATUS` tinyint(1) NOT NULL COMMENT '0=关闭，1=开启',
  `OS_NAME` varchar(50) DEFAULT '0',
  `USED_CPU` varchar(30) NOT NULL,
  `DISK_INFO` text NOT NULL COMMENT '磁盘信息',
  `NIC_INFO` text NOT NULL COMMENT '网卡信息',
  `IPS` varchar(255) DEFAULT NULL,
  `USED_MEMORY` varchar(30) NOT NULL,
  `TOTAL_MEMORY` varchar(30) NOT NULL,
  `MEMORY_PERCENT` varchar(30) DEFAULT NULL,
  `USED_DISK` varchar(30) NOT NULL,
  `TOTAL_DISK` varchar(30) NOT NULL,
  `DISK_PERCENT` varchar(30) DEFAULT NULL,
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `PORTS` int(10) DEFAULT NULL,
  `NOTE` varchar(255) DEFAULT NULL,
  `SEND_BYTE_PER_SECOND` int(11) NULL DEFAULT NULL COMMENT '每秒发送字节',
  `RECEIVE_BYTE_PER_SECOND` int(11) NULL DEFAULT NULL COMMENT '每秒接收字节',
  PRIMARY KEY (`AGENT_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for index_monitor
-- ----------------------------
DROP TABLE IF EXISTS `index_monitor`;
CREATE TABLE `index_monitor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL COMMENT '模块名',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引',
  `record_field` varchar(255) DEFAULT NULL COMMENT '入库时间的字段名',
  `index_condition` varchar(255) DEFAULT NULL COMMENT '条件',
  `status` int(11) DEFAULT NULL COMMENT '状态；0正常，1异常，2告警',
  `software_name` varchar(255) DEFAULT NULL COMMENT '程序名',
  `type` int(10) DEFAULT NULL COMMENT '0基础数据、1挖掘数据',
  `note` varchar(255) DEFAULT NULL,
  `index_type` int(10) DEFAULT NULL COMMENT '索引类型；0固定索引名、1日期格式索引名',
  `today` int(10) DEFAULT NULL,
  `yesterday` int(10) DEFAULT NULL,
  `ignore_status` tinyint(4) NULL DEFAULT 0 COMMENT '0:否，1：是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4;


-- ----------------------------
-- Table structure for index_monitor_history
-- ----------------------------
DROP TABLE IF EXISTS `index_monitor_history`;
CREATE TABLE `index_monitor_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_id` int(11) DEFAULT NULL,
  `count` int(11) DEFAULT NULL COMMENT '数量',
  `time` varchar(255) DEFAULT NULL,
  `time_type` int(11) DEFAULT NULL COMMENT '时间类型；0入库时间；1捕获时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3541 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for note
-- ----------------------------
DROP TABLE IF EXISTS `note`;
CREATE TABLE `note` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `content` varchar(255) DEFAULT NULL,
  `author` varchar(255) DEFAULT NULL,
  `modify_time` datetime DEFAULT NULL,
  `remind` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for operate_record
-- ----------------------------
DROP TABLE IF EXISTS `operate_record`;
CREATE TABLE `operate_record` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `USER` varchar(100) DEFAULT '',
  `OPERATE` text,
  `OPERATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `operate_time_index` (`OPERATE_TIME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2364 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for permission
-- ----------------------------
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT NULL,
  `url` varchar(100) NOT NULL,
  `parentId` int(11) DEFAULT NULL,
  `order` int(11) DEFAULT NULL COMMENT '为了页面菜单栏的顺序',
  `pathUrl` varchar(100) NOT NULL COMMENT '登录后页面跳转用的url',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for platform_role
-- ----------------------------
DROP TABLE IF EXISTS `platform_role`;
CREATE TABLE `platform_role` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(255) NOT NULL COMMENT '角色名称',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `NAME` (`NAME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for deploy_record
-- ----------------------------
DROP TABLE IF EXISTS `deploy_record`;
CREATE TABLE `deploy_record` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(255) DEFAULT NULL COMMENT '名称',
  `VERSION` varchar(255) DEFAULT NULL COMMENT '版本号',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `STATUS` tinyint(11) DEFAULT '0',
  `MODIFY_TIME` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `REMARK1` text,
  `REMARK2` text,
  `REMARK3` text,
  `REMARK4` text,
  `REMARK5` text,
  `CONFIG_PATH` text,
  `PRODUCT` varchar(100) DEFAULT NULL,
  `PROGRAM_PATHS` text,
  `FULL_PACKAGE` varchar(100) DEFAULT NULL,
  `IS_MODIFY_COMMON_CONFIG` tinyint(11) DEFAULT '0' COMMENT '是否修改公共配置',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=194 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for plugin_info
-- ----------------------------
DROP TABLE IF EXISTS `plugin_info`;
CREATE TABLE `plugin_info` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(50) DEFAULT '',
  `PATH` varchar(500) DEFAULT '',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `LAST_UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `IS_DELETE` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0=close，1=open',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `uk_name` (`NAME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for plugin_warn_message
-- ----------------------------
DROP TABLE IF EXISTS `plugin_warn_message`;
CREATE TABLE `plugin_warn_message` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `METRIC_TYPE` varchar(20) DEFAULT NULL,
  `WARN_MESSAGE` mediumtext,
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `IS_DELETE` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7433 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for plugin_warn_metric
-- ----------------------------
DROP TABLE IF EXISTS `plugin_warn_metric`;
CREATE TABLE `plugin_warn_metric` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `METRIC_TYPE` varchar(20) DEFAULT NULL,
  `METRIC_NAME` varchar(20) DEFAULT NULL,
  `METRIC_VALUE` varchar(32) DEFAULT NULL,
  `UPDATE_TIME` datetime DEFAULT NULL,
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `uk_name` (`METRIC_NAME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for preprocess_disk_monitor
-- ----------------------------
DROP TABLE IF EXISTS `preprocess_disk_monitor`;
CREATE TABLE `preprocess_disk_monitor` (
  `id` bigint(100) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `ip` varchar(255) DEFAULT NULL COMMENT '监控服务器ip地址',
  `dir` varchar(255) DEFAULT NULL COMMENT '监控预处理目录',
  `avail_disk` varchar(255) DEFAULT NULL COMMENT '可用磁盘空间大小',
  `used_disk` varchar(255) DEFAULT NULL COMMENT '已用磁盘大小',
  `total_disk` varchar(255) DEFAULT NULL COMMENT '总磁盘大小',
  `disk_percent` varchar(255) DEFAULT NULL COMMENT '磁盘使用百分比',
  `dir_size` varchar(255) DEFAULT NULL COMMENT '监控目录大小',
  `alarm_status` int(10) DEFAULT NULL COMMENT '告警状态(0：不告警，1：正在告警)',
  `alarm_desc` varchar(255) DEFAULT NULL COMMENT '告警描述信息',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for preprocess_index_count
-- ----------------------------
DROP TABLE IF EXISTS `preprocess_index_count`;
CREATE TABLE `preprocess_index_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `monitor_time` datetime DEFAULT NULL COMMENT '统计时间',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
  `count` bigint(20) DEFAULT NULL COMMENT '数量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1513 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for product_info
-- ----------------------------
DROP TABLE IF EXISTS `product_info`;
CREATE TABLE `product_info` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `PRODUCT_NAME` varchar(255) NOT NULL COMMENT '产品名称',
  `PRODUCT_VERSION` varchar(10) NOT NULL COMMENT '产品版本号',
  `SIZE` int(32) NOT NULL COMMENT '部署包的大小',
  `MD5` varchar(255) DEFAULT '' COMMENT '部署包的MD5',
  `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '部署时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `nick_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for role_permission
-- ----------------------------
DROP TABLE IF EXISTS `role_permission`;
CREATE TABLE `role_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=751 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for security_audit_log
-- ----------------------------
DROP TABLE IF EXISTS `security_audit_log`;
CREATE TABLE `security_audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `loguser` varchar(50) DEFAULT NULL,
  `logip` varchar(20) DEFAULT NULL,
  `logtime` timestamp NULL DEFAULT NULL,
  `hostip` varchar(20) DEFAULT NULL,
  `system` varchar(20) DEFAULT NULL,
  `logtype` varchar(20) DEFAULT NULL,
  `role` varchar(20) DEFAULT NULL,
  `alarm` varchar(20) DEFAULT NULL,
  `logstate` varchar(100) DEFAULT NULL,
  `ipaddress` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25433 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for software_history_health
-- ----------------------------
DROP TABLE IF EXISTS `software_history_health`;
CREATE TABLE `software_history_health` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL,
  `software_id` int(11) NOT NULL,
  `cpu_percent` varchar(255) DEFAULT NULL,
  `memory_percent` varchar(255) DEFAULT NULL,
  `disk_percent` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL,
  `USED_MEMORY` varchar(30) DEFAULT '',
  `USED_DISK` varchar(255) DEFAULT NULL,
  `USED_CPU` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_history_resource` (`agent_id`,`software_id`,`cpu_percent`,`memory_percent`,`disk_percent`,`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1191200 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for software_info
-- ----------------------------
DROP TABLE IF EXISTS `software_info`;
CREATE TABLE `software_info` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `SERVER_ID` int(32) NOT NULL,
  `NAME` varchar(50) DEFAULT '',
  `VERSION` varchar(10) DEFAULT NULL,
  `PID` int(6) DEFAULT NULL COMMENT '进程号',
  `PROGRAM_SIZE` bigint(64) DEFAULT NULL COMMENT '程序占用大小',
  `USED_MEMORY` varchar(30) DEFAULT NULL,
  `TOTAL_MEMORY` varchar(30) DEFAULT NULL,
  `MEMORY_PERCENT` varchar(30) DEFAULT NULL,
  `CPU_PERCENT` varchar(30) DEFAULT NULL,
  `DISK_PERCENT` varchar(30) DEFAULT NULL,
  `START_PARAM` text COMMENT '启动参数',
  `AUTO_DAEMON` tinyint(1) DEFAULT '1' COMMENT '开启守护，默认开启',
  `BASE_DIR` varchar(255) DEFAULT NULL COMMENT '程序所在目录',
  `REAL_DIR` varchar(255) DEFAULT NULL COMMENT '程序真实目录',
  `LOG_INFO` text COMMENT '日志信息',
  `CONF_INFO` text COMMENT '配置文件信息',
  `COMMON_INFO` text COMMENT '常规文件信息',
  `SELF_INFO` text COMMENT '其他信息',
  `LAST_HEARTBEAT_TIME` datetime DEFAULT NULL,
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `STATUS` tinyint(1) DEFAULT '0',
  `RESTART_COUNT` int(10) DEFAULT '0',
  `OPERATE_TYPE` tinyint(1) DEFAULT '0',
  `RESTART_TIME` datetime DEFAULT NULL,
  `PROCESS_COUNT` int(100) DEFAULT '1',
  `LOG_COUNT` int(100) DEFAULT '0',
  `IS_EXISTS` tinyint(1) DEFAULT '1' COMMENT '启动脚本是否存在；0-不存在；1-存在',
  `PROGRAM_STATUS` tinyint(1) DEFAULT '1',
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `HEART_MONITOR` tinyint(1) DEFAULT '1',
  `NOTE` varchar(255) DEFAULT NULL,
  `CONFIG` tinyint(1) DEFAULT NULL,
  `SOFTWARE_KEYS` varchar(255) DEFAULT NULL,
  `SCRIPT` varchar(255) DEFAULT NULL,
  `SCRIPT_PATH` varchar(255) DEFAULT NULL,
  `HEAP_SIZE` int(10) DEFAULT NULL,
  `MIN_HEAP_SIZE` int(10) DEFAULT NULL,
  `CLOSE_SCRIPT` varchar(255) DEFAULT NULL,
  `TEST` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=197 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for software_restart_history
-- ----------------------------
DROP TABLE IF EXISTS `software_restart_history`;
CREATE TABLE `software_restart_history` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `SOFTWARE_ID` int(32) NOT NULL,
  `STATUS` tinyint(1) NOT NULL COMMENT '0=green，1=yellow，2=red',
  `DESCRIPTION` varchar(50) DEFAULT '',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `uk_restart_history` (`SOFTWARE_ID`,`STATUS`,`DESCRIPTION`,`CREATE_TIME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=319684 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `pswd` varchar(255) NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `last_login_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for `mysql_backup_info`
-- ----------------------------
DROP TABLE IF EXISTS `mysql_backup_info`;
CREATE TABLE `mysql_backup_info` (
  `id` bigint(100) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `backup_time` datetime DEFAULT NULL COMMENT '备份时间',
  `backup_type` int(100) DEFAULT NULL COMMENT '备份类型',
  `backup_databases` varchar(255) DEFAULT NULL COMMENT '备份数据库',
  `success` tinyint(100) DEFAULT NULL COMMENT '是否备份成功；0-失败，1，成功',
  `ip` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'IP',
  `remote_success` int(11) NULL DEFAULT NULL COMMENT '异机备份是否成功',
  `ignore_status` tinyint(4) NULL DEFAULT 0 COMMENT '计算状态时是否忽略该条记录',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for warn
-- ----------------------------
DROP TABLE IF EXISTS `warn`;
CREATE TABLE `warn` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `warn_type` int(10) NOT NULL,
  `status` int(10) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `param1` varchar(255) DEFAULT NULL,
  `param2` varchar(255) DEFAULT NULL,
  `param3` varchar(255) DEFAULT NULL,
  `param4` varchar(255) DEFAULT NULL,
  `param5` varchar(255) DEFAULT NULL,
  `param6` varchar(255) DEFAULT NULL,
  `param7` varchar(255) DEFAULT NULL,
  `param8` varchar(255) DEFAULT NULL,
  `param9` varchar(255) DEFAULT NULL,
  `param10` varchar(255) DEFAULT NULL,
  `modify_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `request_record`;
CREATE TABLE `request_record` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `URI` varchar(200) NOT NULL,
  `MODULE` varchar(200) NOT NULL COMMENT '模块，即uri的第一级',
  `START_TIME` datetime NOT NULL,
  `END_TIME` datetime NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `TIME_SPENT` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
-- ----------------------------
-- Records of es_timing_task
-- ----------------------------
INSERT INTO `es_timing_task` VALUES (1, 'closeIndex', 90, 'false', '关闭索引的规则天数');
INSERT INTO `es_timing_task` VALUES (2, 'deleteIndex', 0, 'false', '删除总开关');
INSERT INTO `es_timing_task` VALUES (3, 'indexStoageNumber', 0, 'false', '还能存放索引的天数');
INSERT INTO `es_timing_task` VALUES (4, 'calculatedPercentage', 0, 'false', '集群空间磁盘占比');
INSERT INTO `es_timing_task` VALUES (5, 'diskRatio', 85, 'false', '磁盘占比的空间大于number字段值删除线程才能进行删除操作');
INSERT INTO `es_timing_task` VALUES (6, 'httpMinimumRetentionDays', 180, 'false', 'http索引删除最小保留天数');
INSERT INTO `es_timing_task` VALUES (7, 'dnsMinimumRetentionDays', 180, 'false', 'dns索引删除最小保留天数');
INSERT INTO `es_timing_task` VALUES (8, 'ipMinimumRetentionDays', 180, 'false', 'ip索引删除最小保留天数');
INSERT INTO `es_timing_task` VALUES (9, 'checkCycleNumber', 5, 'false', '循环检查是否达到删除要求的次数(默认为5次)');
INSERT INTO `es_timing_task` VALUES (10, 'deleteNumber', 20, 'false', '每次批量删除索引的个数(默认为20个)');
INSERT INTO `es_timing_task` VALUES (11, 'closeIndexCopyIsNull', 0, 'false', '关闭索引时是否设置待关闭的索引的副本为0');
INSERT INTO `es_timing_task` VALUES (12, 'startCycle', 20088, 'false', '项目启动的周期(单位:秒)');
INSERT INTO `es_timing_task` VALUES (13, 'storeRatio', 110, 'false', 'ES检测体积比告警值');

-- ----------------------------
-- Records of platform_role
-- ----------------------------
INSERT INTO `platform_role` VALUES ('27', 'admin/admin');
INSERT INTO `platform_role` VALUES ('4', 'CDH-DN');
INSERT INTO `platform_role` VALUES ('11', 'CDH-NN');
INSERT INTO `platform_role` VALUES ('18', 'CDH-SNN');
INSERT INTO `platform_role` VALUES ('6', 'Codis');
INSERT INTO `platform_role` VALUES ('1', 'ES1');
INSERT INTO `platform_role` VALUES ('5', 'ES2');
INSERT INTO `platform_role` VALUES ('8', 'Kafka');
INSERT INTO `platform_role` VALUES ('16', 'MySQL');
INSERT INTO `platform_role` VALUES ('19', 'Oracle');
INSERT INTO `platform_role` VALUES ('14', 'WEB');
INSERT INTO `platform_role` VALUES ('10', 'ZK');
INSERT INTO `platform_role` VALUES ('21', '分析机');
INSERT INTO `platform_role` VALUES ('7', '动态分析');
INSERT INTO `platform_role` VALUES ('12', '数据分析');
INSERT INTO `platform_role` VALUES ('13', '数据挖掘-主');
INSERT INTO `platform_role` VALUES ('17', '数据挖掘-从');
INSERT INTO `platform_role` VALUES ('20', '汇聚上传');
INSERT INTO `platform_role` VALUES ('23', '汇聚导出');
INSERT INTO `platform_role` VALUES ('9', '静态分析');
INSERT INTO `platform_role` VALUES ('2', '静态沙箱');
INSERT INTO `platform_role` VALUES ('15', '预处理-主');
INSERT INTO `platform_role` VALUES ('3', '预处理-从');

-- ----------------------------
-- Records of permission
-- ----------------------------
INSERT INTO `permission` VALUES ('1', '用户管理', '/user', null, '10', '/userManage');
INSERT INTO `permission` VALUES ('2', '添加用户', '/user/registry', '1', null, '');
INSERT INTO `permission` VALUES ('3', '注销用户', '/user/cancel', '1', null, '');
INSERT INTO `permission` VALUES ('4', '初始化密码', '/user/initPassword', '1', null, '');
INSERT INTO `permission` VALUES ('5', '用户列表', '/user/userList.json', '1', null, '');
INSERT INTO `permission` VALUES ('6', '角色列表', '/user/roleList.json', '1', null, '');
INSERT INTO `permission` VALUES ('7', '分配角色', '/user/assignRoles', '1', null, '');
INSERT INTO `permission` VALUES ('8', '分配权限', '/user/assignPermissions', '1', null, '');
INSERT INTO `permission` VALUES ('9', 'Agent管理', '/agent', null, '4', '/agentManage');
INSERT INTO `permission` VALUES ('10', 'Agent列表', '/agent/list.json', '9', null, '');
INSERT INTO `permission` VALUES ('11', '角色列表', '/agent/roles.json', '9', null, '');
INSERT INTO `permission` VALUES ('12', '添加Agent', '/agentWebsocket/4', '9', null, '');
INSERT INTO `permission` VALUES ('15', '权限列表', '/user/permissionList.json', '1', null, '');
INSERT INTO `permission` VALUES ('16', '服务器管理', '/server', null, '2', '/hardWare');
INSERT INTO `permission` VALUES ('17', '程序管理', '/program', null, '3', '/procedureManage');
INSERT INTO `permission` VALUES ('18', '集群管理', '/colony', null, '5', '/colonyManage');
INSERT INTO `permission` VALUES ('19', '前端管理', '/front-device', null, '7', '/frontManage/probeStatus');
INSERT INTO `permission` VALUES ('20', '审计管理', '/audit', null, '9', '/auditManage');
INSERT INTO `permission` VALUES ('21', '系统首页', '/index', null, null, '/index');
INSERT INTO `permission` VALUES ('22', '服务器列表', '/hardware/list.json', '16', null, '');
INSERT INTO `permission` VALUES ('23', '服务器详情', '/hardware/detail_*.json', '16', null, '');
INSERT INTO `permission` VALUES ('26', '程序列表', '/software/list.json', '17', null, '');
INSERT INTO `permission` VALUES ('27', '程序配置文件列表', '/software/configs', '17', null, '');
INSERT INTO `permission` VALUES ('28', '下载程序日志', '/software/log', '17', null, '');
INSERT INTO `permission` VALUES ('29', '读取程序配置文件', '/software/config', '17', null, '');
INSERT INTO `permission` VALUES ('30', '修改程序配置文件', '/software/config', '17', null, '');
INSERT INTO `permission` VALUES ('31', '程序日志列表', '/software/logs', '17', null, '');
INSERT INTO `permission` VALUES ('32', '一键部署', '/softwareWebsocket/4', '17', null, '');
INSERT INTO `permission` VALUES ('33', '继续部署', '/software/deploy_task.json', '17', null, '');
INSERT INTO `permission` VALUES ('34', '前端列表', '/front-device/list.json', '19', null, '');
INSERT INTO `permission` VALUES ('35', '前端详情', '/front-device/detail.json', '19', null, '');
INSERT INTO `permission` VALUES ('38', '审计列表', '/operate/record.json', '20', null, '');
INSERT INTO `permission` VALUES ('39', '日志管理', '/log', null, '6', '/logAnalyze');
INSERT INTO `permission` VALUES ('40', '业务管理', '/business', null, '8', '/businessManage');
INSERT INTO `permission` VALUES ('41', '操作程序', '/softwareWebsocket/0,/softwareWebsocket/1', '17', null, '');
INSERT INTO `permission` VALUES ('44', '操作Agent', '/agentWebsocket/1,/agentWebsocket/0', '9', null, '');
INSERT INTO `permission` VALUES ('46', '更新Agent', '/agentWebsocket/3', '9', null, '');
INSERT INTO `permission` VALUES ('47', '开启防火墙', '/hardwareWebsocket/1', '16', null, '');
INSERT INTO `permission` VALUES ('48', '添加程序', '/software/add_software', '17', null, '');
INSERT INTO `permission` VALUES ('49', '编辑程序', '/software/update_soft', '17', null, '');
INSERT INTO `permission` VALUES ('50', '删除程序', '/software/delete_soft', '17', null, '');
INSERT INTO `permission` VALUES ('51', '重启次数置零', '/software/clear_restart_count', '17', null, '');
INSERT INTO `permission` VALUES ('52', '动态分析列表', '/dynamicAndStatic/list.json', '40', null, '');
INSERT INTO `permission` VALUES ('53', '设置标准分析量', '/dynamicAndStatic/addThreshold', '40', null, '');
INSERT INTO `permission` VALUES ('54', '静态分析列表', '/dynamicAndStatic/list.json', '40', null, '');
INSERT INTO `permission` VALUES ('55', '磁盘信息', '/preprocessDiskMonitor/list.json', '40', null, '');
INSERT INTO `permission` VALUES ('56', '索引信息', '/preprocessDiskMonitor/index.json', '40', null, '');
INSERT INTO `permission` VALUES ('58', 'Codis队列信息', '/dynamicAndStatic/getTaskQueueLen', '18', null, '');
INSERT INTO `permission` VALUES ('59', '任务管理', '/api/_tasks', '18', null, '');
INSERT INTO `permission` VALUES ('60', 'CDH管理监控详情', '/plugin/plugin_business/CdhPlugin', '18', null, '');
INSERT INTO `permission` VALUES ('61', 'CDH告警信息', '/plugin/plugin_business/*Plugin/get*', '18', null, '');
INSERT INTO `permission` VALUES ('64', 'CDH管理详情', '', '18', null, '');
INSERT INTO `permission` VALUES ('65', 'Codis管理详情', '', '18', null, '');
INSERT INTO `permission` VALUES ('66', 'kafka监控详情', '/plugin/plugin_business/KafkaPlugin', '18', null, '');
INSERT INTO `permission` VALUES ('67', 'ES管理监控详情', '/plugin/plugin_business/EsPlugin', '18', null, '');
INSERT INTO `permission` VALUES ('68', '日志监控列表', '', '39', null, '');
INSERT INTO `permission` VALUES ('69', '程序详情', '/software/detail_*.json', '17', null, '');
INSERT INTO `permission` VALUES ('70', '心跳监控', '/software/heart_monitor', '17', null, '');
INSERT INTO `permission` VALUES ('71', '重启程序', '/softwareWebsocket/2', '17', null, '');
INSERT INTO `permission` VALUES ('72', 'ES管理详情', '', '18', null, '');
INSERT INTO `permission` VALUES ('73', '修改关闭索引状态', '/plugin/plugin_business/EsPlugin/closeIndexPower', '18', null, '');
INSERT INTO `permission` VALUES ('74', '修改删除索引状态', '/plugin/plugin_business/EsPlugin/deleteIndexPower', '18', null, '');
INSERT INTO `permission` VALUES ('75', '添加磁盘', '/preprocessDiskMonitor/addPreprocessDir', '40', null, '');
INSERT INTO `permission` VALUES ('76', '分发logstash', '/softwareWebsocket/5', '17', null, '');
INSERT INTO `permission` VALUES ('77', '更新logstash配置文件', '/softwareWebsocket/6', '17', null, '');
INSERT INTO `permission` VALUES ('78', '报告管理', '/report', null, '11', '/reportManage');
INSERT INTO `permission` VALUES ('79', '下载报告', '/report/down', '78', null, '');
INSERT INTO `permission` VALUES ('80', '生成报告', '/home/<USER>', '78', null, '');
INSERT INTO `permission` VALUES ('81', '导入Agent', '/agent/import', '9', null, '');
INSERT INTO `permission` VALUES ('82', '一键时间同步', '/agentWebsocket/7', '9', null, '');
INSERT INTO `permission` VALUES ('83', '跳转探针', '/frontDeviceWebsocket/8', '19', null, '');
INSERT INTO `permission` VALUES ('84', '一键升级', '/front-device/getFrontPage', '19', null, '');
INSERT INTO `permission` VALUES ('85', '分发公共配置', '/softwareWebsocket/9', '9', null, '');
INSERT INTO `permission` VALUES ('86', '接口调用', '/request', null, '12', '/requestManage');
INSERT INTO `permission` VALUES ('87', '接口调用统计', '/request/record/statis', '86', null, '');
INSERT INTO `permission` VALUES ('88', '一键关闭平台', '/agentWebsocket/10', 9, NULL, '  ');
INSERT INTO `permission` VALUES ('89', '一键开启平台', '/agentWebsocket/11', 9, NULL, '   ');

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES ('1', 'ROLE_ADMIN', '管理员');
INSERT INTO `role` VALUES ('2', 'ROLE_USER', '研发部');
INSERT INTO `role` VALUES ('3', 'ROLE_TEST', '测试部');
INSERT INTO `role` VALUES ('4', 'ROLE_SUPPORT', '技术支持部');
INSERT INTO `role` VALUES ('5', 'ROLE_COMMAND', '客户');

-- ----------------------------
-- Records of role_permission
-- ----------------------------
INSERT INTO `role_permission` VALUES ('1', '1', '1');
INSERT INTO `role_permission` VALUES ('2', '1', '2');
INSERT INTO `role_permission` VALUES ('3', '1', '3');
INSERT INTO `role_permission` VALUES ('4', '1', '4');
INSERT INTO `role_permission` VALUES ('5', '1', '5');
INSERT INTO `role_permission` VALUES ('6', '1', '6');
INSERT INTO `role_permission` VALUES ('7', '1', '7');
INSERT INTO `role_permission` VALUES ('8', '1', '8');
INSERT INTO `role_permission` VALUES ('9', '1', '15');
INSERT INTO `role_permission` VALUES ('571', '2', '9');
INSERT INTO `role_permission` VALUES ('572', '2', '10');
INSERT INTO `role_permission` VALUES ('573', '2', '11');
INSERT INTO `role_permission` VALUES ('574', '2', '12');
INSERT INTO `role_permission` VALUES ('575', '2', '16');
INSERT INTO `role_permission` VALUES ('576', '2', '17');
INSERT INTO `role_permission` VALUES ('577', '2', '18');
INSERT INTO `role_permission` VALUES ('578', '2', '19');
INSERT INTO `role_permission` VALUES ('579', '2', '20');
INSERT INTO `role_permission` VALUES ('580', '2', '21');
INSERT INTO `role_permission` VALUES ('581', '2', '22');
INSERT INTO `role_permission` VALUES ('582', '2', '23');
INSERT INTO `role_permission` VALUES ('583', '2', '26');
INSERT INTO `role_permission` VALUES ('584', '2', '27');
INSERT INTO `role_permission` VALUES ('585', '2', '28');
INSERT INTO `role_permission` VALUES ('586', '2', '29');
INSERT INTO `role_permission` VALUES ('587', '2', '30');
INSERT INTO `role_permission` VALUES ('588', '2', '31');
INSERT INTO `role_permission` VALUES ('589', '2', '32');
INSERT INTO `role_permission` VALUES ('590', '2', '33');
INSERT INTO `role_permission` VALUES ('591', '2', '34');
INSERT INTO `role_permission` VALUES ('592', '2', '35');
INSERT INTO `role_permission` VALUES ('593', '2', '38');
INSERT INTO `role_permission` VALUES ('594', '2', '39');
INSERT INTO `role_permission` VALUES ('595', '2', '40');
INSERT INTO `role_permission` VALUES ('596', '2', '41');
INSERT INTO `role_permission` VALUES ('597', '2', '44');
INSERT INTO `role_permission` VALUES ('598', '2', '46');
INSERT INTO `role_permission` VALUES ('599', '2', '47');
INSERT INTO `role_permission` VALUES ('600', '2', '48');
INSERT INTO `role_permission` VALUES ('601', '2', '49');
INSERT INTO `role_permission` VALUES ('602', '2', '50');
INSERT INTO `role_permission` VALUES ('603', '2', '51');
INSERT INTO `role_permission` VALUES ('604', '2', '52');
INSERT INTO `role_permission` VALUES ('605', '2', '53');
INSERT INTO `role_permission` VALUES ('606', '2', '54');
INSERT INTO `role_permission` VALUES ('607', '2', '55');
INSERT INTO `role_permission` VALUES ('608', '2', '56');
INSERT INTO `role_permission` VALUES ('609', '2', '58');
INSERT INTO `role_permission` VALUES ('610', '2', '59');
INSERT INTO `role_permission` VALUES ('611', '2', '60');
INSERT INTO `role_permission` VALUES ('612', '2', '61');
INSERT INTO `role_permission` VALUES ('613', '2', '64');
INSERT INTO `role_permission` VALUES ('614', '2', '65');
INSERT INTO `role_permission` VALUES ('615', '2', '66');
INSERT INTO `role_permission` VALUES ('616', '2', '67');
INSERT INTO `role_permission` VALUES ('617', '2', '68');
INSERT INTO `role_permission` VALUES ('618', '2', '69');
INSERT INTO `role_permission` VALUES ('619', '2', '70');
INSERT INTO `role_permission` VALUES ('620', '2', '71');
INSERT INTO `role_permission` VALUES ('621', '2', '72');
INSERT INTO `role_permission` VALUES ('622', '2', '73');
INSERT INTO `role_permission` VALUES ('623', '2', '74');
INSERT INTO `role_permission` VALUES ('624', '2', '75');
INSERT INTO `role_permission` VALUES ('625', '2', '76');
INSERT INTO `role_permission` VALUES ('626', '2', '77');
INSERT INTO `role_permission` VALUES ('627', '2', '78');
INSERT INTO `role_permission` VALUES ('628', '2', '79');
INSERT INTO `role_permission` VALUES ('629', '2', '80');
INSERT INTO `role_permission` VALUES ('630', '2', '81');
INSERT INTO `role_permission` VALUES ('631', '2', '82');
INSERT INTO `role_permission` VALUES ('632', '2', '83');
INSERT INTO `role_permission` VALUES ('633', '2', '84');
INSERT INTO `role_permission` VALUES ('634', '2', '85');
INSERT INTO `role_permission` VALUES ('948', '1', '86');
INSERT INTO `role_permission` VALUES ('949', '1', '87');
INSERT INTO `role_permission` VALUES ('1081', '2', '88');
INSERT INTO `role_permission` VALUES ('1082', '2', '89');

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES ('1', 'admin', '$2a$10$unFUmcYcoVov8VHnR1y9Fu9k214dgwsavhEjJAZizds/umAN1kfoC', now(), now());
INSERT INTO `user` VALUES ('2', 'test', '$2a$10$unFUmcYcoVov8VHnR1y9Fu9k214dgwsavhEjJAZizds/umAN1kfoC', now(), now());

-- ----------------------------
-- Records of user_role
-- ----------------------------
INSERT INTO `user_role` VALUES ('1', '1', '1');
INSERT INTO `user_role` VALUES ('2', '2', '2');


DROP TABLE IF EXISTS `ping_agent_result`;
CREATE TABLE `ping_agent_result`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID,自增',
  `CALL_TIME` datetime(0) NULL DEFAULT NULL COMMENT '下发互PING命令的时间',
  `START_TIME` datetime(0) NULL DEFAULT NULL COMMENT 'PING开始时间',
  `SOURCE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '源IP',
  `DEST` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '目的IP',
  `TIME_SPENT` int(11) NULL DEFAULT NULL COMMENT 'PING耗时，单位：毫秒',
  `CONNECT_FLAG` tinyint(1) NULL DEFAULT NULL COMMENT '是否连接成功，0：否，1：是',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8047 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `db_backup`;
CREATE TABLE `db_backup`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `LOCAL_PATH` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '本机备份路径',
  `LOCAL_IP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '本机IP',
  `DB_USER` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '数据库用户名',
  `DB_PASS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '数据库密码',
  `REMOTE_PATH` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异机备份路径',
  `REMOTE_IP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异机IP',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for `modify_config`
-- ----------------------------
DROP TABLE IF EXISTS `modify_config`;
CREATE TABLE `modify_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deploy_path` varchar(255) NOT NULL,
  `program_name` varchar(20) NOT NULL,
  `config_path` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of db_backup
-- ----------------------------
INSERT INTO `db_backup` VALUES (1, 'D:\\Data\\', '********', 'deploy', 'ansec_888_999_2019', '/Data/', '*********');
INSERT INTO `db_backup` VALUES (2, '/Data/', '*********', 'root', 'hadoop', 'D:\\Data\\', '********');