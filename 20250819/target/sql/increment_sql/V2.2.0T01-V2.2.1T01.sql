INSERT INTO `permission` VALUES ('85', '分发公共配置', '/softwareWebsocket/9', 9, null, '');

INSERT INTO `role_permission` VALUES ('634', '2', '85');

UPDATE `es_timing_task` SET `NUMBER` = 0, `REMARK` = '删除总开关' WHERE `ID` = 2 AND `NAME` = Cast('deleteIndex' AS Binary(11));
UPDATE `es_timing_task` SET `NAME` = 'httpMinimumRetentionDays', `NUMBER` = 180, `REMARK` = 'http索引删除最小保留天数' WHERE `ID` = 6 AND `NAME` = Cast('minimumRetentionDaysForIndexDeletion' AS Binary(36));
UPDATE `es_timing_task` SET `NAME` = 'dnsMinimumRetentionDays', `NUMBER` = 180, `REMARK` = 'dns索引删除最小保留天数' WHERE `ID` = 7 AND `NAME` = Cast('closeIndexCopyIsNull' AS Binary(20));
UPDATE `es_timing_task` SET `NAME` = 'ipMinimumRetentionDays', `NUMBER` = 180, `REMARK` = 'ip索引删除最小保留天数' WHERE `ID` = 8 AND `NAME` = Cast('startCycle' AS Binary(10));
INSERT INTO `es_timing_task` VALUES (9, 'checkCycleNumber', 5, 'false', '循环检查是否达到删除要求的次数(默认为5次)');
INSERT INTO `es_timing_task` VALUES (10, 'closeIndexCopyIsNull', 0, 'false', '关闭索引时是否设置待关闭的索引的副本为0');
INSERT INTO `es_timing_task` VALUES (11, 'startCycle', 28800, 'false', '项目启动的周期(单位:秒)');
INSERT INTO `es_timing_task` VALUES (12, 'storeRatio', 110, 'false', 'ES检测体积比告警值');
INSERT INTO `es_timing_task` VALUES (13, 'deleteNumber', 20, 'false', '每次批量删除索引的个数(默认为20个)');



ALTER TABLE `front_device` MODIFY `DESCRIPTION` varchar(255) DEFAULT '';
ALTER TABLE `front_device` MODIFY `soft_lost_ratio` double(10,5) DEFAULT NULL COMMENT '程序丢包率';

