update dynamic_static_monitor set monitor_time=date_sub(monitor_time,interval 1 day);

alter table hardware_info add column SEND_BYTE_PER_SECOND int comment '每秒发送字节',add column RECEIVE_BYTE_PER_SECOND int comment '每秒接收字节';
alter table hardware_history_health add column SEND_BYTE_PER_SECOND int comment '每秒发送字节',add column RECEIVE_BYTE_PER_SECOND int comment '每秒接收字节';

DROP TABLE IF EXISTS `db_backup`;
CREATE TABLE `db_backup`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `LOCAL_PATH` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '本机备份路径',
  `LOCAL_IP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '本机IP',
  `DB_USER` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '数据库用户名',
  `DB_PASS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '数据库密码',
  `REMOTE_PATH` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异机备份路径',
  `REMOTE_IP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异机IP',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_backup
-- ----------------------------
INSERT INTO `db_backup` VALUES (1, 'D:\\Data\\', '********', 'deploy', 'ansec_888_999_2019', '/Data/', '*********');
INSERT INTO `db_backup` VALUES (2, '/Data/', '*********', 'root', 'hadoop', 'D:\\Data\\', '********');

alter table mysql_backup_info add column ip varchar(32) comment 'IP',add column remote_success int comment '异机备份是否成功',add column `ignore_status` tinyint default 0 comment '计算状态时是否忽略该条记录';

alter table agent drop column ntp_status;
alter table agent add column NTP_STATUS int(10) DEFAULT NULL COMMENT 'ntp服务状态；0异常，1正常';

DROP TABLE IF EXISTS `deploy_record`;
CREATE TABLE `deploy_record` (
  `ID` int(32) NOT NULL AUTO_INCREMENT,
  `NAME` varchar(255) DEFAULT NULL COMMENT '名称',
  `VERSION` varchar(255) DEFAULT NULL COMMENT '版本号',
  `CREATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `STATUS` tinyint(11) DEFAULT '0',
  `MODIFY_TIME` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `REMARK1` text,
  `REMARK2` text,
  `REMARK3` text,
  `REMARK4` text,
  `REMARK5` text,
  `CONFIG_PATH` text,
  `PRODUCT` varchar(100) DEFAULT NULL,
  `PROGRAM_PATHS` text,
  `FULL_PACKAGE` varchar(100) DEFAULT NULL,
  `IS_MODIFY_COMMON_CONFIG` tinyint(11) DEFAULT '0' COMMENT '是否修改公共配置',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=194 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for `modify_config`
-- ----------------------------
DROP TABLE IF EXISTS `modify_config`;
CREATE TABLE `modify_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deploy_path` varchar(255) NOT NULL,
  `program_name` varchar(20) NOT NULL,
  `config_path` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

alter table software_info add column MIN_HEAP_SIZE int(10) DEFAULT NULL COMMENT '最小堆内存';
update software_info set MIN_HEAP_SIZE = HEAP_SIZE;


ALTER TABLE deploy_task DROP COLUMN deploy_dir
ALTER TABLE deploy_task ADD COLUMN `SOURCE_PATH` varchar(255) NOT NULL;
ALTER TABLE deploy_task ADD COLUMN `TARGET_PATH` varchar(255) NOT NULL;
ALTER TABLE deploy_task ADD COLUMN `NEED_DELETE_FILES` varchar(255) DEFAULT NULL;
ALTER TABLE deploy_task ADD COLUMN `IS_FULL_PACKAGE` varchar(255) DEFAULT NULL,
ALTER TABLE deploy_task ADD COLUMN `IGNORE_BACKUP` varchar(255) DEFAULT NULL,
ALTER TABLE deploy_task ADD COLUMN `OLD_NAME` varchar(255) NOT NULL;

ALTER TABLE agent ADD COLUMN `EXISTS_VPN` tinyint(1) DEFAULT '0' COMMENT '是否配置了VPN:0没有,1有配置';























