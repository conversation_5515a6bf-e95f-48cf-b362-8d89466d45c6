ALTER TABLE `software_info` ADD `IS_EXISTS` tinyint(1) DEFAULT '1' COMMENT '启动脚本是否存在；0-不存在；1-存在';

DROP TABLE IF EXISTS `request_record`;
CREATE TABLE `request_record` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `URI` varchar(200) NOT NULL,
  `MODULE` varchar(200) NOT NULL COMMENT '模块，即uri的第一级',
  `START_TIME` datetime NOT NULL,
  `END_TIME` datetime NOT NULL,
  `CREATE_TIME` datetime NOT NULL,
  `TIME_SPENT` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

INSERT INTO `permission` VALUES ('86', '接口调用', '/request', null, '12', '/requestManage');
INSERT INTO `permission` VALUES ('87', '接口调用统计', '/request/record/statis', '86', null, '');

INSERT INTO `role_permission`(role_id,permission_id) VALUES ('1', '86');
INSERT INTO `role_permission`(role_id,permission_id) VALUES ('1', '87');

INSERT ignore INTO `index_monitor` VALUES ('56', '线索查证系统-IP会话', 'ip-*', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null);
INSERT ignore INTO `index_monitor` VALUES ('57', '线索查证系统-DNS解析', 'dns-*', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null);
INSERT ignore INTO `index_monitor` VALUES ('58', '线索查证系统-HTTP访问', 'http-*', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null);

INSERT INTO `permission` VALUES ('88', '一键关闭平台', '/agentWebsocket/10', 9, NULL, '  ');
INSERT INTO `permission` VALUES ('89', '一键开启平台', '/agentWebsocket/11', 9, NULL, '   ');

INSERT INTO `role_permission`(role_id,permission_id) VALUES (2, 88);
INSERT INTO `role_permission`(role_id,permission_id) VALUES (2, 89);

DROP TABLE IF EXISTS `ping_agent_result`;
CREATE TABLE `ping_agent_result`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID,自增',
  `CALL_TIME` datetime(0) NULL DEFAULT NULL COMMENT '下发互PING命令的时间',
  `START_TIME` datetime(0) NULL DEFAULT NULL COMMENT 'PING开始时间',
  `SOURCE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '源IP',
  `DEST` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '目的IP',
  `TIME_SPENT` int(11) NULL DEFAULT NULL COMMENT 'PING耗时，单位：毫秒',
  `CONNECT_FLAG` tinyint(1) NULL DEFAULT NULL COMMENT '是否连接成功，0：否，1：是',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8047 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

alter table index_monitor add column ignore_status tinyint default 0 COMMENT '0:否，1：是';

ALTER TABLE `front_device` ADD `plugin_active_list` text;
ALTER TABLE `front_device` ADD `ignore_exception_info` text COMMENT '忽略异常信息';
ALTER TABLE `front_device` ADD `exception_type` varchar(255) DEFAULT NULL COMMENT '异常类型：0-正常；1-回传异常；2-丢包异常；3-重启异常；4堆积异常；5-传输异常；';
ALTER TABLE `front_device` ADD `ignore_exception_num` int(10) DEFAULT '0' COMMENT '已忽略异常数量';
ALTER TABLE `front_device` ADD `is_display` tinyint(1) DEFAULT '1' COMMENT '0-不显示；1-显示';