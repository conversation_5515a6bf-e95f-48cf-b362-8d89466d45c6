# maintain-server-2.0 详细流程图与思维导图

## 1. 系统整体架构流程图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           maintain-server-2.0 系统架构                          │
└─────────────────────────────────────────────────────────────────────────────────┘

┌──────────────────┐    ICE协议     ┌──────────────────┐    HTTP/WebSocket  ┌──────────────────┐
│  maintain-agent  │ ──────��───────▶│ maintain-server  │ ──────────────────▶│ maintain-web     │
│                  │                │                  │                    │                  │
│ 数据采集端        │                │ 数据处理中心      │                    │ 数据展示端        │
│                  │                │                  │                    │                  │
│ 核心类:          │                │ 核心类:          │                    │ 核心技术:        │
│ MaintainAgentMain│                │ IceMaintainServer│                    │ Vue.js 2.5.2     │
│ HardInfo         │                │ BigdataClient    │                    │ Element UI       │
│ SoftwareMonitor  │                │ HttpUtil         │                    │ ECharts 4.2.0    │
│                  │                │                  │                    │                  │
│ 配置文件:        │                │ 配置文件:        │                    │ 端口: 8080       │
│ conf/config.json │                │ application.yml  │                    │                  │
└──────────────────┘                └──────────────────┘                    └──────────────────┘
                                              │
                                              ▼
                                    ┌──────────────────┐
                                    │   外部系统集成    │
                                    │                  │
                                    │ Elasticsearch    │
                                    │ 端口: 9200/9300  │
                                    │                  │
                                    │ Kafka           │
                                    │ 端口: 9092      │
                                    │                  │
                                    │ MySQL           │
                                    │ 端口: 3306      │
                                    │                  │
                                    │ HBase           │
                                    │ CDH/Ambari      │
                                    └──────────────────┘
```

## 2. 数据接收与处理核心流程图

```
┌─────────────────────────────���───────────────────────────────────────────────────┐
│                        IceMaintainServer 数据处理流程                            │
└─────────────────────────────────────────────────────────────────────────────────┘

Agent数据上报
      │
      ▼
┌──────────────────┐
│ /request接口     │ ──── 类: IceMaintainServer.java
│ @RequestMapping  │      方法: request(int flag, String param)
└──────────────────┘
      │
      ▼
┌──────────────────┐
│ 限流检查         │ ──── RateLimiter limiter = RateLimiter.create(5)
│ RateLimiter      │      RateLimiter limiter2 = RateLimiter.create(10)
└──────────────────┘
      │
      ▼
┌──────────────────┐
│ Flag参数路由     │
└──────────────────┘
      │
      ├─ flag=1 ──▶ ┌───────────���──────┐
      │            │ 硬件监控数据      │ ──── 方法: dealHardwareData(String params)
      │            │ HARDWARE_MONITOR │      类: HardwareVo, HardwareService
      │            └──────────────────┘      配置: 30秒间隔采集
      │
      ├─ flag=2 ──▶ ┌──────────────────┐
      │            │ 软件监控数据      │ ──── 方法: dealSoftwareData(String params)
      │            │ SOFTWARE_MONITOR │      类: SoftwareVo, SoftwareService
      │            └──────────────────┘      配置: 180秒间隔采集
      │
      ├─ flag=3 ──▶ ┌──────────────────┐
      │            │ 业务监控数据      │ ──── 方法: dealBusinessMonitor(String params)
      │            │ BUSINESS_MONITOR │      类: BusinessMonitorVo
      │            └──────────────────┘
      │
      ├─ flag=4 ──▶ ┌──────────────────┐
      │            │ 重启日志         │ ──── 方法: restartLog(String params)
      │            │ RESTART_LOG      │      类: SoftwareRestartVo
      │            └──────────────────┘
      │
      ├─ flag=5 ──▶ ┌──────────────────┐
      │            │ 获取软件信息      │ ──── 方法: softwareService.getSoftwareInfoList()
      │            │ GET_SOFTWARE_INFO│      返回: JSON格式软件配置
      │            └──────────────────┘
      │
      └─ flag=6 ──▶ ┌──────────────────┐
                   │ 心跳响应         │ ──── 返回: "pong"
                   │ PONG            │      用于: 连接状态检测
                   └──────────────────┘
```

## 3. 硬件数据处理详细流程图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           硬件数据处理流程                                       │
└────────────────────────────────────────────────────���────────────────────────────┘

Agent硬件数据上报
      │
      ▼
┌──────────────────┐
│ JSON数据解析     │ ──── 类: HardwareVo
│ JsonUtil.parse   │      字段: type="hardware"
└──────────────────┘
      │
      ▼
┌──────────────────┐
│ Agent信息验证    │ ──── 类: AgentService
│ 根据IP查找Agent  │      方法: getAgentByCriteria()
└──────────────────┘      表: agent表
      │
      ▼
┌──────────────────┐
│ 硬件信息提取     │ ──── 类: HardwareVo.Info
│ CPU/内存/磁盘    │      字段: cpu, mem, disk, network
└──────────────────┘
      │
      ├─ CPU处理 ──▶ ┌──────────────────┐
      │             │ CPU使用率计算    │ ──── hardwareVo.setUsedCpu()
      │             │ usedPercent     │      来源: hardwareInfo.getCpu()
      │             └──────────────────┘
      │
      ├─ 内存处理 ──▶ ┌─────��────────────┐
      │             │ 内存使用率计算    │ ──── hardwareVo.setUsedMemory()
      │             │ actualUsed/total│      hardwareVo.setMemoryUsedPercent()
      │             └──────────────────┘
      │
      ├─ 磁盘处理 ──▶ ┌────���─────────────┐
      │             │ 磁盘容量计算      │ ──── 单位转换: G/M/T
      │             │ 告警阈值判断      │      告警级别: GREEN/YELLOW/RED
      │             └──────────────────┘      配置: diskRange = "80,90"
      │
      └─ 网络处理 ──▶ ┌──────────────────┐
                    │ 网络流量计算      │ ──── setSendBytePerSecond()
                    │ 发送/接收速率     │      setReceiveBytePerSecond()
                    └──────────────────┘
      │
      ▼
┌──────────────────┐
│ 数据库存储       │ ──── 类: HardwareService
│ MySQL入库       │      方法: addHardwareInfo()
└──────────────────┘      表: hardware_history_health
```

## 4. 软件数据处理详细流程图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           软件数据处理流程                                       │
└─────────────────────────────────────────────────────────────────────────────────┘

Agent软件数据上报
      │
      ▼
┌──────────────────┐
│ JSON数据解析     │ ──── 类: SoftwareVo
│ 程序基本信息     │      字段: name, host, pid, status
└──────────────────┘
      │
      ▼
┌──────────────────┐
│ 程序名称验证     │ ──── 过滤: MaintainServer, MaintainAgent
│ 排除系统程序     │      验证: StringUtils.isEmpty()
└──────────────────┘
      │
      ▼
┌──────────────────┐
│ Agent关联查询    │ ──── 类: AgentService
│ 根据host查找     │      方法: getAgentByCriteria()
└──────────────────┘
      │
      ▼
┌──────────────────┐
│ 内存信息处理     │ ──── 方法: softwareSetMemoryInfo()
│ 使用率计算       │      单位转换: m/M, g/G, 字节
└──────────────────┘      公式: used/total*100
      │
      ▼
┌──────────────────┐
│ 磁盘信息处理     │ ──── 方法: softwareSetDiskInfo()
│ 程序占用计算     │      关联: HardwareService
└──────────────────┘      路径: Linux="/", Windows="D:\"
      │
      ▼
┌──────────────────┐
│ 进程信息处理     │ ──── 方法: handleSoftwareBaseInfo()
│ common/self/log  │      字段: cpuPercent, pid, startParam
└──────────────────┘      时间: startTime -> createTime
      │
      ▼
┌──────────────────┐
│ 数据库存储       │ ──── 类: SoftwareService
│ 软件监控入库     │      方法: setSoftwareInfo()
└──────────────────┘      表: software表
```

## 5. cloudapi模块架构思维导图

```
                            cloudapi模块
                                │
                ┌───────────────┼───────────────┐
                │               │               │
            bigdata/          cloud/          util/
         (大数据平台)       (云平台-待扩展)   (工具类)
                │                              │
        ┌───────┼───────┐                     │
        │       │       │                     │
    config/   dto/   service/              HttpUtil.java
        │       │       │                 (HTTP客户端)
        │       │       │                     │
BigDataProperties  数据传输对象        ┌─────┼─────┐
(平台配置)          │              │     │     │
        │           │              │   GET  POST PUT DELETE
    endPoint    HostApiDto      BigdataApi      │
    username    ServiceApiDto   (标准接口)   RestTemplate
    password    ComponentApiDto      │       (Spring HTTP)
    apiVersion  CurrentAlertApiDto   │
                HistoryAlertApiDto   │
                                    │
                              BigdataClient
                            (Ambari实现)
                                    │
                        ┌───────────┼───────────┐
                        │           │           │
                   认证处理      数据解析     API调用
                 Basic Auth    JSON转DTO   /api/v1/*
                Base64编码    FastJSON解析  RESTful接口
                        │           │           │
                   getHttpHeaders() getDtoList() getUrlPrefix()
```

## 6. 配置体系思维导图

```
                        maintain-server-2.0 配置体系
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
              application.yml   JSON配置文件    外部配置
              (主配置文件)         │           (环境相关)
                    │               │               │
        ┌───────────┼───────────┐   │       ┌───────┼───────┐
        │           │           │   │       │       │       │
    服务器配置   数据库配置   外部集成  │   SSL证书  环境变量  JVM参数
        │           │           │   │       │       │       │
    localHost   双数据源配置  ES/Kafka │   keystore JAVA_OPTS -Xmx
    port:8085   maintain库   集群地址  │              │       │
    websocket   guard库     认证信息   │         系统属性  内存设置
    port:8089      │           │      │
                   │           │      │
            ┌─────┼─────┐     │      │
            │     │     │     │      │
        连接池   事务   索引   │   platform.json
        配置    管理   配置    │   software.json
            │     │     │     │   module.json
        Druid  JTA   prefix   │   data-push-config.json
        监控   原子性  前缀    │
               一致性         │
                             │
                    ┌────────┼────────┐
                    │        │        │
                告警配置   监控配置   业务配置
                    │        │        │
                阈值设置   采集间隔   前端模式
                多级告警   存储路径   升级配置
                通知方式   日志级别   备份策略
```

## 7. 数据流转完整流程图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          数据���转完整流程                                        │
└──────────────────────���──────────────────────────────────────────────────────────┘

┌──────────────────┐    ICE协议     ┌──────────────────┐
│ Agent数据采集    │ ──────────────▶│ Server数据接收   │
│                  │                │                  │
│ 硬件监控:30秒    │                │ /request接口     │
│ 软件监控:180秒   │                │ flag参数路由     │
│ 日志监控:600秒   │                │ 限流处理         │
│                  │                │                  │
│ 类:              │                │ 类:              │
│ HardInfo         │                │ IceMaintainServer│
│ SoftwareMonitor  │                │ AgentService     │
│ MonitoringService│                │ HardwareService  │
└──────────────────┘                │ SoftwareService  │
                                    └──────────────────┘
                                              │
                                              ▼
                                    ┌──────────────────┐
                                    │ 数据处理与计算   │
                                    │                  │
                                    │ 硬件指标计算     │
                                    │ 软件状态分析     │
                                    │ 告警阈值判断     │
                                    │                  │
                                    │ 方法:            │
                                    │ dealHardwareData │
                                    │ dealSoftwareData │
                                    │ updateAgentHeart │
                                    └──────────────────┘
                                              │
                    ┌─────────────────────────┼─────────────────────────┐
                    │                         │                         │
                    ▼                         ▼                         ▼
          ┌──────────────────┐      ┌──────────────────┐      ┌──────────────────┐
          │ MySQL数据存储    │      │ 实时数据推送     │      │ 外部系统集成     │
          │                  │      │                  │      │                  │
          │ 表:              │      │ WebSocket:8089   │      │ Elasticsearch    │
          │ hardware_history │      │ 前端实时更新     │      │ 日志存储         │
          │ software         │      │ 监控数据变化     │      │                  │
          │ business_monitor │      │                  │      │ Kafka            │
          │ agent            │      │ 类:              │      │ 消息推送         │
          │                  │      │ WebSocketService │      │                  │
          │ 服务:            │      │                  │      │ HBase            │
          │ HardwareService  │      │                  │      │ 大数据存储       │
          │ SoftwareService  │      │                  │      │                  │
          │ AgentService     │      │                  │      │ CDH/Ambari       │
          └──────────────────┘      └──────────────────┘      │ 集群监控         │
                    │                         │               └──────────────────┘
                    │                         │                         │
                    ▼                         ▼                         ▼
          ┌──────────────────┐      ┌──────────────────┐      ┌──────────────────┐
          │ 前端数据查询     │      │ 告警通知处理     │      │ 定时任务调度     │
          │                  │      │                  │      │                  │
          │ HTTP REST API    │      │ 多级告警机制     │      │ 数据清理         │
          │ 分页查询         │      │ 阈值配置         │      │ 报告生成         │
          │ 条件筛选         │      │ 通知推送         │      │ 集群数据同步     │
          │                  │      │                  │      │                  │
          │ 控制器:          │      │ 告警级别:        │      │ 调度器:          │
          │ HardwareController│      │ GREEN/YELLOW/RED │      │ Spring Scheduler │
          │ SoftwareController│      │                  │      │ @Scheduled       │
          │ AgentController  │      │                  │      │                  │
          └──────────────────┘      └──────────────────┘      └──────────────────┘
```

## 8. cloudapi大数据平台集成流程图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                      cloudapi大数据平台集成流程                                  │
└─────────────────────────────────────────────────────────────────────────────────┘

定时任务触发
      │
      ▼
┌──────────────────┐
│ BigDataService   │ ──── 类: BigDataServiceImpl
│ apiDataToMysql() │      方法: 数据同步入口
└──────────────────┘
      │
      ▼
┌────────���─────────┐
│ BigdataClient    │ ──── 类: BigdataClient
│ API调用          │      实现: BigdataApi接口
└──────────────────┘
      │
      ├─ 集群信息 ──▶ ┌──────────────────┐
      │             │ getClusterName() │ ──── API: /clusters
      │             │ 获取集群名称     │      返回: cluster_name
      │             └──────────────────┘
      │
      ├─ 宿主机信息 ─▶ ┌──────────────────┐
      │             │ getHosts()       │ ──── API: /clusters/{name}/hosts
      │             │ 服务器列表       │      DTO: HostApiDto
      │             └──────────────────┘      字段: cpu_count, total_mem, disk_info
      │
      ├─ 服务信息 ──▶ ┌──────────────────┐
      │             │ getServices()    │ ──── API: /clusters/{name}/services
      │             │ 服务状态         │      DTO: ServiceApiDto
      │             └──────────────────┘      字段: service_name, state
      │
      ├�� 组件信息 ──▶ ┌──────────────────┐
      │             │ getComponents()  │ ──── API: /clusters/{name}/components
      │             │ 组件状态         │      DTO: ComponentApiDto
      │             └──────────────────┘      字段: component_name, state
      │
      └─ 告警信息 ──▶ ┌──────────────────┐
                    │ getAlerts()      │ ──── API: /clusters/{name}/alerts
                    │ 当前告警         │      DTO: CurrentAlertApiDto
                    └──────────────────┘      字段: alert_name, state, text
      │
      ▼
┌──────────────────┐
│ HTTP请求处理     │ ──── 类: HttpUtil
│ RestTemplate     │      方法: get(), postJson()
└──────────────────┘      认证: Basic Auth (Base64)
      │
      ▼
┌─────────────────���┐
│ JSON数据解析     │ ──── 工具: FastJSON
│ DTO对象转换      │      方法: getDtoList()
└──────────────────┘      解析: items数组 -> DTO列表
      │
      ▼
┌──────────────────┐
│ MySQL数据存储    │ ──── 表: bd_host, bd_service
│ 大数据平台信息   │           bd_component, bd_alert
└──────────────────┘      服务: BigDataService
      │
      ▼
┌──────────────────┐
│ 前端API接口      │ ──── 控制器: BigDataController
│ REST接口暴露     │      接口: /bigdata/pageHost
└──────────────────┘            /bigdata/pageService
                               /bigdata/pageAlerts
```

## 9. 前后端交互架构图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          前后端交互架构                                          │
└─────────────────────────────────────────────────────────────────────────────────┘

┌──────────────────┐    HTTP请求     ┌──────────────────┐    WebSocket    ┌──────────────────┐
│ maintain-web     │ ──────────────▶│ maintain-server  │ ──────────────▶│ 实时数据推送     │
│ Vue.js 2.5.2     │                │ Spring Boot      │                │                  │
│                  │                │                  │                │ 端口: 8089       │
│ 前端组件:        │                │ 后端控制器:      │                │ 线程数: 10       │
│ Element UI       │                │                  │                │ 超时: 30秒       │
│ ECharts图表      │                │ @RestController  │                │                  │
│ Axios HTTP       │                │                  │                │ 推送内容:        │
│                  │                │ HardwareController│                │ 监控数据变化     │
│ 请求格式:        │                │ SoftwareController│                │ 告警信息         │
│ {                │                │ AgentController  │                │ 系统状态         │
│   pageNum: 1,    │                │ BigDataController│                │                  │
│   pageSize: 10,  │                │                  │                │ 类:              │
│   hostName: "xx" │                │ 响应格式:        │                │ WebSocketService │
│ }                │                │ {                │                └──────────────────┘
│                  │                │   code: 200,     │
│ 状态管理:        │                │   message: "ok", │
│ Vuex Store       │                │   data: {        │
│                  │                │     total: 100,  │
│                  │                │     list: [...]  │
│                  │                │   }              │
│                  │                │ }                │
└──────────────────┘                └──────────────────┘
        │                                     │
        │                                     │
        ▼                                     ▼
┌──────────────────┐                ┌──────────────────┐
│ 路由管理         │                │ 服务层处理       │
│ Vue Router       │                │                  │
│                  │                │ HardwareService  │
│ 页面路由:        │                │ SoftwareService  │
│ /hardware        │                │ AgentService     │
│ /software        │                │ BigDataService   │
│ /alerts          │                │                  │
│ /bigdata         │                │ 数据库访问:      │
│                  │                │ MyBatis Mapper   │
│ 权限控制:        │                │                  │
│ 登录验证         │                │ HardwareMapper   │
│ 角色权限         │                │ SoftwareMapper   │
│ 操作日志         │                │ AgentMapper      │
└──────────────────┘                └──────────────────┘
```

## 10. 系统启动与初始化流程图

```
┌───────────────────────────────────────��─────────────────────────────────────────┐
│                        系统启动与初始化流程                                      │
└─────────────────────────────────────────────────────────────────────────────────┘

JVM启动
    │
    ▼
┌──────────────────┐
│ Spring Boot启动  │ ──── 类: MaintainServerApplication
│ main()方法       │      注解: @SpringBootApplication
└──────────────────┘      排除: SecurityAutoConfiguration等
    │
    ▼
┌──────────────────┐
│ 配置文件加载     │ ──── 文件: application.yml
│ BaseConfigUtil   │      方法: BaseConfigUtil.init()
└──────────────────┘      配置: 数据库、ES、Kafka等
    │
    ▼
┌──────────────────┐
│ 数据源初始化     │ ──── 配置: spring.datasource.druid
│ Druid连接池      │      数据源: maintain + guard
└──────────��───────┘      连接池: 初始10，最大256
    │
    ▼
┌──────────────────┐
│ MyBatis初始化    │ ──── 配置: mybatis.mapper-locations
│ Mapper扫描       │      包扫描: com.maintain.server.mapper
└──────────────────┘      XML位置: classpath:mapper/**/*.xml
    │
    ▼
┌──────────────────┐
│ 外部系统连接     │ ──── ES客户端: TransportClient
│ ES/Kafka/HBase   │      Kafka客户端: KafkaProducer
└──────────────────┘      HBase客户端: Connection
    │
    ▼
┌──────────────────┐
│ Web容器启动      │ ──── 容器: Undertow
│ HTTP服务         │      端口: 8085
└──────────────────┘      上下文: /maintain
    │
    ▼
┌──────────────────┐
│ WebSocket启动    │ ──── 端口: 8089
│ 实时通信服务     │      线程数: 10
└──────────────────┘      超时: 30秒
    │
    ▼
┌──────────────────┐
│ 定时任务启动     │ ──── 注解: @EnableScheduling
│ 调度器初始化     │      任务: 数据清理、报告生成
└──────────────────┘      集群数据同步
    │
    ▼
┌──────────────────┐
│ ICE服务注册      │ ──── 类: IceMaintainServer
│ Agent通信准备    │      接口: /request
└──────────────────┘      协议: ICE
    │
    ▼
┌──────────────────┐
│ 应用就绪         │ ──── 事件: ContextRefreshedEvent
│ 服务可用         │      监听: ApplicationListener
└──────────────────┘      状态: RUNNING
```

这些流程图和思维导图详细展示了maintain-server-2.0项目的各个方面，每个节点都标注了对应的类、方法、配置文件和具体实现。通过这些图表，可以清晰地理解系统的整体架构、数据流转过程、核心模块功能以及各组件之间的交互关系。

<function_calls>
<invoke name="add_observations">
<parameter name="observations">[{"entityName": "maintain-server-2.0项目详细分析", "observations": ["已创建详细的流程图与思维导图文档", "文档位置：d:\\code\\2025081901\\20250819\\maintain-server-2.0详细流程图与思维导图.md", "包含10个详细图表：系统整体架构流程图、数据接收与处理核心流程图、硬件数据处理详细流程图、软件数据处理详细流程图、cloudapi模块架构思维导图、配置体系思维导图、数据流转完整流程图、cloudapi大数据平台集成流程图、前后端交互架构图、系统启动与初始化流程图", "每个节点都标注了对应的类名、方法名、配置文件、端口号等具体实现细节", "重点突出了cloudapi模块的技术架构和实现方式", "提供了完整的数据流转路径和组件交互关系", "文档特点：图文并茂、层次清晰、技术细节丰富、便于理解系统架构"]}]