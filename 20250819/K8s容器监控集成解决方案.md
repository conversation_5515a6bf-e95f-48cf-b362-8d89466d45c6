# K8s容器监控集成解决方案

## 🎯 项目概述

基于maintain-server-2.0项目现有架构，集成K8s API实现Docker容器监控功能，复用cloudapi模块的设计模式，实现与现有系统的无缝集成。

## 📋 需求分析

### 监控指标对应关系

| 监控指标 | K8s API来源 | 实现状态 | 备注 |
|---------|------------|---------|------|
| 容器程序名称 | Pod详情接口 metadata.name | ✅ 可实现 | 直接获取 |
| 容器进程号 | - | ❌ 预留 | K8s不直接提供，预留假数据 |
| 容器启动参数 | Pod详情接口 spec.containers[].args | ✅ 可实现 | 启动命令参数 |
| 容器内存使用大小 | Pod指标接口 usage.memory | ✅ 可实现 | 如"2242444Ki" |
| 容器内存使用率 | 计算得出 | ✅ 可实现 | usage/requests*100% |
| 容器CPU使用率 | Pod指标接口 usage.cpu | ✅ 可实现 | 如"5704362n" |
| 容器磁盘使用大小 | - | ❌ 预留 | K8s��直接提供，预留假数据 |
| 容器磁盘使用率 | - | ❌ 预留 | K8s不直接提供，预留假数据 |
| 容器端口占用 | Pod详情接口 spec.containers[].ports | ✅ 可实现 | containerPort |
| 容器操作系统 | - | ❌ 预留 | 可从节点信息推断，预留假数据 |
| 容器防火墙状态 | - | ❌ 预留 | K8s不提供，预留假数据 |
| 容器网卡信息 | Pod详情接口 status.podIP | ✅ 可实现 | Pod IP信息 |
| 容器版本号 | Pod详情接口 spec.containers[].image | ✅ 可实现 | 镜像版本 |

## 🏗️ 架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "K8s集群"
        K1[K8s API Server<br/>**************:6443]
        K2[Pod: datacenter]
        K3[Pod: maintainserver]
        K4[Pod: o2server]
    end
    
    subgraph "maintain-server-2.0"
        subgraph "com.maintain.server.k8s"
            A1[K8sProperties<br/>配置类]
            A2[K8sApi<br/>接口定义]
            A3[K8sClient<br/>API客户端]
            A4[K8sService<br/>业务服务]
            A5[K8sController<br/>REST控制器]
        end
        
        subgraph "复用cloudapi架构"
            B1[HttpUtil<br/>HTTP工具类]
            B2[BigDataController<br/>参考模式]
        end
    end
    
    subgraph "前端展示"
        C1[容器监控页面]
        C2[容器详情页面]
        C3[容器日志页面]
    end
    
    K1 -->|Bearer Token| A3
    A3 --> A4
    A4 --> A5
    A5 --> C1
    A5 --> C2
    A5 --> C3
    B1 --> A3
```

### 模块结构设计

```
com.maintain.server.k8s/
├── config/
│   └── K8sProperties.java          # K8s配置类
├── dto/
│   ├── PodDto.java                 # Pod数据传输对象
│   ├── ContainerDto.java           # 容器数据传输对象
│   ├── PodMetricsDto.java          # Pod指标数据传输对象
│   └── ContainerMetricsDto.java    # 容器指标数据传输对象
├── enums/
│   ├── PodPhaseEnum.java           # Pod状态枚举
│   └── ContainerStateEnum.java     # 容器状态枚举
├── service/
│   ├── K8sApi.java                 # K8s API接口定义
│   ├── K8sClient.java              # K8s API客户端实现
│   ├── K8sService.java             # K8s业务服务接口
│   └── K8sServiceImpl.java         # K8s业务服务实现
└── controller/
    └── K8sController.java          # K8s REST控制器
```

## 💻 核心代码实现

### 1. K8s配置类

```java
@ConfigurationProperties(prefix = "k8s")
@Configuration
@Data
public class K8sProperties {
    private String apiServer = "https://**************:6443";
    private String token = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImhESXlUUGtTQkFiWmFaXzNDcFBDTUhQelR6cXp3V2l3bHQ3cUVUSm1Iem8ifQ...";
    private String namespace = "default";
    private Integer timeout = 30000;
}
```

### 2. K8s API接口定义

```java
public interface K8sApi {
    /**
     * 获取Pod列表
     */
    List<PodDto> getPods(String namespace);
    
    /**
     * 获取Pod详情
     */
    PodDto getPodDetail(String namespace, String podName);
    
    /**
     * 获取Pod日志
     */
    String getPodLogs(String namespace, String podName, String containerName, Integer tailLines);
    
    /**
     * 获取Pod指标
     */
    PodMetricsDto getPodMetrics(String namespace, String podName);
}
```

### 3. K8s客户端实现

```java
@Component
public class K8sClient implements K8sApi {
    @Autowired
    private K8sProperties properties;
    
    @PostConstruct
    public void init() {
        // 初始化HTTP客户端，配置SSL忽略等
    }
    
    @Override
    public List<PodDto> getPods(String namespace) {
        String url = properties.getApiServer() + "/api/v1/namespaces/" + namespace + "/pods";
        HttpHeaders headers = getAuthHeaders();
        
        String response = HttpUtil.get(url, null, headers, String.class);
        return parsePodList(response);
    }
    
    @Override
    public PodDto getPodDetail(String namespace, String podName) {
        String url = properties.getApiServer() + "/api/v1/namespaces/" + namespace + "/pods/" + podName;
        HttpHeaders headers = getAuthHeaders();
        
        String response = HttpUtil.get(url, null, headers, String.class);
        return parsePodDetail(response);
    }
    
    @Override
    public String getPodLogs(String namespace, String podName, String containerName, Integer tailLines) {
        String url = properties.getApiServer() + "/api/v1/namespaces/" + namespace + "/pods/" + podName + "/log";
        Map<String, Object> params = new HashMap<>();
        params.put("container", containerName);
        params.put("tailLines", tailLines);
        
        HttpHeaders headers = getAuthHeaders();
        return HttpUtil.get(url, params, headers, String.class);
    }
    
    @Override
    public PodMetricsDto getPodMetrics(String namespace, String podName) {
        String url = properties.getApiServer() + "/apis/metrics.k8s.io/v1beta1/namespaces/" + namespace + "/pods/" + podName;
        HttpHeaders headers = getAuthHeaders();
        
        String response = HttpUtil.get(url, null, headers, String.class);
        return parseMetrics(response);
    }
    
    private HttpHeaders getAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + properties.getToken());
        headers.set("Accept", "application/json");
        return headers;
    }
}
```

### 4. 业务服务实现

```java
@Service
public class K8sServiceImpl implements K8sService {
    @Autowired
    private K8sClient k8sClient;
    
    @Override
    public PageVo<ContainerVo> getContainers(ContainerReq req) {
        // 1. 获取Pod列表
        List<PodDto> pods = k8sClient.getPods(req.getNamespace());
        
        // 2. 转换为容器监控数据
        List<ContainerVo> containers = new ArrayList<>();
        for (PodDto pod : pods) {
            for (ContainerDto container : pod.getContainers()) {
                ContainerVo vo = convertToContainerVo(pod, container);
                containers.add(vo);
            }
        }
        
        // 3. 分页处理
        return PageUtil.getPageVo(containers, req.getPageNum(), req.getPageSize());
    }
    
    @Override
    public ContainerDetailVo getContainerDetail(String namespace, String podName, String containerName) {
        // 1. 获取Pod详情
        PodDto pod = k8sClient.getPodDetail(namespace, podName);
        
        // 2. 获取Pod指标
        PodMetricsDto metrics = k8sClient.getPodMetrics(namespace, podName);
        
        // 3. 组装详细信息
        return buildContainerDetail(pod, metrics, containerName);
    }
    
    private ContainerVo convertToContainerVo(PodDto pod, ContainerDto container) {
        ContainerVo vo = new ContainerVo();
        
        // 基本信息
        vo.setName(container.getName());                    // 容器程序名称
        vo.setPodName(pod.getName());
        vo.setNamespace(pod.getNamespace());
        vo.setImage(container.getImage());                  // 容器版本号
        vo.setStatus(container.getState());
        
        // 网络信息
        vo.setPodIp(pod.getPodIP());                       // 容器网卡信息
        
        // 端口信息
        List<Integer> ports = new ArrayList<>();
        if (container.getPorts() != null) {
            for (ContainerPortDto port : container.getPorts()) {
                ports.add(port.getContainerPort());        // 容器端口占用
            }
        }
        vo.setPorts(ports);
        
        // 启动参数
        vo.setArgs(container.getArgs());                   // 容器启动参数
        vo.setCommand(container.getCommand());
        
        // 预留字段（暂时用假数据）
        vo.setPid(-1);                                     // 容器进程号（预留���
        vo.setDiskUsage("N/A");                           // 容器磁盘使用大小（预留）
        vo.setDiskUsagePercent("N/A");                    // 容器磁盘使用率（预留）
        vo.setOsType("Linux");                            // 容器操作系统（预留）
        vo.setFirewallStatus("Unknown");                  // 容器防火墙状态（预留）
        
        return vo;
    }
    
    private ContainerDetailVo buildContainerDetail(PodDto pod, PodMetricsDto metrics, String containerName) {
        ContainerDetailVo detail = new ContainerDetailVo();
        
        // 基本信息
        detail.setName(containerName);
        detail.setPodName(pod.getName());
        detail.setNamespace(pod.getNamespace());
        detail.setCreationTime(pod.getCreationTimestamp());
        detail.setStatus(pod.getPhase());
        
        // 资源使用情况
        if (metrics != null && metrics.getContainers() != null) {
            for (ContainerMetricsDto containerMetrics : metrics.getContainers()) {
                if (containerName.equals(containerMetrics.getName())) {
                    // CPU使用率
                    String cpuUsage = containerMetrics.getUsage().getCpu();
                    detail.setCpuUsage(parseCpuUsage(cpuUsage));
                    
                    // 内��使用大小和使用率
                    String memoryUsage = containerMetrics.getUsage().getMemory();
                    detail.setMemoryUsage(parseMemoryUsage(memoryUsage));
                    detail.setMemoryUsagePercent(calculateMemoryPercent(memoryUsage, pod, containerName));
                    break;
                }
            }
        }
        
        return detail;
    }
}
```

### 5. REST控制器

```java
@RestController
@RequestMapping("/k8s")
public class K8sController {
    @Autowired
    private K8sService k8sService;
    
    @PostMapping("/containers")
    public Result<PageVo<ContainerVo>> getContainers(@RequestBody ContainerReq req) {
        return Result.success(k8sService.getContainers(req));
    }
    
    @GetMapping("/containers/{namespace}/{podName}/{containerName}")
    public Result<ContainerDetailVo> getContainerDetail(
            @PathVariable String namespace,
            @PathVariable String podName,
            @PathVariable String containerName) {
        return Result.success(k8sService.getContainerDetail(namespace, podName, containerName));
    }
    
    @GetMapping("/containers/{namespace}/{podName}/{containerName}/logs")
    public Result<String> getContainerLogs(
            @PathVariable String namespace,
            @PathVariable String podName,
            @PathVariable String containerName,
            @RequestParam(defaultValue = "100") Integer tailLines) {
        return Result.success(k8sService.getContainerLogs(namespace, podName, containerName, tailLines));
    }
}
```

## 🔧 配置文件

### application.yml配置

```yaml
# K8s集群配置
k8s:
  api-server: https://**************:6443
  token: eyJhbGciOiJSUzI1NiIsImtpZCI6ImhESXlUUGtTQkFiWmFaXzNDcFBDTUhQelR6cXp3V2l3bHQ3cUVUSm1Iem8ifQ...
  namespace: default
  timeout: 30000
```

## 📊 数据流转流程

```mermaid
sequenceDiagram
    participant F as 前端页面
    participant C as K8sController
    participant S as K8sService
    participant K as K8sClient
    participant A as K8s API Server
    
    F->>C: 请求容器列表
    C->>S: getContainers()
    S->>K: getPods()
    K->>A: GET /api/v1/namespaces/default/pods
    A-->>K: Pod列表JSON
    K-->>S: List<PodDto>
    
    loop 每个Pod
        S->>K: getPodMetrics()
        K->>A: GET /apis/metrics.k8s.io/v1beta1/namespaces/default/pods/{name}
        A-->>K: 指标JSON
        K-->>S: PodMetricsDto
    end
    
    S->>S: 数据转换和组装
    S-->>C: PageVo<ContainerVo>
    C-->>F: Result<PageVo<ContainerVo>>
```

## 🎨 前端集成方案

### 1. 容器监控页面

参考现有的软件监控页面(`/software/list.json`)，创建容器监控页面：

```javascript
// 容器列表API调用
axios.post('/k8s/containers', {
    pageNum: 1,
    pageSize: 20,
    namespace: 'default'
}).then(response => {
    this.containerList = response.data.data.list;
    this.total = response.data.data.total;
});
```

### 2. 容器详情页面

参考现有的软件详情页面(`/software/detail_{softwareId}.json`)，创建容器详情页面：

```javascript
// 容器详��API调用
axios.get(`/k8s/containers/${namespace}/${podName}/${containerName}`)
.then(response => {
    this.containerDetail = response.data.data;
});
```

## 🔄 与现有系统集成

### 1. 复用现有架构

- **配置管理**：参考`BigDataProperties`模式
- **HTTP客户端**：复用`HttpUtil`工具类
- **数据传输**：参考`BigDataController`的DTO设计
- **业务服务**：参考`BigDataService`的分层架构
- **异常处理**：复用全局异常处理器

### 2. 数据库扩展

可选择扩展现有数据库表结构，存储容器监控历史数据：

```sql
-- 容器监控历史表
CREATE TABLE k8s_container_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    namespace VARCHAR(100) NOT NULL,
    pod_name VARCHAR(200) NOT NULL,
    container_name VARCHAR(200) NOT NULL,
    cpu_usage DECIMAL(10,2),
    memory_usage BIGINT,
    memory_usage_percent DECIMAL(5,2),
    status VARCHAR(50),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pod_container (pod_name, container_name),
    INDEX idx_create_time (create_time)
);
```

## 🚀 部署和测试

### 1. 开发环境配置

1. 确保K8s集群可访问
2. 配置正确的Bearer Token
3. 添加SSL证书信任（如需要）

### 2. 测试用例

```java
@Test
public void testGetPods() {
    List<PodDto> pods = k8sClient.getPods("default");
    assertNotNull(pods);
    assertTrue(pods.size() > 0);
}

@Test
public void testGetPodMetrics() {
    PodMetricsDto metrics = k8sClient.getPodMetrics("default", "datacenter");
    assertNotNull(metrics);
    assertNotNull(metrics.getContainers());
}
```

## 📈 扩展规划

### 1. 短期扩展

- 支持多命名空间监控
- 增加容器事件监控
- 实现容器日志实时推送

### 2. 长期规划

- 集成Prometheus指标
- 支持容器操作（重启、扩缩容）
- 实现告警规则配置

## 🎯 总结

本方案完全基于maintain-server-2.0现有架构设计，通过复用cloudapi模块的成功模式，实现了K8s容器监控功能的无缝集成。方案具有以下特点：

1. **架构一致性**：完全遵循现有项目的分层架构和设计模式
2. **代码复用性**：最大化复用HttpUtil、配置管理等现有组件
3. **扩展性良好**：预留了未来功能扩展的接口和数据结构
4. **兼容性强**：与现有监控体系完美融合，不影响原有功能
5. **实用性高**：能够满足当前容器监控的核心需求

通过这个方案，你可以快速实��K8s容器监控功能，同时为后续的功能扩展奠定坚实基础。