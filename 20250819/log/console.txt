2025-08-19 17:26:28.368  INFO 13492 --- [main] c.m.s.MaintainServerApplication          : Starting MaintainServerApplication on DESKTOP-C0C1A73 with PID 13492 (D:\code\20250819\target\libs\classes started by 1 in D:\code\20250819)
2025-08-19 17:26:28.382  INFO 13492 --- [main] c.m.s.MaintainServerApplication          : No active profile set, falling back to default profiles: default
2025-08-19 17:26:37.297  INFO 13492 --- [main] o.a.c.c.StandardService                  : Starting service [Tomcat]
2025-08-19 17:26:37.298  INFO 13492 --- [main] o.a.c.c.StandardEngine                   : Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-19 17:26:37.311  INFO 13492 --- [localhost-startStop-1] o.a.c.c.AprLifecycleListener             : The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\software\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\software\jdk8\bin;D:\software\jdk8\bin;%JAVA_HOME%\bin;C:\Windows\System32;D:\pandoc\;D:\pandoc\;D:\pandoc\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;.]
2025-08-19 17:26:37.452  INFO 13492 --- [localhost-startStop-1] o.a.c.c.C.[.[.[/maintain]                : Initializing Spring embedded WebApplicationContext
2025-08-19 17:26:39.375  INFO 13492 --- [localhost-startStop-1] c.a.d.p.DruidDataSource                  : {dataSource-1} inited
2025-08-19 17:26:41.309  INFO 13492 --- [localhost-startStop-1] c.a.d.p.DruidDataSource                  : {dataSource-2} inited
2025-08-19 17:26:43.811  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : Loaded jar:file:/D:/software/apache-maven-3.9.9/ex_im_repository/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2025-08-19 17:26:43.817  WARN 13492 --- [main] c.a.i.p.i.AssemblerImp                   : Thanks for using Atomikos! Evaluate http://www.atomikos.com/Main/ExtremeTransactions for advanced features and professional support
or register at http://www.atomikos.com/Main/RegisterYourDownload to disable this message and receive FREE tips & advice
2025-08-19 17:26:43.827  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2025-08-19 17:26:43.828  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.allow_subtransactions = true
2025-08-19 17:26:43.828  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.recovery_delay = 10000
2025-08-19 17:26:43.828  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.automatic_resource_registration = true
2025-08-19 17:26:43.828  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.oltp_max_retries = 5
2025-08-19 17:26:43.828  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.client_demarcation = false
2025-08-19 17:26:43.828  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.threaded_2pc = false
2025-08-19 17:26:43.829  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.serial_jta_transactions = true
2025-08-19 17:26:43.829  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.log_base_dir = ./
2025-08-19 17:26:43.829  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.rmi_export_class = none
2025-08-19 17:26:43.830  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.max_actives = 50
2025-08-19 17:26:43.830  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.checkpoint_interval = 500
2025-08-19 17:26:43.831  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.enable_logging = true
2025-08-19 17:26:43.831  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.log_base_name = tmlog
2025-08-19 17:26:43.831  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.max_timeout = 300000
2025-08-19 17:26:43.832  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.trust_client_tm = false
2025-08-19 17:26:43.832  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2025-08-19 17:26:43.833  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.tm_unique_name = *************.tm
2025-08-19 17:26:43.833  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2025-08-19 17:26:43.833  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.oltp_retry_interval = 10000
2025-08-19 17:26:43.833  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: java.naming.provider.url = rmi://localhost:1099
2025-08-19 17:26:43.833  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2025-08-19 17:26:43.835  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.default_jta_timeout = 10000
2025-08-19 17:26:43.837  INFO 13492 --- [main] c.a.i.p.i.AssemblerImp                   : Using default (local) logging and recovery...
2025-08-19 17:26:45.728 TRACE 13492 --- [WebSocketSelector-86] o.j.AbstractWebSocket                    : Connection lost timer started
2025-08-19 17:26:45.838  INFO 13492 --- [WebSocket-Thread] c.m.s.MaintainServerApplication          : Started MaintainServerApplication in 18.432 seconds (JVM running for 22.026)
2025-08-19 17:26:46.773  WARN 13492 --- [pool-2-thread-1] c.a.j.AbstractDataSourceBean             : AtomikosDataSoureBean 'DataSource-**********': poolSize equals default - this may cause performance problems!
2025-08-19 17:26:46.837  INFO 13492 --- [pool-2-thread-1] c.a.d.x.XATransactionalResource          : DataSource-**********: refreshed XAResource
2025-08-19 17:27:11.276  INFO 13492 --- [Thread-49] c.a.d.p.DruidDataSource                  : {dataSource-2} closed
2025-08-19 17:27:11.283  INFO 13492 --- [Thread-49] c.a.d.p.DruidDataSource                  : {dataSource-1} closed
2025-08-19 18:43:29.851  INFO 19692 --- [main] c.m.s.MaintainServerApplication          : Starting MaintainServerApplication on DESKTOP-C0C1A73 with PID 19692 (D:\code\20250819\target\libs\classes started by 1 in D:\code\20250819)
2025-08-19 18:43:29.861  INFO 19692 --- [main] c.m.s.MaintainServerApplication          : No active profile set, falling back to default profiles: default
2025-08-19 18:43:34.708  INFO 19692 --- [main] o.a.c.c.StandardService                  : Starting service [Tomcat]
2025-08-19 18:43:34.709  INFO 19692 --- [main] o.a.c.c.StandardEngine                   : Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-19 18:43:34.716  INFO 19692 --- [localhost-startStop-1] o.a.c.c.AprLifecycleListener             : The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\software\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\software\jdk8\bin;D:\software\jdk8\bin;%JAVA_HOME%\bin;C:\Windows\System32;D:\pandoc\;D:\pandoc\;D:\pandoc\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;.]
2025-08-19 18:43:34.888  INFO 19692 --- [localhost-startStop-1] o.a.c.c.C.[.[.[/maintain]                : Initializing Spring embedded WebApplicationContext
2025-08-19 18:43:36.372  INFO 19692 --- [localhost-startStop-1] c.a.d.p.DruidDataSource                  : {dataSource-1} inited
2025-08-19 18:43:37.416  INFO 19692 --- [localhost-startStop-1] c.a.d.p.DruidDataSource                  : {dataSource-2} inited
2025-08-19 18:43:39.537  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : Loaded jar:file:/D:/software/apache-maven-3.9.9/ex_im_repository/repository/com/atomikos/transactions/4.0.6/transactions-4.0.6.jar!/transactions-defaults.properties
2025-08-19 18:43:39.541  WARN 19692 --- [main] c.a.i.p.i.AssemblerImp                   : Thanks for using Atomikos! Evaluate http://www.atomikos.com/Main/ExtremeTransactions for advanced features and professional support
or register at http://www.atomikos.com/Main/RegisterYourDownload to disable this message and receive FREE tips & advice
2025-08-19 18:43:39.547  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.default_max_wait_time_on_shutdown = 9223372036854775807
2025-08-19 18:43:39.547  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.allow_subtransactions = true
2025-08-19 18:43:39.548  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.recovery_delay = 10000
2025-08-19 18:43:39.548  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.automatic_resource_registration = true
2025-08-19 18:43:39.548  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.oltp_max_retries = 5
2025-08-19 18:43:39.548  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.client_demarcation = false
2025-08-19 18:43:39.548  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.threaded_2pc = false
2025-08-19 18:43:39.548  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.serial_jta_transactions = true
2025-08-19 18:43:39.549  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.log_base_dir = ./
2025-08-19 18:43:39.549  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.rmi_export_class = none
2025-08-19 18:43:39.549  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.max_actives = 50
2025-08-19 18:43:39.549  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.checkpoint_interval = 500
2025-08-19 18:43:39.549  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.enable_logging = true
2025-08-19 18:43:39.549  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.log_base_name = tmlog
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.max_timeout = 300000
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.trust_client_tm = false
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: java.naming.factory.initial = com.sun.jndi.rmi.registry.RegistryContextFactory
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.tm_unique_name = *************.tm
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.forget_orphaned_log_entries_delay = 86400000
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.oltp_retry_interval = 10000
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: java.naming.provider.url = rmi://localhost:1099
2025-08-19 18:43:39.550  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.force_shutdown_on_vm_exit = false
2025-08-19 18:43:39.551  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : USING: com.atomikos.icatch.default_jta_timeout = 10000
2025-08-19 18:43:39.552  INFO 19692 --- [main] c.a.i.p.i.AssemblerImp                   : Using default (local) logging and recovery...
2025-08-19 18:43:41.299 TRACE 19692 --- [WebSocketSelector-75] o.j.AbstractWebSocket                    : Connection lost timer started
2025-08-19 18:43:41.370  INFO 19692 --- [WebSocket-Thread] c.m.s.MaintainServerApplication          : Started MaintainServerApplication in 12.947 seconds (JVM running for 15.667)
2025-08-19 18:43:42.076  WARN 19692 --- [pool-2-thread-1] c.a.j.AbstractDataSourceBean             : AtomikosDataSoureBean 'DataSource-**********': poolSize equals default - this may cause performance problems!
2025-08-19 18:43:42.102  INFO 19692 --- [pool-2-thread-1] c.a.d.x.XATransactionalResource          : DataSource-**********: refreshed XAResource
2025-08-19 18:43:57.143  INFO 19692 --- [Thread-38] c.a.d.p.DruidDataSource                  : {dataSource-2} closed
2025-08-19 18:43:57.151  INFO 19692 --- [Thread-38] c.a.d.p.DruidDataSource                  : {dataSource-1} closed
