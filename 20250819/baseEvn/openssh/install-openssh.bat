@echo off

taskkill /F /IM sshd.exe
netstat -an|findstr 0.0:22222
set cmdstu=%ERRORLEVEL%

if %cmdstu% == 0 (
	echo #-----------------------------------------#
	echo #    sshd�����Ѿ�����,������װ����...     #
	echo #-----------------------------------------#
	pause
	exit
)

if not exist "D:\dist\OpenSSH-Win64" (
	mkdir D:\dist\OpenSSH-Win64
	xcopy %~dp0OpenSSH-Win64 D:\dist\OpenSSH-Win64 /e /h /f
	cd D:\dist\OpenSSH-Win64
	powershell.exe -ExecutionPolicy Bypass -File D:\dist\OpenSSH-Win64\install-sshd.ps1
	
	sc config sshd start= auto
	
	netsh advfirewall firewall delete rule name="sshserver"
    netsh advfirewall firewall add rule name="sshserver" dir=in action=allow protocol=TCP localport=22222
	
	net start sshd
	
	echo 'sshd����װ��D:\dist\OpenSSH-Win64�ɹ�';
	pause

) else (
	echo '���Ѿ���װ��openssh';
	pause
)

