<?xml version="1.0" encoding="utf-8"?>
<instrumentationManifest xsi:schemaLocation="http://schemas.microsoft.com/win/2004/08/events eventman.xsd" xmlns="http://schemas.microsoft.com/win/2004/08/events" xmlns:win="http://manifests.microsoft.com/win/2004/08/windows/events" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:trace="http://schemas.microsoft.com/win/2004/08/events/trace">
	<instrumentation>
		<events>
			<provider name="OpenSSH" guid="{C4B57D35-0636-4BC3-A262-370F249F9802}" symbol="OpenSSH" resourceFileName="%windir%\system32\openssh\ssh-agent.exe" messageFileName="%windir%\system32\openssh\ssh-agent.exe">
				<events>
					<event symbol="CRITICAL_Event" value="1" version="0" channel="OpenSSH/Admin" level="win:Critical" template="2StrTemplate" message="$(string.OpenSSH.event.message)">
					</event>
					<event symbol="ERROR_Event" value="2" version="0" channel="OpenSSH/Admin" level="win:Error" template="2StrTemplate" message="$(string.OpenSSH.event.message)">
					</event>
					<event symbol="WARNING_Event" value="3" version="0" channel="OpenSSH/Operational" level="win:Warning" template="2StrTemplate" message="$(string.OpenSSH.event.message)">
					</event>
					<event symbol="INFO_Event" value="4" version="0" channel="OpenSSH/Operational" level="win:Informational" template="2StrTemplate" message="$(string.OpenSSH.event.message)">
					</event>
					<event symbol="DEBUG_Event" value="6" version="0" channel="OpenSSH/Debug" level="Debug" template="2StrTemplate" message="$(string.OpenSSH.event.message)">
					</event>
				</events>
				<levels>
					<level name="Debug" symbol="Debug" value="16" message="$(string.OpenSSH.level.Debug.message)">
					</level>
				</levels>
				<channels>
					<channel name="OpenSSH/Admin" chid="OpenSSH/Admin" symbol="OpenSSH_Admin" type="Admin" enabled="true">
					</channel>
					<channel name="OpenSSH/Operational" chid="OpenSSH/Operational" symbol="OpenSSH_Operational" type="Operational" enabled="true">
					</channel>
					<channel
						access="O:BAG:BAD:(A;;0x2;;;BU)(A;;0x2;;;S-1-15-2-1)(A;;0x2;;;S-1-15-3-1024-3153509613-960666767-3724611135-2725662640-12138253-543910227-1950414635-4190290187)(A;;0xf0007;;;SY)(A;;0x7;;;BA)(A;;0x7;;;SO)(A;;0x3;;;IU)(A;;0x3;;;SU)(A;;0x3;;;S-1-5-3)(A;;0x3;;;S-1-5-33)(A;;0x1;;;S-1-5-32-573)"
						isolation="Custom" name="OpenSSH/Debug" chid="OpenSSH/Debug" symbol="OpenSSH_Debug" type="Debug" enabled="false">
					</channel>
				</channels>
				<templates>
					<template tid="2StrTemplate">
						<data name="process" inType="win:UnicodeString" outType="xs:string">
						</data>
						<data name="payload" inType="win:UnicodeString" outType="xs:string">
						</data>
					</template>
				</templates>
			</provider>
		</events>
	</instrumentation>
	<localization>
		<resources culture="en-US">
			<stringTable>
				<string id="level.Warning" value="Warning">
				</string>
				<string id="level.Verbose" value="Verbose">
				</string>
				<string id="level.Informational" value="Information">
				</string>
				<string id="level.Error" value="Error">
				</string>
				<string id="level.Critical" value="Critical">
				</string>
				<string id="OpenSSH.level.Debug.message" value="Debug">
				</string>
				<string id="OpenSSH.event.message" value="%1: %2">
				</string>
			</stringTable>
		</resources>
	</localization>
</instrumentationManifest>
