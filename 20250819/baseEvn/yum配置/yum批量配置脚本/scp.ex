#!/usr/bin/expect -f
set timeout 30
set name [lindex $argv 0]
set password [lindex $argv 1]
set port [lindex $argv 2]
set yumnode [lindex $argv 3]
spawn scp -P ${port} updateYum.sh root@${name}:/root/

expect {
	"*yes/no)?"  {send "yes\r"; exp_continue}
	"*password:"	{send "$password\r"}
}
expect "*#"
spawn ssh $name -p $port /root/updateYum.sh $yumnode
expect "*password:"
send "$password\r"
expect "*#"