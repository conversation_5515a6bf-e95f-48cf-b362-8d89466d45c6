-- `maintain-2.0`.bd_alert definition

CREATE TABLE `bd_alert` (
`id` int NOT NULL AUTO_INCREMENT,
`node` varchar(20) NOT NULL COMMENT '节点 四川',
`cluster_name` varchar(100) NOT NULL COMMENT '集群名称',
`component_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组件名称',
`firmness` varchar(20) DEFAULT NULL COMMENT 'HARD:必须处理 SOFT:可能存在误报',
`host_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '宿主机名称',
`label` varchar(100) NOT NULL COMMENT '告警标签',
`occurrences` int DEFAULT NULL COMMENT '发生次数',
`service_name` varchar(50) DEFAULT NULL COMMENT '服务名称',
`state` varchar(20) DEFAULT NULL COMMENT '状态',
`text` text COMMENT '告警详细信息',
`original_timestamp` datetime DEFAULT NULL COMMENT '开始告警时间',
`latest_timestamp` datetime DEFAULT NULL COMMENT '最近告警时间',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=890 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='告警信息';


-- `maintain-2.0`.bd_component definition

CREATE TABLE `bd_component` (
`node` varchar(20) NOT NULL COMMENT '节点 四川',
`category` varchar(100) NOT NULL COMMENT '角色 SLAVE',
`cluster_name` varchar(50) DEFAULT NULL COMMENT '集群名称',
`component_name` varchar(50) DEFAULT NULL COMMENT '组件名称',
`desired_stack` varchar(50) DEFAULT NULL COMMENT 'hdp版本 HDP-3.3',
`desired_version` varchar(50) DEFAULT NULL COMMENT '具体版本 3.3.1.0-004',
`display_name` varchar(50) DEFAULT NULL COMMENT '页面展示名称',
`service_name` varchar(50) DEFAULT NULL COMMENT '服务名称',
`started_count` int DEFAULT NULL COMMENT '启动次数',
`state` varchar(20) DEFAULT NULL COMMENT '组件状态',
`total_count` int NOT NULL COMMENT '组件总数',
`unknown_count` int DEFAULT NULL COMMENT '组件未知总数',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
UNIQUE KEY `idx_cluster_service_component` (`node`,`cluster_name`,`service_name`,`component_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='组件信息';


-- `maintain-2.0`.bd_history_alert definition

CREATE TABLE `bd_history_alert` (
`id` int NOT NULL AUTO_INCREMENT,
`node` varchar(20) NOT NULL COMMENT '节点 四川',
`cluster_name` varchar(100) NOT NULL COMMENT '集群名称',
`component_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组件名称',
`host_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '宿主机名称',
`label` varchar(100) NOT NULL COMMENT '告警标签',
`service_name` varchar(50) DEFAULT NULL COMMENT '服务名称',
`state` varchar(20) DEFAULT NULL COMMENT '状态',
`text` text COMMENT '告警详细信息',
`timestamp` datetime NOT NULL COMMENT '告警时间',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='历史告警信息';


-- `maintain-2.0`.bd_host definition

CREATE TABLE `bd_host` (
 `cluster_name` varchar(100) NOT NULL COMMENT '集群名称',
 `node` varchar(20) NOT NULL COMMENT '节点 四川',
 `disk_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '磁盘信息 json',
 `host_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
 `host_state` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'host运行状态 INIT,WAITING_FOR_HOST_STATUS_UPDATES,HEALTHY,HEARTBEAT_LOST,UNHEALTHY',
 `host_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'host健康状态(agent上报的) HEALTHY,UNHEALTHY',
 `ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ip',
 `os_arch` varchar(20) DEFAULT NULL COMMENT '架构',
 `host_os_family` varchar(20) DEFAULT NULL COMMENT '操作系统家族',
 `os_type` varchar(20) DEFAULT NULL COMMENT '操作系统',
 `public_host_name` varchar(20) DEFAULT NULL COMMENT 'hostname',
 `rack_info` varchar(20) DEFAULT NULL COMMENT '机架信息',
 `total_mem` int DEFAULT NULL COMMENT '总内存 kb',
 `alerts_summary` text COMMENT '告警统计 json',
 `host_components` text COMMENT '主机对应组件 json',
 `cpu_count` int DEFAULT NULL COMMENT 'cpu总数',
 `ph_cpu_count` int DEFAULT NULL COMMENT '物理cpu总数',
 `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 UNIQUE KEY `idx_host_name` (`node`,`cluster_name`,`host_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='宿主机信息';


-- `maintain-2.0`.bd_service definition

CREATE TABLE `bd_service` (
  `node` varchar(20) NOT NULL COMMENT '节点 四川',
  `service_name` varchar(100) NOT NULL COMMENT '服务名称',
  `service_type` varchar(50) DEFAULT NULL COMMENT '服务类型 计算/存储',
  `cluster_name` varchar(20) DEFAULT NULL COMMENT '集群名称',
  `state` varchar(20) DEFAULT NULL COMMENT '服务状态',
  `alerts_summary` text COMMENT '告警统计 json',
  `components` text COMMENT '服务对应组件 json',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `idx_cluster_service` (`node`,`cluster_name`,`service_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务信息';