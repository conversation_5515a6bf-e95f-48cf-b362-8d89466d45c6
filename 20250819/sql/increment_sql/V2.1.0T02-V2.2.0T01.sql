

-- ----------------------------
-- Table structure for webmalware_monitor
-- ----------------------------
DROP TABLE IF EXISTS `webmalware_monitor`;
CREATE TABLE `webmalware_monitor` (
  `id` bigint(100) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `monitor_time` datetime DEFAULT NULL COMMENT '监控时间',
  `suspicious_num` bigint(100) DEFAULT '0' COMMENT '可疑数量',
  `danger_num` bigint(100) DEFAULT '0' COMMENT '危险数量',
  `high_risk_num` bigint(100) DEFAULT '0' COMMENT '高危数量',
  `feature_num` bigint(100) DEFAULT '0' COMMENT '特征检测数量',
  `static_num` bigint(100) DEFAULT '0' COMMENT '静态检测数量',
  `dynamic_num` bigint(100) DEFAULT '0' COMMENT '动态检测数量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3068 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- ----------------------------
-- Table structure for `mysql_backup_info`
-- ----------------------------
DROP TABLE IF EXISTS `mysql_backup_info`;
CREATE TABLE `mysql_backup_info` (
  `id` bigint(100) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `backup_time` datetime DEFAULT NULL COMMENT '备份时间',
  `backup_type` int(100) DEFAULT NULL COMMENT '备份类型',
  `backup_databases` varchar(255) DEFAULT NULL COMMENT '备份数据库',
  `success` tinyint(100) DEFAULT NULL COMMENT '是否备份成功；0-失败，1，成功',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8;


ALTER TABLE `front_device` ADD `EX_NUMBER` int(255) DEFAULT '0' COMMENT '探针前端异常数量';


ALTER TABLE `front_device_count` ADD `name` varchar(255) DEFAULT '' COMMENT '节点名称';


INSERT INTO `role_permission` VALUES ('631', '2', '82');
INSERT INTO `role_permission` VALUES ('632', '2', '83');
INSERT INTO `role_permission` VALUES ('633', '2', '84');

UPDATE `user` set pswd='$2a$10$unFUmcYcoVov8VHnR1y9Fu9k214dgwsavhEjJAZizds/umAN1kfoC' WHERE id=1;

TRUNCATE TABLE `permission`;

ALTER TABLE `permission` ADD `pathUrl` varchar(100) NOT NULL COMMENT '登录后页面跳转用的url';

INSERT INTO `permission` VALUES ('1', '用户管理', '/user', null, '10', '/userManage');
INSERT INTO `permission` VALUES ('2', '添加用户', '/user/registry', '1', null, '');
INSERT INTO `permission` VALUES ('3', '注销用户', '/user/cancel', '1', null, '');
INSERT INTO `permission` VALUES ('4', '初始化密码', '/user/initPassword', '1', null, '');
INSERT INTO `permission` VALUES ('5', '用户列表', '/user/userList.json', '1', null, '');
INSERT INTO `permission` VALUES ('6', '角色列表', '/user/roleList.json', '1', null, '');
INSERT INTO `permission` VALUES ('7', '分配角色', '/user/assignRoles', '1', null, '');
INSERT INTO `permission` VALUES ('8', '分配权限', '/user/assignPermissions', '1', null, '');
INSERT INTO `permission` VALUES ('9', 'Agent管理', '/agent', null, '4', '/agentManage');
INSERT INTO `permission` VALUES ('10', 'Agent列表', '/agent/list.json', '9', null, '');
INSERT INTO `permission` VALUES ('11', '角色列表', '/agent/roles.json', '9', null, '');
INSERT INTO `permission` VALUES ('12', '添加Agent', '/agentWebsocket/4', '9', null, '');
INSERT INTO `permission` VALUES ('15', '权限列表', '/user/permissionList.json', '1', null, '');
INSERT INTO `permission` VALUES ('16', '服务器管理', '/server', null, '2', '/hardWare');
INSERT INTO `permission` VALUES ('17', '程序管理', '/program', null, '3', '/procedureManage');
INSERT INTO `permission` VALUES ('18', '集群管理', '/colony', null, '5', '/colonyManage');
INSERT INTO `permission` VALUES ('19', '前端管理', '/front-device', null, '7', '/frontManage/probeStatus');
INSERT INTO `permission` VALUES ('20', '审计管理', '/audit', null, '9', '/auditManage');
INSERT INTO `permission` VALUES ('21', '系统首页', '/index', null, null, '/index');
INSERT INTO `permission` VALUES ('22', '服务器列表', '/hardware/list.json', '16', null, '');
INSERT INTO `permission` VALUES ('23', '服务器详情', '/hardware/detail_*.json', '16', null, '');
INSERT INTO `permission` VALUES ('26', '程序列表', '/software/list.json', '17', null, '');
INSERT INTO `permission` VALUES ('27', '程序配置文件列表', '/software/configs', '17', null, '');
INSERT INTO `permission` VALUES ('28', '下载程序日志', '/software/log', '17', null, '');
INSERT INTO `permission` VALUES ('29', '读取程序配置文件', '/software/config', '17', null, '');
INSERT INTO `permission` VALUES ('30', '修改程序配置文件', '/software/config', '17', null, '');
INSERT INTO `permission` VALUES ('31', '程序日志列表', '/software/logs', '17', null, '');
INSERT INTO `permission` VALUES ('32', '一键部署', '/softwareWebsocket/4', '17', null, '');
INSERT INTO `permission` VALUES ('33', '继续部署', '/software/deploy_task.json', '17', null, '');
INSERT INTO `permission` VALUES ('34', '前端列表', '/front-device/list.json', '19', null, '');
INSERT INTO `permission` VALUES ('35', '前端详情', '/front-device/detail.json', '19', null, '');
INSERT INTO `permission` VALUES ('38', '审计列表', '/operate/record.json', '20', null, '');
INSERT INTO `permission` VALUES ('39', '日志管理', '/log', null, '6', '/logAnalyze');
INSERT INTO `permission` VALUES ('40', '业务管理', '/business', null, '8', '/businessManage');
INSERT INTO `permission` VALUES ('41', '操作程序', '/softwareWebsocket/0,/softwareWebsocket/1', '17', null, '');
INSERT INTO `permission` VALUES ('44', '操作Agent', '/agentWebsocket/1,/agentWebsocket/0', '9', null, '');
INSERT INTO `permission` VALUES ('46', '更新Agent', '/agentWebsocket/3', '9', null, '');
INSERT INTO `permission` VALUES ('47', '开启防火墙', '/hardwareWebsocket/1', '16', null, '');
INSERT INTO `permission` VALUES ('48', '添加程序', '/software/add_software', '17', null, '');
INSERT INTO `permission` VALUES ('49', '编辑程序', '/software/update_soft', '17', null, '');
INSERT INTO `permission` VALUES ('50', '删除程序', '/software/delete_soft', '17', null, '');
INSERT INTO `permission` VALUES ('51', '重启次数置零', '/software/clear_restart_count', '17', null, '');
INSERT INTO `permission` VALUES ('52', '动态分析列表', '/dynamicAndStatic/list.json', '40', null, '');
INSERT INTO `permission` VALUES ('53', '设置标准分析量', '/dynamicAndStatic/addThreshold', '40', null, '');
INSERT INTO `permission` VALUES ('54', '静态分析列表', '/dynamicAndStatic/list.json', '40', null, '');
INSERT INTO `permission` VALUES ('55', '磁盘信息', '/preprocessDiskMonitor/list.json', '40', null, '');
INSERT INTO `permission` VALUES ('56', '索引信息', '/preprocessDiskMonitor/index.json', '40', null, '');
INSERT INTO `permission` VALUES ('58', 'Codis队列信息', '/dynamicAndStatic/getTaskQueueLen', '18', null, '');
INSERT INTO `permission` VALUES ('59', '任务管理', '/api/_tasks', '18', null, '');
INSERT INTO `permission` VALUES ('60', 'CDH管理监控详情', '/plugin/plugin_business/CdhPlugin', '18', null, '');
INSERT INTO `permission` VALUES ('61', 'CDH告警信息', '/plugin/plugin_business/*Plugin/get*', '18', null, '');
INSERT INTO `permission` VALUES ('64', 'CDH管理详情', '', '18', null, '');
INSERT INTO `permission` VALUES ('65', 'Codis管理详情', '', '18', null, '');
INSERT INTO `permission` VALUES ('66', 'kafka监控详情', '/plugin/plugin_business/KafkaPlugin', '18', null, '');
INSERT INTO `permission` VALUES ('67', 'ES管理监控详情', '/plugin/plugin_business/EsPlugin', '18', null, '');
INSERT INTO `permission` VALUES ('68', '日志监控列表', '', '39', null, '');
INSERT INTO `permission` VALUES ('69', '程序详情', '/software/detail_*.json', '17', null, '');
INSERT INTO `permission` VALUES ('70', '心跳监控', '/software/heart_monitor', '17', null, '');
INSERT INTO `permission` VALUES ('71', '重启程序', '/softwareWebsocket/2', '17', null, '');
INSERT INTO `permission` VALUES ('72', 'ES管理详情', '', '18', null, '');
INSERT INTO `permission` VALUES ('73', '修改关闭索引状态', '/plugin/plugin_business/EsPlugin/closeIndexPower', '18', null, '');
INSERT INTO `permission` VALUES ('74', '修改删除索引状态', '/plugin/plugin_business/EsPlugin/deleteIndexPower', '18', null, '');
INSERT INTO `permission` VALUES ('75', '添加磁盘', '/preprocessDiskMonitor/addPreprocessDir', '40', null, '');
INSERT INTO `permission` VALUES ('76', '分发logstash', '/softwareWebsocket/5', '17', null, '');
INSERT INTO `permission` VALUES ('77', '更新logstash配置文件', '/softwareWebsocket/6', '17', null, '');
INSERT INTO `permission` VALUES ('78', '报告管理', '/report', null, '11', '/reportManage');
INSERT INTO `permission` VALUES ('79', '下载报告', '/report/down', '78', null, '');
INSERT INTO `permission` VALUES ('80', '生成报告', '/home/<USER>', '78', null, '');
INSERT INTO `permission` VALUES ('81', '导入Agent', '/agent/import', '9', null, '');
INSERT INTO `permission` VALUES ('82', '一键时间同步', '/agentWebsocket/7', '9', null, '');
INSERT INTO `permission` VALUES ('83', '跳转探针', '/frontDeviceWebsocket/8', '19', null, '');
INSERT INTO `permission` VALUES ('84', '一键升级', '/front-device/getFrontPage', '19', null, '');
