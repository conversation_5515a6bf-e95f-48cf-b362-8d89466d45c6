
-- ----------------------------
-- Records of index_monitor
-- ----------------------------

TRUNCATE TABLE `index_monitor`;

INSERT INTO `index_monitor` VALUES ('1', '木马邮件检测系统-木马邮件', 'inf-eml-risk', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('2', '木马邮件检测系统-木马附件', 'inf-attachment-risk', '@createtime', null, null, 'multi-task-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('3', '恶意码址检测系统-黑域名检测', 'inf-black-domain', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('4', '恶意码址检测系统-黑IP检测', 'inf-black-ip', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('5', '恶意码址检测系统-黑链接检测', 'inf-black-http', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('6', '恶意码址检测系统-黑邮箱检测-黑邮箱登录', 'inf-black-emlbox-log', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('7', '恶意码址检测系统-黑邮箱检测-黑邮箱往来', 'inf-black-emlbox-eml', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('8', '恶意码址检测系统-木马通信检测', 'inf-sign-net', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('9', '恶意码址检测系统-HTTP上线检测', 'inf-sign-http', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('10', '恶意码址检测系统-黑样本检测-邮件附件', 'inf-black-attachment', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('11', '恶意码址检测系统-黑样本检测-文件伪造', 'inf-black-http-abnormal-file', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('12', '恶意码址检测系统-黑样本检测-HTTP木马', 'inf-black-http-alarm', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('13', '恶意码址检测系统-黑样本检测-ELF文件传输', 'inf-black-http-elf-file', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('14', '恶意码址检测系统-黑样本检测-TCP下载文件', 'inf-black-tcp-file', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('15', 'HTTP木马检测系统-HTTP木马检测', 'inf-http-alarm-risk', '@createtime', null, null, 'multi-task-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('16', '文件伪造检测', 'inf-http-abnormal-file', '@createtime', null, null, 'multi-task-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('17', '网络劫持检测系统-DNS劫持检测', 'inf-dns-hack-risk', 'updatetime', null, null, 'dnsrobber-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('18', '网络劫持检测系统-DNS劫持记录', 'inf-dns-hack', '@createtime', null, null, 'PcapDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('19', '远程控制检测系统-远程控制检测', 'inf-remote-mining', 'detecttime', null, null, 'remote-ctr-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('20', '远程控制检测系统-数据库检测', 'inf-basicdb-mining', 'detecttime', null, null, 'remote-ctr-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('21', '远程控制检测系统-远程控制记录', 'inf-remote', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('22', '远程控制检测系统-数据库远程登录', 'inf-basic-db', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('23', '网络入侵检测系统-网络入侵检测', 'inf-snort', '@createtime', null, null, 'PcapDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('24', '网络入侵检测系统-HTTP入侵检测', 'inf-http-snort', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('25', '异常通信检测系统-明文SHELL检测', 'inf-shell-cmd', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('26', '异常通信检测系统-HTTPSHELL检测', 'inf-http-shell-cmd', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('27', '异常通信检测系统-DNS隧道检测', 'inf-dns-scan', '@createtime', null, null, 'dns-tunnel-detect-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('28', '异常通信检测系统-ELF文件传输', 'inf-http-elf-file', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('29', '异常通信检测系统-TCP下载', 'inf-tcp-file', '@createtime', null, null, 'multi-task-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('30', 'SSL证书检测系统-可疑证书检测', 'inf-ssl-detect', 'detecttime', null, null, 'certificate-mining-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('31', 'SSL证书检测系统-证书查证', 'inf-ssl-certificate', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('32', '网页挂马检测系统-威胁检测', 'inf-http-webmalware', 'detecttime', null, null, 'web-malware-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('33', '受控邮箱检测系统-地域切换', 'inf-emlbox-dyqh', 'detecttime', null, null, 'ctred-mailbox-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('34', '受控邮箱检测系统-暴力破解', 'inf-emlbox-blpj', 'detecttime', null, null, 'ctred-mailbox-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('35', '受控邮箱检测系统-异常邮箱', 'inf-emlbox-ycyx', 'detecttime', null, null, 'ctred-mailbox-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('36', '受控邮箱检测系统-代收邮件', 'inf-emlbox-dsyj', 'detecttime', null, null, 'ctred-mailbox-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('37', '受控邮箱检测系统-登录异常', 'inf-emlbox-dlyc', 'detecttime', null, null, 'ctred-mailbox-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('38', '受控邮箱检测系统-加密收邮检测', 'inf-emlbox-sslmail', 'detecttime', null, null, 'ctred-mailbox-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('39', '受控邮箱检测系统-转发检测-转发设置检测', 'inf-eml-box-forward', 'detecttime', null, null, 'RecordDataImport', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('40', '受控邮箱检测系统-转发检测-转发邮件检测', 'inf-emlbox-zfyc', 'detecttime', null, null, 'ctred-mailbox-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('41', '钓鱼邮件检测系统-钓鱼邮件', 'inf-fishingmail-detectresults', 'detecttime', null, null, 'phishingmail-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('42', '钓鱼邮件检测系统-跨站邮件', 'inf-eml', '@createtime', 'iskzgj=1', null, 'MailDataImport', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('43', 'WEB渗透检测系统-威胁检测', 'inf-http-attack', 'detecttime', null, null, '	web-att-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('44', 'WEB渗透检测系统-可疑页面检测', 'inf-http-webshell', '@createtime', null, null, 'web-att-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('45', 'WEB渗透检测系统-二阶跟踪', 'inf-http-black-host', '@createtime', null, null, '	web-att-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('46', 'BS木马检测系统-HTTP检测', 'inf-bs-detect', '@createtime', null, null, 'bs-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('47', 'BS木马检测系统-DNS检测', 'inf-bs-detect-dns', '@createtime', null, null, 'bs-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('48', '网关设备检测系统-路由流量检测', 'inf-router-dataflow-mining', '@createtime', null, null, 'webdevice-flow-mining-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('49', '网关设备检测系统-路由流量识别', 'inf-router-dataflow', '@createtime', null, null, 'PcapDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('50', '云控威胁追踪系统-邮件外链', 'inf-eml-clouddetection', 'updatetime', null, null, 'threattrack-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('51', '云控威胁追踪系统-二阶样本', 'inf-html-clouddetection', 'updatetime', null, null, 'threattrack-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('52', '风险域名检测系统-DGA域名检测', 'inf-dga-static', 'detecttime', null, null, 'susdomain-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('53', '风险域名检测系统-可疑域名记录', 'inf-dga-dynamic', 'detecttime', null, null, 'susdomain-mining-service', '1', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('54', '重点资产告警系统-资产告警', 'inf-server-alarm', 'detecttime', null, null, 'asset-mining-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('55', '重点资产告警系统-资产肖像刻画', 'inf-server-mining', 'detecttime', null, null, 'asset-mining-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('56', '线索查证系统-IP会话', 'ip-*', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('57', '线索查证系统-DNS解析', 'dns-*', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('58', '线索查证系统-HTTP访问', 'http-*', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('60', '邮件查证系统-信息查证-详情', 'inf-eml', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('61', '邮件查证系统-信息查证-详情', 'inf-attachment', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('62', '邮件查证系统-信息查证-详情', 'inf-eml-clouddetection', 'updatetime', null, null, null, '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('63', '邮件查证系统-信息查证-详情', 'inf-maillogin', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('64', '邮件查证系统-邮件信息-邮件列表', 'inf-eml', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('65', '邮件查证系统-邮件信息-附件列表', 'inf-attachment', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('66', '邮件查证系统-邮件信息-外链列表', 'inf-eml-clouddetection', 'updatetime', null, null, 'Threat-TrackMining-Service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('67', '邮件查证系统-邮箱分析-发件箱', 'inf-eml', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('68', '邮件查证系统-邮箱分析-收件箱', 'inf-eml', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('69', '邮件查证系统-邮箱分析-密送账号', 'inf-eml', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('70', '邮件查证系统-邮箱分析-转发账号', 'inf-eml-box-forward', 'detecttime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('71', '邮件查证系统-邮箱分析-联系人', 'inf-eml-box-contact', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('72', '邮件查证系统-邮箱分析-最近登录信息', 'inf-maillogin', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('73', '邮件查证系统-邮箱日志', 'inf-maillogin', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('74', '行为查证-口令破解', 'inf-password', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('75', '行为查证-破网工具-使用记录', 'inf-agent', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('76', '行为查证-破网工具-VPN记录', 'inf-vpn', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('77', '行为查证-wifi手机信息-wifi手机列表', 'inf-wifi-phone-analysis', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('78', '行为查证-wifi手机信息-wifi手机分析', 'inf-wifi-phone', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('79', '行为查证-即时通信-上线记录', 'inf-im-login', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('80', '行为查证-即时通信-文件传输', 'inf-im-file', '@createtime', null, null, 'PcapDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('81', '行为查证-文件共享-SMB登录记录', 'inf-basic-samba-login', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('82', '行为查证-文件共享-SMB文件传输', 'inf-basic-samba-file', '@createtime', null, null, 'PcapDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('83', '行为查证-文件共享-SMB访问记录', 'inf-basic-samba-flow', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('84', 'HTTP文件查证系统-HTTP木马查证', 'inf-http-alarm', '@createtime', null, null, 'PcapDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('85', 'FTP信息查证系统-FTP登录信息', 'inf-basic-ftp-login', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('86', 'FTP信息查证系统-FTP文件传输信息', 'inf-basic-ftp-file', '@createtime', null, null, 'PcapDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('87', '文件深度分析-文件列表', 'inf-upload-file', '@createtime', null, null, 'file-upload-service', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('88', '网络阻断系统-统计分析-设备统计', 'inf-network-block-report', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('89', '网络阻断系统-阻断报告', 'inf-network-block-report', '@createtime', null, null, 'RecordDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('90', '综合设置系统-邮件过滤-邮件过滤记录', 'inf-filter-eml-log', '@createtime', null, null, 'MailDataImport', '0', null, '0', null, null,0);
INSERT INTO `index_monitor` VALUES ('91', 'SSl证书监测系统-异常流量', 'inf-ssl-stat', 'detecttime', null, null, 'certificate-mining-service', '0', null, '0', null, null,0);