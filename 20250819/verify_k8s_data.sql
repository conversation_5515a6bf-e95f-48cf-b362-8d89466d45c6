-- K8s监控数据验证SQL脚本
-- 作者：xiong
-- 日期：2025-01-19

-- 1. 查询所有K8s Pod数据
SELECT 
    id,
    name,
    server_ip,
    status,
    program_status,
    process_count,
    cpu_percent,
    memory_percent,
    used_memory,
    create_time,
    last_heartbeat_time
FROM software_info 
WHERE version = 'k8s-pod'
ORDER BY create_time DESC;

-- 2. 统计K8s Pod数量和状态分布
SELECT 
    COUNT(*) as total_pods,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as green_count,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as yellow_count,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as red_count,
    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as gray_count
FROM software_info 
WHERE version = 'k8s-pod';

-- 3. 查询最近1小时采集的K8s数据
SELECT 
    name,
    server_ip,
    status,
    cpu_percent,
    used_memory,
    create_time
FROM software_info 
WHERE version = 'k8s-pod'
  AND create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY create_time DESC;

-- 4. 按节点IP分组统计Pod数量
SELECT 
    server_ip,
    COUNT(*) as pod_count,
    GROUP_CONCAT(name) as pod_names
FROM software_info 
WHERE version = 'k8s-pod'
GROUP BY server_ip
ORDER BY pod_count DESC;

-- 5. 查询资源使用情况
SELECT 
    name,
    server_ip,
    cpu_percent,
    memory_percent,
    used_memory,
    process_count as container_count
FROM software_info 
WHERE version = 'k8s-pod'
  AND cpu_percent IS NOT NULL
  AND memory_percent IS NOT NULL
ORDER BY CAST(REPLACE(cpu_percent, '%', '') AS DECIMAL(5,2)) DESC;

-- 6. 检查数据完整性
SELECT 
    'Total Records' as check_item,
    COUNT(*) as count
FROM software_info 
WHERE version = 'k8s-pod'

UNION ALL

SELECT 
    'Records with CPU data' as check_item,
    COUNT(*) as count
FROM software_info 
WHERE version = 'k8s-pod' AND cpu_percent IS NOT NULL

UNION ALL

SELECT 
    'Records with Memory data' as check_item,
    COUNT(*) as count
FROM software_info 
WHERE version = 'k8s-pod' AND used_memory IS NOT NULL

UNION ALL

SELECT 
    'Records created today' as check_item,
    COUNT(*) as count
FROM software_info 
WHERE version = 'k8s-pod' AND DATE(create_time) = CURDATE();

-- 7. 清理测试数据（谨慎使用）
-- DELETE FROM software_info WHERE version = 'k8s-pod';

-- 8. 查看最新的采集日志时间
SELECT 
    MAX(create_time) as last_collection_time,
    COUNT(*) as total_records,
    TIMESTAMPDIFF(MINUTE, MAX(create_time), NOW()) as minutes_ago
FROM software_info 
WHERE version = 'k8s-pod';