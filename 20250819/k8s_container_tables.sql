-- K8s容器信息表
CREATE TABLE `k8s_container_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `container_name` varchar(200) NOT NULL COMMENT '容器名称',
  `pod_name` varchar(200) NOT NULL COMMENT 'Pod名称',
  `namespace` varchar(100) NOT NULL DEFAULT 'default' COMMENT '命名空间',
  `node_name` varchar(200) DEFAULT NULL COMMENT '节点名称',
  `image` varchar(500) DEFAULT NULL COMMENT '镜像名称',
  `image_tag` varchar(100) DEFAULT NULL COMMENT '镜像标签',
  `pod_ip` varchar(50) DEFAULT NULL COMMENT 'Pod IP',
  `host_ip` varchar(50) DEFAULT NULL COMMENT '宿主机IP',
  `ports` varchar(200) DEFAULT NULL COMMENT '端口信息',
  `labels` text COMMENT '标签信息JSON',
  `status` varchar(50) DEFAULT NULL COMMENT '容器状态',
  `restart_count` int(11) DEFAULT 0 COMMENT '重启次数',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `start_time` datetime DEFAULT NULL COMMENT '启动时间',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否活跃(1:是 0:否)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_container` (`namespace`,`pod_name`,`container_name`),
  KEY `idx_namespace` (`namespace`),
  KEY `idx_pod_name` (`pod_name`),
  KEY `idx_host_ip` (`host_ip`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K8s容器信息表';

-- K8s容器监控数据表
CREATE TABLE `k8s_container_metrics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `container_id` bigint(20) NOT NULL COMMENT '容器ID(关联k8s_container_info.id)',
  `cpu_usage` decimal(10,2) DEFAULT NULL COMMENT 'CPU使用率(%)',
  `memory_usage` bigint(20) DEFAULT NULL COMMENT '内存使用量(bytes)',
  `memory_usage_percent` decimal(10,2) DEFAULT NULL COMMENT '内存使用率(%)',
  `network_rx_bytes` bigint(20) DEFAULT NULL COMMENT '网络接收字节数',
  `network_tx_bytes` bigint(20) DEFAULT NULL COMMENT '网络发送字节数',
  `disk_usage` bigint(20) DEFAULT NULL COMMENT '磁盘使用量(bytes)',
  `disk_usage_percent` decimal(10,2) DEFAULT NULL COMMENT '磁盘使用率(%)',
  `collect_time` datetime NOT NULL COMMENT '采集时间',
  PRIMARY KEY (`id`),
  KEY `idx_container_id` (`container_id`),
  KEY `idx_collect_time` (`collect_time`),
  CONSTRAINT `fk_container_metrics` FOREIGN KEY (`container_id`) REFERENCES `k8s_container_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K8s容器监控数据表';

-- K8s容器日志表
CREATE TABLE `k8s_container_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `container_id` bigint(20) NOT NULL COMMENT '容器ID',
  `log_content` text COMMENT '日志内容',
  `log_level` varchar(20) DEFAULT NULL COMMENT '日志级别',
  `log_time` datetime NOT NULL COMMENT '日志时间',
  `collect_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '采集时间',
  PRIMARY KEY (`id`),
  KEY `idx_container_id` (`container_id`),
  KEY `idx_log_time` (`log_time`),
  CONSTRAINT `fk_container_logs` FOREIGN KEY (`container_id`) REFERENCES `k8s_container_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K8s容器日志表';

-- 创建索引优化查询性能
CREATE INDEX idx_metrics_container_time ON k8s_container_metrics(container_id, collect_time);
CREATE INDEX idx_logs_container_time ON k8s_container_logs(container_id, log_time);
