/**
 * Created by ji<PERSON>n<PERSON> on 16/11/18.
 */

export function isvalidUsername(str) {
    const valid_map = ['admin', 'editor']
    return valid_map.indexOf(str.trim()) >= 0
}

/* 合法uri*/
export function validateURL(textval) {
    const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
    return urlregex.test(textval)
}

/* 小写字母*/
export function validateLowerCase(str) {
    const reg = /^[a-z]+$/
    return reg.test(str)
}

/* 大写字母*/
export function validateUpperCase(str) {
    const reg = /^[A-Z]+$/
    return reg.test(str)
}

/* 大小写字母*/
export function validatAlphabets(str) {
    const reg = /^[A-Za-z]+$/
    return reg.test(str)
}
/**
 * 判断是否为空
 */
export function validatenull(val) {
    if (typeof val === 'boolean') {
        return false
    }
    if (val instanceof Array) {
        if (val.length === 0) return true
    } else if (val instanceof Object) {
        if (JSON.stringify(val) === '{}') return true
    } else {
        if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') return true
        return false
    }
    return false
}

/**
 * 判断手机号码是否正确
 */
export function isvalidatemobile(phone) {
    const list = []
    let result = true
    let msg = ''
    var isPhone = /^0\d{2,3}-?\d{7,8}$/
    // 增加134 减少|1349[0-9]{7}，增加181,增加145，增加17[678]
    // const isMob = /^((\+?86)|(\(\+86\)))?(13[0123456789][0-9]{8}|15[012356789][0-9]{8}|18[012356789][0-9]{8}|14[57][0-9]{8}|17[3678][0-9]{8})$/
    if (!validatenull(phone)) {
        if (phone.length === 11) {
            if (isPhone.test(phone)) {
                msg = '手机号码格式不正确'
            } else {
                result = false
            }
        } else {
            msg = '手机号码长度不为11位'
        }
    } else {
        msg = '手机号码不能为空'
    }
    list.push(result)
    list.push(msg)
    return list
}

/**
* 判断是否为JSON对象
*/
export function isJson(str) {
    if (typeof str == 'string') {
        try {
            let obj = JSON.parse(str);
            if (typeof obj == 'object' && obj) {
                return true;
            } else {
                return false;
            }
        } catch (e) {
            return false;
        }
    }
}

/**
* 判断IP地址是否正确
*/
export function checkIPNotEnablePort(str) {
    if (str == undefined || str == null || typeof str != 'string' || str.length == 0) {
        return false;
    }
    str = str.trim();
    if (str.indexOf(' ') > -1) {
        return false;
    }
    if (str.indexOf(':') > -1) {
        //str = str.split(":")[0];
        return false;
    }
    if (str.indexOf('.') > -1) {
        if (str[str.length - 1] == '.') { return false; }
        str = str.trim();
        var ips = str.split('.');
        if (ips.length == 4) {
            for (var i in ips) {
                if (ips.hasOwnProperty(i)) {
                    if (isNaN(ips[i]) || ips[i] == undefined || ips[i] == null || ips[i] == '') {
                        return false;
                    } else {
                        if (ips[i].length > 1 && ips[i].charAt(0) === '0') {
                            return false;  // 大于1位的开头不可以是0  Modify by yangxuan
                        }
                        else {
                            var integerIp = parseInt(ips[i]);
                            if (integerIp < 0 || integerIp > 255) {
                                return false;
                            }
                        }

                    }
                }
            }
        } else {
            return false;
        }
        return true;
    }
    return false;
};

/**
* 正则验证字符串是否为域名
*/
export function checkDomain(str) {
    var regex = /^[0-9a-zA-Z_-]+(\\.[0-9a-zA-Z_-]+)*(\\.[a-zA-Z]{2,}\\.)$/;
    if (check(str, regex))
        return true;

    var regex = /^([0-9a-zA-Z][0-9a-zA-Z-]{0,62}\.)+([0-9a-zA-Z][0-9a-zA-Z-]{0,62})\.?$/;
    if (check(str, regex))
        return true;

    return false;
};

/**
* 正则验证字符串是否为端口
*/
export function checkPort(str) {
    var checkResult = str.match(/^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/);
    if (checkResult == null)
        return false;
    else
        return true;
};

/**
* 正则验证字符串是否为邮箱
*/
export function checkEmail(str) {
    var reg = /[\w!#$%&'*+/=?^`{|}~-]+(?:\.[\w!#$%&'*+/=?^`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/;
    if (reg.test(str))
        return true;
    // reg = /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/;
    //if (reg.test(str))
    //    return true;
    return false;
};

/**
* 正则验证字符串是否为URL
*/
export function checkURL(str) {
    if (str.match(/(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/g))
        return true;
    var reg = /\b(([\w-]+:\/\/?|www[.])[^\s()<>]+(?:\([\w\d]+\)|([^[:punct:]\\s]|\/)))/;
    if (reg.test(str)) return true;
    // reg = "[a-zA-z]+://[^\s]*"; 网站的
    reg = /^(((ht|f)tp(s?))\:\/\/)?(www.|[a-zA-Z].)[a-zA-Z0-9\-\.]+\.(com|edu|gov|mil|net|org|biz|info|name|museum|us|ca|uk)(\:[0-9]+)*(\/($|[a-zA-Z0-9\.\,\;\?\'\\\+&%\$#\=~_\-]+))*$/;
    if (reg.test(str)) return true;
    reg = /^((ht|f)tps?):\/\/[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:\/`\+#]*[\w\-\@?^=%&\/`\+#])?$/;
    if (reg.test(str)) return true;
    return false;
};

/**
* 验证字符串是否为md5
*/
export function checkMD5(str) {
    if (str.length == 32)
        return /^([A-Fa-f0-9]{32})$/.test(str);
    if (str.length == 16)
        return /^([A-Fa-f0-9]{16})$/.test(str);
    return false;
};

/**
* 判断是否为中文
*/
export function checkChinese(str) {
    const reg = /[\u4E00-\u9FA5]/g
    return !reg.test(str)
}