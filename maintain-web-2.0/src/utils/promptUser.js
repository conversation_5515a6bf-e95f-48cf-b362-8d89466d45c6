import Vue from 'vue'
import './request.js'
import { getStore } from './store.js'
import store from '@/store'

let api = new Vue();
const promptUser = () => {
    let userName = '',
        template = '';

    userName = store.getters.operationName || getStore({ name: 'operationName' });
    template = `<p>用户名为：${userName}，请输入该用户密码：</p>`;

    Vue.prototype.$prompt(template, '请输入用户信息验证', {
        dangerouslyUseHTMLString: true,
        cancelButtonText: '关闭',
        confirmButtonText: '确定',
        confirmButtonClass: 'confirm-success',
        customClass: 'userPrompt',
        inputType: 'password',
        inputPlaceholder: '请输入密码',
        beforeClose: (action, instance, done) => {

            if (action == 'cancel') {
                done();
                return
            }
            let data = {
                username: userName,
                password: instance.inputValue
            }
            Vue.prototype.$http.post('user/pass/confirm', Vue.prototype.qs.stringify(data)).then((res) => {
                if (res.data.code == 0) {
                    done();
                } else {
                    Vue.prototype.waFn('密码输入错误');
                }
            }).catch((error) => {
                console.log(error)
            })
        }
    }).then(() => {
        // Promise.resolve();
        // return new Promise.resolve()
    }).catch(() => {
        // return new Promise((resolve, reject) => {
        //     reject();
        // });
    })
}

export default promptUser