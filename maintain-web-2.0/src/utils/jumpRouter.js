
import Vue from 'vue'
import store from '@/store'
import { clearStore, setStore, getStore } from '@/utils/store.js'
import { setToken, TokenKey,AuthKey, getToken } from '@/utils/auth'
import router from '@/router'
let vue = new Vue();
router.beforeEach((to, from, next) => {
    const { token } = to.query;
    if (token) {
        vue.$http.get(`quickLogin?token=${token}`).then((res)=>{
            clearStore({});
            let data = res.data;
            if (data.msg == '登陆成功') {
                localStorage.setItem(AuthKey,token)
                localStorage.setItem(TokenKey, data.data.token)
                setStore({
                    name: 'operationName',
                    content: data.data.user.name, 
                })
                setStore({
                    name: 'menuOptions',
                    content: data.data.menuOptions,
                })
                setStore({
                    name: 'id',
                    content: data.data.user.id,
                })
                next(false);
                router.push(data.data.menuOptions[0].path || '/index');
            } else {
                data.msg ? vue.waFn(data.msg) : vue.erFn();
            }
        }).catch(()=>{
            vue.erFn();
            next({
                path: '/login',
            })
        })
    } else {
        if (to.path != '/login') {
            if (getStore({ name: 'operationName' })) {
                next()
            } else {
                clearStore({});
                store.commit('saveMenuOptions', []);
                clearInterval(store.getters.timer);
                next({
                    path: '/login',
                })
            }
        } else {
            next();
        }
    }
})
