import Vue from 'vue'
import './request.js'
import { getStore } from './store.js'
import store from '@/store'

let api = new Vue();
const getPermission = (name, btn) => {
    let data = {},
        btnGroup = [];

    btn = btn || {};
    data.name = name;
    data.username = store.getters.operationName || getStore({ name: 'operationName' });
    api.$http.post('user/permissionByModule', api.qs.stringify(data)).then((res) => {
        if (res.data.msg == '请先登录') {
            return
        } else {
            if (res.data.data) {
                btnGroup = res.data.data.children || []
                btnGroup.forEach(index => {
                    $.each(btn, (index1, item) => {
                        if (index.name == item.name) {
                            item.value = true;
                        }
                    })
                });
            }

        }
    }).catch((error) => {
        console.log(error)
    })
}

export default getPermission