import Cookies from 'js-cookie'

export const TokenKey = 'JSESSIONID'
export const AuthKey='Authorization'

export function getToken(key) {
    return Cookies.get(key)
}

export function setToken(key, token) {
    let option = {};
    if (location.protocol.includes('https')) {
        option = {
            sameSite: 'None',
            secure: true
        }
    }
    return Cookies.set(key, token, option)
}

export function removeToken(key) {
    return Cookies.remove(key)
}

