import Vue from 'vue'
import axios from 'axios'
import store from '@/store'
import { clearStore } from '@/utils/store.js'
import { MessageBox } from 'element-ui'
import { TokenKey,AuthKey } from '@/utils/auth'
import router from '@/router'
// import { getToken } from '@/utils/auth'

// 创建axios实例
const request = axios.create({
    baseURL: '/maintain/',
    timeout: 30000,
    withCredentials: true,
})

let msgShow = true;



// request拦截器
request.interceptors.request.use(config => {
    config.headers['sessionid'] = localStorage.getItem(TokenKey)
    config.headers['Authorization'] = localStorage.getItem(AuthKey)
    return config
}, error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
})
// respone拦截器
request.interceptors.response.use(
    response => {
        const res = response.data;
        if (res.code !== 0) {
            if (msgShow) {
                if (res.msg === '请先登录') {
                    clearStore({})
                    store.commit('saveMenuOptions', []);
                    clearInterval(store.getters.timer);
                    router.push({
                        path: '/login',
                    })
                }
            }
            return Promise.reject(response)
        } else {
            return response
        }
    },
    error => {
        console.log('err' + error)// for debug
        if ((error.code == 'ECONNABORTED' && error.message.indexOf('timeout') != -1) || (error.request.readyState == 4 && error.request.status == 0)) {
            clearInterval(store.getters.timer);
            store.commit('saveTimer', null);
        }
        return Promise.reject(error)
    }
)


//添加定时请求
// let this = new Vue();
// let api = new Vue({store});
// export function requestInterval(methods) {
//   if(timer){
//     clearInterval(timer);
//   } else {
//     console.log(methods)
//       timer = setInterval(() => {
//         methods.forEach(item => {
//           console.log(item);
//           item();
//         });
//       },10000)
//   } 
// }

Vue.prototype.$http = request
