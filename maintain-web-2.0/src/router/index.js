import Vue from 'vue'
import Router from 'vue-router'
import Home from '@/views/home//Home'
import Login from '@/components/Login'

// 首页
import Index from '@/views/home/<USER>'
// Agent管理
import AgentManage from '@/views/agentManage/agentManage'
//用户管理
import UserManage from '@/views/userManage/userManage'
//接口调用
import requestManage from '@/views/userManage/requestManage'
//分配权限
import assignPermissions from '@/views/userManage/assignPermissions'
//集群管理
import ColonyManage from '@/views/colonyManage/colonyManage'
//cdh监控详情
import cdhDetail from '@/views/colonyManage/cdhManage/cdhDetail'
//es监控详情
import esManage from '@/views/colonyManage/esManage/esDetail'
//kafka监控详情
import kafkaDetail from '@/views/colonyManage/cdhManage/kafkaDetail'
//前端管理
import FrontManage from '@/views/frontManage/frontManage'
//前端详情
import FrontDetail from '@/views/frontManage/listDetail'
//探针状态
import probeStatus from '@/views/frontManage/probeStatus'
//探针状态详情
import probeDetail from '@/views/frontManage/probeDetail'
//服务器管理
import hardWare from '@/views/hardware/hardWare'
//服务器详情
import hardWareDetail from '@/views/hardware/hardWareDetail'
//程序管理
import procedureManage from '@/views/procedureManage/procedureManage'
//程序详情
import procedureDetail from '@/views/procedureManage/procedureDetail'
//审计管理
import auditManage from '@/views/auditManage/auditManage'
//日志管理
import logAnalyze from '@/views/logStash/logAnalyze'
//业务管理
import businessManage from '@/views/businessManage/businessManage'
//报告管理
import reportManage from '@/views/reportManage/reportManage'
//task测试
import task from '@/views/colonyManage/esManage/task'
//cdh跳转表单
import cdhHref from '@/views/colonyManage/cdhHref'
//版本更新日志说明
import versionDetail from '@/views/versionDetail/versionDetail'

Vue.use(Router)

//export 输出指定名称的变量（function 或者object）,import时必须对应输出的变量名称
//输出：export function test() { }  导入：import { test as '改名字'}  from  test(输出时的文件名)

//export default 输出任意名称的变量,import时不用对应输出的变量名称。
//export default 后面不能再使用任何变量声明语句(var function 等),default本身就是一个变量
export default new Router({
  mode: 'history',
  routes: [
    {
      path:'/',
      redirect:'index'
    },
    {
      path:'/login',
      name:'login',
      component:Login,
    },
    {
      path: '/home',
      name: 'home',
      component: Home,
      children:[
        {
          path:'/index',
          name:'系统首页',
          component:Index,
          meta:{
            requireAuth:true, //进入这个路由是需要登陆的
          },
        },
        {
          path:'/agentManage',
          name:'Agent管理',
          component:AgentManage,
        },
        {
          path:'/userManage',
          name:'用户管理',
          component:UserManage
        },
        {
          path:'/requestManage',
          name:'接口调用',
          component:requestManage
        },
        {
          path:'/assignPermissions',
          name:'分配权限',
          component:assignPermissions
        },
        {
          path:'/colonyManage',
          name:'集群管理',
          component:ColonyManage,
          meta: {
            keepAlive:true
          }
        },
        {
          path: '/cdhDetail',
          name: 'CDH监控详情',
          component: cdhDetail
        },
        {
          path: '/kafkaDetail',
          name: 'kafka监控详情',
          component: kafkaDetail
        },
        {
          path: '/esManage',
          name: 'ES监控详情',
          component: esManage
        },
        {
          path:'/frontManage',
          name:'前端管理',
          component:FrontManage,
        },
        {
            path:'/frontManage/probeStatus',
            name:'探针状态',
            component:probeStatus
        },
        {
            path:'/frontManage/probeDetail',
            name:'探针状态详情',
            component:probeDetail
        },
        {
          path:'/frontDetail',
          name:'前端详情',
          component:FrontDetail
        },
        {
          path:'/hardWare',
          name:'服务器管理',
          component:hardWare,
        },
        {
          path:'/hardWareDetail',
          name:'服务器详情',
          component:hardWareDetail
        },
        {
          path:'/procedureManage',
          name:'程序管理',
          component:procedureManage,
          meta: {
            keepAlive:true,
            scrollTop:0
          }
        },
        {
          path:'/procedureDetail',
          name:'程序详情',
          component:procedureDetail
        },
        {
          path:'/auditManage',
          name:'审计管理',
          component:auditManage
        },
        {
          path:'/logAnalyze',
          name:'日志管理',
          component:logAnalyze
        },
        {
          path: '/businessManage',
          name: '业务管理',
          component: businessManage
        },
        {
          path: '/task',
          name: 'ES任务管理',
          component: task,
        }, 
        {
            path: '/reportManage',
            name: '报告管理',
            component: reportManage,
        }, 
        {
            path: '/cdhHref',
            name: 'cdhHref',
            component: cdhHref,
        },
      ]
    },
    {
        path: '/versionDetail',
        name: 'versionDetail',
        component: versionDetail,
    },
  ]
})
