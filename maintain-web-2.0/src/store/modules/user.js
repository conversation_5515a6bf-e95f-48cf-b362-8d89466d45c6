// import { login, logout, getInfo } from '@/api/login'
const user = {
    state: {
        operationName:'',
        menuOptions:[],
        operationStatus:[],
        socketUrl: `ws://${location.host}/websocket/`,
        timer:null
    },

    mutations: {
        saveOperationName: (state, operationName) => {
            state.operationName = operationName;
        },
        saveMenuOptions: (state, menuOptions) => {
            state.menuOptions = menuOptions;
        },
        saveOperationStatus: (state, operationStatus) => {
            state.operationStatus = operationStatus;
        },
        saveTimer: (state, timer) => {
            state.timer = timer;
        },
    },

    actions: {
        // // 登录
        // Login({ commit }, userInfo) {
        //   const username = userInfo.username.trim()
        //   return new Promise((resolve, reject) => {
        //     login(username, userInfo.password).then(response => {
        //       const data = response
        //       setToken(data.token)
        //       commit('SET_TOKEN', data.token)
        //       resolve()
        //     }).catch(error => {
        //       reject(error)
        //     })
        //   })
        // },

        // // 获取用户信息
        // GetInfo({ commit, state }) {
        //   return new Promise((resolve, reject) => {
        //     getInfo(state.token).then(response => {
        //       const data = response
        //       if (data.roles && data.roles.length > 0) { // 验证返回的roles是否是一个非空数组
        //         commit('SET_ROLES', data.roles)
        //       } else {
        //         reject('getInfo: roles must be a non-null array !')
        //       }
        //       commit('SET_NAME', data.name)
        //       commit('SET_AVATAR', data.avatar)
        //       resolve(response)
        //     }).catch(error => {
        //       reject(error)
        //     })
        //   })
        // },

        // // 登出
        // LogOut({ commit, state }) {
        //   return new Promise((resolve, reject) => {
        //     logout(state.token).then(() => {
        //       commit('SET_TOKEN', '')
        //       commit('SET_ROLES', [])
        //       commit('CLEAR_LOCK')
        //       removeToken()
        //       resolve()
        //     }).catch(error => {
        //       reject(error)
        //     })
        //   })
        // },

        // // 前端 登出
        // FedLogOut({ commit }) {
        //   return new Promise(resolve => {
        //     commit('SET_TOKEN', '')
        //     removeToken()
        //     resolve()
        //   })
        // },
        // // 动态修改权限
        // ChangeRoles({ commit }, role) {
        //   return new Promise(resolve => {
        //     commit('SET_TOKEN', role)
        //     setToken(role)
        //     getInfo(role).then(response => {
        //       const data = response
        //       commit('SET_ROLES', data.roles)
        //       commit('SET_NAME', data.name)
        //       commit('SET_AVATAR', data.avatar)
        //       resolve()
        //     })
        //   })
        // }
    }
}

export default user
