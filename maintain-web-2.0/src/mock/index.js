import Mock from 'mockjs'
import softAPI from './softWare'
import auditAPI from './audit'

// 设置全局延时 没有延时的话有时候会检测不到数据变化 建议保留
// Mock.setup({
//   timeout: '300-600'
// })



// Mock.mock('http://192.168.120.70:8080/maintain/software/category.json', 'post', softAPI.category)
// Mock.mock('http://192.168.120.70:8080/maintain/software/config.json', 'post', softAPI.config)
// Mock.mock('http://192.168.120.70:8080/maintain/audit/list.json', 'post', auditAPI.auditList)

export default Mock
