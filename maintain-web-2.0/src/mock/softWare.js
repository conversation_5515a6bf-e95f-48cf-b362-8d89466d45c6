
const softMap = {
  configContent: {
      "localHost": "***************", //本机内网IP
      "agentWorkDirWindows": "D:\\serverdist\\MaintainAgent", //Windows下Agent端程序的工作目录名
      "agentWorkDirLinux": "/dist/MaintainAgent", //Linux下Agent端程序的工作目录名
      "agentDir": "D:\\serverdist\\agent", //agent程序目录
      "updateAgent":false, //更新Agent,默认为false
      "remoteCmdPort": 22222, //远程命令执行的端口
      "timeOut":5000,
      "ipDnsIndexPrefix": "da-",//ip、dns索引前缀
      "normalIndexPrefix": "da-",//普通索引前缀
      "cmAddress": "http://************:7180/cmf/login", //Cloudera Manager的地址
      "hdfsConfig": {//hdfs连接配置
        "fs.defaultFS": "hdfs://hadoop-cluster",
        "dfs.client.block.write.replace-datanode-on-failure.policy": "NEVER",
        "dfs.namenode.rpc-address.hadoop-cluster.nn1": "************:8020",
        "dfs.client.block.write.replace-datanode-on-failure.enabled": "true",
        "dfs.ha.namenodes.hadoop-cluster": "nn1, nn2",
        "dfs.namenode.rpc-address.hadoop-cluster.nn2": "************:8020",
        "dfs.nameservices": "hadoop-cluster",
        "dfs.client.failover.proxy.provider.hadoop-cluster": "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider"
      },
      "mainInsertDbDealPath": "D:\\Data\\DataParse\\Input",//主入库目录
      "mainInsertDbDefaultPath": "D:\\Data\\EmlParse\\Input\\",//邮件入库目录
      "resultPath": "D:\\Data\\EmlParse\\Output",//邮件处理后的存放路径
      "reportTimeMinute": 30,//报告生成的间隔时间（分钟） 30
      "deviceTimeMinute": 30,//前端状态数据同步时间间隔（分钟） 30
      "reportSyncTime": 30,//报告数据同步时间 30
      "updateCacheTime":15, // 刷新前端设备的时间
      "frontDeadTime": 120,//前端断开时间（分钟），超过这个时间没有接收到前端心跳，就认为前端断开
      "softwareDeadTime": 1800, //软件状态断开时间，单位是（秒）
      "threadPoolSize": 20,//线程池大小
      "emlIndexName": "inf-eml",//邮件索引名
      "attIndexName": "inf-attachment",//附件索引名
      "blackIpIndexName": "inf-black-ip",//黑ip索引名
      "blackDomainIndexName": "inf-black-domain",//黑域名索引名
      "oracleTable": ["INF_SOM_RESSTAT","INF_EML","INF_SHELL_CMD","INF_DNS_SCAN","INF_EML_BOX","INF_REMOTE","INF_BLACK_IP","INF_BLACK_DOMAIN"],//判断前端是否断开要查询的表
      "esIndex": ["ip-2018-*"],//判断前端是否断开要查询的es索引，不加前缀
      "whiteFront": ["117765","100020","100081","100121","100021","100123","100060","100124","100140", "100161","100002","100080","100122","111111","113495","113496","113497","117739","118603","119495"],//前端过滤apkey
      "encodeType":["UTF-8", "GBK", "GB2312", "UTF-16", "BIG5", "Unicode", "其他"],
      "defaultSecretKey":"(中*7Kk#"
    },
  configList: {
    "code": 0,
    "data": {
      "programPath": "D:\\MAINTAIN_TEMP\\",
      "programs": [
        {
          "name": "EsSearchService",
          "config": [
            {
              "path": "D:\\MAINTAIN_TEMP\\EsSearchService\\conf\\config.json",
              "fileName": "config.json"
            },
            {
              "path": "D:\\MAINTAIN_TEMP\\EsSearchService\\conf\\feature-task.json",
              "fileName": "feature-task.json"
            },
            {
              "path": "D:\\MAINTAIN_TEMP\\EsSearchService\\conf\\index.json",
              "fileName": "index.json"
            },
            {
              "path": "D:\\MAINTAIN_TEMP\\EsSearchService\\conf\\ip-correction.json",
              "fileName": "ip-correction.json"
            },
            {
              "path": "D:\\MAINTAIN_TEMP\\EsSearchService\\conf\\ipdns-query.json",
              "fileName": "ipdns-query.json"
            },
            {
              "path": "D:\\MAINTAIN_TEMP\\EsSearchService\\conf\\type-module.json",
              "fileName": "type-module.json"
            }
          ]
        },
        {
          "name": "MaintainAgent",
          "config": [
            {
              "path": "D:\\MAINTAIN_TEMP\\MaintainAgent\\conf\\config.json",
              "fileName": "config.json"
            },
            {
              "path": "D:\\MAINTAIN_TEMP\\MaintainAgent\\conf\\log4j.properties",
              "fileName": "log4j.properties"
            },
            {
              "path": "D:\\MAINTAIN_TEMP\\MaintainAgent\\conf\\restart.json",
              "fileName": "restart.json"
            }
          ]
        },
        {
          "name": "网络空间探知平台-8.0.0-DJWAZMF23I2DNW2LOJQQQ.7z",
          "config": []
        }
      ],
      "publishPath": "D:\\dist\\"
    },
    "msg": "请求成功"
  }
}

export default {
  config: () => {
    return softMap.configContent
  },
  category: () => {
    return softMap.configList
  }
}
