// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'    //内置文件
import App from './App'   //App.vue
import router from './router'    //导入路由配置

import Vuex from 'vuex'          //状态管理 --组件
import Element from 'element-ui'
import 'font-awesome/css/font-awesome.css'
import 'element-ui/lib/theme-chalk/index.css'
import echarts from 'echarts'
import qs from 'qs'
import store from './store'

import { getStore, clearStore } from '@/utils/store.js'
import './directives/clickoutside.js'
import './directives/dialogDrag.js'
import './utils/request.js'
import './utils/jumpRouter.js'
import './assets/css/global.css'
import './assets/css/plugin.css'
import './assets/css/userAssign.css'
import './assets/iconfont/iconfont.css'
import './mock'


Vue.use(Element, { size: 'small' })
Vue.use(Vuex)


Vue.prototype.$echarts = echarts
Vue.prototype.qs = qs

// router.beforeEach((to, from, next) => {
//   if (to.path != '/login') {
//     let urlArr = window.location.href.split('?token=');

//     if(urlArr.length > 1 && urlArr[1]) {
//       Axios.get('quickLogin?token='+ urlArr[1]).then((res) => {
//         clearStore({});
//         if(res.data.msg == '登陆成功'){
//           console.log(1111)
//             // res.data.data && this.initData(res.data.data);
//         }else{
//             res.data.msg ? this.waFn(res.data.msg) : this.erFn();
//         }
//       }).catch((error) => {
//           this.erFn();
//           console.log(error);
//       })
//     } else {
//       if (getStore({ name: 'operationName' })) {
//         next()
//       } else {
//         clearStore({});
//         store.commit('saveMenuOptions',[]);
//         clearInterval(store.getters.timer);
//         next({
//           path: '/login',
//         })
//       }
//     }

//   } else {
//     next();
//   }
// })

//成功提示
Vue.prototype.suFn = function (data) {
    this.$message({
        showClose: true,
        message: data,
        type: 'success',
        duration: 2000
    })
}

//错误提示
Vue.prototype.erFn = function (data) {
    if (data == '' || data == null || data == undefined) {
        data = '服务错误，请刷新重试或联系管理员'
    }
    this.$message({
        showClose: true,
        message: data,
        type: 'error',
        duration: 2000
    })
}
//警告提示
Vue.prototype.waFn = function (data) {
    this.$message({
        showClose: true,
        message: data,
        type: 'warning',
        duration: 2000
    })
}
//提示消息
Vue.prototype.msgFn = function (data) {
    this.$message({
        showClose: true,
        message: data,
        type: 'info',
        duration: 2000
    })
}

Vue.prototype.getStatus = function () {
    if (this.$route.path == '/userManage') {
        return;
    }
    this.$http.get('home/monitor.json').then((res) => {
        if (res.data.code == 0) {
            this.$store.commit('saveOperationStatus', res.data.data);
        }
    }).catch((error) => {
        // _this.erFn();
        console.log(error)
    })
}

Vue.config.productionTip = false

/* eslint-disable no-new */
new Vue({
    el: '#app',
    router,      //获取导入的路由信息  
    store,        
    components: { App },  //声明App组件
    template: '<App/>'    //template里面的内容将会替换 #app，而这里的template中只有<App/>,表示用App组件替换内容,实际上可以写成 <div><App></App><div> ,往template嵌套更多的东西
})
