<!-- 操作过程 -->
<template>
  <div class="restrat-content">
    <!-- 第一步  -->
    <div class="step-item" v-if="paramData.step == 1">
      <!-- 头部 主要用于展示 消息 -->
      <div
        class="restrat-head"
        v-if="
          showData.dataList.diff == null || showData.dataList.deploy == null
        "
      >
        <div v-for="(item, index) in showData.msgList" :key="index">
          <span>{{ item.txt }}</span>
          <a class="retry-btn" v-if="item.isRetry" @click="retrySend"
            >点击重试</a
          >
          <span
            v-else-if="showData.msgList.length == index + 1 && textLoading"
            class="el-icon-loading"
          ></span>
        </div>
      </div>
      <!-- 内容块 主要用于展示 返回的信息 -->
      <div class="restrat-body" v-else>
        <div class="file-compar">
          <div class="file-compar-title">比较结果</div>
          <div class="file-compar-content">
            <el-input
              type="textarea"
              class="restrat-textarea"
              v-model="showData.dataList.diff"
              :readonly="true"
            ></el-input>
          </div>
        </div>
        <div class="file-compar">
          <div class="file-compar-title">部署版本</div>
          <div class="file-compar-content">
            <el-input
              class="restrat-textarea"
              type="textarea"
              v-model="showData.dataList.deploy"
              :readonly="false"
            ></el-input>
          </div>
        </div>
      </div>
    </div>
    <!-- 第二步  -->
    <div class="step-item" v-else-if="paramData.step == 2">
      <!-- 头部 主要用于展示 消息 -->
      <div class="restrat-head" v-if="showData.dataList.task == null">
        <div v-for="(item, index) in showData.msgList" :key="index">
          <span>{{ item.txt }}</span>
          <a class="retry-btn" v-if="item.isRetry" @click="retrySend"
            >点击重试</a
          >
          <span
            v-else-if="showData.msgList.length == index + 1 && textLoading"
            class="el-icon-loading"
          ></span>
        </div>
      </div>
      <!-- 内容块 主要用于展示 返回的信息 -->
      <div class="restrat-body" v-else>
        <el-tabs
          class="restrat-tabs"
          tab-position="left"
          v-if="
            typeof showData.dataList.task == 'object' &&
            Object.keys(showData.dataList.task).length > 0
          "
        >
          <el-tab-pane
            v-for="(item, name) in showData.dataList.task"
            :key="name"
            :label="String(name)"
          >
            <!-- 头部内容 -->
            <span slot="label">
              <i class="iconfont icon-computer"></i>
              {{ name }}
            </span>
            <div class="checkbox-title">
              <span>
                主机
                <span class="title-color">{{ name }}</span>
                本次需要部署的程序：
              </span>
              <span>（版本号：{{ taskVersion }}）</span>
            </div>
            <!-- 切换的内容 -->
            <div class="checkbox-content">
              <el-row>
                <el-col
                  v-for="(item1, inx) in item"
                  :key="inx"
                  :span="6"
                  class="checkGroup"
                >
                  <el-tag size="small" class="deploy-produce">{{
                    item1.name
                  }}</el-tag>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="noData-div" v-else>暂无差异项</div>
      </div>
    </div>
    <!-- 第三步  -->
    <div class="step-item" v-else-if="paramData.step == 3">
      <!-- 头部 主要用于展示 消息 -->
      <div
        class="restrat-head"
        v-if="showData.dataList.commonConfigStr == null"
      >
        <div v-for="(item, index) in showData.msgList" :key="index">
          <span>{{ item.txt }}</span>
          <a class="retry-btn" v-if="item.isRetry" @click="retrySend"
            >点击重试</a
          >
          <span
            v-else-if="showData.msgList.length == index + 1 && textLoading"
            class="el-icon-loading"
          ></span>
        </div>
      </div>
      <!-- 内容块 主要用于展示 返回的信息 -->
      <div class="restrat-body" v-else>
        <el-input
          type="textarea"
          class="restrat-textarea"
          v-model="showData.dataList.commonConfigStr"
        ></el-input>
      </div>
      <div class="restrat-msg">
        <p class="promptText">1、使用 application.yml 文件作为基础配置。</p>
        <p class="promptText">
          2、待分发主机( localAddress )，分发程序时将对各Agent角色机IP进行替换。
        </p>
      </div>
    </div>
    <!-- 第四步  -->
    <div class="step-item" v-else-if="paramData.step == 4">
      <!-- 头部 主要用于展示 消息 -->
      <div class="restrat-head">
        <div v-for="(item, index) in showData.msgList" :key="index">
          <span>{{ item.txt }}</span>
          <a class="retry-btn" v-if="item.isRetry" @click="retrySend"
            >点击重试</a
          >
          <span
            v-else-if="showData.msgList.length == index + 1 && textLoading"
            class="el-icon-loading"
          ></span>
        </div>
      </div>
    </div>
    <!-- 第五步  -->
    <div class="step-item" v-else-if="paramData.step == 5">
      <!-- 头部 主要用于展示 消息 -->
      <div class="restrat-head" v-if="showData.dataList == null">
        <div v-for="(item, index) in showData.msgList" :key="index">
          <span>{{ item.txt }}</span>
          <a class="retry-btn" v-if="item.isRetry" @click="retrySend"
            >点击重试</a
          >
          <span
            v-else-if="showData.msgList.length == index + 1 && textLoading"
            class="el-icon-loading"
          ></span>
        </div>
      </div>
      <!-- 内容块 主要用于展示 返回的信息 -->
      <div v-else class="restrat-body">
        <el-row>
          <el-col :span="5" class="tree-title">
            <el-tree
              node-key="id"
              class="tree-content"
              :data="showData.dataList"
              @node-click="nodeClick"
              :auto-expand-parent="true"
              :highlight-current="true"
              :default-expand-all="true"
              :check-on-click-node="true"
              :render-content="renderTree"
              empty-text="暂无数据"
            ></el-tree>
          </el-col>
          <el-col
            :span="19"
            class="content-container"
            v-loading.fullscreen="fullscreenLoading"
          >
            <span class="comperEdit">
              <span v-if="!showArea">
                <i
                  class="el-icon-edit-outline"
                  title="修改配置"
                  @click="showArea = true"
                ></i>
              </span>

              <span v-if="showArea" @click="showArea = false">
                <i class="el-icon-switch-button" title="关闭编辑"></i>
              </span>
              <span v-if="showArea" @click="saveConfig">
                <i class="el-icon-tickets" title="保存配置"></i>
              </span>
            </span>

            <CodeDiff
              v-if="!comperData.isEqual"
              :context="1000"
              :old-string="comperData.oldtxt"
              :new-string="comperData.newtxt"
              outputFormat="side-by-side"
            />
            <div class="noData-div" v-else>暂无差异项</div>
            <div class="comperArea" v-if="showArea">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="comperData.newtxt"
              ></el-input>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
//第四步  用于展示表格
import { headerRowClassName } from "@/utils/tableStyle.js";
import CodeDiff from "vue-code-diff";

import { getStore } from "@/utils/store.js";
import { mapGetters } from "vuex";

let Base64 = require("js-base64").Base64;

export default {
  name: "restrat-content",
  props: {
    setStep: Function,
    setpData: Object,
  },
  components: { CodeDiff },
  data() {
    return {
      websock: null,
      timer: null,
      textLoading: true,
      showArea: false,
      fullscreenLoading: false,
      taskVersion: "", //第二步 版本
      $node: null, //左侧数
      comperData: {
        oldtxt: "",
        newtxt: "",
        programName: "",
        ip: "",
        isEqual: true, //文件是否相同
      },
      showData: {
        msgList: [],
        dataList: [],
      },
      sendClick: 0, //记录当前的点击次数
      //参数
      paramData: {
        step: 1, //记录当前的执行位置
      },
      parObj: null,
      //请求参数
      socketData: {
        operateType: 4,
        service: "software",
        user: {
          id: getStore({ name: "id" }),
          name: getStore({ name: "operationName" }),
        },
        // param: JSON.stringify(this.paramData),
      },
    };
  },
  methods: {
    initData() {
      console.log("开始连接:" + new Date().toLocaleString());
      console.time("连接时长");
      this.showData.msgList.push({ txt: "连接服务中..." });
      this.websock = new WebSocket(this.socketUrl);
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror;
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onclose = this.websocketonclose;
      this.sendSock();
    },
    //初始化连接
    websocketonopen(e) {
      this.showData.msgList.push({ txt: "后台服务连接成功。" });
    },
    websocketonerror(e) {
      this.showData.msgList.push({
        txt: "服务连接错误。",
        isRetry: true,
      });
    },
    websocketonmessage(e) {
      let result = e.data;
      let that = this;
      switch (true) {
        //仅仅用于保持连接
        case result == "pong":
          break;
        case result == "CLOSE":
        case result == "FAILURE":
          this.websocketonclose();
          this.sendClick = 0;
          break;
        case result == "SUCCESS":
          if (this.paramData.step == 4) {
            this.parObj = {
              step: 5,
            };
          }
          this.setStep(this.parObj, this);
          this.textLoading = false;
          this.sendClick = 0;
          break;
        case result.includes("code"):
          let res = JSON.parse(result);
          let isRetry = res.code == "200" ? false : true; //是否 重试
          let data = res.data;

          if (typeof data == "string" || typeof data == "undefined") {
            let msg = { txt: res.msg, isRetry: isRetry };
            this.showData.msgList.push(msg);
          } else {
            let list = data.list;
            switch (data.step) {
              case 1: //第一步  返回IP 配置
                this.showData.dataList = {
                  diff: JSON.stringify(JSON.parse(list.diff), null, 4),
                  deploy: JSON.stringify(JSON.parse(list.deploy), null, 4),
                };
                this.parObj = {
                  step: 2,
                  //   deploy: JSON.parse(list.deploy),
                };
                break;
              case 2: //第二步 消息展示
                this.showData.dataList = {
                  task: list.task,
                };
                this.taskVersion = list.version;
                //如果不编辑公共配置 则直接跳过步骤3
                this.parObj = this.setpData.edidCommonConfig
                  ? { step: 3 }
                  : {
                      step: 4,
                      content: list.commonConfigStr,
                      upgradeType: "0",
                    };

                break;
              case 3: //第三步 公共配置
                this.showData.dataList = {
                  commonConfigStr: Base64.decode(list.commonConfigStr),
                };
                this.parObj = {
                  step: 4,
                  //   content: Base64.encode(
                  //     this.showData.dataList.commonConfigStr
                  //   ),
                  upgradeType: "0",
                };
                break;
              case 5: //第五步  进行配置
                this.showData.dataList = list;
                let treeData = JSON.parse(
                  JSON.stringify(
                    this.showData.dataList[0]
                      ? this.showData.dataList[0].children[0].children[0]
                      : {}
                  )
                );
                this.fullscreenLoading = true;
                this.$nextTick().then(() => {
                  const firstNode = document.querySelector(
                    ".el-tree-node .el-tree-node__children .el-tree-node__children .el-tree-node__content"
                  );
                  firstNode && firstNode.click();
                  this.fullscreenLoading = false;
                });
                this.parObj = {
                  step: 6,
                };
                break;
              default:
                let msg = { txt: res.msg, isRetry: isRetry };
                this.showData.msgList.push(msg);
                break;
            }
          }
          //this.sendClick==0时，可以点击重试按钮，防止重复请求
          !isRetry && (this.sendClick = 0);
          break;
        default:
          let msg = { txt: result };
          this.showData.msgList.push(msg);
      }
      setTimeout(function () {
        let div = that.$el.firstChild.firstChild;
        div.className == "restrat-head" && (div.scrollTop = div.scrollHeight);
      }, 0);
    },
    websocketonclose(e) {
      clearInterval(this.timer);
      this.timer = null;
      this.websock.close();
      console.log("结束连接:" + new Date().toLocaleString());
      console.timeEnd("连接时长");
    },
    websocketsend(data) {
      this.websock.send(data);
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = setInterval(() => {
          this.websock.send("ping");
        }, 30000);
      } else {
        this.timer = setInterval(() => {
          this.websock.send("ping");
        }, 30000);
      }
    },
    sendSock(param) {
      if (this.websock.readyState === this.websock.OPEN) {
        this.socketData.param = JSON.stringify(this.setParamData(param));
        this.websocketsend(JSON.stringify(this.socketData));
      } else if (this.websock.readyState === this.websock.CONNECTING) {
        setTimeout(() => {
          this.sendSock();
        }, 1000);
      } else {
        setTimeout(() => {
          this.sendSock();
        }, 1000);
      }
    },
    setParamData(param) {
      this.paramData = param || this.paramData;
      if (param)
        this.showData = {
          msgList: [],
          dataList: [],
        };

      //正在加载的状态
      this.textLoading = true;
      return this.paramData;
    },
    //节点选中
    nodeClick(e, node) {
      if (node.level != 3) return false;
      let data = node.data;
      node.data.ip = node.parent.parent.label;
      node.data.programName = node.parent.label;
      this.comperData = JSON.parse(JSON.stringify(data));
      this.comperData.newtxt = this.getTxtData(node.data.newtxt);
      this.comperData.oldtxt = this.getTxtData(node.data.oldtxt);
      this.$node = node;
    },
    getTxtData(txt) {
      return Base64.decode(txt || "");
    },
    //第四步 保存配置
    saveConfig() {
      let _this = this;
      let enNewtxt = Base64.encode(_this.comperData.newtxt);

      if (enNewtxt.length == 0) {
        _this.waFn("无内容保存！");
        _this.showArea = false;
        return false;
      }

      var param = {
        ip: _this.comperData.ip,
        programName: _this.comperData.programName,
        confPath: _this.comperData.confPath,
        content: enNewtxt,
      };
      _this.fullscreenLoading = true;
      _this.$http
        .post(
          "software/write_modify_result_to_config.json",
          _this.qs.stringify(param)
        )
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("保存成功！");
            _this.showArea = false;
            $node.data.newtxt = enNewtxt;
          } else res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
        });
    },
    setStepParam(param) {
      var newParam = JSON.parse(JSON.stringify(param));
      switch (newParam.step) {
        case 2: //第二步
          if (!this.isJson(this.showData.dataList.deploy)) {
            this.waFn("请输入正常的JSON格式");
            return null;
          } else {
            newParam.deploy = JSON.parse(this.showData.dataList.deploy);
          }
          break;
        case 4: //第四步
          if (!newParam.content) {
            newParam.content = Base64.encode(
              this.showData.dataList.commonConfigStr
            );
          }
          break;
      }
      return newParam;
    },
    isJson(str) {
      if (typeof str == "string") {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == "object" && obj) {
            return true;
          } else return false;
        } catch (e) {
          return false;
        }
      } else {
        return false;
      }
    },
    //重试
    retrySend(e) {
      e.currentTarget.remove();
      this.sendClick++;
      //设置防抖 避免重复请求
      this.sendClick == 1 && this.sendSock();
    },
    renderTree(h, { node, data, store }) {
      return (
        <span>
          {node.level == 1 ? (
            <span>
              <i class="iconfont icon-computer" />
            </span>
          ) : node.level == 2 ? (
            <span class="el-icon-folder" />
          ) : (
            <span class="el-icon-document" />
          )}
          <span>{node.label}</span>
        </span>
      );
    },
  },
  computed: {
    ...mapGetters(["socketUrl"]),
  },
  created() {
    //设置 使用哪一个的数据
    this.initData();
  },
  destroyed() {
    clearInterval(this.timer);
    this.timer = null;
    this.websocketonclose();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.restrat-content {
  height: 100%;
}

/* 重试 按钮  */
.restrat-content a.retry-btn {
  margin: 4px 0px;
  cursor: pointer;
  color: #2196f3;
  text-decoration: underline;
}

.step-item {
  height: 100%;
}
/* 第一步  */
.restrat-head {
  overflow-y: auto;
  height: 100%;
  padding: 10px;
  color: #203e66;
  background-color: #fcfdff;
}

.restrat-head > div {
  padding: 8px 0px 0px 10px;
}
.restrat-body {
  display: flex;
  height: 100%;
}
.restrat-content .file-compar {
  width: 50%;
}

.restrat-content .file-compar .file-compar-content {
  height: calc(100% - 35px);
  background-color: #fcfdff;
}

.restrat-content .file-compar .file-compar-title {
  height: 35px;
  font-size: 14px;
  background-color: #f4f6fa;
  line-height: 35px;
  color: #203e66;
  padding-left: 20px;
  font-weight: 600;
}

.restrat-content .file-compar:first-child {
  border-right: 1px solid #e9effa;
}
/* 第一步  */

/* 第二步  */

.tabMenu {
  padding: 5px;
  width: 90%;
  margin-left: 5%;
  margin-bottom: 20px;
}

.checkbox-title {
  padding: 0 10px;
  background-color: #f4f6fa;
  color: #203e66;
  font-weight: 600;
  height: 40px;
  line-height: 40px;
}

.checkbox-content {
  padding: 5px 20px;
  color: #203e66;
  height: calc(100% - 37px);
  overflow-y: auto;
}

.checkGroup {
  margin-top: 15px;
}

.restrat-tabs {
  width: 100%;
  height: 100%;
}

/* .title-icon {
    display: inline-block;
    height: 24px;
    width: 20px;
    margin: auto;
    background-size: cover;
    background: url("../assets/images/procedureManage/icon-procedure.png");
    background-position: 0px -299px;
    vertical-align: sub;
} */
/* .tree-content .title-icon,
[data-name="tab-item"].is-active .title-icon {
  background-position: 0px -299px;
} */

.deploy-produce {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #9198a8;
  border: 1px solid rgba(141, 197, 255, 0.5);
  border-radius: 2px;
  background-color: rgba(238, 249, 254, 0.3);
  font-size: 12px;
}

.title-color {
  color: #409eff;
}

/* 第二步  */

/* 第三步  */
.restrat-content .restrat-textarea {
  height: 100%;
}
.restrat-content .restrat-textarea /deep/ .el-textarea__inner {
  height: 100%;
  resize: none;
  border: none;
  background-color: #fcfdff;
}

.comperArea /deep/ .el-textarea__inner {
  border: none;
  height: 100%;
  resize: none;
  padding: 0px 0px 0px 60px;
  color: #203e66;
  font-size: 12px;
  background-color: #fcfdff;
}
/* 第三步  */

/* 第四步  */
.el-row {
  width: 100%;
}

.tree-content {
  height: 100%;
  overflow-y: auto;
}

.comperEdit {
  position: absolute;
  z-index: 2;
  right: 11px;
  /* bottom: 16px; */
  cursor: pointer;
  top: 5px;
  background-color: #fff;
}
.comperEdit i {
  color: #203e66;
}

.comperEdit i {
  font-size: 21px;
}
.restrat-body {
  height: 100%;
}
.restrat-body .el-col-20 {
  position: relative;
}
.restrat-body /deep/ .el-tabs__content {
  height: 100%;
  background-color: rgba(238, 249, 254, 0.3);
}
.restrat-body /deep/ .el-tabs--left .el-tabs__nav-wrap.is-left::after,
.restrat-body /deep/ .el-tabs--left .el-tabs__nav-wrap.is-right::after {
  width: 1px;
}
.restrat-body /deep/ .el-tabs__active-bar {
  display: none;
}
.restrat-body /deep/ .el-tabs--left .el-tabs__header.is-left {
  margin: 0px;
  width: 175px;
}
.restrat-body /deep/ .el-tabs__item {
  border-bottom: 1px solid rgba(244, 246, 250, 0.5);
  color: #203e66;
  text-align: center;
}
.restrat-body /deep/ .el-tabs__item:hover,
.restrat-body /deep/ .el-tabs__item.is-active {
  background-color: rgba(64, 158, 255, 0.05);
  color: #409eff;
}
.restrat-body /deep/ .el-tabs__item span {
  display: flex;
  align-items: center;
}
.restrat-body /deep/ .el-tabs__item span i {
  margin-right: 5px;
}
/* 第四步  */

/* 第五步  */
.restrat-body .tree-title {
  border-right: 1px solid #e9effa;
}
.restrat-body .content-container {
  background-color: #fcfdff;
  position: relative;
}
.restrat-body .comperArea {
  position: absolute;
  height: 100%;
  width: 100%;
  right: 0px;
  top: 0;
  z-index: 1;
}
.restrat-body .comperArea .el-textarea {
  height: 100%;
}
.restrat-body /deep/ .el-tree-node__content {
  color: #203e66;
  height: 30px;
  line-height: 30px;
}
.restrat-body /deep/ .el-tree-node__content span:nth-child(2) {
  display: flex;
  align-items: center;
}
.restrat-body /deep/ .el-tree-node__content span:nth-child(2) i {
  margin-right: 5px;
}
.restrat-body
  /deep/
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  color: #409eff;
}
/* 第五步  */

.restrat-body /deep/ .el-tabs__nav-scroll {
  overflow-y: auto;
}

.tree-content /deep/ .title-icon {
  background: url("../assets/images/procedureManage/icon-procedure.png");
  display: inline-block;
  height: 24px;
  width: 20px;
  margin: 0px 5px 0px 0px;
  background-size: cover;
  background-position: 0px -299px;
  vertical-align: sub;
}

.tree-content /deep/ .el-icon-folder,
.tree-content /deep/ .el-icon-document {
  font-size: 19px;
  margin-right: 5px;
}

.restrat-tabs /deep/ .el-tabs__header.is-left {
  margin-left: 0px;
}

.step-item .restrat-body /deep/ .el-col,
.step-item .restrat-body /deep/ .el-col > div .d2h-wrapper,
.step-item .restrat-body /deep/ .el-col > div .d2h-wrapper .d2h-file-side-diff {
  height: 100%;
}

.step-item .restrat-body /deep/ .el-col > div .d2h-wrapper .d2h-file-side-diff {
  height: calc(50% - 1px);
  overflow-x: auto;
  width: 100%;
}

.step-item .restrat-body /deep/ .d2h-code-wrapper {
  overflow-y: auto;
  height: 100%;
}

/* .step-item .restrat-body /deep/ .d2h-file-wrapper .d2h-files-diff {
    display: flex;
} */

.restrat-body /deep/ .d2h-code-side-linenumber {
  color: #203e66;
  position: relative;
}

.step-item .restrat-body /deep/ .d2h-file-wrapper .d2h-file-side-diff {
  margin: 0px;
  overflow: hidden;
}

.step-item .restrat-body /deep/ .el-col > div .d2h-wrapper .d2h-file-wrapper {
  height: calc(100%);
  margin-bottom: 0px;
  border: none;
}

.restrat-tabs /deep/ .el-tabs__content {
  height: 100%;
}

.restrat-tabs /deep/ el-tab-pane {
  height: 100%;
}

.steps-item /deep/ .el-step__head.is-process {
  color: #c0c4cc;
  border-color: #c0c4cc;
}

.steps-item /deep/ .el-step__title.is-process {
  color: #c0c4cc;
  font-weight: 500;
}
.restrat-body /deep/ #app {
  height: calc(100% - 1px);
}
.restrat-body /deep/ #app > div {
  height: 100%;
}
</style>