<!-- 编辑文件 -->
<template>
    <div class="config-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-dialog 
            append-to-body 
            title="编辑公共配置文件" 
            :visible.sync="editFormVisible" 
            class="dialog-border" width="1000px" 
            :close-on-click-modal="false"
            @close='resetForm'
            v-dialogDrag>
            <el-row class="dialog-table">
                <el-row>
                    <el-col :span="24">
                        <p class="configTitle">
                            <span>{{editForm.configName || 'common-config.json'}}：</span>
                            <span class="promptText" v-if="editForm.checkAgentRole">(注意：{{editForm.checkAgentRole}})</span>
                        </p>
                    </el-col>
                    <!-- <el-col :span="12" class="button-col">
                        <el-button type="text" class="text-button" @click="checkJson">
                            <div class="button-icon button-json"></div>
                            <span>JSON格式化</span>
                        </el-button>
                    </el-col> -->
                </el-row>
                <el-row class="edit-row">
                    <el-col :span="24">
                        <el-input type="textarea" v-model='editForm.configContent' rows="20"></el-input> 
                    </el-col>
                    <slot name="promptSlot"></slot>
                </el-row>
            </el-row>
            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="editFormVisible = false" class="btn-style">关闭</button>
                <button @click.prevent="saveBtn" class="btn-style primary-btn">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getStore } from "@/utils/store.js";
import { mapGetters } from "vuex";

export default {
    name: "config-content",
    props: { editData: Object },
    data() {
        return {
            title: "",
            url: "",
            returnText: "",
            websock: null,
            timer: null,

            fullscreenLoading: false,
            editFormVisible: true,

            editForm: {
                configContent: "",
                configName: ""
            }
        };
    },
    methods: {
        checkJson() {
            let configContent = this.editForm.configContent;

            this.editForm.configContent = JSON.stringify(
                JSON.parse(this.editForm.configContent),
                null,
                4
            );
            // try {
            //     let obj = JSON.parse(configContent);
            //     if(typeof obj == 'object' && obj){
            //         this.editForm.configContent = JSON.stringify(JSON.parse(this.editForm.configContent),null,4);
            //         this.suFn('json格式成功');
            //     } else {
            //         this.waFn('json格式错误');
            //     }
            // } catch (e) {
            //     console.log(e);
            //     this.waFn('json格式错误');
            // }
        },
        getDataList(row) {
            let _this = this;
            let Base64 = require("js-base64").Base64;

            // _this.editForm.configName = row.name;
            _this.editFormVisible = true;
            _this.fullscreenLoading = true;

            _this.$http
                .get("software/commonConfig")
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            this.editForm.configContent = Base64.decode(
                                res.data.data.commonConfigStr
                            );
                            this.editForm.checkAgentRole =
                                res.data.data.checkAgentRole;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        saveBtn() {
            this.$emit("editFileReturn", this.editForm);
        },
        resetForm(val) {
            this.editFormVisible = false;
            this.$emit("editFileReturn");
        }
    },
    created() {
        this.editForm = Object.assign(this.editForm, this.editData);
        this.editFormVisible = true;
        this.getDataList();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.config-content {
    height: 100%;
    overflow-y: auto;
}
.configTitle {
    color: #203e66;
    font-weight: bold;
    padding: 5px 0 0 5px;
}
.button-icon {
    display: inline-block;
    height: 18px;
    width: 20px;
    background-size: cover !important;
    vertical-align: sub;
    background: url("../assets/images/procedureManage/icon-dialog.png");
}
.button-edit {
    background-position: 0px -51px;
}
.button-json {
    background-position: 0px -198px;
}
.button-col {
    text-align: right;
}
.text-button:hover .button-edit {
    background-position: 0px -151px;
}
.text-button:hover .button-json {
    background-position: 0px -242px;
}
.btn-style {
    padding: 0 20px;
}
.btn-style:first-child {
    margin-right: 10px;
}
.dialog-table {
    padding: 0 20px;
}
</style>