<!-- 导入文件 -->
<template>
    <div class="agentAdd-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-dialog title="导入文件" :visible.sync="addFormVisible"    :close-on-click-modal="false" width="600px" top="10vh" class="dialog-border" @close="resetForm">
            <el-row type="flex" class="row-bg">
                <el-upload 
                    :action="uploadUrl"
                    :file-list='fileList' 
                    :on-success="successUpload" 
                    name="file" class="upload"
                    :with-credentials="true" 
                    :before-upload="beforeChange"
                    :on-error="uploadFail"
                    :on-change="handleChange">
                    <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
            </el-row>

            <div class="upload-text">
                <p class="upload-prompt">提示说明：</p>
                <p class="el-upload_tip">1、文件格式只能为excel/csv文件</p>
                <p class="el-upload_tip">2、请仔细阅读模板里的批注</p>
                <p>3、<a :href="downUrl" download>excel模板</a></p>
            </div>
            
            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="addFormVisible = false" class="btn-style">关闭</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import $ from "jquery";

export default {
    name: "agentAdd-content",
    props: { importData: Object },
    data() {
        return {
            downUrl: "",
            uploadUrl: "",
            closeText: "",

            addFormVisible: false,
            fullscreenLoading: false,

            fileList: []
        };
    },
    methods: {
        handleChange(file, fileList) {
            this.fileList = fileList.slice(-3);
            if (file.response != undefined) {
                file.response.code != 0 && this.fileList.pop();
            }
        },
        beforeChange(file) {
            let fileName = file.name;
            let index = fileName.lastIndexOf(".");
            let fileLength = fileName.length;
            let suffix = fileName.substring(index + 1, fileLength);

            if (suffix != "xls" && suffix != "xlsx" && suffix != "csv") {
                this.waFn("仅支持excel和csv文件");
                this.fileList.pop();
                return false;
            }
        },
        successUpload(res) {
            if (res.code == 0) {
                this.suFn("导入成功");
                this.closeText = "SUCCESS";
            } else {
                this.fileList.pop();
                this.waFn(res.msg);
            }
        },
        uploadFail() {
            this.erFn("上传发生错误，请稍后重试");
        },
        resetForm() {
            this.$emit("returnMain", this.closeText);
        }
    },
    mounted() {
        this.uploadUrl =
            this.$http.defaults.baseURL + this.importData.requestUrl;
        this.downUrl = "/static/template/" + this.importData.downFile + ".xlsx";
        this.addFormVisible = true;
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.agentAdd-content {
    height: 100%;
    overflow-y: auto;
}
.agentAdd-form {
    width: 90%;
    margin: auto;
}
.btn-style:first-child {
    margin-right: 10px;
}
.btn-style {
    padding: 0 20px;
}
.upload {
    width: 100%;
    color: #203e66;
}
.upload-text {
    padding-left: 1px;
    letter-spacing: 1px;
    line-height: 20px;
    color: #203e66;
}
.upload-text a {
    color: #409eff;
    text-decoration: none;
}
.upload-prompt {
    margin: 20px 0 10px;
}
.fail-row {
    color: #203e66;
}
.fail-row .fail-content {
    max-height: 300px;
    overflow-y: auto;
    padding-left: 1px;
    letter-spacing: 1px;
    line-height: 20px;
}
.fail-row .fail-title {
    min-width: 150px;
    margin-right: 10px;
    display: inline-block;
}
</style>