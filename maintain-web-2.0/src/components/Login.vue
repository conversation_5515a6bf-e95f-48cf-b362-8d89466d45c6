<!-- 登录页 -->
<template>
    <div class="login-content" v-loading.fullscreen="fullscreenLoading">
        <div class="login-top">
            <!-- <img :src="Logo" class="logo"> -->
            <span class="systemname">平台管理</span> 
            <!-- <span class="version">V2.2.3</span> -->
        </div>
        <div class="login-bottom">
            <div class="login-input">
                <div class="dv_login">
                    <input
                        type="text"
                        v-focus="true"
                        class="txt_login"
                        v-model="form.username"
                        @keyup.enter="loginBtn"
                    >
                </div>
                <div class="dv_pwd">
                    <input
                        type="password"
                        class="txt_pwd"
                        @keyup.enter="loginBtn"
                        v-model="form.password"
                    >
                </div>
                <input
                    type="button"
                    id="btnLogin"
                    class="btn_login"
                    value="登   录"
                    @click="loginBtn"
                >
                <div class="suggestion">建议使用1440*900屏幕分辨率</div>
            </div>
        </div>
    </div>
</template>

<script>
import { setStore, clearStore } from "@/utils/store.js";
import { mapMutations } from "vuex";
import store from "@/store";

export default {
    name: "login-content",
    data() {
        return {
            fullscreenLoading: false,
            form: {
                username: "",
                password: ""
            }
            // pathOptions:[
            //     {
            //         moduleName:'系统首页',
            //         path:'/index',
            //     },
            //     {
            //         moduleName:'Agent管理',
            //         path:'/agentManage',
            //     },
            //     {
            //         moduleName:'用户管理',
            //         path:'/userManage',
            //     },
            //     {
            //         moduleName:'集群管理',
            //         path:'/colonyManage',
            //     },
            //     {
            //         moduleName:'前端管理',
            //         path:'/frontManage/probeStatus',
            //     },
            //     {
            //         moduleName:'服务器管理',
            //         path:'/hardWare',
            //     },
            //     {
            //         moduleName:'程序管理',
            //         path:'/procedureManage',
            //     },
            //     {
            //         moduleName:'审计管理',
            //         path:'/auditManage',
            //     },
            //     {
            //         moduleName:'日志管理',
            //         path:'/logAnalyze',
            //     },
            //     {
            //         moduleName:'业务管理',
            //         path:'/businessManage',
            //     },
            //     {
            //         moduleName:'报告管理',
            //         path:'/reportManage',
            //     },
            // ]
        };
    },
    methods: {
        ...mapMutations(["saveOperationName", "saveMenuOptions", "saveTimer"]),
        loginBtn() {
            let _this = this;

            if (!this.form.username) {
                this.msgFn("请输入用户名");
                return;
            }
            if (!this.form.password) {
                this.msgFn("请输入密码");
                return;
            }
            _this.fullscreenLoading = true;
            _this.$http
                .post("user/login", _this.qs.stringify(_this.form))
                .then(res => {
                    clearStore({});
                    _this.fullscreenLoading = false;
                    if (res.data.msg == "登陆成功") {
                        res.data.data && this.initData(res.data.data);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        initData(data) {
            if (data.menuOptions.length != 0) {
                setStore({
                    name: "operationName",
                    content: data.user.name
                });
                setStore({
                    name: "menuOptions",
                    content: data.menuOptions
                });
                setStore({
                    name: "id",
                    content: data.user.id
                });
                this.saveMenuOptions(data.menuOptions);
                this.saveOperationName(data.user.name);
                this.saveTimer(
                    setInterval(() => {
                        this.getStatus();
                    }, 60000)
                );
                this.$router.push({
                    path: data.menuOptions[0].path || "/index"
                });
            } else {
                this.waFn("对不起，您没有查看任何页面的权限");
            }
        }
    },
    directives: {
        focus: {
            inserted(el, { value }) {
                if (value) {
                    el.focus();
                }
            }
        }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped >
.login-content {
    height: 100%;
}
.login-top {
    width: 100%;
    min-width: 800px;
    height: 340px;
    margin: auto;
    background-size: cover;
    background-image: url("../assets/images/login/loginLogo.png");
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 40px;
}
.login-top .systemname {
    font-size: 60px;
    color: #535353;
}
.login-top .version {
    width: 70px;
    height: 25px;
    position: absolute;
    bottom: 100px;
    left: calc(50% + 180px);
    font-style: oblique;
    font-size: 16px;
    text-align: center;
    line-height: 25px;
    background-color: #f85254;
    color: #fff;
    border-radius: 2px;
}

.login-top .version::after {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 6px solid transparent;
    border-top: 16px solid #f85254;
    bottom: -11px;
    left: 9px;
    transform: rotate(35deg);
}
.login-bottom {
    width: 100%;
    height: calc(100% - 340px);
    overflow: hidden;
    zoom: 1;
    background-color: #eef2f5;
    text-align: center;
}
.login-input {
    width: 410px;
    height: auto;
    padding-top: 30px;
    margin: auto;
}
.dv_login {
    width: 409px;
    height: 45px;
    margin-bottom: 10px;
    border: 1px solid #dbdbdb;
    border-radius: 5px;
    padding: 0;
    padding-left: 55px;
    background-color: white;
    background-image: url("../assets/images/login/loginUser.png");
    background-position: left center;
    background-repeat: no-repeat;
}
.txt_login {
    font-size: 20px;
    display: block;
    width: 338px;
    height: 41px;
    line-height: 41px;
    border: none;
    border-style: none;
    text-align: left;
    overflow: hidden;
    margin: 0;
    outline: none;
}
.dv_pwd {
    width: 409px;
    height: 45px;
    margin-bottom: 20px;
    border: 1px solid #dbdbdb;
    border-radius: 5px;
    padding: 0;
    padding-left: 55px;
    background-color: white;
    background-image: url("../assets/images/login/loginPwd.png");
    background-position: left center;
    background-repeat: no-repeat;
}
.txt_pwd {
    font-size: 20px;
    display: block;
    width: 338px;
    height: 41px;
    line-height: 41px;
    border: none;
    border-style: none;
    text-align: left;
    overflow: hidden;
    margin: 0;
    outline: none;
}
.btn_login {
    font-size: 20px;
    display: block;
    width: 410px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    /* border: 1px solid #a7a4a4; */
    border-radius: 5px;
    cursor: pointer;
    color: white;
    border: 1px solid White;
    font-family: "Microsoft YaHei";
    background-image: url("../assets/images/login/loginBtn.jpg");
    background-position: center;
    background-repeat: repeat-x;
}
.suggestion {
    margin-top: 10px;
}
</style>