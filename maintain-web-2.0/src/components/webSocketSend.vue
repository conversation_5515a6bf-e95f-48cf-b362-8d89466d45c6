<!-- 操作过程 -->
<template>
    <div class="restrat-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-dialog 
            :title="title" 
            :visible.sync="addFormVisible" 
            width="600px" 
            top="10vh" 
            class="dialog-border" 
            @close="resetForm" 
            :close-on-click-modal="false" 
            :close-on-press-escape='false'>
            <div class="socket-body" ref="socketBody">
                <p class="socket-message" v-for="(value,index) in textOptions" :key="index">{{value}}</p>
                <div class="socketLoading" v-if="loadingShow"></div>
            </div>
            <div slot="footer" class="dialog-footer footer-button">
            <button @click.prevent="addFormVisible = false" class="btn-style">关闭</button>
            <button @click.prevent="addFormVisible = false" class="btn-style primary-btn">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getStore } from "@/utils/store.js";
import { mapGetters } from "vuex";

export default {
    name: "restrat-content",
    props: { socketData: Object },
    data() {
        return {
            title: "",
            url: "",
            returnText: "",
            websock: null,
            timer: null,

            fullscreenLoading: false,
            addFormVisible: true,
            loadingShow: true,

            textOptions: []
        };
    },
    methods: {
        initData() {
            this.title = this.socketData.title || "操作过程";
            this.url = this.socketUrl;
            this.socketData.user = {
                id: getStore({ name: "id" }),
                name: getStore({ name: "operationName" })
            };
            this.socketData["url"] && delete this.socketData["url"];
            this.socketData["title"] && delete this.socketData["title"];
            this.initWebSocket();
        },
        initWebSocket() {
            let data = {};
            //   const wsui = 'ws://192.168.120.62:8088/maintain/software';
            const wsui = this.url;
            console.log(wsui);
            this.textOptions.push("正在连接后台服务中...");
            this.websock = new WebSocket(wsui);
            this.websock.onopen = this.websocketonopen;
            this.websock.onerror = this.websocketonerror;
            this.websock.onmessage = this.websocketonmessage;
            this.websock.onclose = this.websocketonclose;

            this.sendSock();
        },
        websocketonopen() {
            console.log("后台服务连接成功");
            this.textOptions.push("后台服务连接成功");
        },
        websocketonerror(e) {
            console.log("后台服务连接发生错误");
            this.textOptions.push("后台服务连接发生错误");
        },
        websocketonmessage(e) {
            console.log(e.data);
            switch (e.data) {
                case "CLOSE":
                    console.log("CLOSE");
                    this.websocketonclose();
                    break;
                case "SUCCESS":
                    console.log("SUCCESS");
                    this.websocketonclose();
                    this.returnText = e.data;
                    break;
                case "pong":
                    break;
                default:
                    this.textOptions.push(e.data);
                    setTimeout(() => {
                        let div = this.$refs.socketBody;
                        div.scrollTop = div.scrollHeight;
                    }, 0);
            }
        },
        websocketonclose(e) {
            console.log("连接关闭");
            console.log(new Date());
            clearInterval(this.timer);
            this.timer = null;
            this.websock.close();
            this.loadingShow = false;
        },
        websocketsend(data) {
            console.log("send", data);
            this.websock.send(data);
            if (this.timer) {
                clearInterval(this.timer);
            } else {
                this.timer = setInterval(() => {
                    console.log("send", "ping");
                    this.websock.send("ping");
                }, 50000);
            }
        },
        sendSock() {
            if (this.websock.readyState === this.websock.OPEN) {
                this.websocketsend(JSON.stringify(this.socketData));
            } else if (this.websock.readyState === this.websock.CONNECTING) {
                setTimeout(() => {
                    this.sendSock();
                }, 1000);
            } else {
                setTimeout(() => {
                    this.sendSock();
                }, 1000);
            }
        },
        resetForm() {
            this.websock.readyState == 1 && this.websocketsend("CLOSE");
            this.websock.close();
            this.$emit("socketReturn", this.returnText);
        }
    },
    computed: {
        ...mapGetters(["socketUrl"])
    },
    created() {
        let _this = this;
        this.initData();
    },
    destroyed() {
        clearInterval(this.timer);
        this.timer = null;
        this.websocketonclose();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.restrat-content {
    overflow-y: auto;
}
.btn-style:first-child {
    margin-right: 10px;
}
.btn-style {
    padding: 0 20px;
}
.socketLoading {
    width: 100%;
    height: 15px;
    background: url("../assets/images/loading.gif") no-repeat center;
    margin-top: 10px;
}
.socket-message {
    margin: 5px 0;
    word-break: break-all;
    color: #203e66;
}
.socket-body {
    max-height: 400px;
    overflow-y: auto;
}
</style>