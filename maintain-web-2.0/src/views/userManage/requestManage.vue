<!-- 接口管理 -->
<template>
    <div class="request-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-row class="row-content">
            <el-col :span="24" class="content-form">
                <el-form label-width="4rem" class="request-form">
                    <el-row type='flex' class="row-bg">
                        <el-col :span="9">
                            <span>操作时间:</span>
                            <el-date-picker
                                v-model="form.operateTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholde="开始日期"
                                end-placeholde="结束日期"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                :picker-options="pickersOptions"
                                :default-value="new Date()"
                                @change="operateChange">
                            </el-date-picker>
                        </el-col>
                    </el-row>
                </el-form>
            </el-col>
            <el-col :span="24" class="content-table">
                <el-table 
                    class="table-border" 
                    :data="dataList" 
                    height="100%" border 
                    :default-sort="{prop: 'timeAvg', order: 'descending'}"
                    v-loading='tableLoading' 
                    :row-class-name="bodyClass" 
                    :header-cell-style="tableHeader" 
                    :header-row-class-name="headerClass">

                    <el-table-column prop="module" label="模块名" sortable></el-table-column>
                    <el-table-column prop="uri" label="请求路径" min-width="200"></el-table-column>
                    <el-table-column prop="times" label="请求次数" sortable></el-table-column>
                    <el-table-column prop="timeAvg" label="平均耗时" sortable :formatter="formatTime"></el-table-column>
                    <el-table-column prop="timeMax" label="最大耗时" sortable :formatter="formatTime"></el-table-column>
                    <el-table-column prop="timeMin" label="最小耗时" sortable :formatter="formatTime"></el-table-column>
                    <el-table-column prop="timeTotal" label="总耗时" sortable :formatter="formatTime"></el-table-column>
                    
                </el-table>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import { parseTime } from "@/utils/index.js";
import {
    bodyRowClassName,
    tableHeaderStyle,
    headerRowClassName
} from "@/utils/tableStyle.js";

export default {
    name: "user-content",
    data() {
        return {
            tableLoading: false,
            fullscreenLoading: false,
            addFormVisible: false,

            dataList: [],
            form: {
                startTime: "",
                endTime: "",
                operateTime: ""
            },

            pickersOptions: {
                disabledDate(date) {
                    return (
                        date.getTime() > Date.now() - 8.64e6 ||
                        date.getTime() <
                            Date.now() - (30 + 1) * 24 * 3600 * 1000
                    );
                }
            }
        };
    },
    methods: {
        init() {
            let todayTime = Date.parse(new Date());

            this.form.startTime = parseTime(todayTime, "{y}-{m}-{d} 00:00:00");
            this.form.endTime = parseTime(todayTime, "{y}-{m}-{d} 23:59:59");
            this.form.operateTime = [this.form.startTime, this.form.endTime];
            this.getDataList();
        },

        //请求数据函数
        getDataList() {
            let _this = this;

            _this.tableLoading = true;
            _this.$http
                .post("request/record/statis", this.qs.stringify(this.form))
                .then(res => {
                    _this.tableLoading = false;
                    if (res.data.code == 0) {
                        _this.dataList = res.data.data;
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.tableLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        //处理数据函数
        formatTime(row, column, value) {
            return value + "ms";
        },

        //功能函数
        operateChange(val) {
            if (val) {
                this.form.startTime = val[0];
                this.form.endTime = val[1];
            } else {
                this.form.startTime = "";
                this.form.endTime = "";
            }
            this.getDataList();
        },
        bodyClass() {
            return bodyRowClassName();
        },
        tableHeader() {
            return tableHeaderStyle();
        },
        headerClass() {
            return headerRowClassName();
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.request-content {
    height: 100%;
    overflow: hidden;
    width: calc(100% - 40px);
    margin: 0 20px;
}
.request-form {
    height: 100%;
    line-height: 60px;
}
</style>