<!-- 分配权限 -->
<template>
    <div class="assign-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-row>
            <div class="top-title">角色权限分配</div>
        </el-row>
        <el-row class="tab-row">
            <el-tabs v-model="tabName" tab-position="left" class="assign-tab">
                <el-tab-pane 
                v-for="(item,index) in tabOptions" 
                :key="item.value" 
                :name='item.name' 
                class="tabMenu">

                <div slot="label">
                    <div class='title-icon'></div>
                    <span>{{item.name}}</span>
                </div>
                <template slot-scope="scope">
                    <el-checkbox-group v-model="checkedOptions" @change='btnChange(index,$event)'>
                    <div class="checkbox-title">
                        <el-checkbox 
                        :indeterminate="item.isIndeterminate" 
                        v-model="item.checkAll" 
                        :label="item.id" 
                        @change='checkAllChange(index,$event)'>
                        {{item.name}}
                        </el-checkbox>
                    </div>
                    <div class="checkbox-content">
                        <el-row>
                        <el-col v-for="item1 in item.children" :key="item1.value" :span="6" class="checkGroup">
                            <el-checkbox :label="item1.id">{{item1.name}}</el-checkbox>
                        </el-col>
                        </el-row>
                    </div>
                    </el-checkbox-group>
                </template>
                </el-tab-pane>
            </el-tabs>
        </el-row>
        <el-row class='btn-row'>
            <button @click.prevent="returnWeb" class="btn-style">返回</button>
            <button @click.prevent="saveBtn" class="btn-style primary-btn">保存</button>
        </el-row>
    </div>
</template>

<script>
export default {
    name: "assign-content",
    data() {
        return {
            tabName: "",

            fullscreenLoading: false,
            isIndeterminate: false, //样式控制
            checkAll: false, //全部选中状态

            checkedOptions: [], //选择的按钮组
            tabOptions: [],
            twoRow: {}
        };
    },
    methods: {
        init() {
            this.twoRow = this.$route.query;
            this.getDataList();
        },

        //请求数据函数
        getDataList() {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .get("user/permissionList.json?userId=" + this.twoRow.id)
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        res.data.data,
                            res.data.checked &&
                                _this.formatData(
                                    res.data.data,
                                    res.data.checked
                                );
                        // _this.roundColor(res.data.data);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        saveBtn() {
            let _this = this,
                data = {};

            data.roleId = this.twoRow.roleId;
            data.permissionIds = this.checkedOptions.join(",");
            _this.fullscreenLoading = true;
            _this.$http
                .post("user/assignPermissions", _this.qs.stringify(data))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        _this.suFn("保存成功");
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        roundColor(data) {
            this.$nextTick(() => {
                for (let i in data) {
                    $(".el-tabs__item")
                        .eq(i)
                        .attr("data-name", "assign");
                }
            });
        },
        formatData(data, value) {
            for (let i in data) {
                data[i].btnOptions = [];
                data[i].isIndeterminate = false;
                data[i].checkAll = false;
                if (data[i].children) {
                    for (let j in data[i].children) {
                        data[i].btnOptions.push(data[i].children[j].id);
                    }
                }
            }
            this.tabOptions = data;
            this.tabName = this.tabOptions[0].name;
            this.$nextTick(() => {
                this.checkedOptions = value;
                this.roundColor(data);
            });
        },

        //功能函数
        checkAllChange(index, val) {
            let checkedOptions = this.checkedOptions,
                btnOptions = this.tabOptions[index].btnOptions;

            for (let i in btnOptions) {
                for (let j in checkedOptions) {
                    if (checkedOptions[j] === btnOptions[i]) {
                        checkedOptions.splice(j, 1);
                        j--;
                    }
                }
            }
            val && checkedOptions.push.apply(checkedOptions, btnOptions);
            this.tabOptions[index].isIndeterminate = false;
        },
        btnChange(index, value) {
            let checkedCount = 0,
                btnOptions = this.tabOptions[index].btnOptions,
                checkedOptions = this.checkedOptions,
                num = 0;

            for (let i in value) {
                for (let j in btnOptions) {
                    if (value[i] === btnOptions[j]) {
                        checkedCount++;
                    }
                }
            }
            this.tabOptions[index].checkAll =
                checkedCount === btnOptions.length;
            this.tabOptions[index].isIndeterminate =
                checkedCount > 0 && checkedCount < btnOptions.length;
            if (
                !this.tabOptions[index].checkAll &&
                !this.tabOptions[index].isIndeterminate
            ) {
                for (let j in checkedOptions) {
                    if (checkedOptions[j] === this.tabOptions[index].id) {
                        checkedOptions.splice(j, 1);
                        break;
                    }
                }
            } else {
                for (let i in checkedOptions) {
                    if (checkedOptions[i] === this.tabOptions[index].id) {
                        num++;
                    }
                }
                if (num === 0) {
                    checkedOptions.push(this.tabOptions[index].id);
                }
            }
        },
        returnWeb() {
            this.$router.go(-1);
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.assign-content {
    overflow-y: auto;
    margin: 15px;
    width: calc(100% - 30px);
    height: calc(100% - 30px);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 5px;
    background-color: #fff;
}
.top-title {
    width: 100%;
    text-align: left;
    text-indent: 15px;
    color: #203e66;
    font-size: 12px;
    font-weight: bold;
    background-color: #e8f5fc;
    border-bottom: 1px solid #eee;
    height: 33px;
    line-height: 33px;
}
.tab-row {
    margin: 20px 10% 15px;
    width: 80%;
    height: calc(100% - 106px);
}
.assign-tab {
    height: 100%;
}
.tabMenu {
    padding: 5px;
    width: 90%;
    margin-left: 5%;
    border: solid 1px #e5e5e5;
    margin-bottom: 20px;
    height: auto;
}
.checkbox-title {
    padding: 10px 0;
    margin: 0 10px;
    border-bottom: 1px solid #e2e7eb;
}
.checkbox-content {
    padding: 10px 0;
    margin: 0 20px;
}
.checkGroup {
    margin-top: 17px;
}
.btn-row {
    text-align: right;
    height: 30px;
    padding-right: 45px;
}
.title-icon {
    height: 24px;
    width: 24px;
    margin: auto;
    background-size: cover;
    background: url("../../assets/images/menu/icon-user.png");
}
.btn-style:first-child {
    margin-right: 10px;
}
.btn-style {
    padding: 0 20px;
}
</style>