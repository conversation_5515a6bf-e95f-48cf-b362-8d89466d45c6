<!-- 用户管理 -->
<template>
  <div class="user-content" v-loading.fullscreen="fullscreenLoading">
    <el-row class="row-content">
      <el-col :span="24" class="content-form">
        <el-form label-width="4rem" class="user-form">
          <el-row type="flex" class="row-bg">
            <el-col :span="24" class="form-btn">
              <button
                class="btn-style"
                @click.prevent="addBtn"
                icon="el-icon-plus"
                v-if="btn.addUser.value"
              >
                <div class="button-icon button-add"></div>
                <span>添加</span>
              </button>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
      <el-col :span="24" class="content-table">
        <el-table
          class="table-border"
          :data="dataList"
          height="100%"
          border
          v-loading="tableLoading"
          :row-class-name="bodyClass"
          :header-cell-style="tableHeader"
          :header-row-class-name="headerClass"
        >
          <el-table-column
            prop="name"
            label="账户"
            min-width="200px"
          ></el-table-column>
          <el-table-column
            prop="department"
            label="部门"
            min-width="200px"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            min-width="265px"
          ></el-table-column>
          <el-table-column
            prop="lastLoginTime"
            label="最近登录时间"
            min-width="265px"
          ></el-table-column>
          <el-table-column align="center" label="操作" min-width="315px">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="initBtn(scope.row.id)"
                v-if="btn.restPassword.value"
                class="user-button"
              >
                <div class="button-icon button-restart"></div>
                <span>密码重置</span>
              </el-button>
              <el-button
                type="text"
                @click="detelBtn(scope.row.id)"
                v-if="btn.cancelUser.value"
                class="user-button"
              >
                <div class="button-icon button-detele"></div>
                <span>删除</span>
              </el-button>
              <el-button
                type="text"
                @click="assignBtn(scope.row)"
                v-if="btn.assign.value"
                class="user-button"
              >
                <div class="button-icon button-assign"></div>
                <span>分配权限</span>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!-- 新增用户弹窗 -->
    <el-dialog
      title="添加用户"
      :visible.sync="addFormVisible"
      width="600px"
      class="dialog-border"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-row type="flex" class="row-bg">
        <el-form
          label-width="100px"
          :model="addForm"
          :rules="rules"
          ref="addForm"
          class="userAdd-form"
        >
          <el-col :span="24">
            <el-form-item label="账户:" prop="username">
              <el-input v-model="addForm.username"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="密码:" prop="password">
              <el-input v-model="addForm.password"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="角色:" prop="roleIds">
              <el-select v-model="addForm.roleIds">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.value"
                  :label="item.nickName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <div slot="footer" class="dialog-footer footer-button">
        <button
          @click.prevent="addFormVisible = false"
          class="dialogBtn-style btn-style"
        >
          关闭
        </button>
        <button
          @click.prevent="saveBtn"
          class="dialogBtn-style primary-btn btn-style"
        >
          确定
        </button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import assignPermissions from "./assignPermissions.vue";
import getPermission from "@/utils/permissions.js";
import {
  bodyRowClassName,
  tableHeaderStyle,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "user-content",
  data() {
    return {
      tableLoading: false,
      fullscreenLoading: false,
      addFormVisible: false,

      dataList: [],
      roleOptions: [],

      twoRow: {},
      addForm: {
        username: "",
        password: "",
        roleIds: "",
      },
      rules: {
        username: [
          {
            required: true,
            validator: (rule, value, callback) => {
              var vin = /^.{3,15}$/;

              if (value == "") {
                callback(new Error("请输入账户"));
              } else {
                if (vin.test(value)) {
                  callback();
                } else {
                  callback(new Error("账户长度必须为3-15位字符"));
                }
              }
            },
          },
        ],
        password: [
          {
            required: true,
            validator: (rule, value, callback) => {
              var vin = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[`~!@#$%^&*()_\-+=<>?:\"{}|,.\\/;'])[A-Za-z\d`~!@#$%^&*()_\-+=<>?:\"{}|,.\\/;']{6,20}$/;
              // var vin = /^(?![0-9]+$)(?![a-zA-Z]+$)(?![0-9a-zA-Z]+$)(?![0-9\W]+$)(?![a-zA-Z\W]+$)[0-9A-Za-z\W]{6,20}$/;

              if (value == "") {
                callback(new Error("请输入密码"));
              } else {
                if (vin.test(value)) {
                  callback();
                } else {
                  callback(
                    new Error(
                      "密码长度必须为6-20位字符且包含数字,字母和特殊字符"
                    )
                  );
                }
              }
            },
          },
        ],
        roleIds: [
          {
            required: true,
            message: "请选择角色",
          },
        ],
      },
      btn: {
        addUser: {
          name: "添加用户",
          value: false,
        },
        restPassword: {
          name: "初始化密码",
          value: false,
        },
        assign: {
          name: "分配权限",
          value: false,
        },
        cancelUser: {
          name: "注销用户",
          value: false,
        },
      },
    };
  },
  methods: {
    init() {
      this.getDataList();
      getPermission(this.$route.name, this.btn);
    },

    //请求数据函数
    getDataList() {
      let _this = this;

      _this.tableLoading = true;
      _this.$http
        .get("user/userList.json")
        .then((res) => {
          _this.tableLoading = false;
          if (res.data.code == 0) {
            _this.dataList = res.data.data;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getRoleList() {
      let _this = this;

      _this.$http
        .get("user/roleList.json")
        .then((res) => {
          if (res.data.code == 0) {
            _this.roleOptions = res.data.data;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
          console.log(error);
        });
    },
    addUser(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("user/registry", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("添加成功");
            _this.addFormVisible = false;
            _this.getDataList();
          } else {
            _this.waFn(res.data.msg);
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
        });
    },
    initBtn(value) {
      let _this = this;

      this.$confirm("您确定要重置该账户密码吗？", "提示", {
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          _this.fullscreenLoading = true;
          _this.$http
            .get("user/initPassword?userId=" + value)
            .then((res) => {
              _this.fullscreenLoading = false;
              if (res.data.code == 0) {
                _this.suFn("重置成功");
              } else {
                res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
              }
            })
            .catch((error) => {
              _this.fullscreenLoading = false;
              _this.erFn();
              console.log(error);
            });
        })
        .catch((error) => {
          console.log(error);
        });
    },
    detelBtn(value) {
      let _this = this;

      this.$confirm("您确定要删除该账户吗？", "提示", {
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          _this.fullscreenLoading = true;
          _this.$http
            .get("user/cancel?userId=" + value)
            .then((res) => {
              _this.fullscreenLoading = false;
              if (res.data.code == 0) {
                _this.suFn("删除成功");
                _this.getDataList();
              } else {
                res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
              }
            })
            .catch((error) => {
              _this.fullscreenLoading = false;
              _this.erFn();
              console.log(error);
            });
        })
        .catch((error) => {
          console.log(error);
        });
    },

    //功能函数
    resetForm() {
      this.$refs["addForm"].resetFields();
      this.roleOptions = [];
    },
    assignBtn(row) {
      this.$router.push({
        path: "assignPermissions",
        query: {
          roleId: row.roleId,
          id: row.id,
        },
      });
    },
    addBtn() {
      this.addFormVisible = true;
      this.getRoleList();
    },
    saveBtn() {
      let _this = this;

      _this.$refs["addForm"].validate((valid) => {
        if (valid) {
          _this.addUser(_this.addForm);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    bodyClass() {
      return bodyRowClassName();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    headerClass() {
      return headerRowClassName();
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.user-content {
  height: 100%;
  overflow: hidden;
  width: calc(100% - 40px);
  margin: 0 20px;
}
.user-form {
  margin: 1.5rem;
  /* padding-right:1rem; */
}
.userAdd-form {
  width: 90%;
  margin: auto;
}
.button-icon {
  display: inline-block;
  height: 16px;
  width: 16px;
  background-size: cover !important;
  vertical-align: sub;
  background: url("../../assets/images/userManage/icon-user.png");
}
.button-add {
  background: url("../../assets/images/agentManage/icon-Agent.png");
  background-position: 0px -102px;
}
.btn-style:hover .button-add {
  background-position: 0px -152px;
}
.dialogBtn-style:first-child {
  margin-right: 10px;
}
.dialogBtn-style {
  padding: 0 20px;
}
.button-restart {
  background-position: 0px -2px;
}
.button-detele {
  background-position: 0px -41px;
}
.button-assign {
  background-position: 0px -81px;
}
.user-button {
  color: #1e85e5;
}
.user-button:hover .button-restart {
  background-position: 0px -122px;
}
.user-button:hover .button-detele {
  background-position: 0px -161px;
}
.user-button:hover {
  color: #409eff;
}
.user-button:hover .button-assign {
  background-position: 0px -201px;
}
</style>