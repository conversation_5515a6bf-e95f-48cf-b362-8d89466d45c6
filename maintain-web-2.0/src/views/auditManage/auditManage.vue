<!-- 审计管理 -->
<template>
  <div class="audit-content">
    <el-row class="row-content">
      <!-- <el-col class="content-form"></el-col> -->
      <el-col :span="24" class="audit-table">
        <el-table
          class="table-border"
          :data="dataList"
          :row-class-name="bodyClass"
          :header-cell-style="tableHeader"
          :header-row-class-name="headerClass"
          height="100%"
          border
          v-loading="tableLoading"
        >
          <el-table-column
            prop="user"
            width="140"
            :formatter="formatUser"
            label="用户名称"
          ></el-table-column>
          <el-table-column
            prop="operateTime"
            width="145"
            label="操作时间"
            sortable
          ></el-table-column>
          <el-table-column prop="operate" label="操作记录"></el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24" class="table-page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="form.pn"
          :page-sizes="[20, 50, 100]"
          :page-size="form.ps"
          layout="total,sizes,prev,pager,next,jumper"
          :total="form.total"
          background
          popper-class="pagination-popper"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import {
  bodyRowClassName,
  tableHeaderStyle,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "audit-content",
  data() {
    return {
      pageTimer: null,
      tableLoading: false,

      dataList: [],
      form: {
        pn: 1, //当前页
        ps: 20, //当前每页条数
        total: 0, //总条数
      },
    };
  },
  methods: {
    init() {
      this.getDataList(false, this.form);
      this.requestInterval();
      this.getStatus();
    },

    //请求数据函数
    getDataList(IntervalLoading, data) {
      let _this = this;

      _this.tableLoading = IntervalLoading ? false : true;
      _this.$http
        .post("operate/record.json", _this.qs.stringify(data))
        .then((res) => {
          _this.tableLoading = false;
          if (res.data.code == 0) {
            _this.dataList = res.data.data.list;
            _this.form.total = res.data.data.total;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    requestInterval() {
      if (this.pageTimer) {
        clearInterval(this.pageTimer);
      } else {
        this.pageTimer = setInterval(() => {
          this.getDataList(true, this.form);
        }, 60000);
      }
    },

    //处理数据函数
    formatUser(row, cell, value) {
      value = value || "--";
      return value;
    },

    //功能函数
    handleSizeChange(val) {
      this.form.ps = val;
      this.getDataList(false, this.form);
    },
    handleCurrentChange(val) {
      this.form.pn = val;
      this.getDataList(false, this.form);
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
  },
  mounted() {
    this.init();
  },
  destroyed() {
    clearInterval(this.pageTimer);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.audit-content {
  height: 98%;
  width: 98%;
  margin: 1%;
}
.table-row {
  margin-top: 10px;
}
.title-row:not(:nth-child(1)) {
  margin-top: 30px;
}
.audit-table {
  height: calc(100% - 5rem);
  padding-bottom: 8px;
}
</style>