<template>
    <div class="home">
        <div class="head">
            <div class="head-name">
                <a >
                    <span class="head-sysName">平台管理</span>

                    <!-- <div class="icon-title version-icon"></div> -->
                    <div class="head-version1">
                        <!-- <span>V2.2.3</span> -->
                        <span class="head-icon"></span>
                    </div>
                </a>
            </div>
            <div class="head-menu" v-loading.fullscreen="fullscreenLoading">
                <el-menu
                    mode="horizontal"
                    background-color="#060533"
                    text-color="#adbed8"
                    :default-active="$route.path"
                    router
                >
                    <el-menu-item
                        v-for="item in menuOptions"
                        :key="item.value"
                        :index="String(item.path)"
                        :style="{ padding: menuOptions.length > 8 && '0 12px' }"
                    >
                        <template>
                            <!-- <div class="icon-menu" :style="getStyle(item)"></div> -->
                            <img :src="item.icon" class="img">
                            <span>{{ item.moduleName }}</span>
                            <div
                                class="nav-round"
                                :style="{
                                    background: item.color,
                                    right: menuOptions.length > 8 && '5px',
                                }"
                                v-if="item.color"
                            ></div>
                        </template>
                    </el-menu-item>
                </el-menu>
            </div>
            <div class="head-right">
                <div>
                    <a href="javascript:void(0);" class="userInfomation">
                        <span>{{ loginName }}</span>
                        <span>，欢迎您！</span>
                    </a>
                </div>
                <div v-if="isShow">
                    <a href="javascript:void(0);" title="检查更新" class="img-icon" @click="networkBtn">
                        <i :class="{ 'iconfont icon-upgrade': true, update: isUpdate }"></i>
                    </a>
                </div>
                <div>
                    <a
                        href="javascript:void(0);"
                        title="消息通知"
                        class="img-icon"
                        @click="messageFormVisible = true"
                    >
                        <div class="img-home menu-icon"></div>
                    </a>
                </div>
                <div>
                    <a href="javascript:void(0);" title="退出系统" class="img-icon" @click="logoutBtn">
                        <div class="img-quit menu-icon"></div>
                    </a>
                </div>
            </div>
        </div>
        <div class="home-content">
            <!-- <keep-alive>
        <router-view class="view-content" v-if="$route.meta.keepAlive"></router-view>
            </keep-alive>-->
            <keep-alive :include="pageCacheName">
                <router-view class="view-content"></router-view>
            </keep-alive>
        </div>
        <messageForm v-if="messageFormVisible" @returnMain="returnMain" :promptData="promptData"></messageForm>
        <webSocketSend v-if="socketShow" @socketReturn="closeDialog" :socketData="socketData"></webSocketSend>
    </div>
</template>

<script>
import router from "@/router";
import messageForm from "./systemMessage";
import webSocketSend from "@/components/webSocketSend.vue";
import { clearStore, getStore } from "@/utils/store.js";
import { mapMutations, mapGetters } from "vuex";
import Home from '@/assets/images/menu/home.png';
import Server from '@/assets/images/menu/server.png';
import Program from '@/assets/images/menu/program.png';
import Agent from '@/assets/images/menu/agent.png';
import User from '@/assets/images/menu/user.png';
import Shenji from '@/assets/images/menu/shenji.png'
export default {
    name: "Home",
    components: { messageForm, webSocketSend },
    data() {
        return {
            loginName: "",

            promptData: null,
            updateTimer: null,
            fullscreenLoading: false,
            messageFormVisible: false,
            socketShow: false,
            isUpdate: false,
            isShow: false,

            menuOptions: [],
            dataStatus: [],
            positionOptions: [
                {
                    moduleName: "系统首页",
                    icon: Home
                },
                {
                    moduleName: "Agent管理",
                    icon: Agent,
                },
                {
                    moduleName: "用户管理",
                    icon: User,
                },
                {
                    moduleName: "集群管理",
                    icon: User,
                },
                {
                    moduleName: "前端管理",
                    icon: User,
                },
                {
                    moduleName: "服务器管理",
                    icon: Server,
                },
                {
                    moduleName: "程序管理",
                    icon: Program,
                },
                {
                    moduleName: "审计管理",
                    icon: Shenji,
                },
                {
                    moduleName: "日志管理",
                    icon: User,
                },
                {
                    moduleName: "业务管理",
                    icon: User,
                },
                {
                    moduleName: "报告管理",
                    icon: User,
                },
                {
                    moduleName: "接口调用",
                    icon: User,
                }
            ]
        };
    },
    methods: {
        ...mapMutations(["saveMenuOptions"]),
        //获取数据
        getDataList() {
            let _this = this;

            if (
                this.$route.path == "/userManage" ||
                this.$route.path == "/index"
            ) {
                return;
            }
            _this.$http
                .get("home/monitor.json")
                .then(res => {
                    if (res.data.code == 0) {
                        this.dataStatus = res.data.data;
                        this.formatColor(this.dataStatus);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    // _this.erFn();
                    console.log(error);
                });
        },
        getCount() {
            let _this = this;

            if (
                this.$route.path == "/userManage" ||
                this.$route.path == "/index"
            ) {
                _this.$http
                    .get("note/count")
                    .then(res => {
                        if (res.data.code == 0) {
                            this.messageBtn(res.data.data);
                        }
                    })
                    .catch(error => {
                        // _this.erFn();
                        console.log(error);
                    });
            }
        },
        getUpdateStatus() {
            let _this = this;

            _this.$http
                .get("software/checkUpgradePackage")
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            this.isUpdate = res.data.data.checkResult;
                            this.isShow = res.data.data.isOpen;
                            this.isShow == false &&
                                clearInterval(this.updateTimer);
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    // _this.erFn();
                    console.log(error);
                });
        },
        //处理数据
        formatMenu() {
            this.loginName = getStore({ name: "operationName" });
            this.menuOptions = getStore({ name: "menuOptions" });

            for (let i in this.menuOptions) {
                for (let j in this.positionOptions) {
                    if (
                        this.menuOptions[i].moduleName ==
                        this.positionOptions[j].moduleName
                    ) {
                        this.menuOptions[i].icon = this.positionOptions[
                            j
                        ].icon;
                    }
                }
            }
        },
        formatColor(data) {
            for (let j in data) {
                $.each(data[j].status, (index, item) => {
                    if (item == "RED") {
                        data[j].color = "#f53749";
                        return false;
                    }
                    if (item == "YELLOW") {
                        data[j].color = "#fc833d";
                    }
                    data[j].color = data[j].color || "#21db8d";
                });
                for (let i in this.menuOptions) {
                    if (typeof this.menuOptions[i] == "object") {
                        if (
                            this.menuOptions[i].moduleName == data[j].moduleName
                        ) {
                            this.$set(
                                this.menuOptions[i],
                                "color",
                                data[j].color
                            );
                        }
                    }
                }
            }
            this.$set(this.menuOptions, this.menuOptions);
        },
        //功能函数
        logoutBtn() {
            this.$confirm("您确定要退出系统吗？", "提示", {
                closeOnClickModal: false,
                cancelButtonText: "关闭",
                confirmButtonText: "确定",
                confirmButtonClass: "confirm-success"
            })
                .then(() => {
                    clearStore({});
                    this.saveMenuOptions([]);
                    clearInterval(this.timer);
                    this.$router.push({
                        path: "/login"
                    });
                })
                .catch(error => {
                    console.log(error);
                });
        },
        closeDialog() {
            this.socketShow = false;
            this.getUpdateStatus();
        },
        networkBtn() {
            if (this.isUpdate) {
                this.$confirm(
                    "检测出有新的升级包，您确定要更新此升级包吗？",
                    "提示",
                    {
                        closeOnClickModal: false,
                        cancelButtonText: "关闭",
                        confirmButtonText: "确定",
                        confirmButtonClass: "confirm-success"
                    }
                )
                    .then(() => {
                        this.socketData = {
                            operateType: 4,
                            service: "software",
                            param: JSON.stringify({
                                upgradeType: 1,
                                content: ""
                            }),
                            title: "升级程序过程"
                        };
                        this.socketShow = true;
                    })
                    .catch(error => {
                        console.log(error);
                    });
            } else {
                this.msgFn("当前已是最新版本");
            }
        },
        versionBtn() {
            const { href } = this.$router.resolve({
                name: "versionDetail"
            });
            window.open(href, "_blank");
        },
        getStyle(item) {
            return "background-position:0px " + item.position;
        },
        returnMain() {
            this.promptData = null;
            this.messageFormVisible = false;
        },
        openMessage() {
            this.promptData = true;
            this.messageFormVisible = true;
        },
        messageBtn(data = 0) {
            const h = this.$createElement;
            this.$notify.info({
                title: "消息",
                dangerouslyUseHTMLString: true,
                message: h("p", [
                    h("span", "共有 " + data + " 条消息提示，点击"),
                    h(
                        "span",
                        {
                            on: {
                                click: this.openMessage
                            },
                            style: {
                                color: "#409EFF",
                                cursor: "pointer"
                            }
                        },
                        "此处"
                    ),
                    h("span", "查看")
                ])
            });
        }
    },
    computed: {
        ...mapGetters(["operationStatus", "timer", "cacheName"]),
        pageCacheName() {
            return this.cacheName;
        }
    },
    watch: {
        operationStatus: {
            handler(newVal, oldVal) {
                this.formatColor(this.operationStatus);
            },
            deep: true
        }
    },
    created() {
        this.formatMenu()
    },
    mounted() {
        this.getCount();
        this.getUpdateStatus();
        this.updateTimer = setInterval(() => {
            this.getUpdateStatus();
        }, 180000);
        // this.getDataList();
    },
    destroyed() {
        clearStore({});
        clearInterval(this.updateTimer);
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.home {
    height: 100%;
}
.head {
    height: 60px;
    background: #f4f4f4;
    width: 100%;
}
.head-name {
    float: left;
    width: 180px;
    height: 60px;
    font-size: 16px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none !important;
}
.head:after {
    clear: both;
    content: "";
}
.head-name a {
    display: inline-block;
    height: 100%;
    width: 100%;
    line-height: 60px;
    color: #fff;
    text-decoration: none;
    cursor: pointer;
    position: relative;
    text-align:center;
}
.head-name .head-sysName {
    letter-spacing: 2px;
    margin-left: 15px;
    font-size: 16px;
    color:#000000; 
    font-weight: bold;
}
.head-name .head-version {
    width: 37px;
    height: 13px;
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 12px;
    text-align: center;
    line-height: 11px;
    color: #fff;
    border-radius: 2px;
    border: 1px solid rgba(248, 82, 84, 0.7);
    color: rgba(248, 82, 84, 0.7);
}
.head-name .head-version .head-icon {
    position: absolute;
    width: 0;
    height: 0;
    border-right: 5px solid transparent;
    border-top: 5px solid rgba(248, 82, 84, 0.7);
    bottom: -5px;
    left: -1px;
}
.head-name .head-version .head-icon::before {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border-right: 3px solid transparent;
    border-top: 3px solid rgb(54, 72, 95);
    bottom: 3px;
    left: 1px;
}
.head-menu {
    float: left;
}
.icon-menu {
    display: inline-block;
    height: 24px;
    width: 24px;
    background-size: cover !important;
    background: url("../../assets/images/menu/icon-permission.png");
}
.img {
    
}
.icon-title {
    display: inline-block;
    height: 26px;
    width: 38px;
    background-size: cover !important;
    background: url("../../assets/images/menu/icon-menu.png");
}
.version-icon {
    background-position: 0px 85px;
}
.img-icon {
    display: flex;
    align-items: center;
    height: 60px;
}
.img-icon:before {
    content: "";
    display: inline-block;
    width: 2px;
    height: 16px;
    position: absolute;
    left: -3px;
    top: 22px;
    background: linear-gradient(to right, #283649 15%, #516480 100%);
    background: -webkit-linear-gradient(left, #283649 15%, #516480 100%);
    background: -o-linear-gradient(right, #283649 15%, #516480 100%);
    background: -ms-linear-gradient(right, #283649 15%, #516480 100%);
}
.menu-icon {
    width: 18px;
    height: 18px;
    background-size: cover !important;
    display: inline-block;
    background: url("../../assets/images/menu/menuTool.png");
}
.img-home {
    background-position: 0px -268px !important;
}
.img-quit {
    background-position: 0px -303px !important;
}

.el-menu-item {
    position: relative;
    z-index: 2;
    font-size: 12px;
    background-color: #f4f4f4 !important;
    color:#000000 !important;
    width: 115px;
}
.el-menu-item:before {
    content: "";
    position: absolute;
    z-index: -1;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background-color: #44a9ff !important;
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
    -webkit-transform-origin: 50% 0;
    transform-origin: 50% 0;
    -webkit-transition-property: transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    height: 60px;
}
.el-menu-item:after {
    content: "";
    display: inline-block;
    width: 2px;
    height: 30px;
    position: absolute;
    left: 0;
    top: 15px;
    /* background: linear-gradient(to right, #283649 15%, #516480 100%);
    background: -webkit-linear-gradient(left, #283649 15%, #516480 100%);
    background: -o-linear-gradient(right, #283649 15%, #516480 100%);
    background: -ms-linear-gradient(right, #283649 15%, #516480 100%); */
}
.el-menu-item:hover {
    color: #000000 !important;
}
.el-menu-item:hover::after {
    background: none;
}
/* .el-menu-item:hover::before {
    transform: scaleY(1);
    color: #000000;
} */
.el-menu-item.is-active {
    border-bottom: 1px solid #409eff;
    color: #000000 !important;
    background-color: #f4f4f4 !important;
}
.head-right {
    display:none;
    float: right;
    padding-right: 3px;
}
.head-right > div {
    display: inline-block;
}
.head-right a {
    color: #fff;
    padding: 0 7px;
    text-align: center;
    height: 60px;
    text-decoration: none;
    font-size: 14px;
    position: relative;
}
a.userInfomation {
    top: -3px;
}
.head-right > div > a > i：after {
    content: "";
    position: absolute;
    display: inline-block;
    width: 2px;
    height: 18px;
    background: linear-gradient(0deg, #283649 15%, #516480 100%);
    background: -webkit-linear-gradient(0deg, #283649 15%, #516480 100%);
    background: -o-linear-gradient(0deg, #283649 15%, #516480 100%);
    background: -ms-linear-gradient(0deg, #283649 15%, #516480 100%);
    right: -1px;
    top: 21px;
}
.home-content {
    margin: 8px;
    height: calc(100% - 76px);
    background-color: #f4f4f4;
    overflow: hidden;
    width: calc(100% - 16px);
}
.nav-round {
    width: 5px;
    height: 5px;
    border-radius: 100%;
    position: absolute;
    top: 19px;
    right: 11px;
}
.view-content {
    width: 100%;
    height: 100%;
}
a:focus {
    outline: 0;
}
.icon-upgrade {
    font-size: 18px;
    position: relative;
}
.icon-upgrade.update::after {
    content: "";
    width: 5px;
    height: 5px;
    border-radius: 100%;
    position: absolute;
    top: -3px;
    right: -5px;
    background: red;
}
</style>