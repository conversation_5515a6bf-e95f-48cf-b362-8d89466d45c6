<!-- 首页 -->
<template>
    <div class="index-content">
        <el-row :gutter="40" class="row-content" v-loading.fullscreen = 'fullscreenLoading'>
            <router-link
                :to='item.path' 
                v-for='item in indexOptions' 
                :key='item.value'>
                
                <el-col :span="colNum" class="module-col">
                    <div class="moduleDiv">
                        <el-row>
                            <el-col :span="22" :offset="1" class="module-name">
                                {{item.moduleName}}
                            </el-col>
                        </el-row>
                        <el-row>
                            <div class="module-content">
                                <el-col :span="12">
                                    <el-col>
                                        <div class='police-icon status-icon'></div>
                                        <div class="status-content">错误</div>
                                        <div :class="{'num-red':item.policeNum != 0}">{{item.policeNum}}</div>
                                    </el-col>
                                    <el-col class="middleText">
                                        <div class='alarm-icon status-icon'></div>
                                        <div class="status-content">警告</div>
                                        <div :class="{'num-yellow':item.alarmNum != 0}">{{item.alarmNum}}</div>
                                    </el-col>
                                    <el-col>
                                        <div class='good-icon status-icon'></div>
                                        <div class="status-content">良好</div>
                                        <div :class="{'num-green':item.goodNum != 0}">{{item.goodNum}}</div>
                                    </el-col>
                                </el-col>
                                <el-col :span="12">
                                    <div class='totalStatus-icon' :style="getStyle(item)"></div>
                                </el-col>
                            </div>
                        </el-row>
                    </div>
                </el-col>
            </router-link>
        </el-row>
    </div>
</template>

<script>
import { getStore } from "@/utils/store.js";
import { mapGetters, mapMutations } from "vuex";

export default {
    name: "index-content",
    data() {
        return {
            screenWidth: null,
            pageTimer: null,
            colNum: 6,

            fullscreenLoading: false,

            pathOptions: [],
            indexOptions: []
        };
    },
    methods: {
        init() {
            let _this = this;

            _this.screenWidth = document.body.clientWidth;
            _this.getDataList();
            _this.getStatus();
            _this.requestInterval();
            window.onresize = () => {
                return (() => {
                    _this.screenWidth = document.body.clientWidth;
                })();
            };
        },

        //请求数据函数
        getDataList() {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .get("home/monitor.json")
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        let data = res.data.data;
                        _this.formatIndexMenu(data);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        requestInterval() {
            if (this.pageTimer) {
                clearInterval(this.pageTimer);
            } else {
                this.pageTimer = setInterval(() => {
                    this.getDataList();
                }, 60000);
            }
        },

        //处理数据函数
        formatMenu() {
            this.pathOptions =
                this.menuOptions.length != 0
                    ? this.menuOptions
                    : getStore({ name: "menuOptions" });
        },
        formatIndexMenu(data) {
            let _this = this;
            let dataMenu = [];

            for (let i in data) {
                data[i].policeNum = 0;
                data[i].alarmNum = 0;
                data[i].goodNum = 0;
                for (let k in data[i].status) {
                    let item = data[i].status[k];

                    if (k != "/") {
                        switch (item) {
                            case "GREEN":
                                data[i].goodNum++;
                                break;
                            case "YELLOW":
                                data[i].alarmNum++;
                                break;
                            case "RED":
                                data[i].policeNum++;
                                break;
                        }
                    }
                }
                if (data[i].policeNum != 0) {
                    data[i].position = "-16px";
                } else if (data[i].alarmNum != 0) {
                    data[i].position = "-128px";
                } else {
                    data[i].position = "-248px";
                }
                for (let j in _this.pathOptions) {
                    if (
                        typeof _this.pathOptions[j] == "object" &&
                        data[i].moduleName == _this.pathOptions[j].moduleName
                    ) {
                        data[i].path = _this.pathOptions[j].path;
                        dataMenu.push(data[i]);
                    }
                }
            }
            this.indexOptions = dataMenu;
        },

        //功能函数
        getStyle(item) {
            return "background-position:0px " + item.position;
        }
    },
    watch: {
        screenWidth(val) {
            this.colNum = val > 1585 ? 6 : 8;
        }
    },
    computed: {
        ...mapGetters(["menuOptions"])
    },
    created() {
        this.formatMenu();
    },
    mounted() {
        this.init();
    },
    destroyed() {
        clearInterval(this.pageTimer);
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.index-content {
    width: calc(100% - 2%);
    margin: 0 1%;
    height: 100%;
}
.module-col {
    margin-top: 40px;
}
.moduleDiv {
    background-color: #ffffff;
    text-align: center;
    cursor: pointer;
    border: 1px solid #e4e8ec;
}
.row-content {
    padding: 6px 16px;
}
.moduleDiv:hover {
    box-shadow: 0 5px 10px 0px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
    transition: 0.2s linear all;
}
.module-name {
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #dce3e6;
    text-align: left;
    padding-left: 5px;
    font-size: 14px;
    font-weight: bold;
    color: #203e66;
}
.module-content {
    height: 125px;
    display: flex;
    align-items: center;
    color: #203e66;
    font-weight: bold;
}
.module-content > .el-col:first-child i {
    font-size: 65px;
    color: red;
}
.module-content > .el-col:nth-child(2) {
    height: 80px;
    text-align: left;
    font-size: 14px;
    color: #8e98b2;
}
.status-content + div {
    font-weight: 500;
    display: inline-block;
    margin-left: 25px;
    width: 30px;
}
.status-content {
    min-width: 25px;
    display: inline-block;
}
.middleText {
    margin: 10px 0;
}
.status-icon {
    display: inline-block;
    height: 18px;
    width: 18px;
    background-size: cover !important;
    background: url("../../assets/images/index/icon-index.png");
    vertical-align: sub;
}
.police-icon {
    background-position: 0px -11px;
}
.alarm-icon {
    background-position: 0px -61px;
}
.good-icon {
    background-position: 0px -120px;
}
.totalStatus-icon {
    display: inline-block;
    height: 85px;
    width: 142px;
    background-size: cover !important;
    background: url("../../assets/images/index/icon-totalStatus.png");
}
</style>