<template>
  <div class="systemMessage" v-loading.fullscreen="messageLoading">
    <el-dialog
      title="消息通知"
      :visible.sync="messageFormVisible"
      width="800px"
      top="10vh"
      class="dialog-border"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-row class="search-row">
        <el-col :span="8">
          <span>消息状态：</span>
          <el-select
            v-model="searchForm.remind"
            placeholder="请选择"
            class="messageSelect"
            clearable
          >
            <el-option
              v-for="(item, index) in promptOption"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-input
            placeholder="请输入内容"
            v-model="searchForm.inputVal"
            @keyup.enter.native="searchBtn"
          >
            <el-select
              v-model="searchForm.selectVal"
              slot="prepend"
              placeholder="请选择"
              class="searchSelect"
              @change="handelChange"
              clearable
            >
              <el-option
                v-for="(item, index) in selectOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <!-- <el-button slot="append" icon='el-icon-search' @click="searchBtn"></el-button> -->
          </el-input>
        </el-col>
        <el-col :span="8" class="search-button">
          <el-button class="btn-addForm" @click.prevent="searchBtn">
            <div class="button-icon button-search"></div>
            <span>查询</span>
          </el-button>
          <el-button @click="addFormVisible = true" class="btn-addForm">
            <div class="button-icon button-add"></div>
            <span>添加</span>
          </el-button>
        </el-col>
      </el-row>
      <el-row type="flex" class="row-bg message-row">
        <el-form
          label-width="100px"
          :model="messageForm"
          ref="messageForm"
          class="message-form"
          v-if="messageData.length != 0"
        >
          <el-col
            :span="24"
            v-for="(item, index) in messageData"
            :key="index"
            class="message-col"
          >
            <div>
              <el-row class="header-row">
                <el-col :span="16">
                  <span class="title">{{ item.title }}</span>
                  <span class="author">{{ item.author }}</span>
                </el-col>
                <el-col :span="8" class="modifyTime">
                  <span
                    class="prompt iconfont icon-prompt"
                    :style="{ color: item.remind ? '#e6a23c' : '#ccc' }"
                    :title="item.remind ? '消息提示' : '消息不提示'"
                    @click="promptClick(item)"
                  >
                  </span>
                  <span>{{ item.modifyTime }}</span>
                </el-col>
              </el-row>
              <div class="content-row" :class="{ contentShow: item.editShow }">
                <div v-if="item.editShow">
                  <p
                    class="name-wrapper"
                    :class="{ wrapperShow: item.buttonShow && !item.exChange }"
                  >
                    {{ item.content }}
                  </p>
                  <div class="unfold" v-if="item.buttonShow">
                    <el-button
                      type="text"
                      @click="item.exChange = !item.exChange"
                      >{{ item.exChange ? "收起" : "展开" }}</el-button
                    >
                  </div>
                </div>
                <el-input
                  v-else
                  v-model="item.inputVal"
                  :autosize="true"
                  :show-word-limit="true"
                  maxlength="1024"
                  type="textarea"
                >
                </el-input>
              </div>
              <div class="button-row" v-if="item.editShow">
                <el-button type="text" @click="opreateBtn(item, '编辑')"
                  >编辑</el-button
                >
                <el-button type="text" @click="opreateBtn(item, '删除')"
                  >删除</el-button
                >
              </div>
              <div class="button-row" v-else>
                <el-button type="text" @click="opreateBtn(item, '确定')"
                  >确定</el-button
                >
                <el-button type="text" @click="opreateBtn(item, '取消')"
                  >取消</el-button
                >
              </div>
            </div>
          </el-col>
        </el-form>
        <div v-else class="noData-div">暂无数据</div>
      </el-row>

      <el-dialog
        append-to-body
        title="添加备注"
        :close-on-click-modal="false"
        @close="resetAddForm"
        :visible.sync="addFormVisible"
      >
        <el-form
          label-width="100px"
          :model="addForm"
          :rules="rules"
          ref="addForm"
          class="add-form"
        >
          <el-col :span="24">
            <el-form-item label="标题:" prop="title">
              <el-input v-model="addForm.title"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="作者:" prop="author">
              <el-input v-model="addForm.author"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容:" prop="content">
              <el-input
                type="textarea"
                v-model="addForm.content"
                :show-word-limit="true"
                maxlength="1024"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否提示:">
              <el-radio-group v-model="addForm.remind">
                <el-radio :label="true">提示</el-radio>
                <el-radio :label="false">不提示</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-form>
        <div slot="footer" class="dialog-footer footer-button">
          <button @click.prevent="addFormVisible = false" class="btn-style">
            关闭
          </button>
          <button @click.prevent="saveBtn" class="btn-style primary-btn">
            确定
          </button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "systemMessage",
  props: ["promptData"],
  data() {
    return {
      messageFormVisible: true,
      messageLoading: false,
      addFormVisible: false,

      messageData: [],
      selectOption: [
        {
          label: "标题",
          value: "title",
        },
        {
          label: "作者",
          value: "author",
        },
        {
          label: "内容",
          value: "content",
        },
      ],
      promptOption: [
        {
          label: "提示",
          value: true,
        },
        {
          label: "不提示",
          value: false,
        },
      ],

      messageForm: {},
      searchForm: {
        inputVal: "",
        selectVal: "",
        remind: null,
      },
      form: {},
      addForm: {
        title: "",
        author: "",
        content: "",
        remind: true,
      },
      rules: {
        title: [
          {
            required: true,
            message: "请输入标题",
          },
        ],
        author: [
          {
            required: true,
            message: "请输入作者",
          },
        ],
        content: [
          {
            required: true,
            message: "请输入内容",
          },
        ],
      },
    };
  },
  methods: {
    init() {
      if (this.promptData) {
        this.form.remind = this.promptData;
        this.searchForm.remind = this.promptData;
      }
      this.getDataList(this.form);
    },

    //请求数据函数
    getDataList(data) {
      let _this = this;

      _this.messageLoading = true;
      _this.$http
        .post("note/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.messageLoading = false;
          if (res.data.code == 0) {
            res.data.data && _this.formatData(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.messageLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    changeData(url, data, addShow) {
      let _this = this;

      _this.messageLoading = true;
      _this.$http
        .post(url, _this.qs.stringify(data))
        .then((res) => {
          _this.messageLoading = false;
          if (res.data.code == 0) {
            _this.suFn("操作成功");
            addShow && (_this.addFormVisible = false);
            _this.getDataList(this.form);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.messageLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //处理数据函数
    formatData(data) {
      data.forEach((item, index) => {
        item.inputVal = "";
        item.editShow = true;
        item.buttonShow = false;
        item.exChange = false;
      });
      this.messageData = data;
      this.$nextTick(() => {
        this.getRem();
      });
    },

    //功能函数
    getRem() {
      let txtDom;

      if (this.$refs["messageForm"]) {
        txtDom = this.$refs["messageForm"].$el.getElementsByTagName("p");

        $.each(txtDom, (index, item) => {
          if (item.offsetHeight > 58) {
            this.$set(this.messageData[index], "buttonShow", true);
          }
        });
      }
    },
    resetForm() {
      this.$emit("returnMain");
    },
    resetAddForm() {
      this.$refs["addForm"].resetFields();
    },
    opreateBtn(item, name) {
      let data = {},
        url = "";

      switch (name) {
        case "编辑":
          item.editShow = false;
          item.inputVal = item.content;
          break;
        case "取消":
          item.editShow = true;
          item.inputVal = "";
          break;
        case "确定":
          data = {
            id: item.id,
            author: item.author,
            content: item.inputVal,
            title: item.title,
            remind: item.remind,
          };
          url = "note/updateNote";
          this.changeData(url, data);
          break;
        case "删除":
          data = {
            id: item.id,
          };
          url = "note/deleteNote";
          this.changeData(url, data);
          break;
      }
    },
    handelChange() {
      this.searchForm.inputVal = "";
      this.form = {};
    },
    searchBtn() {
      if (this.searchForm.selectVal) {
        this.form[this.searchForm.selectVal] = this.searchForm.inputVal;
      }
      this.form.remind = this.searchForm.remind;
      this.getDataList(this.form);
    },
    saveBtn() {
      this.$refs["addForm"].validate((valid) => {
        if (valid) {
          let url = "note/addNote";
          this.changeData(url, this.addForm, true);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    promptClick(item) {
      let message = item.remind ? "不提示" : "提示";
      let data = { ...item };

      this.$confirm("您确定要将改消息设置为" + message + "吗？", "提示", {
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          let url = "note/updateNote";

          data.remind = !data.remind;
          this.changeData(url, data);
        })
        .catch(() => {});
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style scoped>
.message-row {
  max-height: 450px;
  overflow-y: auto;
  min-height: 150px;
  padding: 0 10px;
  position: relative;
}
.search-row {
  /* height: 200px; */
  margin-bottom: 30px;
  padding: 0 25px;
}
.search-row .searchSelect {
  width: 95px;
}
.search-row .messageSelect {
  width: 140px;
}
.search-row .search-button {
  text-align: right;
}
.message-col {
  /* border: 1px solid #ebeaea; */
  padding: 10px 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  /* color:#666; */
  color: #203e66;
  position: relative;
}
.message-col:after {
  position: absolute;
  content: "";
  display: inline-block;
  width: 100%;
  border-bottom: 1px dashed #bfbfbf;
  bottom: 0;
  left: 0;
}
.popoverSpan {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  line-height: 18px;
}
.button-row {
  text-align: right;
}
.button-row button + button {
  margin-left: 0;
}
.message-form {
  width: 100%;
}
.message-form .title {
  font-weight: bold;
  padding: 0px 10px;
  height: 22px;
  line-height: 22px;
  border-radius: 10px;
  background: #e3f2ff;
  color: #2093ff;
  display: inline-block;
  text-align: center;
  margin-right: 20px;
}
.message-form .author {
  margin-left: 10px;
  color: black;
  font-weight: bold;
}
.message-form .content-row {
  margin: 10px 0;
}
.message-form .contentShow {
  background: #f2f4f5;
  border: 1px solid #e7e9f0;
  padding: 10px;
}
.message-form .modifyTime {
  text-align: right;
  padding-right: 10px;
  height: 22px;
  line-height: 22px;
}
.add-form {
  min-height: 180px;
}
.popoverContent {
  max-width: 700px;
  color: #203e66;
  line-height: 20px;
}
.btn-style:first-child {
  margin-right: 10px;
}
.btn-style {
  padding: 0 20px;
}
.button-icon {
  background: url("../../assets/images/agentManage/icon-Agent.png");
  display: inline-block;
  height: 16px;
  width: 16px;
  background-size: cover !important;
  vertical-align: sub;
}
.button-add {
  background-position: 0px -102px;
}
.btn-addForm:hover .button-add {
  background-position: 0px -152px;
}
.button-search {
  background-position: 0px 129px;
}
.btn-addForm:hover .button-search {
  background-position: 0px 89px;
}
.prompt {
  cursor: pointer;
}
.content-row .name-wrapper {
  white-space: pre-line;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 18px;
  word-wrap: break-word;
}
.content-row .wrapperShow {
  -webkit-line-clamp: 3;
}
.content-row .unfold {
  text-align: right;
}
</style>