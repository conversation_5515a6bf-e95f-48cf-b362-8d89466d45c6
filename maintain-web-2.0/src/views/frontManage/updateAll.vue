<!-- 前端节点升级 -->
<template>
    <div class="node-dialog">
        <el-dialog 
            title="前端节点升级" 
            :visible.sync="nodeFormVisible"
            width="700px" 
            @close="resetNode" 
            :close-on-click-modal="false"
            class="dialog-border">
            <el-row class="row-bg path-row">
                <div class="path-title">
                    <p>1、请选择在{{nodeData.packagePath}}下需要升级的升级包：</p>
                </div>
                <div class="path-content">
                    <el-radio-group v-model="packageName">
                        <el-radio 
                            v-for="(item, index) in nodeData.packageOption" 
                            :key="index" :label="item"
                            class="path-radio">{{item}}
                        </el-radio>
                    </el-radio-group>
                </div>
            </el-row>
            <el-row class="row-bg node-row">
                <div class="node-title">
                    <p>2、请选择需要升级的前端节点：</p>
                </div>
                <div class="node-header">
                    <el-checkbox 
                        :indeterminate="isIndeterminate" 
                        v-model="checkAll"
                        @change="handelCheckAllChange">全选</el-checkbox>
                </div>
                <div class="node-content">
                    <el-checkbox-group v-model="checkedArr" @change="handelCheckChange">
                        <el-col 
                            :span="12"
                            v-for="(item,index) in nodeData.nodeOption" 
                            :key="index" 
                            class="nodeCheck">
                            <el-checkbox 
                                :label="item.apKey">
                                <span>{{item.name || '未命名'}}</span>
                                <span>{{'(版本号：' + item.version + ')'}}</span>
                            </el-checkbox>
                        </el-col>
                    </el-checkbox-group>
                </div>
            </el-row>
            
            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="nodeFormVisible = false" class="btn-style">关闭</button>
                <button @click.prevent="saveBtn"  class="btn-style primary-btn">确定</button>
            </div>
        </el-dialog>
        <webSocketSend v-if='socketShow' @socketReturn='closeDialog' :socketData='socketData'></webSocketSend> 
    </div>
</template>

<script>
import webSocketSend from "@/components/webSocketSend.vue";
import getPermission from "@/utils/permissions.js";

export default {
    name: "node-dialog",
    props: { nodeData: Object },
    components: { webSocketSend },
    data() {
        return {
            nodeFormVisible: true,
            isIndeterminate: false,
            checkAll: false,
            socketShow: false,
            packageName: "",

            checkedOption: [],
            checkedArr: [],

            socketData: {}
        };
    },
    methods: {
        init() {
            this.initData();
        },
        //请求数据函数

        //功能函数
        initData() {
            this.packageName = this.nodeData.packageOption[0] || "";
            this.nodeData.nodeOption.forEach(item => {
                this.checkedOption.push(item.apKey);
            });
        },
        handelCheckAllChange(val) {
            this.checkedArr = val ? this.checkedOption : [];
            this.isIndeterminate = false;
        },
        handelCheckChange(value) {
            let checkedConut = value.length,
                nodeLength = this.nodeData.nodeOption.length;

            this.checkAll = checkedConut === nodeLength;
            this.isIndeterminate =
                checkedConut > 0 && checkedConut < nodeLength;
        },
        resetLog() {
            this.$emit("returnMain");
        },
        saveBtn() {
            let data = {
                apKey: this.checkedArr.join(","),
                packageName: this.packageName
            };
            if (this.checkedArr.length == 0) {
                this.waFn("请先选择需要升级的前端节点");
                return;
            }
            this.socketData = {
                operateType: 8,
                param: JSON.stringify(data),
                service: "frontDevice",
                title: "前端节点升级过程"
            };
            this.socketShow = true;
        },
        closeDialog(val) {
            this.socketData = {};
            this.socketShow = false;
            val === "SUCCESS" && this.resetNode(val);
        },
        resetNode() {
            this.nodeFormVisible = false;
            this.$emit("returnMain");
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.node-row,
.path-row {
    padding: 0 10px;
}
.node-title p,
.path-title p {
    color: #203e66;
    font-size: 14px;
    margin-bottom: 15px;
}
.path-content {
    min-height: 50px;
    max-height: 200px;
    overflow: auto;
    margin: 10px 0;
}
.node-content {
    max-height: 380px;
    overflow: auto;
}
.nodeCheck {
    overflow: hidden;
    text-overflow: ellipsis;
    /* padding: 0 8px; */
}
.node-row /deep/ .el-checkbox {
    margin-bottom: 10px;
}
.path-radio {
    margin-bottom: 5px;
}
.btn-style {
    padding: 0 20px;
}
.btn-style:first-child {
    margin-right: 10px;
}
</style>