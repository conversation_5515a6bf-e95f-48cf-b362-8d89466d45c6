<!-- 前端列表详情 -->
<template>
    <div class="front-detail" v-loading.fullscreen = 'fullscreenLoading'>
        <div class="detail-content">
            <el-row>
                <el-col :span="12">
                    <div class="top-title">前端列表详情</div>
                </el-col>
                <el-col :span="12" class="return-col">
                    <button @click.prevent="returnWeb" class="btn-return btn-style">
                        <div class="button-return"></div>
                        <span>返回</span>
                    </button>
                </el-col> 
            </el-row>
            <el-row class="content-row">
                <el-row class='network-row'>
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">资源占用信息</div>
                        </div>
                        <el-row class="table-row">
                            <el-table 
                                :data="resourceList" 
                                class="resourceTable" 
                                :cell-style="resourceAlarm" 
                                :header-cell-style="tableHeader" 
                                :header-row-class-name="headerClass">

                                <el-table-column prop="cpuAvg" label="CPU平均值" class-name="cpuAvg"></el-table-column>
                                <el-table-column prop="cpuTop" label="CPU峰值" class-name="cpuTop"></el-table-column>
                                <el-table-column prop="usedMemory" label="内存使用" class-name="usedMemory" :formatter="currencyCapacity"></el-table-column>
                                <el-table-column prop="totalMemory" label="内存总大小" class-name="totalMemory" :formatter="currencyCapacity"></el-table-column>
                                <el-table-column prop="temp" label="温度" class-name="temp">
                                    <template slot-scope="scope">
                                        <span>{{scope.row.temp && scope.row.temp+'℃'}}</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-row>
                    </el-row>
                </el-row>

                <el-row class="disk-row">
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">硬盘信息</div>
                        </div>
                         <el-row class="table-row">
                            <el-table 
                                :data="diskList" 
                                :cell-style="cellStyle" 
                                :row-class-name="tableRowClassName" 
                                :header-cell-style="tableHeader" 
                                :header-row-class-name="headerClass">

                                <el-table-column prop="name" label="名称" width="180"></el-table-column>
                                <el-table-column prop="path" label="挂载路径"></el-table-column>
                                <el-table-column prop="total" label="总空间大小" :formatter="currencyCapacity"></el-table-column>
                                <el-table-column prop="used" label="已使用空间" :formatter="currencyCapacity"></el-table-column>
                                <el-table-column prop="free" label="剩余空间" :formatter="currencyCapacity"></el-table-column>
                                <el-table-column prop="usedRate" label="使用率"></el-table-column>
                            </el-table>
                        </el-row>
                    </el-row>
                </el-row>
                
                <el-row class='procedure-row'>
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">程序详细信息</div>
                        </div>
                        <el-row class="table-row">
                            <el-table 
                                :data="procedureList" 
                                class="procedureTable" 
                                :cell-style="cellStyle" 
                                :header-cell-style="tableHeader" 
                                :header-row-class-name="headerClass">

                                <el-table-column prop="reportTime" label="数据上报时间" class-name="reportTime">
                                    <template slot-scope="scope">
                                        <span>{{scope.row.reportTime || '--'}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="version" label="前端版本" class-name="version"></el-table-column>
                                <el-table-column prop="flowSpeed" label="流量大小" class-name="flowSpeed"></el-table-column>
                                <el-table-column prop="softLostRatio" label="程序丢包率" class-name="softLostRatio">
                                    <template slot-scope="scope">
                                        <span>{{scope.row.softLostRatio && Number(scope.row.softLostRatio)*100 + '%'}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="mirrorTcpLostRatio" label="镜像丢失率" class-name="mirrorTcpLostRatio">
                                    <template slot-scope="scope">
                                        <span>{{scope.row.mirrorTcpLostRatio && Number(scope.row.mirrorTcpLostRatio)*100 + '%'}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="netBlockRuleList" label="网络阻断规则" class-name="netBlockRuleList overflowColumn">
                                    <template slot-scope="scope">
                                        <el-popover 
                                            :trigger="scope.row.netBlockRuleList ? 'hover' : 'manual'" 
                                            placement="top-start" 
                                            width="300" >
                                            <p class="popoverContent">{{scope.row.netBlockRuleList}}</p>
                                            <div 
                                                slot="reference" 
                                                class="startParam-popover">
                                                {{scope.row.netBlockRuleList || '--'}}
                                            </div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-row>
                    </el-row>
                </el-row>
                <el-row class='frontCount-row'>
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">入库信息</div>
                        </div>
                        <el-row type='flex' class="count-form">
                            <el-col :span="24" class="form-search">
                                <el-col :span="21">
                                    <span>监控时间:</span>
                                    <el-date-picker
                                        v-model="fontCountForm.monitorTime"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholde="开始日期"
                                        end-placeholde="结束日期"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        @change="frontChange"
                                        :default-time="['00:00:00','23:59:59']">
                                    </el-date-picker>
                                </el-col>
                                <el-col :span="3" class="button-col">
                                    <button class="btn-style" @click="frontSearchBtn">
                                        <div class="button-form button-search"></div>
                                        <span>查询</span>
                                    </button>
                                </el-col>
                            </el-col>
                        </el-row>
                        <el-row class="table-row">
                            <el-table 
                                :data="frontCountList" 
                                class="procedureTable" 
                                :cell-style="cellStyle" 
                                :header-cell-style="tableHeader" 
                                :header-row-class-name="headerClass"
                                v-loading='fontCountLoading'>

                                <el-table-column prop="apKey" label="节点编号" class-name="apKey"></el-table-column>
                                <el-table-column prop="count" label="入库数量" class-name="count"></el-table-column>
                                <el-table-column prop="monitorTime" label="监控时间" class-name="monitorTime"></el-table-column>
                            </el-table>
                        </el-row>
                    </el-row>
                </el-row>
            </el-row>
        </div>
    </div>
</template>

<script>
import { headerRowClassName, bodyRowClassName } from "@/utils/tableStyle.js";
import { parseTime } from "@/utils/index.js";

export default {
    name: "front-detail",
    data() {
        return {
            pageTimer: null,
            fullscreenLoading: false,
            fontCountLoading: false,

            procedureList: [],
            diskList: [],
            resourceList: [],
            frontCountList: [],

            twoRow: {},
            alarmTypeMap: {},
            fontCountForm: {
                apKey: "",
                startTime: "",
                endTime: "",
                monitorTime: []
            }
        };
    },
    methods: {
        init() {
            this.twoRow = this.$route.query;

            this.getDataList(false, this.twoRow);
            this.getfontCount();
            this.requestInterval();
        },

        //请求数据函数
        getDataList(IntervalLoading, data) {
            let _this = this;

            _this.fullscreenLoading = IntervalLoading ? false : true;
            _this.$http
                .post("front-device/detail.json", _this.qs.stringify(data))
                .then(res => {
                    let dataList = [];
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            dataList.push(res.data.data);
                            _this.procedureList = dataList;
                            _this.resourceList = dataList;
                            _this.diskList = res.data.data.diskInfos;
                            _this.alarmTypeMap = res.data.data.alarmTypeMap;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getfontCount() {
            let _this = this;

            _this.fontCountForm.apKey = _this.twoRow.apKey;
            _this.fontCountLoading = true;
            _this.$http
                .post(
                    "front-device/frontCount",
                    _this.qs.stringify(_this.fontCountForm)
                )
                .then(res => {
                    let dataList = [];
                    _this.fontCountLoading = false;
                    if (res.data.code == 0) {
                        this.frontCountList = [...res.data.data];
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fontCountLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        requestInterval() {
            if (this.pageTimer) {
                clearInterval(this.pageTimer);
            } else {
                this.pageTimer = setInterval(() => {
                    this.getDataList(true, this.twoRow);
                }, 60000);
            }
        },

        //处理数据函数
        currencyCapacity(row, column, value) {
            if (value && value != "未知") {
                value = Number(value);
                row.usedMemory && row.totalMemory && (value = value * 1024);

                if (value > 0 && value < 1024) {
                    value = value + "KB";
                } else if (value >= 1024 && value < 1048576) {
                    value = (value / 1024).toFixed(2) + "MB";
                } else if (value >= 1048576 && value < 1073741824) {
                    value = (value / 1048576).toFixed(2) + "GB";
                } else if (value >= 1073741824) {
                    value = (value / 1073741824).toFixed(2) + "TB";
                }
                return value;
            } else {
                return "-";
            }
        },
        initAlarmContent(className, columnIndex) {
            let alarmArr = [],
                alarmContent = "",
                alarmDom = "",
                _this = this;

            _this.$nextTick(() => {
                alarmDom = $(className)
                    .find("tr.el-table__row td")
                    .eq(columnIndex);
                alarmArr = alarmDom.attr("class").split(" ");
                alarmContent = alarmArr[alarmArr.length - 1];

                $.each(_this.alarmTypeMap, (index, item) => {
                    switch (item) {
                        case "GREEN":
                            item = "#25ce88";
                            break;
                        case "YELLOW":
                            item = "#fb843b";
                            break;
                        case "RED":
                            item = "#f93846";
                            break;
                    }
                    if (alarmContent == index) {
                        alarmDom.css("color", item);
                    }
                });
            });
        },

        //功能函数
        returnWeb() {
            this.$router.go(-1);
        },
        frontSearchBtn() {
            this.getfontCount();
        },
        frontChange(val) {
            if (val) {
                this.fontCountForm.startTime = val[0];
                this.fontCountForm.endTime = val[1];
            } else {
                this.fontCountForm.startTime = "";
                this.fontCountForm.endTime = "";
            }
        },
        tableHeader() {
            return "border:0;";
        },
        cellStyle({ columnIndex }) {
            columnIndex != undefined &&
                this.initAlarmContent(".procedureTable", columnIndex);
            return "border:0;";
        },
        headerClass() {
            return headerRowClassName();
        },
        bodyClass() {
            return bodyRowClassName();
        },
        tableRowClassName({ row, rowIndex }) {
            let value = "";

            switch (row.diskStatus) {
                case "RED":
                    value = "warning-row";
                    break;
                case "YELLOW":
                    value = "alarm-row";
                    break;
            }
            return value;
        },
        resourceAlarm({ columnIndex }) {
            columnIndex != undefined &&
                this.initAlarmContent(".resourceTable", columnIndex);
        }
    },
    created() {
        this.fontCountForm.startTime = parseTime(
            new Date(),
            "{y}-{m}-{d} 00:00:00"
        );
        this.fontCountForm.endTime = parseTime(
            new Date(),
            "{y}-{m}-{d} 23:59:59"
        );
        this.fontCountForm.monitorTime.push(this.fontCountForm.startTime);
        this.fontCountForm.monitorTime.push(this.fontCountForm.endTime);
    },
    mounted() {
        this.init();
    },
    destroyed() {
        clearInterval(this.pageTimer);
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.front-detail {
    height: 100%;
    width: 100%;
}
.detail-content {
    width: 98%;
    margin: 0 1% 1%;
    height: 100%;
}
.top-title {
    text-align: left;
    text-indent: 15px;
    color: #1e85e6;
    font-size: 15px;
    font-weight: bold;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    margin-top: 10px;
}
.content-row {
    width: 100%;
    height: calc(100% - 60px);
    background-color: #ffffff;
    border: 1px solid #e2e2e2;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
    overflow: auto;
}
.host-content {
    padding: 0px 15px;
}
.host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.disk-row {
    margin: 0 10px;
}
.network-row {
    margin: 10px 10px 0 10px;
}
.procedure-row,
.frontCount-row {
    margin: 0 10px;
}
.table-row {
    padding: 40px 40px;
}
.return-col {
    text-align: right;
    padding-right: 20px;
    height: 40px;
    line-height: 40px;
    margin-top: 10px;
}
.btn-return {
    background-color: #1e85e6;
    color: #fff;
    padding-left: 5px;
}
.button-return {
    display: inline-block;
    width: 25px;
    height: 16px;
    background-size: cover !important;
    vertical-align: sub;
    background: url("../../assets/images/hardWare/icon-detail.png");
    background-position: 0 26px;
}
.btn-return:hover {
    background-color: #2d91ef;
    color: #fff;
}
.button-form {
    background: url("../../assets/images/agentManage/icon-Agent.png");
    display: inline-block;
    height: 16px;
    width: 16px;
    background-size: cover !important;
    vertical-align: sub;
}
.button-search {
    background-position: 0px 129px;
}
.btn-style:hover .button-search {
    background-position: 0px 89px;
}
.count-form {
    padding: 20px 40px 0;
    text-align: right;
}
.frontCount-row .table-row {
    padding-top: 20px;
}
</style>