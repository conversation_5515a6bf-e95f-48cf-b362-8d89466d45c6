<!-- 前端告警信息忽略设置 -->
<template>
    <div class="node-dialog" v-loading.fullscreen = 'fullscreenLoading'>
        <el-dialog 
            title="忽略异常设置" 
            v-dialogDrag
            :visible.sync="nodeFormVisible"
            width="700px" 
            @close="resetNode" 
            :close-on-click-modal="false"
            class="dialog-border">

            <el-collapse accordion>
                <el-collapse-item
                    v-for="(item, index) in ignoreForm.ignoreExceptionVoList"
                    :key="index"
                    class="ignore-item">
                    <template slot="title">
                        <span class="collapse-text">{{ignoreForm.description[index]}}</span>
                        <span v-if="item.isIgnore">（已忽略）</span>
                    </template>
                    <el-row>
                        <el-form 
                            label-width="100px" 
                            :model="ignoreForm"
                            ref="ignoreForm" class="ignore-form">
                            <el-col :span="24">
                                <el-form-item label="到期时间:" prop="reportTime">
                                    <el-date-picker 
                                        v-model="item.expireTime"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        type="datetime" 
                                        :picker-options="pickersOptions"
                                        :clearable="false">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="是否忽略:">
                                    <el-radio-group v-model="item.isIgnore">
                                        <el-radio :label="true">忽略</el-radio>
                                        <el-radio :label="false">不忽略</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="备注:" :required='item.isIgnore'>
                                    <el-input type="textarea" v-model="item.desc"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-form>
                    </el-row>
                </el-collapse-item>
            </el-collapse>
            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="nodeFormVisible = false" class="btn-style">关闭</button>
                <button @click.prevent="saveBtn"  class="btn-style primary-btn">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";

export default {
    name: "node-dialog",
    props: { ignoreData: Object },
    data() {
        return {
            nodeFormVisible: true,
            fullscreenLoading: false,

            ignoreForm: {
                ignoreExceptionVoList: []
            },
            pickersOptions: {
                disabledDate(date) {
                    return date.getTime() < Date.now() - 8.64e7;
                }
            }
        };
    },
    methods: {
        init() {
            this.initData();
        },
        //请求数据函数
        saveList(data = {}) {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .post("front-device/setIgnoreException", data)
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        _this.resetNode("SUCCESS");
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //功能函数
        initData() {
            this.ignoreForm = { ...this.ignoreData };
            console.log(this.ignoreForm);
        },
        saveBtn() {
            let data = { ...this.ignoreForm },
                isCheck = true,
                list = this.ignoreForm.ignoreExceptionVoList;

            list &&
                list.forEach((item, index) => {
                    if (item.isIgnore) {
                        if (!item.desc) {
                            this.waFn("忽略项备注为必填项");
                            isCheck = false;
                            return;
                        }
                    }
                });
            if (isCheck) {
                delete data["description"];
                this.saveList(data);
            }
        },
        resetNode(val) {
            this.nodeFormVisible = false;
            this.$emit("returnMain", val);
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.ignore-form {
    margin-top: 10px;
}
.collapse-text {
    margin-left: 15px;
}
.ignore-item {
    margin-bottom: 10px;
}
.btn-style {
    padding: 0 20px;
}
.btn-style:first-child {
    margin-right: 10px;
}
</style>