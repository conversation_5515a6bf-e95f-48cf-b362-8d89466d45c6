<!-- 探针状态详情 -->
<template>
  <div class="probe-detail" v-loading.fullscreen="fullscreenLoading">
    <div class="probe-container">
      <el-row>
        <el-col :span="12">
          <div class="top-title">探针状态详情</div>
        </el-col>
        <el-col :span="12" class="return-col">
          <button @click.prevent="returnWeb" class="btn-return btn-style">
            <div class="button-icon button-return"></div>
            <span>返回</span>
          </button>
        </el-col>
      </el-row>
      <el-row class="content-row" @scroll.native="handelScroll" ref="content">
        <el-row class="base-row">
          <el-row class="content-header">
            <div class="content-text">基本情况</div>
          </el-row>
          <el-row class="base-container">
            <el-form class="host-form" label-width="110px">
              <el-row>
                <el-col :span="6">
                  <el-form-item class="label-color">
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-name"></div>
                      <span>前端名称</span>
                    </div>
                    <span>{{ hostForm.deviceName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-runStatus"></div>
                      <span>运行状态</span>
                    </div>
                    <template slot-scope="scope">
                      <span
                        :class="{ 'status-error': hostForm.status == 'RED' }"
                        v-if="hostForm.status"
                      >
                        {{ hostForm.status | formatStatus }}
                      </span>
                    </template>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-cpu"></div>
                      <span>cpu均值</span>
                    </div>
                    <template slot-scope="scope">
                      <el-progress
                        v-if="hostForm.cpuAvgRate"
                        class="probeProgress"
                        :percentage="Number(hostForm.cpuAvgRate)"
                        color="#fe5857"
                      >
                      </el-progress>
                    </template>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-restart"></div>
                      <span>今日重启次数</span>
                    </div>
                    <span>{{ hostForm.todayRestartCount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-nodeNum"></div>
                      <span>节点编号</span>
                    </div>
                    <span>{{ hostForm.apKey }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-nodeStatus"></div>
                      <span>节点状态</span>
                    </div>
                    <el-switch
                      disabled
                      v-if="hostForm.isDelete != undefined"
                      v-model="hostForm.isDelete"
                      :active-value="0"
                      :inactive-value="1"
                      active-color="#25ce88"
                      inactive-color="#f93846"
                      @click.native="switchClick(hostForm.isDelete)"
                    >
                    </el-switch>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-memory"></div>
                      <span>内存使用</span>
                    </div>
                    <el-progress
                      v-if="hostForm.memoryRate"
                      class="probeProgress"
                      :percentage="Number(hostForm.memoryRate)"
                      color="#f79a26"
                    >
                    </el-progress>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-fileNum"></div>
                      <span>今日文件数</span>
                    </div>
                    <span>{{ hostForm.fileNum }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-nodeSequence"></div>
                      <span>节点序列号</span>
                    </div>
                    <span>{{ hostForm.serial }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-version"></div>
                      <span>版本号</span>
                    </div>
                    <span>{{ hostForm.version }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-disk"></div>
                      <span>硬盘使用</span>
                    </div>
                    <el-progress
                      v-if="hostForm.diskRate"
                      class="probeProgress"
                      :percentage="Number(hostForm.diskRate)"
                      color="#2696f3"
                    >
                    </el-progress>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-packetLoss"></div>
                      <span>今日丢包率</span>
                    </div>
                    <span>{{
                      hostForm.softLostRatio &&
                      hostForm.softLostRatio * 100 + "%"
                    }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-importTime"></div>
                      <span>入库时间</span>
                    </div>
                    <span>{{ hostForm.reportTime }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <div slot="label" class="formItem">
                      <div class="front-icon icon-drive"></div>
                      <span>驱动</span>
                    </div>
                    <span :class="{ 'status-error': !hostForm.zcpActiveType }">
                      {{ hostForm.zcpActiveType ? "已安装" : "未安装" }}
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-row>
        </el-row>
        <el-row class="equipment-row">
          <el-row class="content-header">
            <div class="content-text">设备历史状态</div>
          </el-row>
          <el-row class="equipment-container">
            <el-row class="chart-container">
              <div id="historyStatus"></div>
              <div v-show="historyShow" class="chartShow">暂无数据</div>
            </el-row>
          </el-row>
        </el-row>
        <el-row class="system-row">
          <el-row class="content-header">
            <div class="content-text">系统监控</div>
          </el-row>
          <el-row class="equipment-container">
            <el-col :span="12" class="chart-col">
              <el-row class="chart-form">
                <div>CPU情况</div>
                <div>
                  <span>cpu均值</span>
                  <span>{{ hostForm.cpuAvg }}</span>
                </div>
                <div>
                  <span>cpu峰值</span>
                  <span>{{ hostForm.cpuTop }}</span>
                </div>
                <div>
                  <span>温度</span>
                  <span>{{ hostForm.temp && hostForm.temp + "°" }}</span>
                </div>
              </el-row>
              <el-row class="chart-container">
                <div id="cpuDetail"></div>
                <div v-show="cpuShow" class="chartShow">暂无数据</div>
              </el-row>
            </el-col>
            <el-col :span="12" class="chart-col">
              <el-row class="chart-form">
                <div>内存情况</div>
                <div>
                  <span>内存使用</span>
                  <span>{{
                    currencyCapacity(hostForm, {}, hostForm.usedMemory)
                  }}</span>
                </div>
                <div>
                  <span>内存大小</span>
                  <span>{{
                    currencyCapacity(hostForm, {}, hostForm.totalMemory)
                  }}</span>
                </div>
              </el-row>
              <el-row class="chart-container">
                <div id="memoryDetail"></div>
                <div v-show="memoryShow" class="chartShow">暂无数据</div>
              </el-row>
            </el-col>
          </el-row>
        </el-row>
        <el-row class="disk-row">
          <div class="content-header">
            <div class="content-text">硬盘情况</div>
          </div>
          <el-row class="table-row">
            <el-table
              :data="diskInfo"
              max-height="300"
              :cell-style="cellStyle"
              :header-cell-style="tableHeader"
              :header-row-class-name="headerClass"
            >
              <el-table-column
                prop="name"
                label="名称"
                width="180"
              ></el-table-column>
              <el-table-column prop="path" label="挂载路径"></el-table-column>
              <el-table-column
                prop="total"
                label="总空间大小"
                :formatter="currencyCapacity"
              ></el-table-column>
              <el-table-column
                prop="used"
                label="已使用空间"
                :formatter="currencyCapacity"
              ></el-table-column>
              <el-table-column
                prop="free"
                label="剩余空间"
                :formatter="currencyCapacity"
              ></el-table-column>
              <el-table-column prop="usedRate" label="使用率"></el-table-column>
            </el-table>
          </el-row>
        </el-row>
        <el-row class="network-row">
          <div class="content-header">
            <div class="content-text">网卡情况</div>
          </div>
          <el-row class="table-row">
            <el-table
              :data="nicInfo"
              max-height="300"
              :cell-style="cellStyle"
              :header-cell-style="tableHeader"
              :header-row-class-name="headerClass"
            >
              <el-table-column
                prop="netCardName"
                label="名称"
              ></el-table-column>
              <el-table-column
                prop="driverName"
                label="网卡类型"
              ></el-table-column>
            </el-table>
          </el-row>
        </el-row>
        <el-row class="plugin-row">
          <div class="content-header">
            <div class="content-text">生效插件</div>
          </div>
          <el-row class="plugin-content">
            <div
              v-for="(item, index) in pluginActiveList"
              :key="index"
              class="plugin-name"
            >
              <span :title="item">{{ item }}</span>
            </div>
          </el-row>
        </el-row>
        <el-row class="transfer-row">
          <el-row class="content-header">
            <div class="content-text">传输流程</div>
          </el-row>
          <el-row class="transfer-container">
            <el-row class="time-row">
              <el-col :span="24">
                <el-date-picker
                  v-model="transferForm.transferTime"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="至"
                  start-placeholde="开始日期"
                  end-placeholde="结束日期"
                  @change="timeChange"
                  :clearable="false"
                >
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row class="transfer-content">
              <div class="transfer-table">
                <div
                  v-for="(item, index) in phaseOptions"
                  :key="'transfer' + index"
                  :class="{ hasLine: item.content && item.content.length > 1 }"
                >
                  <div class="phase">
                    <span>{{ item.name }}</span>
                  </div>
                  <div
                    v-for="(obj, index1) in item.content"
                    :key="'phase' + index1"
                    :style="{ top: 20 + 45 * index1 + '%' }"
                    class="phase-content"
                    @click="phaseDetail(obj, item.name)"
                  >
                    <div
                      v-if="obj && obj.text"
                      :title="
                        obj.text + (obj.num || 0) + '(' + (obj.size || 0) + ')'
                      "
                    >
                      <span>{{ obj.text }}</span>
                      <span>{{ obj.num || 0 }}</span>
                      <span>({{ obj.size || 0 }})</span>
                    </div>
                    <div
                      :class="[
                        'front-icon',
                        !obj.detailShow ? 'icon-selectDown' : 'icon-selectUp',
                      ]"
                    ></div>
                    <div class="transfer-detail" v-show="obj.detailShow">
                      <el-row>
                        <p v-if="item.name == '前端压缩加密阶段'">压缩后：</p>
                        <el-table
                          :data="obj.transferInfo"
                          max-height="160"
                          :cell-style="cellStyle"
                          :header-cell-style="tableHeader"
                          :header-row-class-name="headerClass"
                        >
                          <el-table-column
                            prop="type"
                            label="类型编号"
                            min-width="68"
                          ></el-table-column>
                          <el-table-column
                            prop="num"
                            label="文件数量"
                            min-width="68"
                          ></el-table-column>
                          <el-table-column
                            prop="size"
                            label="文件大小"
                            show-overflow-tooltip
                            min-width="68"
                          ></el-table-column>
                        </el-table>
                      </el-row>
                      <el-row v-if="item.name == '前端压缩加密阶段'">
                        <p>压缩前：</p>
                        <el-table
                          :data="obj.transferInfoBefore"
                          max-height="160"
                          :cell-style="cellStyle"
                          :header-cell-style="tableHeader"
                          :header-row-class-name="headerClass"
                        >
                          <el-table-column
                            prop="type"
                            label="类型编号"
                            min-width="68"
                          ></el-table-column>
                          <el-table-column
                            prop="num"
                            label="文件数量"
                            min-width="68"
                          ></el-table-column>
                          <el-table-column
                            prop="size"
                            label="文件大小"
                            show-overflow-tooltip
                            min-width="68"
                          ></el-table-column>
                        </el-table>
                      </el-row>
                    </div>
                  </div>
                </div>
              </div>
            </el-row>
          </el-row>
        </el-row>
        <el-row class="history-row">
          <el-row class="content-header">
            <div class="content-text">历史信息</div>
          </el-row>
          <el-row class="history-container">
            <el-col
              :span="12"
              class="history-col"
              v-for="(item, index) in historyOptions"
              :key="'history' + index"
            >
              <el-row class="chart-container">
                <div :id="item.id"></div>
              </el-row>
            </el-col>
            <div v-show="historyOptions.length == 0" class="chartShow">
              暂无数据
            </div>
          </el-row>
        </el-row>
        <el-row class="procedure-row">
          <div class="content-header">
            <div class="content-text">程序运行情况</div>
          </div>
          <el-row class="table-row">
            <el-row class="procedure-message">
              <div>
                <span>前端版本：</span>
                <span>{{ hostForm.version }}</span>
              </div>
              <div>
                <span>零拷贝驱动安装：</span>
                <span>{{
                  hostForm.zcpActiveType ? hostForm.zcpActiveType : "未安装"
                }}</span>
              </div>
              <div>
                <span>零拷贝驱动生效网卡：</span>
                <span>{{ hostForm.zcpActiveDriver }}</span>
              </div>
            </el-row>
            <el-table
              :data="procedureInfo"
              max-height="300"
              :cell-style="cellStyle"
              :header-cell-style="tableHeader"
              :header-row-class-name="headerClass"
            >
              <el-table-column
                prop="softwareName"
                label="程序名称"
              ></el-table-column>
              <!-- <el-table-column prop="total" label="流量大小"></el-table-column>
                                <el-table-column prop="total" label="程序丢包率"></el-table-column>
                                <el-table-column prop="total" label="镜像丢失率"></el-table-column> -->
              <el-table-column
                prop="restartCount"
                label="重启次数"
              ></el-table-column>
              <!-- <el-table-column prop="total" label="数据上报时间"></el-table-column> -->
            </el-table>
          </el-row>
        </el-row>
      </el-row>
    </div>
    <div class="nav-row">
      <el-row class="nav-content">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in navOptions"
            :key="'nav' + index"
            :hide-timestamp="true"
            class="navItem"
            :type="index == navIndex ? 'primary' : ''"
            @click.native="navClick(index, item.class)"
          >
            <span :class="{ active: index == navIndex, navSpan: true }">{{
              item.content
            }}</span>
          </el-timeline-item>
        </el-timeline>
      </el-row>
    </div>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import {
  bodyRowClassName,
  tableHeaderStyle,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "probe-detail",
  data() {
    return {
      navIndex: 0,

      fullscreenLoading: false,
      historyShow: true,
      cpuShow: true,
      memoryShow: true,

      diskInfo: [],
      nicInfo: [],
      procedureInfo: [],
      transferInfo: [],
      pluginActiveList: [],
      phaseOptions: [
        {
          name: "前端压缩加密阶段",
        },
        {
          name: "前端发送数据阶段",
        },
        {
          name: "接受压缩包阶段",
        },
        {
          name: "预处理阶段",
        },
        {
          name: "文件展示阶段",
        },
      ],
      chartOptions: [],
      historyOptions: [
        // {
        //     name:'历史文件数',
        //     id:'historyFileNum',
        // },
        // {
        //     name:'历史文件大小',
        //     id:'historyFileSize',
        // },
        // {
        //     name:'历史流量大小',
        //     id:'historyFlowRate',
        // },
        // {
        //     name:'历史丢包率',
        //     id:'historyPacketLoss',
        // }
      ],
      navOptions: [
        {
          content: "基本情况",
          class: "base-row",
        },
        {
          content: "设备历史状态",
          class: "equipment-row",
        },
        {
          content: "系统监控",
          class: "system-row",
        },
        {
          content: "硬盘情况",
          class: "disk-row",
        },
        {
          content: "网卡情况",
          class: "network-row",
        },
        {
          content: "生效插件",
          class: "plugin-row",
        },
        {
          content: "传输流程",
          class: "transfer-row",
        },
        {
          content: "历史信息",
          class: "history-row",
        },
        {
          content: "程序运行情况",
          class: "procedure-row",
        },
      ],

      form: {},
      hostForm: {},
      twoRow: {},
      transferForm: {
        startTime: "",
        endTime: "",
        apKey: "",
        transferTime: [],
      },
    };
  },
  methods: {
    init() {
      this.twoRow = this.$route.query;
      this.transferForm.startTime = this.twoRow.startTime;
      this.transferForm.endTime = this.twoRow.endTime;
      this.transferForm.apKey = this.twoRow.apKey;
      this.transferForm.transferTime.push(this.twoRow.startTime);
      this.transferForm.transferTime.push(this.twoRow.endTime);

      this.getStatus();
      this.getDataList(this.twoRow);
      this.getTransmit(this.twoRow);
      this.getChart(this.twoRow);
    },
    //请求函数
    getDataList(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/detail.json", _this.qs.stringify(data), {
          timeout: null,
        })
        .then((res) => {
          let dataList = [];
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            res.data.data && this.formatData(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getChart(data) {
      let _this = this;

      _this.$http
        .post("front-device/history/all", _this.qs.stringify(data))
        .then((res) => {
          if (res.data.code == 0) {
            res.data.data && _this.formatChart(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getTransmit(data) {
      let _this = this;

      // _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/transmit", _this.qs.stringify(data))
        .then((res) => {
          let dataList = [];
          // _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            res.data.data && this.formatTransmit(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          // _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getTransmitDetail(data, row) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/transmit/detail", _this.qs.stringify(data))
        .then((res) => {
          let dataList = [];
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            data.type == "compress_before"
              ? this.$set(row, "transferInfoBefore", res.data.data || [])
              : this.$set(row, "transferInfo", res.data.data || []);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    updateIsDelete(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/updateIsDelete", _this.qs.stringify(data))
        .then((res) => {
          let dataList = [];
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            this.getDataList(this.twoRow);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    //处理数据
    formatData(data) {
      this.hostForm = data.detail || {};
      this.pluginActiveList = data.detail.pluginActiveList
        ? data.detail.pluginActiveList.split(";")
        : [];
      this.pluginActiveList.length && this.pluginActiveList.pop();
      this.diskInfo = data.list.diskInfos || [];
      this.nicInfo = data.list.netCardInfos || [];
      this.procedureInfo = data.list.softwareRestartInfos || [];
    },
    formatTransmit(data) {
      let arr = [];
      $.each(data, (index, item) => {
        let obj = {};
        switch (index) {
          case "compress":
            obj = {
              name: "前端压缩加密阶段",
              content: [
                {
                  text: "总压缩包:",
                  num: item.compress_after_num,
                  size: item.compress_after_size,
                  type: "compress_after",
                  detailShow: false,
                },
              ],
            };
            break;
          case "send":
            obj = {
              name: "前端发送数据阶段",
              content: [
                {
                  text: "发送压缩包:",
                  num: item.send_num,
                  size: item.send_size,
                  type: "send",
                  detailShow: false,
                },
              ],
            };
            break;
          case "receive":
            obj = {
              name: "接受压缩包阶段",
              content: [
                {
                  text: "接受压缩包:",
                  num: item.receive_num,
                  size: item.receive_size,
                  type: "receive",
                  detailShow: false,
                },
                {
                  text: "待回传压缩包:",
                  num: item.wait_send_num,
                  size: item.wait_send_size,
                  type: "wait_send",
                  detailShow: false,
                },
              ],
            };
            break;
          case "decrypt":
            obj = {
              name: "预处理阶段",
              content: [
                {
                  text: "解压后:",
                  num: item.decrypt_after_num,
                  size: item.decrypt_after_size,
                  type: "decrypt_after",
                  detailShow: false,
                },
                {
                  text: "解压前:",
                  num: item.decrypt_before_num,
                  size: item.decrypt_before_size,
                  type: "decrypt_before",
                  detailShow: false,
                },
              ],
            };
            break;
          case "storage":
            obj = {
              name: "文件展示阶段",
              content: [
                {
                  text: "文件展示阶段:",
                  num: item.storage_num,
                  size: item.storage_size,
                  type: "storage",
                  detailShow: false,
                },
              ],
            };
            break;
        }
        arr.push(obj);
      });
      this.phaseOptions = [...arr];
    },
    formatChart(data) {
      $.each(data, (index, item) => {
        let obj = {};
        switch (index) {
          case "status":
            obj = {
              name: "设备历史状态",
              id: "historyStatus",
              showName: "historyShow",
              x: item.timeList,
              data: [
                {
                  name: "设备历史状态",
                  type: "line",
                  smooth: true,
                  itemStyle: {
                    normal: {
                      color: "rgb(99,223,246)",
                    },
                    emphasis: {
                      borderColor: "red",
                    },
                  },
                  data: item.dataList,
                },
              ],
            };
            break;
          case "fileNum":
            obj = {
              name: "历史文件数",
              id: "historyFileNum",
              x: item.timeList,
              data: [
                {
                  name: "历史文件数",
                  type: "line",
                  smooth: true,
                  itemStyle: {
                    normal: {
                      color: "rgb(99,223,246)",
                    },
                    emphasis: {
                      borderColor: "red",
                    },
                  },
                  data: item.dataList,
                },
              ],
            };
            item.timeList.length != 0 && this.historyOptions.push(obj);
            break;
          case "fileSize":
            obj = {
              name: "历史文件大小",
              id: "historyFileSize",
              x: item.timeList,
              data: [
                {
                  name: "历史文件大小",
                  type: "line",
                  smooth: true,
                  itemStyle: {
                    normal: {
                      color: "rgb(99,223,246)",
                    },
                    emphasis: {
                      borderColor: "red",
                    },
                  },
                  data: item.dataList,
                },
              ],
            };
            item.timeList.length != 0 && this.historyOptions.push(obj);
            break;
          case "flowSpeed":
            obj = {
              name: "历史流量大小",
              id: "historyFlowRate",
              x: item.timeList,
              data: [
                {
                  name: "历史流量大小",
                  type: "line",
                  smooth: true,
                  itemStyle: {
                    normal: {
                      color: "rgb(99,223,246)",
                    },
                    emphasis: {
                      borderColor: "red",
                    },
                  },
                  data: item.dataList,
                },
              ],
            };
            item.timeList.length != 0 && this.historyOptions.push(obj);
            break;
          case "softLostRatio":
            obj = {
              name: "历史丢包率",
              id: "historyPacketLoss",
              x: item.timeList,
              data: [
                {
                  name: "历史丢包率",
                  type: "line",
                  smooth: true,
                  itemStyle: {
                    normal: {
                      color: "rgb(99,223,246)",
                    },
                    emphasis: {
                      borderColor: "red",
                    },
                  },
                  data: item.dataList,
                },
              ],
            };
            item.timeList.length != 0 && this.historyOptions.push(obj);
            break;
          case "cpu":
            obj = {
              name: "cpu使用情况",
              showName: "cpuShow",
              id: "cpuDetail",
              x: item.timeList,
              data: [],
            };
            $.each(item, (key, value) => {
              switch (key) {
                case "cpuAvgList":
                  obj.data.push({
                    name: "cpu平均值",
                    type: "line",
                    smooth: true,
                    itemStyle: {
                      normal: {
                        color: "rgb(99,223,246)",
                      },
                      emphasis: {
                        borderColor: "red",
                      },
                    },
                    data: item.cpuAvgList,
                  });
                  break;
                case "cpuTopList":
                  obj.data.push({
                    name: "cpu最大值",
                    type: "line",
                    smooth: true,
                    itemStyle: {
                      normal: {
                        color: "rgb(247, 154, 38)",
                      },
                      emphasis: {
                        borderColor: "red",
                      },
                    },
                    data: item.cpuTopList,
                  });
                  break;
                case "tempList":
                  obj.data.push({
                    name: "cpu温度",
                    type: "line",
                    smooth: true,
                    itemStyle: {
                      normal: {
                        color: "rgb(254, 88, 87)",
                      },
                      emphasis: {
                        borderColor: "red",
                      },
                    },
                    data: item.tempList,
                  });
                  break;
              }
            });
            break;
          case "mem":
            obj = {
              name: "内存使用情况",
              showName: "memoryShow",
              id: "memoryDetail",
              x: item.timeList,
              data: [
                {
                  name: "内存使用情况",
                  type: "line",
                  smooth: true,
                  itemStyle: {
                    normal: {
                      color: "rgb(99,223,246)",
                    },
                    emphasis: {
                      borderColor: "red",
                    },
                  },
                  data: item.dataList,
                },
              ],
            };
            break;
        }
        this.chartOptions.push(obj);
      });
      this.$nextTick(() => {
        this.drawLine(this.chartOptions);
      });
    },
    currencyCapacity(row, column, value) {
      if (value && value != "未知") {
        value = Number(value);
        row.usedMemory && row.totalMemory && (value = value * 1024);

        if (value > 0 && value < 1024) {
          value = value + "KB";
        } else if (value >= 1024 && value < 1048576) {
          value = (value / 1024).toFixed(2) + "MB";
        } else if (value >= 1048576 && value < 1073741824) {
          value = (value / 1048576).toFixed(2) + "GB";
        } else if (value >= 1073741824) {
          value = (value / 1073741824).toFixed(2) + "TB";
        }
        return value;
      } else {
        return "-";
      }
    },
    //功能函数
    returnWeb() {
      this.$router.go(-1);
    },
    drawLine(data) {
      data.forEach((item) => {
        if (item.x && item.x.length != 0) {
          let chartDom = document.getElementById(item.id),
            options = {},
            showTitle = true;

          (item.name == "cpu使用情况" ||
            item.name == "内存使用情况" ||
            item.name == "设备历史状态") &&
            (showTitle = false);
          this[item.showName] = false;
          options = {
            title: {
              show: showTitle ? true : false,
              text: item.name,
              textStyle: {
                fontSize: 12,
                color: "#1e85e6",
              },
              left: 0,
            },
            dataZoom: [
              {
                type: "slider",
                show: item.x.length > 10,
                bottom: 10,
                start: item.x.length > 10 ? 50 : 0,
                end: 100,
                textStyle: {
                  color: "#203e66",
                },
                height: 8,
                realtime: false,
                showDetail: true,
                showDataShadow: false,
                preventDefaultMouseMove: false,
                borderColor: "transparent",
                backgroundColor: "#f2f2f2",
                handleSize: 14,
                handleStyle: { color: "#00a8ff" },
                fillerColor: "#77cdf9", // 选中范围的填充颜色
                handleIcon:
                  "M512 512m-512 0a500 500 0 1 0 1024 0 500 500 0 1 0-1024 0Z",
              },
            ],
            tooltip: {
              trigger: "axis",
              // formatter:'{b0}</br>{a0}: {c0}',
              formatter: (obj) => {
                let content = "";
                switch (item.name) {
                  case "设备历史状态":
                    let val = "";
                    switch (obj[0].value) {
                      case 0:
                        val = "良好";
                        break;
                      case 1:
                        val = "告警";
                        break;
                      case 2:
                        val = "错误";
                        break;
                    }
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      val;
                    break;
                  case "cpu使用情况":
                    content = obj[0].name + "<br/>";
                    obj.forEach((value) => {
                      content +=
                        value.marker +
                        value.seriesName +
                        "：" +
                        (value.seriesName == "cpu温度"
                          ? value.value + "°"
                          : value.value + "%") +
                        "<br/>";
                    });
                    break;
                  case "历史流量大小":
                  case "历史丢包率":
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      (item.name == "历史流量大小"
                        ? obj[0].value + "mb/s"
                        : obj[0].value + "%");
                    break;
                  case "内存使用情况":
                  case "历史文件大小":
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      obj[0].value +
                      "mb";
                    break;
                  default:
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      obj[0].value;
                }
                return content;
              },
              axisPointer: {
                lineStyle: { type: "dashed" },
              },
            },
            calculable: true,
            grid: {
              top: showTitle ? "18%" : "10%",
              bottom: "20%",
              left: 60,
              right: "3%",
            },
            xAxis: {
              type: "category",
              boundaryGap: ["10%", "10%"],
              axisLine: {
                lineStyle: { color: "#e6e9f1" },
              },
              axisLabel: {
                color: "#203e66",
                length: 7,
                formatter: (val) => {
                  let str = val.split(" ");
                  return str.join("\n");
                },
              },
              data: item.x,
            },
            yAxis: [
              {
                type: "value",
                axisTick: { show: false }, //坐标轴刻度
                axisLine: {
                  lineStyle: { color: "#e6e9f1" },
                },
                minInterval:
                  (item.name == "历史文件数" ||
                    item.name == "历史文件大小" ||
                    item.name == "设备历史状态") &&
                  1,
                axisLabel: {
                  show: true,
                  textStyle: {
                    color: "#203e66",
                  },
                  formatter: (val) => {
                    switch (item.name) {
                      case "设备历史状态":
                        switch (val) {
                          case -1:
                            val = "";
                            break;
                          case 0:
                            val = "良好";
                            break;
                          case 1:
                            val = "告警";
                            break;
                          case 2:
                            val = "错误";
                            break;
                        }
                        break;
                      case "历史丢包率":
                        val = val + "%";
                        break;
                    }
                    return val;
                  },
                },
                splitLine: {
                  lineStyle: { color: "#e6e9f1" },
                },
              },
            ],
            series: item.data,
          };
          item.chartContent = this.$echarts.init(chartDom);
          item.chartContent.setOption(options, true);
        } else {
          item.chartContent && item.chartContent.dispose();
        }
      });
      window.addEventListener("resize", this.bindChartEvent);
    },
    bindChartEvent(data) {
      this.chartOptions.forEach((item) => {
        if (item.x && item.x.length != 0) {
          item.chartContent.resize();
        }
      });
    },
    tableHeader() {
      return "border:0;";
    },
    cellStyle() {
      return "border:0;";
    },
    headerClass() {
      return headerRowClassName();
    },
    handelScroll() {
      let scrollTop = this.$refs.content.$el.scrollTop,
        scrollBottom =
          this.$refs.content.$el.scrollHeight -
          scrollTop -
          this.$refs.content.$el.offsetHeight,
        bodyContainer = document.querySelector(".content-row"),
        navItem = document.querySelectorAll(".content-row > div");

      navItem.forEach((item, index) => {
        item.min = $(item).position().top + scrollTop - 18;
        item.max = $(item).position().top + $(item).height() + scrollTop;

        switch (true) {
          case scrollTop <= 120:
            this.navIndex = 0;
            break;
          case scrollBottom <= 120:
            this.navIndex = navItem.length - 1;
          case scrollTop >= item.min && scrollTop < item.max:
            this.navIndex = index;
            break;
        }
      });
    },
    navClick(index, className) {
      let bodyContainer = document.querySelector(".content-row"),
        scrollDiv = document.querySelector("." + className),
        positionTop = $(scrollDiv).position().top,
        scrollTop = $(bodyContainer).scrollTop();
      $(bodyContainer).animate(
        { scrollTop: positionTop + scrollTop - 10 },
        500
      );
    },
    timeChange(val) {
      if (val && val.length != 0) {
        this.transferForm.startTime = val[0] + " 00:00:00";
        this.transferForm.endTime = val[1] + " 23:59:59";
      }
      this.getTransmit(this.transferForm);
    },
    switchClick(val) {
      let data = {},
        isDelete = null;
      switch (val) {
        case 0:
          isDelete = 1;
          break;
        case 1:
          isDelete = 0;
          break;
      }
      data = {
        apKey: this.twoRow.apKey,
        isDelete: isDelete,
      };
      isDelete != null && this.updateIsDelete(data);
    },
    phaseDetail(row, name) {
      let data = {
        startTime: this.transferForm.startTime,
        endTime: this.transferForm.endTime,
        apKey: this.transferForm.apKey,
        type: row.type,
      };
      if ($(event.target).parents(".transfer-detail").length == 0) {
        if (name == "前端压缩加密阶段") {
          let beforeData = { ...data };
          beforeData.type = "compress_before";
          !row.detailShow && this.getTransmitDetail(data, row);
          !row.detailShow && this.getTransmitDetail(beforeData, row);
        } else {
          !row.detailShow && this.getTransmitDetail(data, row);
        }
        row.detailShow = !row.detailShow;
      }
    },
  },
  filters: {
    formatStatus(val) {
      if (val) {
        switch (val) {
          case "RED":
            val = "错误";
            break;
          case "YELLOW":
            val = "警告";
            break;
          case "GREEN":
            val = "良好";
            break;
          case "GRAY":
            val = "无";
            break;
        }
      }
      return val;
    },
  },
  mounted() {
    this.init();
  },
  destroyed() {
    window.removeEventListener("resize", this.bindChartEvent);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.probe-detail {
  height: 100%;
  width: 100%;
  padding: 0 1% 1%;
  display: flex;
}

.probe-detail > div {
  display: inline-block;
}
.probe-container {
  width: calc(100% - 200px);
  height: 100%;
}
.nav-row {
  height: 100%;
  width: 195px;
  padding-top: 50px;
}
.top-title {
  text-align: left;
  text-indent: 15px;
  color: #1e85e6;
  font-size: 15px;
  font-weight: bold;
  height: 40px;
  line-height: 40px;
  display: inline-block;
  margin-top: 10px;
}
.content-row {
  width: 100%;
  height: calc(100% - 50px);
  padding: 10px 10px 0 10px;
  background-color: #ffffff;
  border: 1px solid #e2e2e2;
  -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  overflow-y: scroll;
}
.return-col {
  text-align: right;
  padding-right: 20px;
  height: 40px;
  line-height: 40px;
  margin-top: 10px;
}
.btn-return {
  background-color: #1e85e6;
  color: #fff;
  padding-left: 5px;
}
.button-return {
  display: inline-block;
  height: 21px;
  width: 25px;
  background-size: cover !important;
  vertical-align: sub;
  background: url("../../assets/images/hardWare/icon-detail.png");
  background-position: 0 26px;
  height: 16px;
}
.btn-return:hover {
  background-color: #2d91ef;
  color: #fff;
}
.content-header {
  height: 30px;
  line-height: 36px;
  font-size: 14px;
  border-bottom: 1px solid #cdd0d3;
}
.content-text {
  display: inline-block;
  height: 30px;
  line-height: 36px;
  border-bottom: 2px solid #5174b4;
  padding: 0 3px;
  color: #203e66;
  font-weight: bold;
}
.base-row {
  padding: 0px 15px;
  height: 280px;
}
.base-container {
  height: calc(100% - 30px);
  display: flex;
  align-items: center;
  padding: 15px 0 0 50px;
}
.status-error {
  color: #ff0000;
}
.probeProgress {
  display: inline-block;
  width: 150px;
}
.equipment-row,
.system-row {
  padding: 0px 15px;
  height: 300px;
}
.equipment-container {
  height: calc(100% - 30px);
  padding: 25px 30px 10px;
}
.chart-container,
.chart-container div,
.equipment-container .chart-col {
  height: 100%;
  position: relative;
}
.chart-container .chartShow,
.history-container .chartShow {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #909399;
}
.history-row {
  padding: 0 15px;
  min-height: 400px;
}
.history-container {
  padding: 25px 30px 10px;
  min-height: 370px;
}
.history-container .history-col .chart-container {
  height: 300px;
}
.chart-form {
  height: 30px;
}
.chart-col .chart-container {
  height: calc(100% - 30px);
}
.chart-form > div {
  display: inline-block;
  margin-right: 15px;
  color: #203e66;
}
.chart-form > div:first-child {
  color: #1e85e6;
  font-weight: 700;
  margin-right: 20px;
}
.network-row,
.disk-row,
.procedure-row,
.plugin-row {
  padding: 0px 15px;
}
.table-row {
  padding: 25px 30px;
}
.procedure-message {
  height: 30px;
}
.procedure-message > div {
  display: inline-block;
  margin-right: 45px;
  color: #203e66;
}
.nav-content {
  padding: 10px 0 0 40px;
}
.navItem {
  cursor: pointer;
}
.navSpan.active {
  color: #409eff;
}
.transfer-row {
  padding: 0px 15px;
  height: 600px;
}
.transfer-container {
  padding: 25px 30px;
  height: 100%;
}
.transfer-container .time-row {
  height: 30px;
}
.transfer-container .time-row > div {
  text-align: right;
}
.transfer-content {
  height: calc(100% - 30px);
  padding: 40px 0 25px;
}
.transfer-table {
  width: 100%;
  height: 100%;
  border: 1px solid #96b4f0;
  display: flex;
}
.transfer-table > div {
  height: 100%;
  width: 20%;
  border-right: 1px solid #96b4f0;
  background-image: linear-gradient(90deg, #ebf1fa 5%, #f5fbff 10%),
    linear-gradient(#ebf1fa 5%, #f5fbff 10%);
  background-size: 10px 10px;
  position: relative;
}
.transfer-table > div:last-child {
  border: none;
}
.transfer-table .phase {
  height: 40px;
  width: 90%;
  background: linear-gradient(180deg, #6ea8ff, #5482ef);
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  text-align: center;
  line-height: 40px;
}
.transfer-table .phase span {
  font-size: 14px;
  color: #fff;
}
.transfer-table .phase-content {
  width: 80%;
  height: 30px;
  background: #fff;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  left: 50%;
  line-height: 30px;
  padding: 0 5px 0 10px;
  box-shadow: 3px 3px 5px #96b1e3;
  top: 50%;
  color: #203e66;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  z-index: 1;
}
.transfer-table .phase-content > div:nth-child(1) {
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.transfer-table .transfer-detail {
  max-height: 300px;
  width: 122%;
  position: absolute;
  left: -11%;
  top: 35px;
  background: #fff;
  z-index: 2;
}
.transfer-table > div:not(:last-child)::before {
  content: "";
  border: 1px dashed #fe9534;
  width: 16%;
  display: inline-block;
  position: absolute;
  top: calc(20% - 1px);
  left: 92%;
  z-index: 1;
}
.transfer-table > div.hasLine::after {
  content: "";
  border: 1px dashed #fe9534;
  height: calc(45% - 1px);
  display: inline-block;
  position: absolute;
  top: 20%;
  left: 0;
}
.transfer-table > div:not(:first-child) .phase-content::after {
  content: "";
  width: 0;
  height: 0;
  border-right: 5px solid transparent;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid #fe9534;
  display: inline-block;
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}
.transfer-table
  > div:not(:first-child)
  .phase-content:nth-child(n + 3)::before {
  content: "";
  border: 1px dashed #fe9534;
  width: 8%;
  display: inline-block;
  position: absolute;
  top: calc(50% - 1px);
  right: calc(100% + 6px);
}
.formItem {
  display: flex;
  align-items: center;
}
.formItem .front-icon {
  margin-right: 5px;
}
.front-icon {
  display: inline-block;
  height: 19px;
  width: 21px;
  background: url("../../assets/images/frontManage/frontDetail.png");
}
.icon-selectDown {
  background-position: 0 -25px;
}
.icon-selectUp {
  background-position: 0 -55px;
}
.icon-name {
  background-position: 0 -86px;
}
.icon-nodeNum {
  background-position: 0 -126px;
}
.icon-nodeSequence {
  background-position: 0 -166px;
}
.icon-importTime {
  background-position: 0 -206px;
}
.icon-runStatus {
  background-position: 0 -246px;
}
.icon-nodeStatus {
  background-position: 0 -286px;
}
.icon-version {
  background-position: 0 -326px;
}
.icon-drive {
  background-position: 0 -366px;
}
.icon-cpu {
  background-position: 0 -406px;
}
.icon-memory {
  background-position: 0 -446px;
}
.icon-disk {
  background-position: 0 -476px;
}
.icon-restart {
  background-position: 0 -515px;
}
.icon-fileNum {
  background-position: 0 -555px;
}
.icon-packetLoss {
  background-position: 0 -595px;
}
.plugin-content {
  padding: 35px 30px 25px 50px;
}
.plugin-name {
  display: inline-block;
  height: 30px;
  text-align: center;
  padding: 0 10px;
  line-height: 30px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #dbefff;
  color: #2093ff;
  margin-right: 12px;
  margin-bottom: 10px;
  width: 150px;
}
</style>

