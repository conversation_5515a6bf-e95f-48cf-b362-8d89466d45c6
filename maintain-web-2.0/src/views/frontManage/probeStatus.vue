<!-- 探针状态 -->
<template>
  <div class="probe-page" v-loading.fullscreen="fullscreenLoading">
    <div class="probe-container">
      <el-row class="content-row">
        <el-row class="list-row">
          <el-row class="content-header">
            <div class="content-text">
              <span
                >设备列表（<span>{{ total }}</span
                >）</span
              >
            </div>
          </el-row>
          <el-row class="list-container">
            <el-col :span="24">
              <el-row class="list-search">
                <el-col :span="12">
                  <div
                    v-for="(item, index) in statusOption"
                    :key="index"
                    @click="statusClick(item)"
                    :class="{ 'list-status': true, active: item.active }"
                  >
                    <div></div>
                    <span
                      >{{ item.content }}(<span
                        v-text="$data['statusMap'][item.filed] || 0"
                      ></span
                      >)</span
                    >
                  </div>
                  <el-button type="text" class="statusBtn" @click="statusBtn"
                    >重置</el-button
                  >
                </el-col>
                <el-col :span="12" class="form-col">
                  <el-button @click="updateAll" v-if="isOpen"
                    >一键升级</el-button
                  >
                  <el-input
                    placeholder="支持设备名称搜索"
                    class="search-input"
                    v-model="form.deviceName"
                    clearable
                    @keyup.enter.native="searchBtn"
                  >
                    <i
                      slot="prefix"
                      class="iconfont icon-search"
                      @click="searchBtn"
                    ></i>
                  </el-input>
                </el-col>
              </el-row>
              <el-row class="list-content">
                <el-col
                  :span="6"
                  v-for="(item, index) in listOptions"
                  :key="'list' + index"
                  class="list-module"
                >
                  <div :class="{ active: index == clickIndex }">
                    <div class="list-name">
                      <div>
                        <p>
                          <a target="_blank" @click="listDetail(index, item)">{{
                            item.deviceName || "未知前端"
                          }}</a>
                          <el-switch
                            disabled
                            v-model="item.isDelete"
                            :active-value="0"
                            :inactive-value="1"
                            active-color="#25ce88"
                            inactive-color="#cfcfcf"
                            :active-text="
                              item.isDelete == 1 ? '(下架)' : '(上架)'
                            "
                            @click.native="switchClick(item)"
                          >
                          </el-switch>
                        </p>
                        <p>{{ item.version }}</p>
                      </div>
                      <div>
                        <el-button
                          v-if="item.isDisplay"
                          type="text"
                          class="ignoreBtn"
                          @click="ignoreBtn(item)"
                          >忽略设置({{
                            item.ignoreExceptionNum || 0
                          }})</el-button
                        >
                        <el-button
                          v-if="isOpen && item.isDelete != 1"
                          type="text"
                          @click="jumpBtn(item.apKey)"
                          >跳转探针系统</el-button
                        >
                      </div>
                      <el-popover
                        trigger="hover"
                        placement="right-start"
                        v-if="item.status != 'GREEN'"
                      >
                        <p class="popoverContent">
                          {{ item.description || "-" }}
                        </p>
                        <div slot="reference" class="name-wrapper">
                          <div
                            class="status-tag front-icon"
                            :style="{
                              backgroundPosition: '0px ' + item.position,
                            }"
                          ></div>
                        </div>
                      </el-popover>

                      <div
                        v-else
                        class="status-tag front-icon"
                        :style="{ backgroundPosition: '0px ' + item.position }"
                      ></div>
                    </div>
                    <el-row class="list-num">
                      <el-col :span="12">
                        <div class="list-data">
                          <div
                            class="front-icon icon-drive"
                            :class="{ shelves: item.isDelete == 1 }"
                          ></div>
                          <div class="list-key" title="驱动">驱动</div>
                          <div
                            class="list-value"
                            :title="item.zcpActiveType ? '正常' : '异常'"
                          >
                            {{ item.zcpActiveType ? "正常" : "异常" }}
                          </div>
                        </div>
                        <div class="list-data">
                          <div
                            class="front-icon icon-fileSize"
                            :class="{ shelves: item.isDelete == 1 }"
                          ></div>
                          <div class="list-key" title="文件大小">文件大小</div>
                          <div
                            class="list-value"
                            :title="
                              !item.fileSize && item.fileSize != 0
                                ? '--'
                                : bytesToSize(item.fileSize, 2)
                            "
                          >
                            {{
                              !item.fileSize && item.fileSize != 0
                                ? "--"
                                : bytesToSize(item.fileSize, 2)
                            }}
                          </div>
                        </div>
                        <div class="list-data">
                          <div
                            class="front-icon icon-restart"
                            :class="{ shelves: item.isDelete == 1 }"
                          ></div>
                          <div class="list-key" title="今日重启数">
                            今日重启数
                          </div>
                          <div
                            class="list-value"
                            :title="
                              !item.restartCount && item.restartCount != 0
                                ? '--'
                                : item.restartCount
                            "
                          >
                            {{
                              !item.restartCount && item.restartCount != 0
                                ? "--"
                                : item.restartCount
                            }}
                          </div>
                        </div>
                        <div class="list-data" v-if="isOpen">
                          <div
                            class="front-icon icon-exNumber"
                            :class="{ shelves: item.isDelete == 1 }"
                          ></div>
                          <div class="list-key" title="异常数量">异常数量</div>
                          <div
                            class="list-value"
                            :title="
                              !item.exNumber && item.exNumber != 0
                                ? '--'
                                : item.exNumber
                            "
                          >
                            {{
                              !item.exNumber && item.exNumber != 0
                                ? "--"
                                : item.exNumber
                            }}
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="list-data">
                          <div
                            class="front-icon icon-packetLoss"
                            :class="{ shelves: item.isDelete == 1 }"
                          ></div>
                          <div class="list-key" title="今日丢包率">
                            今日丢包率
                          </div>
                          <div
                            class="list-value"
                            :title="
                              !item.softLostRatio && item.softLostRatio != 0
                                ? '--'
                                : item.softLostRatio * 100 + '%'
                            "
                          >
                            {{
                              !item.softLostRatio && item.softLostRatio != 0
                                ? "--"
                                : item.softLostRatio * 100 + "%"
                            }}
                          </div>
                        </div>
                        <div class="list-data">
                          <div
                            class="front-icon icon-fileNum"
                            :class="{ shelves: item.isDelete == 1 }"
                          ></div>
                          <div class="list-key" title="今日文件数">
                            今日文件数
                          </div>
                          <div
                            class="list-value"
                            :title="
                              !item.fileNum && item.fileNum != 0
                                ? '--'
                                : item.fileNum
                            "
                          >
                            {{
                              !item.fileNum && item.fileNum != 0
                                ? "--"
                                : item.fileNum
                            }}
                          </div>
                        </div>
                        <div class="list-data">
                          <div
                            class="front-icon icon-heap"
                            :class="{ shelves: item.isDelete == 1 }"
                          ></div>
                          <div class="list-key" title="当前堆积数据">
                            当前堆积数据
                          </div>
                          <div
                            class="list-value"
                            :title="
                              !item.waitSendSize && item.waitSendSize != 0
                                ? '--'
                                : item.waitSendSize
                            "
                          >
                            {{
                              !item.waitSendSize && item.waitSendSize != 0
                                ? "--"
                                : item.waitSendSize
                            }}
                          </div>
                        </div>
                        <div class="list-data" v-if="isOpen">
                          <div class="front-icon"></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-col>
                <div v-show="listOptions.length == 0" class="chartShow">
                  暂无数据
                </div>
              </el-row>
            </el-col>
          </el-row>
        </el-row>
      </el-row>
      <updateAll
        v-if="nodeShow"
        :nodeData="nodeData"
        @returnMain="closeDialog('一键升级', $event)"
      ></updateAll>
      <ignoreDialog
        v-if="ignoreShow"
        :ignoreData="ignoreData"
        @returnMain="closeDialog('忽略', $event)"
      ></ignoreDialog>
    </div>
  </div>
</template>

<script>
import updateAll from "./updateAll.vue";
import ignoreDialog from "./ignoreDialog.vue";
import getPermission from "@/utils/permissions.js";
import { parseTime } from "@/utils/index.js";
import {
  bodyRowClassName,
  tableHeaderStyle,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "probe-page",
  components: { updateAll, ignoreDialog },
  data() {
    return {
      clickIndex: 0,
      total: 0,

      chartShow: true,
      fullscreenLoading: false,
      // chartLoading: false,
      nodeShow: false,
      ignoreShow: false,
      isOpen: false,

      listOptions: [],
      chartOptions: [],
      statusOption: [
        {
          filed: "RED",
          content: "错误",
          active: false,
        },
        {
          filed: "YELLOW",
          content: "告警",
          active: false,
        },
        {
          filed: "GREEN",
          content: "良好",
          active: false,
        },
        {
          filed: "GRAY",
          content: "下架",
          active: false,
        },
        {
          filed: "IGNORE",
          content: "已忽略",
          active: false,
        },
      ],

      twoRow: {},
      statusMap: {
        RED: "",
        GREEN: "",
        YELLOW: "",
        GRAY: "",
      },
      nodeData: {
        nodeOption: [],
      },
      ignoreData: {},
      form: {
        deviceName: "",
        status: "",
      },
      chartForm: {
        apKey: "",
        startTime: "",
        endTime: "",
        chartTime: [],
      },
    };
  },
  methods: {
    init() {
      this.getStatus();
      this.getDataList();
    },
    //请求函数
    getToken(data) {
      let _this = this,
        form = {
          apKey: data,
        };

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/getFrontPage", _this.qs.stringify(form))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            res.data.data
              ? window.open(res.data.data)
              : this.waFn("该节点不存在相应的探针系统");
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getDataList(data = {}) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.total = 0;
      _this.$http
        .post("front-device/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            res.data.data && _this.formatData(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getChart(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.chartShow = true;
      _this.$http
        .post("front-device/history/all", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            res.data.data && _this.formatChart(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          console.log(error);
        });
    },
    checkUpgradePackage() {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/checkUpgradePackage")
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (res.data.data.status) {
              this.nodeData.packagePath = res.data.data.packagePath;
              this.nodeData.packageOption =
                res.data.data.result.split(",") || [];
              this.nodeShow = true;
            } else {
              this.waFn(res.data.data.result || "获取升级程序包失败");
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          console.log(error);
        });
    },
    updateIsDelete(row) {
      let _this = this,
        data = {
          apKey: row.apKey,
          isDelete: row.isDelete ? 0 : 1,
        };

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/updateIsDelete", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("状态修改成功");
            _this.getDataList(this.form);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          console.log(error);
        });
    },
    //处理数据函数
    initData() {
      this.chartForm.endTime = parseTime(
        Date.parse(new Date()),
        "{y}-{m}-{d} 23:59:59"
      );
      this.chartForm.startTime = parseTime(
        Date.parse(new Date()) - 3 * 24 * 60 * 60 * 1000,
        "{y}-{m}-{d} 00:00:00"
      );
      this.chartForm.chartTime = [
        this.chartForm.startTime,
        this.chartForm.endTime,
      ];
    },
    formatData(res) {
      if (res.frontDeviceVos && res.frontDeviceVos.length) {
        let resData = res.frontDeviceVos;

        this.total = res.frontDeviceNum;
        resData.forEach((item, index) => {
          switch (item.status) {
            case "GREEN":
              item.position = "279px";
              break;
            case "YELLOW":
              item.position = "473px";
              break;
            case "RED":
              item.position = "182px";
              break;
            case "GRAY":
              item.position = "376px";
              break;
          }
          if (
            item.isDelete != undefined &&
            item.isDelete != null &&
            item.isDelete != 1
          ) {
            this.nodeData.nodeOption.push({
              name: item.deviceName,
              apKey: item.apKey,
              version: item.version,
            });
          }
        });
        this.chartForm.apKey = resData[0].apKey;
        this.clickIndex = 0;
        this.twoRow = {
          apKey: resData[0].apKey,
          createTime: resData[0].createTime,
          sourceId: resData[0].sourceId,
          deviceName: resData[0].deviceName,
          startTime: this.chartForm.startTime,
          endTime: this.chartForm.endTime,
        };
        this.listOptions = [...resData];
        this.getChart(this.chartForm);
      } else {
        this.listOptions = [];
      }

      res.statusMap && (this.statusMap = { ...res.statusMap });
      this.isOpen = res.isOpen || false;
    },
    formatChart(data) {
      this.chartOptions = [];
      $.each(data, (index, item) => {
        let obj = {};
        if (item.timeList != 0) {
          switch (index) {
            case "status":
              obj = {
                name: "设备历史状态",
                id: "historyStatus",
                widthCol: 24,
                data: {
                  x: item.timeList,
                  y: item.dataList,
                },
              };
              break;
            case "fileNum":
              obj = {
                name: "历史文件数",
                id: "historyFileNum",
                data: {
                  x: item.timeList,
                  y: item.dataList,
                },
              };
              break;
            case "fileSize":
              obj = {
                name: "历史文件大小",
                id: "historyFileSize",
                data: {
                  x: item.timeList,
                  y: item.dataList,
                },
              };
              break;
            case "flowSpeed":
              obj = {
                name: "历史流量大小",
                id: "historyFlowRate",
                data: {
                  x: item.timeList,
                  y: item.dataList,
                },
              };
              break;
            case "softLostRatio":
              obj = {
                name: "历史丢包率",
                id: "historyPacketLoss",
                data: {
                  x: item.timeList,
                  y: item.dataList,
                },
              };
              break;
          }
          if (index != "cpu" && index != "mem") {
            this.chartOptions.push(obj);
            // this.$nextTick(() => {
            //     this.drawLine(obj);
            // })
          }
        }
      });
      this.$nextTick(() => {
        this.drawLine(this.chartOptions);
      });
    },
    drawLine(data) {
      data.forEach((item) => {
        if (item.data.x && item.data.x.length != 0) {
          let chartDom = document.getElementById(item.id),
            options = {};

          this.chartShow = false;
          options = {
            title: {
              text: item.name,
              textStyle: {
                fontSize: 12,
                color: "#1e85e6",
              },
              left: 0,
            },
            tooltip: {
              trigger: "axis",
              // formatter:'{b0}</br>{a0}: {c0}',
              formatter: (obj) => {
                let content = "";
                switch (item.name) {
                  case "设备历史状态":
                    let val = "";
                    switch (obj[0].value) {
                      case 0:
                        val = "良好";
                        break;
                      case 1:
                        val = "告警";
                        break;
                      case 2:
                        val = "错误";
                        break;
                    }
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      val;
                    break;
                  case "历史流量大小":
                  case "历史丢包率":
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      (item.name == "历史流量大小"
                        ? obj[0].value + "mb/s"
                        : obj[0].value + "%");
                    break;
                  case "历史文件大小":
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      obj[0].value +
                      "mb";
                    break;
                  default:
                    content =
                      obj[0].name +
                      "<br/>" +
                      obj[0].marker +
                      obj[0].seriesName +
                      "：" +
                      obj[0].value;
                }
                return content;
              },
              axisPointer: {
                lineStyle: { type: "dashed" },
              },
            },
            calculable: true,
            grid: {
              top: "18%",
              bottom: "20%",
              left: 60,
              right: "3%",
            },
            xAxis: {
              type: "category",
              boundaryGap: ["10%", "10%"],
              axisLine: {
                lineStyle: { color: "#e6e9f1" },
              },
              axisLabel: {
                color: "#203e66",
                length: 7,
                formatter: (val) => {
                  let str = val.split(" ");
                  return str.join("\n");
                },
              },
              data: item.data.x,
            },
            yAxis: [
              {
                type: "value",
                axisTick: { show: false }, //坐标轴刻度
                axisLine: {
                  lineStyle: { color: "#e6e9f1" },
                },
                minInterval:
                  (item.name == "历史文件数" ||
                    item.name == "历史文件大小" ||
                    item.name == "设备历史状态") &&
                  1,
                axisLabel: {
                  show: true,
                  textStyle: {
                    color: "#203e66",
                  },
                  formatter: (val) => {
                    switch (item.name) {
                      case "设备历史状态":
                        switch (val) {
                          case -1:
                            val = "";
                            break;
                          case 0:
                            val = "良好";
                            break;
                          case 1:
                            val = "告警";
                            break;
                          case 2:
                            val = "错误";
                            break;
                        }
                        break;
                      case "历史丢包率":
                        val = val + "%";
                        break;
                    }
                    return val;
                  },
                },
                splitLine: {
                  lineStyle: { color: "#e6e9f1" },
                },
              },
            ],
            series: [
              {
                name: item.name,
                type: "line",
                smooth: true,
                itemStyle: {
                  normal: {
                    color: "rgb(99,223,246)",
                  },
                  emphasis: {
                    borderColor: "red",
                  },
                },
                data: item.data.y,
              },
            ],
          };
          item.chartContent = this.$echarts.init(chartDom);
          item.chartContent.setOption(options, true);
        } else {
          item.chartContent && item.chartContent.dispose();
        }
      });
      window.addEventListener("resize", this.bindChartEvent);
    },
    bindChartEvent(data) {
      this.chartOptions.forEach((item) => {
        if (item.data.x && item.data.x.length != 0) {
          item.chartContent.resize();
        }
      });
    },
    //功能函数
    switchClick(row) {
      let text = row.isDelete ? "上架" : "下架";

      this.$confirm("您确定要" + text + "该前端节点？", "提示", {
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          this.updateIsDelete(row);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    jumpBtn(apk) {
      if (apk) {
        this.getToken(apk);
      } else {
        this.waFn("获取apKey失败");
      }
    },
    ignoreBtn(data) {
      var descriptionArr = data.description && data.description.split(";"),
        exceptionArr = data.exceptionType && data.exceptionType.split(","),
        ignoreArr = [];
      if (!data.ignoreExceptionInfo) {
        descriptionArr &&
          descriptionArr.length &&
          descriptionArr.forEach((item, index) => {
            ignoreArr.push({
              exceptionType:
                exceptionArr && exceptionArr.length && exceptionArr[index],
              isIgnore: false,
              expireTime: "",
              desc: "",
            });
          });
      }
      this.ignoreData = {
        apKey: data.apKey,
        description: descriptionArr || [],
        ignoreExceptionVoList:
          (data.ignoreExceptionInfo && JSON.parse(data.ignoreExceptionInfo)) ||
          ignoreArr,
      };
      this.ignoreShow = true;
    },
    statusBtn() {
      this.statusOption.forEach((item, index) => {
        item.active = false;
      });
      this.form.status = "";
      this.getDataList(this.form);
    },
    updateAll() {
      this.checkUpgradePackage();
    },
    closeDialog(name, val) {
      switch (name) {
        case "一键升级":
          this.nodeShow = false;
          break;
        case "忽略":
          this.ignoreShow = false;
          break;
      }
      val === "SUCCESS" && this.getDataList();
    },
    listClick(index, item) {
      this.clickIndex = index;
      this.chartForm.apKey = item.apKey;
      this.twoRow = {
        apKey: item.apKey,
        createTime: item.createTime,
        sourceId: item.sourceId,
        deviceName: item.deviceName,
        startTime: this.chartForm.startTime,
        endTime: this.chartForm.endTime,
      };
      window.removeEventListener("resize", this.bindChartEvent);
      return false;
      this.getChart(this.chartForm);
    },
    listDetail(index, item) {
      this.clickIndex = index;
      this.chartForm.apKey = item.apKey;
      this.twoRow = {
        apKey: item.apKey,
        createTime: item.createTime,
        sourceId: item.sourceId,
        deviceName: item.deviceName,
        startTime: this.chartForm.startTime,
        endTime: this.chartForm.endTime,
      };
      let routeData = this.$router.resolve({
        path: "/frontManage/probeDetail",
        query: this.twoRow,
      });
      window.open(routeData.href, "_blank");
      // this.$router.push({
      //   path: "/frontManage/probeDetail",
      //   query: this.twoRow,
      // });
    },
    bytesToSize(fileSize, num, type) {
      if (!fileSize || fileSize == 0) return 0;
      if ((fileSize < 1024 && !type) || type == "B") {
        return fileSize + "B";
      } else if ((fileSize < 1024 * 1024 && !type) || type == "KB") {
        return (fileSize / 1024).toFixed(num) + "KB";
      } else if ((fileSize < 1024 * 1024 * 1024 && !type) || type == "MB") {
        return (fileSize / 1024 / 1024).toFixed(num) + "MB";
      } else {
        return (fileSize / 1024 / 1024 / 1024).toFixed(num) + "GB";
      }
    },
    statusClick(row) {
      let content = row.content,
        dataArr = [];

      this.statusOption.forEach((item, index) => {
        if (item.filed == row.filed) {
          row.active = !row.active;
        } else {
          item.active = false;
        }
      });
      this.form.status = row.filed;
      this.getDataList(this.form);
    },
    searchBtn() {
      this.getDataList(this.form);
    },
    dateBtn() {
      this.$refs["datepicker"].pickerVisible = true;
    },
    timeChange(val) {
      if (val && val.length != 0) {
        this.chartForm.startTime = val[0] + " 00:00:00";
        this.chartForm.endTime = val[1] + " 23:59:59";
        this.twoRow.startTime = val[0] + " 00:00:00";
        this.twoRow.endTime = val[1] + " 23:59:59";
      }
      this.getChart(this.chartForm);
    },
  },
  mounted() {
    this.initData();
    this.init();
  },
  destroyed() {
    window.removeEventListener("resize", this.bindChartEvent);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.probe-page {
  width: 100%;
  height: 100%;
  padding: 1%;
}
.probe-container {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #e2e2e2;
  -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  overflow: auto;
}
.content-row {
  margin: 10px 10px 0 10px;
  height: calc(100% - 10px);
}
.content-header {
  height: 30px;
  line-height: 36px;
  font-size: 14px;
  border-bottom: 1px solid #cdd0d3;
}
.content-text {
  display: inline-block;
  height: 30px;
  line-height: 36px;
  border-bottom: 2px solid #5174b4;
  padding: 0 3px;
  color: #203e66;
  font-weight: bold;
}
.form-col {
  text-align: right;
}
.form-col .search-input {
  width: 230px;
  margin-left: 5px;
}
.list-row {
  padding: 0px 15px;
  min-height: 400px;
  height: 100%;
}
.list-container {
  height: calc(100% - 30px);
  padding: 10px 0;
}
.list-container > div {
  height: 100%;
}
.list-container > div:first-child {
  border-right: 1px solid #e4e7ed;
}

.list-search {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 20px 0;
  padding: 0 20px;
}
.list-status {
  display: inline-block;
  margin-right: 10px;
  color: #203e66;
  cursor: pointer;
}
.list-status.active span {
  color: #3a8ee6;
}
.list-status > div:first-child {
  width: 10px;
  height: 10px;
  display: inline-block;
  border-radius: 2px;
}
.list-status:first-of-type > div:first-child {
  background: linear-gradient(180deg, #ffbbbb, #eb2e2e);
}
.list-status:nth-of-type(2) > div:first-child {
  background: linear-gradient(180deg, #ffc198, #eb7a2e);
}
.list-status:nth-of-type(3) > div:first-child {
  background: linear-gradient(180deg, #68e5bd, #18b281);
}
.list-status:nth-of-type(4) > div:first-child {
  background: linear-gradient(180deg, rgb(246, 247, 249), rgb(205, 208, 211));
}
.list-status:nth-of-type(5) > div:first-child {
  background: linear-gradient(180deg, #8bc8fc, #3da6ff);
}

.list-content {
  width: 100%;
  height: calc(100% - 70px);
  padding: 5px 8px 0 5px;
  overflow: auto;
  position: relative;
}
.list-content > div {
  padding-right: 9px;
}
.list-module {
  margin-bottom: 22px;
  height: calc(33% - 15px);
}
.list-module > div {
  height: 100%;
  border: 1px solid #ebeef5;
  box-shadow: 0px 3px 5px #d2ddf3;
}
.list-module > div.active {
  border: 1px solid #203e66b3;
  box-shadow: 0px 0px 10px #3d7bf7b3;
}
.list-name {
  background-color: #eaeff5;
  min-height: 50px;
  display: flex;
  align-items: center;
  padding: 0px 15px;
  height: 37%;
  justify-content: space-between;
  position: relative;
}
.list-name > div:first-child {
  display: inline-block;
}

.list-name > div:nth-child(2) {
  width: 110px;
}
.list-name > div:nth-child(2) button {
  margin-left: 10px;
}

.list-name .status-tag {
  width: 40px;
  height: 80px;
  position: absolute;
  right: 0px;
  top: 0px;
}
.list-name p:first-child {
  color: #203e66;
  font-size: 14px;
  margin-bottom: 0px;
  font-weight: 700;
  text-decoration: underline;
}
.list-name p:first-child span {
  font-weight: 500;
}

.list-name p:first-child a {
  cursor: pointer;
}
.list-name p:nth-child(2) {
  color: #738baa;
}
.list-num {
  height: 63%;
  padding: 10px 0;
  display: flex;
  align-items: center;
}
.list-num > div {
  padding: 0 5%;
  /* height: 100%; */
}
.list-num .list-key {
  width: 65%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 5px;
  white-space: nowrap;
  color: #203e66;
}
.list-num .list-data {
  display: flex;
  line-height: 22px;
}
.list-num .list-value {
  display: inline-block;
  padding-left: 5px;
  white-space: nowrap;
  color: #909399;
  width: 30%;
  overflow: hidden;
  text-align: right;
  text-overflow: ellipsis;
}
.chart-header {
  margin: 5px 10px;
  background-color: #eaeff5;
  display: flex;
  height: 30px;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}
.chart-header button:last-child {
  margin-left: 0;
}
.chart-name {
  color: #203e66;
  font-weight: 700;
  display: flex;
  align-items: center;
}
.chart-name .icon-date {
  margin-left: 5px;
  cursor: pointer;
}
.chart-container {
  height: calc(100% - 40px);
  overflow: auto;
  padding: 10px 10px 0px;
  position: relative;
}
.chart-container > div {
  height: 50%;
}
.chartShow {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #909399;
}
.chart-container .chart-content {
  height: 100%;
}
.front-icon {
  display: inline-block;
  height: 18px;
  width: 18px;
  background: url("../../assets/images/frontManage/frontHome.png");
}
.icon-date {
  background-position: 33px 0px;
}
.icon-drive {
  background-position: 33px -440px;
}
.icon-drive.shelves {
  background-position: 33px -474px;
}
.icon-fileSize {
  background-position: 33px -514px;
}
.icon-fileSize.shelves {
  background-position: 33px -554px;
}
.icon-restart {
  background-position: 33px -839px;
}
.icon-restart.shelves {
  background-position: 33px -877px;
}
.icon-packetLoss {
  background-position: 33px -593px;
}
.icon-packetLoss.shelves {
  background-position: 33px -634px;
}
.icon-fileNum {
  background-position: 33px -674px;
}
.icon-fileNum.shelves {
  background-position: 33px -714px;
}
.icon-heap {
  background-position: 33px -759px;
}
.icon-heap.shelves {
  background-position: 33px -799px;
}
.icon-exNumber {
  background-position: 33px -1368px;
}
.icon-exNumber.shelves {
  background-position: 33px -1408px;
}
.chart-name .datepicker {
  border: 0;
  background: transparent;
  width: 20px;
  position: relative;
  left: -18px;
  cursor: pointer;
}
.datepicker /deep/ i,
.datepicker /deep/ span,
.datepicker /deep/ input {
  display: none;
}
.statusBtn {
  padding: 0;
}

.node-dialog /deep/ .el-collapse {
  border-bottom: none;
}
</style>