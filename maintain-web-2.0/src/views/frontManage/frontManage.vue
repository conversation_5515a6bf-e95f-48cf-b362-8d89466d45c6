<!-- 前端管理 -->
<template>
  <div class="front-content">
    <el-row class="row-content">
      <el-col class="content-form">
        <el-form label-width="4rem" class="front-form">
          <el-row type="flex" class="row-bg">
            <el-col :span="8" class="form-btn">
              <div class="status-describe">
                前端管理错误<span class="num-red"> {{ policeNum }} </span>个，
                警告<span class="num-yellow"> {{ alarmNum }} </span>个，
                良好<span class="num-green"> {{ goodNum }} </span>个
              </div>
            </el-col>
            <el-col :span="16" class="form-search">
              <el-col :span="10">
                <span>运行状态：</span>
                <el-select v-model="form.status" clearable>
                  <el-option
                    v-for="(item, index) in statusOption"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  >
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="10">
                <span>节点状态：</span>
                <el-select v-model="form.isdelete" clearable>
                  <el-option
                    v-for="(item, index) in nodeStatusOption"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  >
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <button class="btn-style" @click.prevent="searchBtn">
                  <div class="button-icon button-search"></div>
                  <span>查询</span>
                </button>
                <!-- <button class="btn-style" @click.prevent="searchBtn">
                                    <div class="button-icon button-update"></div>
                                    <span>重置</span>
                                </button> -->
              </el-col>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
      <el-col :span="24" class="content-table">
        <el-table
          class="table-border"
          :data="dataList"
          :row-class-name="bodyClass"
          :header-cell-style="tableHeader"
          :header-row-class-name="headerClass"
          @cell-click="cellClick"
          height="100%"
          border
          v-loading="tableLoading"
        >
          <el-table-column prop="status" label="运行状态" width="100">
            <template slot-scope="scope">
              <el-popover
                :trigger="scope.row.content != '良好' ? 'hover' : 'manual'"
                placement="top-start"
              >
                <p class="popoverContent">{{ scope.row.description }}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag
                    size="small"
                    class="status-tag"
                    :color="scope.row.status"
                  >
                    {{ scope.row.content }}
                  </el-tag>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="apKey"
            label="节点编号"
            width="100"
          ></el-table-column>
          <el-table-column prop="serial" label="节点序列号" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.serial || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="前端名称" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.deviceName || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="version"
            label="版本"
            min-width="180"
          ></el-table-column>
          <el-table-column
            prop="note"
            label="备注"
            min-width="160"
            class-name="overflowColumn"
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.editShow"
                :ref="scope.row.refName"
                v-model="scope.row.inputVal"
                @blur="noteBlur(scope.row)"
                :autosize="true"
                :show-word-limit="true"
                maxlength="255"
                type="textarea"
                rows="1"
                class="noteInput"
              >
              </el-input>
              <el-popover
                class="popoverSpan"
                trigger="hover"
                placement="top-start"
                v-else
              >
                <p class="popoverContent">{{ scope.row.note || "-" }}</p>
                <span slot="reference" class="name-wrapper edit-span">
                  {{ scope.row.note || "-" }}
                </span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="160">
            <template slot-scope="scope">
              <el-switch
                disabled
                v-model="scope.row.switchValue"
                active-color="#25ce88"
                inactive-color="#cfcfcf"
                :active-text="scope.row.switchText"
                class="font-switch"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="内存使用" min-width="180">
            <template slot-scope="scope">
              <div class="progress-text">
                <div>
                  {{
                    scope.row.usedMemory && scope.row.usedMemory != "未知"
                      ? currencyCapacity(Number(scope.row.usedMemory) * 1024) +
                        "/" +
                        currencyCapacity(Number(scope.row.totalMemory) * 1024)
                      : "-"
                  }}
                </div>
                <span
                  v-if="scope.row.usedMemory && scope.row.usedMemory != '未知'"
                >
                  {{
                    scope.row.memoryUsedPercent &&
                    scope.row.memoryUsedPercent + "%"
                  }}
                </span>
              </div>
              <el-progress
                v-if="scope.row.usedMemory && scope.row.usedMemory != '未知'"
                :percentage="scope.row.memoryUsedPercent"
                :show-text="false"
                color="#8aa7f4"
              >
              </el-progress>
            </template>
          </el-table-column>
          <el-table-column label="磁盘使用" min-width="200">
            <template slot-scope="scope">
              <div class="progress-text">
                <div>
                  {{
                    scope.row.monitorDirFree && scope.row.monitorDirTotal
                      ? currencyCapacity(scope.row.monitorDirUse) +
                        "/" +
                        currencyCapacity(scope.row.monitorDirTotal)
                      : "-"
                  }}
                </div>
                <span
                  v-if="scope.row.monitorDirFree && scope.row.monitorDirTotal"
                >
                  {{
                    scope.row.monitorDirPercent &&
                    scope.row.monitorDirPercent + "%"
                  }}
                </span>
              </div>
              <el-progress
                v-if="scope.row.monitorDirFree && scope.row.monitorDirTotal"
                :percentage="scope.row.monitorDirPercent"
                :show-text="false"
                color="#5db1f2"
              >
              </el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="monitorDirStatus" label="磁盘状态" width="100">
            <template slot-scope="scope">
              <el-popover
                class="popoverSpan"
                :trigger="scope.row.monitorDescription ? 'hover' : 'manual'"
                placement="top-start"
                v-if="scope.row.monitorDirContent"
              >
                <p class="popoverContent">{{ scope.row.monitorDescription }}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag
                    size="small"
                    class="status-tag"
                    :color="scope.row.monitorDirStatus"
                  >
                    {{ scope.row.monitorDirContent }}
                  </el-tag>
                </div>
              </el-popover>
              <span v-else>{{ "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="cpuAvg" label="CPU平均值" width="100">
          </el-table-column>
          <el-table-column
            prop="dayFlowspeedMax"
            label="24小时流量最大值"
            min-width="150"
            :formatter="currencyFlowspeed"
          ></el-table-column>
          <el-table-column
            prop="dayFlowspeedMin"
            label="24小时流量最小值"
            min-width="150"
            :formatter="currencyFlowspeed"
          ></el-table-column>
          <el-table-column
            prop="dayFlowspeedAvg"
            label="24小时流量平均值"
            min-width="150"
            :formatter="currencyFlowspeed"
          ></el-table-column>
          <el-table-column
            prop="historyFlowspeedMax"
            label="历史流量最大值"
            min-width="130"
            :formatter="currencyFlowspeed"
          ></el-table-column>
          <el-table-column
            prop="historyFlowspeedMin"
            label="历史流量最小值"
            min-width="130"
            :formatter="currencyFlowspeed"
          ></el-table-column>
          <el-table-column
            prop="historyFlowspeedAvg"
            label="历史流量平均值"
            min-width="130"
            :formatter="currencyFlowspeed"
          ></el-table-column>
          <el-table-column prop="createTime" label="日志时间" min-width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reportTime" label="入库时间" min-width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.reportTime || "-" }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="deviceId" label="设备编号" min-width="340"></el-table-column> -->
          <el-table-column
            align="center"
            label="操作"
            fixed="right"
            min-width="120"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="showDetail(scope.row)"
                class="text-button form-btn"
                v-if="btn.frontDetail.value && scope.row.sourceId"
              >
                <i class="iconfont icon-detail"></i>
                详情
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <webSocketSend
      v-if="socketShow"
      @socketReturn="closeDialog"
      :socketData="socketData"
    ></webSocketSend>
  </div>
</template>

<script>
import webSocketSend from "@/components/webSocketSend.vue";
import getPermission from "@/utils/permissions.js";
import {
  bodyRowClassName,
  tableHeaderStyle,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "front-content",
  components: { webSocketSend },
  data() {
    return {
      pageTimer: null,
      policeNum: 0,
      alarmNum: 0,
      goodNum: 0,

      detailFormVisible: false,
      tableLoading: false,
      socketShow: false,

      btn: {
        frontDetail: {
          name: "前端详情",
          value: false,
        },
      },
      form: {
        status: null,
        isdelete: null,
      },

      dataList: [],
      statusOption: [
        {
          label: "良好",
          value: 0,
        },
        {
          label: "警告",
          value: 1,
        },
        {
          label: "错误",
          value: 2,
        },
      ],
      nodeStatusOption: [
        {
          label: "下架状态",
          value: 1,
        },
        {
          label: "上架状态",
          value: 0,
        },
      ],
    };
  },
  methods: {
    init() {
      this.getDataList(false, this.form);
      getPermission(this.$route.name, this.btn);
      this.getStatus();
      this.requestInterval();
    },

    //请求数据函数
    getDataList(IntervalLoading, data) {
      let _this = this;

      _this.tableLoading = IntervalLoading ? false : true;
      _this.goodNum = 0;
      _this.alarmNum = 0;
      _this.policeNum = 0;

      _this.$http
        .post("front-device/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.tableLoading = false;
          if (res.data.code == 0) {
            // _this.dataList = res.data.data.list;
            res.data.data && _this.formatData(res.data.data.list);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    saveNote(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/note", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.getDataList(false, this.twoRow, this.form);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    requestInterval() {
      if (this.pageTimer) {
        clearInterval(this.pageTimer);
      } else {
        this.pageTimer = setInterval(() => {
          this.getDataList(true, this.form);
        }, 60000);
      }
    },

    //处理数据函数
    formatData(data) {
      data.forEach((item, index) => {
        item = this.formatStatus(item, "status", "content", "", true);
        item = this.formatStatus(
          item,
          "monitorDirStatus",
          "monitorDirContent",
          "monitorDescription"
        );
        switch (item.isDelete) {
          case 1:
            item.switchValue = false;
            item.switchText = "下架状态";
            break;
          case 0:
            item.switchValue = true;
            item.switchText = "上架状态";
            break;
        }
        item.usedMemory &&
          item.totalMemory &&
          (item.memoryUsedPercent = Number(
            (
              (Number(item.usedMemory) / Number(item.totalMemory)) *
              100
            ).toFixed(2)
          ));

        if (item.monitorDirFree && item.monitorDirTotal) {
          item.monitorDirUse =
            Number(item.monitorDirTotal) - Number(item.monitorDirFree);
          item.monitorDirPercent = Number(
            (
              (Number(item.monitorDirUse) / Number(item.monitorDirTotal)) *
              100
            ).toFixed(2)
          );
        }
        item.editShow = false;
        item.inputVal = item.note || "";
        item.refName = "noteInput" + index;
      });
      this.dataList = [...data];
    },
    formatStatus(item, status, content, description, countShow) {
      switch (item[status]) {
        case "GREEN":
          item[status] = "#25ce88";
          item[content] = "良好";
          countShow == true && this.goodNum++;
          break;
        case "YELLOW":
          item[status] = "#fb843b";
          item[content] = "警告";
          description && (item[description] = "当前剩余空间不足200G");
          countShow == true && this.alarmNum++;
          break;
        case "RED":
          item[status] = "#f93846";
          item[content] = "错误";
          description && (item[description] = "当前剩余空间不足100G");
          countShow == true && this.policeNum++;
          break;
        case "GRAY":
          item[status] = "#cdd0d3";
          item[content] = "关闭";
          break;
      }
      return item;
    },
    currencyFlowspeed(row, column, value) {
      if (value || value == 0) {
        if (value >= 0 && value < 8) {
          value = value + "bits/s";
        } else if (value >= 8 && value < 8192) {
          value = (value / 8).toFixed(2) + "bytes/s";
        } else if (value >= 8192 && value < 8388608) {
          value = (value / 8192).toFixed(2) + "Kb/s";
        } else if (value >= 8388608 && value < 8589934592) {
          value = (value / 8388608).toFixed(2) + "Mb/s";
        } else if (value >= 8589934592) {
          value = (value / 8589934592).toFixed(2) + "Gb/s";
        }
        return value;
      } else {
        return "-";
      }
    },
    currencyCapacity(value) {
      if (value && value != "未知") {
        value = Number(value);
        if (value > 0 && value < 1024) {
          value = value + "KB";
        } else if (value >= 1024 && value < 1048576) {
          value = (value / 1024).toFixed(2) + "MB";
        } else if (value >= 1048576 && value < 1073741824) {
          value = (value / 1048576).toFixed(2) + "GB";
        } else if (value >= 1073741824) {
          value = (value / 1073741824).toFixed(2) + "TB";
        }
        return value;
      } else {
        return "-";
      }
    },

    //功能函数
    searchBtn() {
      this.dataList = [];
      this.getDataList(false, this.form);
    },
    showDetail(row) {
      this.$router.push({
        path: "/frontDetail",
        query: {
          apKey: row.apKey,
          createTime: row.createTime,
          sourceId: row.sourceId,
        },
      });
    },
    bodyClass({ row, rowIndex }) {
      row.rowIndex = rowIndex;
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    switchClick(row) {
      row.switchValue ? this.closeBtn(row) : this.openBtn(row);
    },
    cellClick(row, column, cell, event) {
      if (column.label == "备注" && row.editShow == false) {
        row.inputVal = row.note || "";
        row.editShow = true;
        this.$nextTick(() => {
          $(".noteInput textarea").eq(0).focus();
        });
      }
    },
    noteBlur(row) {
      let data = {};

      row.inputVal == undefined && (row.inputVal = "");
      if (row.inputVal == row.note) {
        row.editShow = false;
        return;
      }

      data = {
        apKey: row.apKey,
        note: row.inputVal,
      };
      this.saveNote(data);
    },
    openBtn(value) {
      this.$confirm("您确定要上架该节点吗？", "提示", {
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          this.socketData = {
            operateType: 1,
            param: JSON.stringify({
              AP_KEY: value.apKey,
              DEVICE_NAME: value.deviceName,
              operateType: 1,
            }),
            service: "frontDevice",
            title: "前端节点上架过程",
          };
          this.socketShow = true;
        })
        .catch(() => {});
    },
    closeBtn(value) {
      this.$confirm("您确定要下架该节点吗？", "提示", {
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          this.socketData = {
            operateType: 0,
            param: JSON.stringify({
              AP_KEY: value.apKey,
              DEVICE_NAME: value.deviceName,
              operateType: 0,
            }),
            service: "frontDevice",
            title: "前端节点下架过程",
          };
          this.socketShow = true;
        })
        .catch(() => {});
    },
    closeDialog(val) {
      this.socketData = {};
      this.socketShow = false;
      val === "SUCCESS" && this.getDataList(false, this.form);
    },
  },
  mounted() {
    this.init();
  },
  destroyed() {
    clearInterval(this.pageTimer);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.front-content {
  height: 100%;
  width: 98%;
  margin: 0 1%;
}
.front-form {
  margin: 1.5rem;
  /* padding-right:1rem; */
}
.table-row {
  margin-top: 10px;
}
.title-row:not(:nth-child(1)) {
  margin-top: 30px;
}
.status-tag {
  color: #fff;
  border: 0;
  width: 40px;
  height: 20px;
  line-height: 20px;
}
.progress-text {
  color: #203e66;
  text-align: right;
}
.progress-text div {
  float: left;
}
.form-search {
  color: #203e66;
  text-align: right;
}
.button-icon {
  background: url("../../assets/images/agentManage/icon-Agent.png");
  display: inline-block;
  height: 16px;
  width: 16px;
  background-size: cover !important;
  vertical-align: sub;
}
.button-search {
  background-position: 0px 129px;
}
.btn-style:hover .button-search {
  background-position: 0px 89px;
}
.noteInput {
  width: 100%;
  max-width: 100%;
}
</style>