<!-- 版本更新说明 -->
<template>
    <div class="versionDetail" v-loading.fullscreen='fullscreenLoading'>
        <el-row class="version-title" type="flex" justify="space-around">
            <el-col :span="12">
                <p>平台管理更新日志</p>
            </el-col>
            <el-col :span="12" class="title-right">
                <span>版本号：</span>
                <el-select v-model="form.version" @change="versionChange">
                    <el-option 
                        v-for="item in versionOptions" 
                        :key="item" 
                        :label="item" 
                        :value="item">
                    </el-option>
                </el-select>
            </el-col>
        </el-row>
        <el-row class="version-content">
            <h1>平台管理升级日志</h1>
            <h3>重大调整 <span v-if="releaseTime">({{releaseTime}})</span></h3>
            <ul v-if="versionContent && versionContent.length != 0">
                <li v-for="item in versionContent" 
                    :key="item">
                    {{item}}
                </li>
            </ul>
        </el-row>
    </div>
</template>

<script>
export default {
    name: "versionDetail",
    data() {
        return {
            releaseTime: "",
            fullscreenLoading: false,
            form: {
                version: ""
            },
            versionOptions: [],
            versionContent: []
        };
    },
    methods: {
        getVersionList() {
            let _this = this;

            _this.$http
                .post("note/version")
                .then(res => {
                    if (res.data.code == 0) {
                        this.versionOptions = res.data.data || [];
                        if (
                            this.versionOptions &&
                            this.versionOptions.length != 0
                        ) {
                            this.form.version = this.versionOptions[0];
                            this.getVersionDetail(this.form.version);
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    console.log(error);
                    _this.erFn();
                });
        },
        getVersionDetail(data) {
            let _this = this,
                form = {
                    version: data || ""
                };

            _this.fullscreenLoading = true;
            _this.$http
                .post("note/version/detail", _this.qs.stringify(form))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        this.versionContent = res.data.data.changeItems || [];
                        this.releaseTime = res.data.data.releaseTime;
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    console.log(error);
                    _this.erFn();
                });
        },
        versionChange(val) {
            this.getVersionDetail(val);
        }
    },
    mounted() {
        this.getVersionList();
    }
};
</script>

<style scoped>
.versionDetail {
    height: 100%;
    width: 100%;
    padding: 8px;
    background: #fff;
}
.version-title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-family: "微软雅黑";
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
    color: #818daa;
    text-align: left;
    background-color: #f0f0f0;
    padding: 0 10px;
}
.title-right {
    text-align: right;
}
.version-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    padding: 20px;
    max-width: 960px;
    margin: 0 auto;
}
h1 {
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
}
h3 {
    margin: 20px 0;
}
ul li {
    font-size: 14px;
}
</style>