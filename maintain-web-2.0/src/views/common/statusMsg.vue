<!-- Agent管理 -->
<template>
  <div class="status-describe">
    <span
      v-for="(item, index) in option"
      :key="index"
      @click="statusClick(item, $event)"
    >
      <el-tag effect="dark" :type="item.className" v-if="item.isShow">
        {{ item.content + "(" + item.num + ")" }}</el-tag
      >
      <!-- <el-badge
        :type="item.className"
        :max="999"
        v-if="item.isShow"
        :value="item.num"
      >
        <el-button> {{ item.content }}</el-button>
      </el-badge> -->

      <!-- <span v-if="item.isShow">
        {{ item.content }}
        <a :class="item.className" v-text="item.num"></a>
        个
      </span> -->
    </span>
  </div>
</template>

<script>
export default {
  name: "status-describe",
  props: {
    statusOption: Object,
    statusData: Object,
    statusChange: Function,
  },
  data() {
    return {
      option: {
        TOTAL: {
          isShow: true,
          field: "",
          content: "全部",
          num: 0,
          className: "default",
        },
        RED: {
          isShow: true,
          field: "RED",
          content: "错误",
          num: 0,
          className: "danger",
        },
        YELLOW: {
          isShow: true,
          field: "YELLOW",
          content: "告警",
          num: 0,
          className: "warning",
        },
        GREEN: {
          isShow: true,
          field: "GREEN",
          content: "良好",
          num: 0,
          className: "success",
        },
        GRAY: {
          isShow: false,
          field: "GRAY",
          content: "关闭",
          num: 0,
          className: "info",
        },
      },
    };
  },
  watch: {
    statusData: {
      handler(newvalue, oldvalue) {
        this.setStatus(newvalue);
      },
      deep: true,
    },
  },
  methods: {
    statusClick(item, el) {
      if (this.statusChange(item) != false) {
        var $children = el.currentTarget.parentNode.children;
        for (var i = 0; i < $children.length; i++) {
          $children[i].classList.remove("active");
        }
        el.currentTarget.classList.add("active");
      }
    },
    setStatus(data) {
      let RED = data.RED || 0;
      let YELLOW = data.YELLOW || 0;
      let GREEN = data.GREEN || 0;
      let GRAY = data.GRAY || 0;

      this.option.RED.num = RED;
      this.option.YELLOW.num = YELLOW;
      this.option.GREEN.num = GREEN;
      this.option.GRAY.num = GRAY;

      this.option.TOTAL.num = RED + YELLOW + GREEN + GRAY;
    },
    setOption() {
      if (typeof this.statusOption !== "object") return;
      let keys = Object.keys(this.statusOption);
      for (var i = 0; i < keys.length; i++) {
        var key = keys[i];
        this.option[key].isShow = this.statusOption[key].isShow;
      }
    },
  },
  created() {
    this.setOption();
    this.setStatus(this.statusData || {});
  },

  destroyed() {},
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.el-tag--dark.el-tag--success {
  background-color: rgb(37, 206, 136);
}

.el-tag--dark.el-tag--danger {
  background-color: rgb(249, 56, 70);
}

.el-tag--dark.el-tag--warning {
  background-color: rgb(251, 132, 59);
}

.el-tag--dark.el-tag--default {
  background-color: #f2f6fc;
  border-color: rgba(0, 0, 0, 0.3);
  color: #283649;
}

.status-describe > span:first-child {
  margin: 0px 6px 0px 0px;
}
.status-describe > span {
  margin: 0px 6px;
  cursor: pointer;
  display: inline-block;
}

.status-describe .active > span.el-tag {
  text-decoration: underline;
}

/* .el-tag--dark.el-tag--danger {
  background-color: rgb(249, 56, 70);
} */
</style>