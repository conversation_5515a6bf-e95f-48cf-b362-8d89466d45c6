<!-- 添加程序 -->
<template>
  <div class="procedure-content" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      :title="dialogData.title"
      :visible.sync="addFormVisible"
      :close-on-click-modal="false"
      width="630px"
      top="10vh"
      class="dialog-border"
      custom-class="procedureDialog"
      :modal="false"
      @close="resetForm"
      v-dialogDrag
    >
      <el-row type="flex" class="row-bg">
        <el-form
          label-width="100px"
          :model="addForm"
          :rules="rules"
          ref="addForm"
          class="procedure-form"
        >
          <el-row>
            <!-- <el-col :span="24">
              <el-form-item label="IP地址:" prop="ip">
                <el-input maxlength="15" v-model="addForm.ip"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="部署目录:" prop="deployPath">
                <el-input
                  maxlength="50"
                  v-model="addForm.deployPath"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="程序名称:" prop="programName">
                <el-input
                  maxlength="50"
                  v-model="addForm.programName"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="配置路径:" prop="configPath">
                <el-input
                  maxlength="50"
                  v-model="addForm.configPath"
                  placeholder="例如：WEB程序中config文件下的conf.json配置,填写为config/conf.json"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="addFormVisible = false" class="btn-style">
          关闭
        </button>
        <button
          @click.prevent="saveBtn"
          v-loading.fullscreen.lock="fullscreenLoading"
          class="btn-style primary-btn"
        >
          确定
        </button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkIPNotEnablePort, checkChinese } from "@/utils/validate.js";

export default {
  name: "procedure-content",
  props: { dialogData: Object },
  data() {
    return {
      fullscreenLoading: false,
      addFormVisible: true,
      addForm: {
        // ip: "", //"IP"
        deployPath: "", // "部署目录"
        programName: "", //"程序名"
        configPath: "", //"配置名"
      },
      rules: {
        deployPath: [
          {
            required: true,
            trigger: "change",
            message: "请输入部署目录",
          },
        ],
        programName: [
          {
            required: true,
            trigger: "change",
            message: "请输入程序名称",
          },
        ],
        configPath: [
          {
            required: true,
            trigger: "change",
            message: "请输入配置路径",
          },
        ],
      },
    };
  },
  methods: {
    init() {
      this.addForm = this.dialogData.row || this.addForm;
    },
    //请求函数
    sendQuest(data, url, text) {
      let _this = this;
      _this.fullscreenLoading = true;
      _this.$http
        .post(url, _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn(text);
            _this.resetForm("SUCCESS");
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    //功能函数
    addData(obj) {
      let url = "";
      let text = "";
      switch (this.dialogData.type) {
        case "add":
          url = "software/add_modify_program.json";
          text = "添加成功";
          break;
        case "edit":
          url = "software/update_modify_program.json";
          text = "编辑成功";
          break;
      }
      this.sendQuest(obj, url, text);
    },
    saveBtn() {
      let _this = this;
      _this.$refs["addForm"].validate((valid) => {
        if (valid) {
          _this.addData(_this.addForm);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(val) {
      this.$refs["addForm"].resetFields();
      this.addFormVisible = false;
      this.$emit("returnMain", val);
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.procedure-content {
  height: 100%;
  overflow-y: auto;
}
.procedure-form {
  width: 90%;
  margin: auto;
}
.btn-style:first-child {
  margin-right: 10px;
}
.btn-style {
  padding: 0 20px;
}
.multipleIP {
  width: 100%;
}
.batchAdd {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>