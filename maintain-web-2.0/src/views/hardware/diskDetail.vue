<!-- 磁盘设置 -->
<template>
  <div class="disk-content" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      title="编辑告警阈值"
      :visible.sync="addFormVisible"
      width="800px"
      top="10vh"
      class="dialog-border"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-row v-if="typeof diskData.ips == 'undefined'">
        <el-button @click="addTab">添加磁盘设置</el-button>
      </el-row>
      <el-tabs
        v-model="diskTabValue"
        type="border-card"
        class="diskTab"
        v-if="diskOptions && diskOptions.length"
      >
        <el-tab-pane
          v-for="(item, index) in diskOptions"
          :key="item.name"
          :label="item.name"
          :name="item.name"
        >
          <el-row type="flex" class="row-bg">
            <el-form
              label-width="120px"
              :model="item.data"
              :rules="rules"
              :ref="'addForm'"
              class="diskForm"
            >
              <el-col :span="24" v-if="item.name == '新增盘符'">
                <el-form-item label="磁盘盘符:" prop="disk">
                  <el-select class="alarmInput" v-model="item.data.disk">
                    <el-option
                      v-for="(item, index) in diskAddOption"
                      :key="index"
                      :label="item"
                      :value="item"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="24"
                v-if="item.name != 'CPU' && item.name != '内存'"
              >
                <el-form-item label="警告阈值:" prop="warnSize">
                  <el-input
                    class="alarmInput"
                    v-model="item.data.warnSize"
                    clearable
                  >
                    <el-select
                      slot="append"
                      class="alarmSelect"
                      v-model="item.data.warnUnit"
                    >
                      <el-option
                        v-for="(item, index) in sizeOption"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="item.name != '全部磁盘'">
                <el-form-item label="警告百分比阈值:" prop="warnPercent">
                  <el-input
                    class="alarmInput"
                    v-model="item.data.warnPercent"
                    clearable
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="24"
                v-if="item.name != 'CPU' && item.name != '内存'"
              >
                <el-form-item label="错误阈值:" prop="errorSize">
                  <el-input
                    class="alarmInput"
                    v-model="item.data.errorSize"
                    clearable
                  >
                    <el-select
                      slot="append"
                      class="alarmSelect"
                      v-model="item.data.errorUnit"
                    >
                      <el-option
                        v-for="(item, index) in sizeOption"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="item.name != '全部磁盘'">
                <el-form-item label="错误百分比阈值:" prop="errorPercent">
                  <el-input
                    class="alarmInput"
                    v-model="item.data.errorPercent"
                    clearable
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col
                :span="24"
                v-if="
                  !item.data.default &&
                  (item.name == '全部磁盘' ||
                    item.name == '内存' ||
                    item.name == 'CPU')
                "
              >
                <el-form-item label="当前设置是否生效:">
                  <el-radio-group v-model="item.data.status">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <p v-if="item.name == '全部磁盘'" class="alarmRemark">
                  备注：设置不生效时，警告阈值默认为500GB，错误阈值默认为1TB
                </p>
                <p v-else class="alarmRemark">
                  备注：设置不生效时，警告百分比阈值默认为80%，错误百分比阈值默认为90%
                </p>
                <p
                  v-if="typeof diskData.ips != 'undefined'"
                  class="alarmRemark"
                >
                  注意：批量修改时，将会对所有选中数据进行覆盖。
                </p>
              </el-col>
              <el-col class="diskButton">
                <button
                  @click.prevent="saveBtn(index, item)"
                  v-loading.fullscreen.lock="fullscreenLoading"
                  class="btn-style btn-dialog primary-btn"
                >
                  确定
                </button>
              </el-col>
            </el-form>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "disk-content",
  props: { diskData: Object },
  data() {
    return {
      diskTabValue: "全部磁盘",
      fullscreenLoading: false,
      addFormVisible: false,
      rules: {
        disk: [{ required: true, message: "请选择盘符" }],
        errorSize: [{ required: true, message: "请输入错误阈值" }],
        warnSize: [{ required: true, message: "请输入警告阈值" }],
        warnPercent: [{ required: true, message: "请输入警告百分比阈值" }],
        errorPercent: [{ required: true, message: "请输入错误百分比阈值" }],
      },
      sizeOption: [
        {
          label: "MB",
          value: "M",
        },
        {
          label: "GB",
          value: "G",
        },
        {
          label: "TB",
          value: "T",
        },
      ],
      diskOptions: [],
      diskAddOption: [],
    };
  },
  methods: {
    //请求数据函数
    getAlarmData(data) {
      let _this = this;
      if (typeof data.ips != "undefined") {
        //默认配置
        this.diskOptions = [
          {
            data: {
              warnSize: "1",
              default: false,
              warnType: "HARDWARE_DISK_SPACE",
              errorUnit: "G",
              errorSize: "500",
              warnUnit: "T",
              status: 1,
            },
            name: "全部磁盘",
          },
          {
            data: {
              warnPercent: "80",
              default: false,
              warnType: "CPU_SPACE",
              errorPercent: "90",
              status: 1,
            },
            name: "CPU",
          },
          {
            data: {
              warnPercent: "80",
              default: false,
              warnType: "MEMORY_SPACE",
              errorPercent: "90",
              status: 1,
            },
            name: "内存",
          },
        ];
      } else {
        _this.fullscreenLoading = true;
        _this.$http
          .post("hardware/queryWarning", _this.qs.stringify(data))
          .then((res) => {
            _this.fullscreenLoading = false;
            if (res.data.code == 0) {
              this.diskOptions = [...res.data.data];
            } else {
              res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
            }
          })
          .catch((error) => {
            _this.fullscreenLoading = false;
            _this.erFn();
            console.log(error);
          });
      }
    },
    getAddDisk(data) {
      let _this = this;
      _this.$http
        .post("hardware/queryDisk", _this.qs.stringify(data))
        .then((res) => {
          if (res.data.code == 0) {
            this.diskAddOption = [...res.data.data];
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
          console.log(error);
        });
    },
    saveAlarmData(data) {
      let _this = this;
      if (typeof data.ips != "undefined") {
        this.$confirm("确定将选中数据的配置进行覆盖吗？", "提示", {
          roundBUtton: true,
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
          closeOnClickModal: false,
        })
          .then(() => {
            _this.fullscreenLoading = true;
            _this.$http
              .post("hardware/set_all_warning", _this.qs.stringify(data))
              .then((res) => {
                _this.fullscreenLoading = false;
                if (res.data.code == 0) {
                  _this.suFn("修改成功");
                  _this.resetForm("success");
                } else {
                  res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                }
              })
              .catch((error) => {
                _this.fullscreenLoading = false;
                _this.erFn();
                console.log(error);
              });
          })
          .catch(() => {});
      } else {
        _this.fullscreenLoading = true;
        _this.$http
          .post("hardware/warning", _this.qs.stringify(data))
          .then((res) => {
            _this.fullscreenLoading = false;
            if (res.data.code == 0) {
              _this.suFn("修改成功");
              _this.diskTabValue == "新增盘符";
              if (_this.diskTabValue == "新增盘符") {
                _this.$nextTick(() => {
                  _this.diskTabValue = "全部磁盘";
                });
                _this.diskOptions.pop();
              }
              _this.getAlarmData(this.diskData);
            } else {
              res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
            }
          })
          .catch((error) => {
            _this.fullscreenLoading = false;
            _this.erFn();
            console.log(error);
          });
      }
    },

    //功能函数
    saveBtn(index, item) {
      let _this = this,
        data = {};

      _this.$refs["addForm"][index].validate((valid) => {
        if (valid) {
          switch (item.name) {
            case "全部磁盘":
              item.data.disk = "DEFAULT";
              break;
            case "新增盘符":
              break;
            default:
              item.data.disk = item.name;
          }
          let data = { ...item.data };
          data = Object.assign(data, this.diskData);
          data.warnSize = data.warnSize && data.warnSize + data.warnUnit;
          data.errorSize = data.errorSize && data.errorSize + data.errorUnit;
          this.saveAlarmData(data);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(val) {
      this.$emit("returnMain", val);
    },
    addTab() {
      let isAdd = true;
      $.each(this.diskOptions, (index, item) => {
        if (item.name == "新增盘符") {
          isAdd = false;
          return false;
        }
      });
      if (isAdd) {
        this.diskOptions.push({
          name: "新增盘符",
          data: {
            ip: "***************",
            status: 1,
            warnType: "HARDWARE_DISK_SPACE",
            disk: null,
            errorPercent: null,
            errorSize: null,
            warnPercent: null,
            warnSize: null,
            errorUnit: "G",
            warnUnit: "G",
          },
        });
        this.diskTabValue = "新增盘符";
        this.getAddDisk(this.diskData);
      } else {
        this.waFn("已存在新增盘符选项");
      }
    },
  },
  mounted() {
    this.addFormVisible = true;
    this.getAlarmData(this.diskData);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
/* .diskTab {
  margin-top: 30px;
} */

.dialog-border .el-row {
  margin-bottom: 15px;
}
.diskForm {
  padding: 15px;
}
.diskButton {
  margin-top: 18px;
  text-align: center;
}
.alarmInput {
  width: calc(100% - 100px);
}
.alarmSelect {
  width: 100px;
}
.alarmRemark {
  margin-left: 10px;
  color: #f53749;
}
.btn-dialog {
  padding: 0 20px;
}
</style>