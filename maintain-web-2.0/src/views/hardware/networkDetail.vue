<!-- 一键部署 -->
<template>
    <div class="network-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-dialog 
            title="网络异常情况" 
            :visible.sync="addFormVisible" 
            class="dialog-border" width="1000px" 
            :close-on-click-modal="false"
            @close="resetForm"
            v-dialogDrag>

            <el-row class="dialog-table dialog-content">
                <el-col :span="24" class="network-table">
                    <el-table 
                        class="table-border" 
                        :data="dataList" 
                        :row-class-name="bodyClass" 
                        :header-cell-style="tableHeader" 
                        :header-row-class-name="headerClass" 
                        height="100%" border >

                        <el-table-column prop="dest" label="目的IP"></el-table-column>
                        <el-table-column prop="source" label="源IP"></el-table-column>
                        <el-table-column prop="startTime" label="时间"></el-table-column>
                        <el-table-column prop="timeSpent" label="响应耗时" :formatter="formatTime"></el-table-column>
                        <el-table-column align="connectFlag" label="是否连接成功">
                            <template slot-scope="scope">
                                <span>{{scope.row.connectFlag ? '是' : '否'}}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="24" class="table-page">
                    <el-pagination
                        @size-change="handleSizeChange" 
                        @current-change="handleCurrentChange" 
                        :current-page="form.pn" 
                        :page-sizes="[20,50,100]"
                        :page-size="form.ps"
                        layout="total,sizes,prev,pager,next,jumper"
                        :total="form.total"
                        background
                        popper-class="pagination-popper">
                    </el-pagination>
                </el-col>
            </el-row>
            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="addFormVisible = false" class="btn-style">关闭</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    bodyRowClassName,
    tableHeaderStyle,
    headerRowClassName
} from "@/utils/tableStyle.js";

export default {
    name: "network-content",
    props: { netData: Object },
    data() {
        return {
            tabName: "",
            version: "",

            addFormVisible: true,
            fullscreenLoading: false,

            dataList: [],

            form: {
                ip: "",
                pn: 1, //当前页
                ps: 20, //当前每页条数
                total: 0 //总条数
            }
        };
    },
    methods: {
        init() {
            this.form.ip = this.netData.ip;
            this.getDataList(this.form);
        },
        //请求数据函数
        getDataList(data) {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .post("hardware/network/exception", _this.qs.stringify(data))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            _this.dataList = res.data.data.list || [];
                            _this.form.total = res.data.data.total || 0;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        formatTime(row, column, value) {
            return value + "ms";
        },

        //功能函数
        resetForm(val) {
            this.addFormVisible = false;
            this.$emit("returnMain", val);
        },
        handleSizeChange(val) {
            this.form.ps = val;
            this.getDataList(this.form);
        },
        handleCurrentChange(val) {
            this.form.pn = val;
            this.getDataList(this.form);
        },
        bodyClass() {
            return bodyRowClassName();
        },
        headerClass() {
            return headerRowClassName();
        },
        tableHeader() {
            return tableHeaderStyle();
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.deploy-content {
    height: 100%;
    overflow-y: auto;
}
.btn-row {
    text-align: right;
    margin-right: 10px;
}
.btn-style {
    padding: 0 20px;
}
.btn-style:first-child {
    margin-right: 10px;
}
.network-table {
    height: 300px;
}
.table-page {
    margin-bottom: 0;
}
</style>