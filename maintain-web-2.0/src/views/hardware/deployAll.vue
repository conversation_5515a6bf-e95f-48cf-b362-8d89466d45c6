<!-- 一键部署 -->
<template>
  <div class="step-content">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="addFormVisible"
      :fullscreen="fullscreen"
      :close-on-click-modal="false"
      @close="closeDialog"
      width="1000px"
      top="9vh"
      class="dialog-panle"
      v-dialogDrag
    >
      <div class="dialog-content">
        <div class="step-btn">
          <span v-if="editTableIcon">
            <i
              class="el-icon-setting"
              title="程序管理"
              @click="editTableDialog = true"
            ></i>
          </span>
        </div>
        <div class="step-head">
          <el-steps :active="stepNum" align-center class="steps-panle">
            <el-step
              v-for="(item, index) in dataConfig"
              :key="index"
              :title="item.title"
              class="steps-item"
            ></el-step>
          </el-steps>
        </div>
        <div class="step-body">
          <WebSocket
            v-if="socketShow"
            :setpData="setpData"
            :setStep="setStep"
          ></WebSocket>
        </div>
        <div class="step-footer">
          <!-- <el-button type="primary">程序管理</el-button> -->
          <el-button
            :disabled="btnIsDisabled"
            class="primary-btn"
            @click="setStepData()"
          >
            {{
              dataConfig[stepNum] != undefined
                ? "下一步，" + dataConfig[stepNum].title
                : "完成"
            }}
          </el-button>
        </div>
      </div>

      <DeployAllDialog
        v-if="editTableDialog"
        @returnMain="closeProdureDialog"
      ></DeployAllDialog>
    </el-dialog>
  </div>
</template>

<script>
import WebSocket from "@/components/webSocketSendIfarme.vue";
import DeployAllDialog from "./deployAllDialog";

export default {
  name: "step-content",
  props: { setpData: Object },
  components: { WebSocket, DeployAllDialog },
  data() {
    return {
      dialogTitle: "一键部署",
      editTableIcon: true, //管理 图标是否展示
      editTableDialog: false, //管理弹出框
      fullscreen: false, //弹出框是否全屏
      addFormVisible: true, //是否展现弹出框
      socketShow: false, //是否展示 socket的内容
      stepNum: 1, //当前的执行步骤
      sockParam: null, //请求参数
      btnIsDisabled: true,
      dataConfig: [
        //第一步
        {
          title: "准备部署",
        },
        //第二步
        {
          title: "程序列表",
        },
        //第三步
        {
          title: "公共配置",
        },
        //第四步
        {
          title: "上传程序",
        },
        //第五步
        {
          title: "程序配置",
        },
      ],
    };
  },
  methods: {
    init() {
      this.socketShow = true;
      this.setStepConfig();
    },
    setStep(param, $sock) {
      this.sockParam = param;
      this.$sock = $sock;
      this.btnIsDisabled = false;
    },
    setStepConfig() {
      if (this.setpData.edidCommonConfig == false) this.dataConfig.splice(2, 1);
      //如果是继续执行
      if (this.setpData.isNew == false) {
        this.dialogTitle = "一键部署（继续执行）";
      }
    },
    setStepData() {
      if (this.stepNum + 1 > this.dataConfig.length) {
        var newParam = this.$sock.setStepParam(this.sockParam);
        if (newParam != null) {
          this.$sock.sendSock(newParam);
          this.closeDialog();
        }
      } else {
        if (this.sockParam == null) {
          this.waFn("当前步骤尚未完成，请稍后再试。");
        } else {
          if (this.sockParam.step == 4 || this.sockParam.step == 5)
            this.editTableIcon = false;

          var newParam = this.$sock.setStepParam(this.sockParam);
          if (newParam != null) {
            this.stepNum++;
            this.$sock.sendSock(newParam);
            this.sockParam = null;
            this.btnIsDisabled = true;
          }
        }
      }
    },
    closeDialog(val) {
      this.socketShow = false;
      this.addFormVisible = false;
      this.$emit("returnMain", "SUCCESS");
    },
    //是否程序管理弹出框
    closeProdureDialog(val) {
      this.editTableDialog = false;
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dialog-content {
  width: 100%;
}
.step-body {
  height: 400px;
  border: 1px solid #eeeeee;
}

.step-footer {
  text-align: right;
  margin-top: 20px;
}

.step-footer > button {
  height: 35px;
}

.steps-panle {
  padding-bottom: 20px;
}

.step-btn {
  position: absolute;
  right: 45px;
  top: 12px;
}
.step-btn span:first-child {
  margin-right: 3px;
}
.step-btn span:first-child i {
  font-size: 16px;
  cursor: pointer;
}

.step-btn span:last-child i {
  font-size: 16px;
  cursor: pointer;
}
.dialog-panle /deep/ .el-dialog__body {
  padding-bottom: 20px;
}
.steps-item /deep/ .el-step__head {
  font-size: 14px;
  font-family: "Microsoft YaHei";
}
.steps-item /deep/ .el-step__head .el-step__icon.is-text {
  height: 32px;
  width: 32px;
  background-color: #eeeeee;
  border: none;
  box-shadow: inset 0px 2px 0px 0px rgba(211, 211, 211, 0.6);
}
.steps-item /deep/ .el-step__head .el-step__icon.is-text .el-step__icon-inner {
  width: 20px;
  height: 20px;
  color: #909399;
  line-height: 19px;
  border-radius: 50%;
  border: 1px solid #cccccc;
  background: linear-gradient(180deg, #e7e5e5, #ffffff);
}
.steps-item
  /deep/
  .el-step__head.is-finish
  .el-step__icon.is-text
  .el-step__icon-inner {
  color: #ffffff;
  background: linear-gradient(180deg, #409eff, #5eadff);
}
.steps-item /deep/ .el-step__head .el-step__line {
  height: 8px;
  background-color: #eeeeee;
  border-color: transparent;
  box-shadow: inset 0px 1px 0px 1px rgba(211, 211, 211, 0.5);
}
.steps-item /deep/ .el-step__main .el-step__title {
  color: #909399;
  font-weight: 500;
  font-size: 14px;
}
.steps-item /deep/ .el-step__main .el-step__title.is-finish {
  color: #409eff;
}
.step-content /deep/ .el-dialog__header {
  height: 45px;
}
.dialog-panle /deep/ .el-dialog__header .el-icon.el-icon-close {
  font-size: 18px;
}
</style>