<!-- 添加程序 -->
<template>
  <div class="dialog-content">
    <el-dialog
      append-to-body
      title="程序管理"
      :visible.sync="formVisible"
      class="dialog-table"
      width="1000px"
      :lock-scroll="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <el-row :span="24" class="dialog-table-content">
        <el-row :span="24" class="table-btn">
          <el-col :span="20">
            <button @click.prevent="addBtn" class="btn-style">
              <div class="button-icon button-add"></div>
              <span>添加</span>
            </button>
          </el-col>
          <el-col :span="4">
            <span class="promptText">仅支持json文件和xml。</span>
          </el-col>
        </el-row>
        <el-row class="table-monitor" v-loading.fullscreen="fullscreenLoading">
          <el-row :span="24" class="backup-table">
            <el-table
              :data="baseData"
              border
              :row-class-name="bodyClass"
              :header-cell-style="tableHeader"
              :header-row-class-name="headerClass"
              height="400"
            >
              <!-- <el-table-column prop="ip" label="IP"></el-table-column> -->
              <el-table-column
                prop="deployPath"
                label="部署目录"
              ></el-table-column>
              <el-table-column
                prop="programName"
                label="程序名称"
              ></el-table-column>
              <el-table-column
                prop="configPath"
                label="配置路径"
              ></el-table-column>

              <el-table-column align="center" label="操作" min-width="100">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    class="text-button"
                    @click="editBtn(scope.row)"
                  >
                    <div class="agentButton button-edit"></div>
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    class="text-button"
                    @click="deleteBtn(scope.row, scope.$index, baseData)"
                  >
                    <div class="agentButton button-delete"></div>
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </el-row>
      </el-row>
      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="closeDialog" class="btn-style">确定</button>
      </div>
      <DeployEditDialog
        v-if="hideDialog"
        @returnMain="closeProdureDialog"
        :dialogData="dialogData"
      ></DeployEditDialog>
    </el-dialog>
  </div>
</template>

<script>
import {
  tableHeaderStyle,
  bodyRowClassName,
  headerRowClassName,
} from "@/utils/tableStyle.js";

import DeployEditDialog from "./deployEditDialog.vue";

export default {
  name: "dialog-content",
  components: { DeployEditDialog },
  data() {
    return {
      hideDialog: false, //编辑弹出框
      dialogData: null, //编辑/新增弹出的 参数
      baseData: [], //当前表格的数据
      formVisible: true, //当前表格的弹出框
      fullscreenLoading: false, //当前表格的遮罩
    };
  },
  methods: {
    deleteBtn(row, index, tableData) {
      let _this = this;
      _this.fullscreenLoading = true;
      _this.$http
        .post(
          "software/delete_modify_program.json",
          _this.qs.stringify({ id: row.id })
        )
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            tableData.splice(index, 1);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    editBtn(row) {
      this.dialogData = { title: "编辑程序", type: "edit", row: row };
      this.hideDialog = true;
    },
    addBtn() {
      this.dialogData = { title: "添加程序", type: "add" };
      this.hideDialog = true;
    },
    //请求数据函数
    setTableList() {
      this.fullscreenLoading = true;
      this.$http
        .get("software/get_modify_program.json")
        .then((res) => {
          if (res.data.code == 0) this.baseData = res.data.data;
          else res.data.msg ? this.waFn(res.data.msg) : this.erFn();
          this.fullscreenLoading = false;
        })
        .catch((error) => {
          this.erFn();
          this.fullscreenLoading = false;
        });
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
    //关闭当前的弹出框
    closeDialog(val) {
      this.formVisible = false;
      this.$emit("returnMain", "SUCCESS");
    },
    //关闭新增/编辑 的弹出框
    closeProdureDialog(val) {
      this.hideDialog = false;
      val === "SUCCESS" && this.setTableList();
    },
  },
  mounted() {
    this.setTableList();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dialog-table .table-btn {
  margin-bottom: 11px;
}
</style>

<style >
.dialog-table .el-dialog__body {
  padding: 20px 20px;
}
</style>