<!-- 服务器管理 -->
<template>
  <div class="hardware-content">
    <el-row class="row-content" v-loading.fullscreen="fullscreenLoading">
      <el-col class="content-form">
        <el-form label-width="4rem" class="hardware-form">
          <el-row type="flex" class="row-bg">
            <el-col :span="24">
              <el-col :span="12" class="status-row">
                <StatusMsg
                  v-if="statusData != null"
                  :statusData="statusData"
                  :statusChange="statusChange"
                ></StatusMsg>
              </el-col>
              <el-col :span="12" class="button-row">
                <el-button @click="edit" class="el-icon-edit-outline"
                  >编辑告警阈值</el-button
                >
              </el-col>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
      <el-col :span="24" class="content-table">
        <el-table
          class="table-border"
          :data="dataList"
          :row-class-name="bodyClass"
          :header-cell-style="tableHeader"
          :header-row-class-name="headerClass"
          @cell-click="cellClick"
          height="100%"
          border
          v-loading="tableLoading"
          @selection-change="tableChange"
          :default-sort="{ porp: 'role', prder: 'descending' }"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column prop="status" label="状态" width="70">
            <template slot-scope="scope">
              <el-popover
                :trigger="scope.row.content != '良好' ? 'hover' : 'manual'"
                placement="top-start"
              >
                <p class="popoverContent">{{ scope.row.description || "" }}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag
                    size="small"
                    class="status-tag"
                    :color="scope.row.status"
                  >
                    {{ scope.row.content || "" }}
                  </el-tag>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="ip" label="IP" width="127" sortable>
            <template slot-scope="scope">
              <el-button
                type="text"
                v-if="btn.hardWareDetail.value"
                class="cell-button text-button"
                @click="showDetail(scope.row)"
              >
                {{ scope.row.ip }}
              </el-button>
              <span v-else>{{ scope.row.ip }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="role"
            label="角色"
            min-width="120"
            sortable
          ></el-table-column>
          <el-table-column
            prop="note"
            label="备注"
            min-width="90"
            class-name="overflowColumn"
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.editShow"
                :ref="scope.row.refName"
                v-model="scope.row.inputVal"
                @blur="noteBlur(scope.row)"
                :autosize="true"
                :show-word-limit="true"
                maxlength="255"
                type="textarea"
                rows="1"
                class="noteInput"
              >
              </el-input>
              <el-popover
                class="popoverSpan"
                trigger="hover"
                placement="top-start"
                v-else
              >
                <p class="popoverContent">{{ scope.row.note || "-" }}</p>
                <span slot="reference" class="name-wrapper edit-span">
                  {{ scope.row.note || "-" }}
                </span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="os"
            label="操作系统"
            width="75"
            :formatter="formatOs"
          ></el-table-column>
          <el-table-column prop="usedCpu" label="CPU" width="170">
            <template slot-scope="scope">
              <div v-if="scope.row.show">
                <div class="progress-text">{{ scope.row.usedCpu }}</div>
                <el-progress
                  :percentage="
                    scope.row.usedCpu | myCurrency(scope.row.usedCpu)
                  "
                  :show-text="false"
                  color="#5db1f2"
                >
                </el-progress>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalMemory" label="硬盘" width="190">
            <template slot-scope="scope">
              <div v-if="scope.row.show">
                <div class="progress-text">
                  <div>
                    {{ scope.row.usedDisk + "/" + scope.row.totalDisk }}
                  </div>
                  <span>{{ scope.row.diskUsedPercent }}</span>
                </div>
                <el-progress
                  :percentage="
                    scope.row.diskUsedPercent
                      | myCurrency(scope.row.diskUsedPercent)
                  "
                  :show-text="false"
                  color="#8aa7f4"
                >
                </el-progress>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="内存" width="190">
            <template slot-scope="scope">
              <div v-if="scope.row.show">
                <div class="progress-text">
                  <div>
                    {{ scope.row.usedMemory + "/" + scope.row.totalMemory }}
                  </div>
                  <span>{{ scope.row.memoryUsedPercent }}</span>
                </div>
                <el-progress
                  :percentage="
                    scope.row.memoryUsedPercent
                      | myCurrency(scope.row.memoryUsedPercent)
                  "
                  :show-text="false"
                  color="#8aa7f4"
                >
                </el-progress>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="lastHeartbeatTime"
            label="最近心跳时间"
            width="145"
            sortable
          >
            <template slot-scope="scope">
              <span>{{ scope.row.lastHeartbeatTime || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="告警阈值操作" width="94">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="text-button form-btn"
                v-if="btn.hardWareAlarmNum.value"
                @click="editBtn(scope.row)"
              >
                <i class="iconfont icon-edit"></i>
                编辑
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="网络异常" width="75">
            <template slot-scope="scope">
              <i
                class="iconfont icon-detail icon-btn"
                title="详情"
                @click="networkBtn(scope.row)"
              ></i>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <diskDetail
        v-if="addFormVisible"
        @returnMain="closeDialog"
        :diskData="diskData"
      ></diskDetail>
      <networkDetail
        v-if="networkVisible"
        @returnMain="closeNet"
        :netData="netData"
      ></networkDetail>
    </el-row>
  </div>
</template>

<script>
import diskDetail from "./diskDetail.vue";
import networkDetail from "./networkDetail.vue";
import getPermission from "@/utils/permissions.js";
import StatusMsg from "@/views/common/statusMsg";

import {
  bodyRowClassName,
  tableHeaderStyle,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "hardware-content",
  components: { diskDetail, networkDetail, StatusMsg },
  data() {
    return {
      pageTimer: null,
      diskTabs: "全部磁盘",

      fullscreenLoading: false,
      tableLoading: false,
      addFormVisible: false,
      networkVisible: false,
      tableSelection: [],
      diskData: {},
      netData: {},
      btn: {
        hardWareDetail: {
          name: "服务器详情",
          value: false,
        },
        hardWareAlarmNum: {
          name: "告警阈值编辑",
          value: true,
        },
      },
      form: {
        status: "",
      },
      statusData: null,
      dataList: [],
    };
  },
  methods: {
    init() {
      this.getDataList(false, this.form);
      getPermission(this.$route.name, this.btn);
      this.requestInterval();
    },
    //请求数据函数
    getDataList(IntervalLoading, data) {
      let _this = this;
      _this.tableLoading = IntervalLoading ? false : true;
      _this.$http
        .post("hardware/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.tableLoading = false;
          if (res.data.code == 0) {
            //设置状态数量
            this.statusData = res.data.data.statusMap;
            this.formatStatus(res.data.data.hardwares);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    saveNote(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("hardware/note", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.getDataList(false);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    requestInterval() {
      if (this.pageTimer) {
        clearInterval(this.pageTimer);
      } else {
        this.pageTimer = setInterval(() => {
          this.getDataList(true, this.form);
        }, 60000);
      }
    },
    statusChange(item) {
      if (item.num == "0") {
        this.waFn("暂无数据");
        return false; //不执行选中的样式
      }
      this.form.status = item.field;
      this.getDataList(false, this.form);
    },
    //处理数据函数
    formatStatus(data) {
      data.forEach((item, index) => {
        switch (item.status) {
          case "GREEN":
            item.status = "#25ce88";
            item.content = "良好";
            this.goodNum++;
            break;
          case "YELLOW":
            item.status = "#fb843b";
            item.content = "警告";
            this.alarmNum++;
            break;
          case "RED":
            item.status = "#f93846";
            item.content = "错误";
            this.policeNum++;
            break;
        }
        item.editShow = false;
        item.inputVal = item.note || "";
        item.refName = "noteInput" + index;
      });
      this.dataList = data;
    },
    formatOs(row, column, value) {
      switch (value) {
        case 0:
          value = "Linux";
          break;
        case 1:
          value = "Windows";
          break;
      }
      return value;
    },
    //功能函数
    searchBtn() {
      this.dataList = [];
      this.getDataList(false, this.form);
    },
    editBtn(row) {
      this.addFormVisible = true;
      this.diskData = { ip: row.ip };
    },
    tableChange(val) {
      this.tableSelection = val;
    },
    edit() {
      let _this = this;
      let ips = [];

      if (this.tableSelection.length == 0)
        return this.waFn("请选择需要操作的数据");

      this.tableSelection.forEach((item) => {
        !ips.includes(item.ip) && ips.push(item.ip);
      });

      this.addFormVisible = true;
      this.diskData =
        ips.length > 1 ? { ips: ips.join(",") } : { ip: ips.join(",") };
    },

    networkBtn(row) {
      this.networkVisible = true;
      this.netData.ip = row.ip;
    },
    closeDialog(val) {
      this.addFormVisible = false;
      val == "success" && this.getDataList(true, this.form);
    },
    closeNet() {
      this.networkVisible = false;
    },
    showDetail(row) {
      this.$router.push({
        path: "/hardWareDetail",
        query: {
          detailData: row.agentId,
        },
      });
    },
    cellClick(row, column, cell, event) {
      if (column.label == "备注" && row.editShow == false) {
        row.inputVal = row.note || "";
        row.editShow = true;
        this.$nextTick(() => {
          this.$refs[row.refName].focus();
        });
      }
    },
    noteBlur(row) {
      let data = {};

      row.inputVal == undefined && (row.inputVal = "");
      if (row.inputVal == row.note) {
        row.editShow = false;
        return;
      }
      data = {
        agentId: row.agentId,
        note: row.inputVal,
      };
      this.saveNote(data);
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
  },
  mounted() {
    this.init();
  },
  destroyed() {
    clearInterval(this.pageTimer);
  },
  filters: {
    myCurrency(value) {
      if (value) {
        value = Number(value.split("%")[0]);
        return Number(value);
      }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.hardware-content {
  height: 100%;
  width: 98%;
  margin: 0 1%;
}
.hardware-form {
  margin: 1.5rem;
  /* padding-right:1rem; */
}
.table-row {
  margin-top: 10px;
}
.title-row:not(:nth-child(1)) {
  margin-top: 30px;
}

.button-row {
  text-align: right;
}
.cell-button {
  text-align: left;
  padding-left: 0 !important;
  text-decoration: underline;
}
.edit-row {
  width: 80%;
  margin: 0 auto;
}
.el-textarea__inner {
  min-height: 300px !important;
}
.progress-text {
  color: #203e66;
  text-align: right;
}
.progress-text div {
  float: left;
}
.status-tag {
  color: #fff;
  border: 0;
  width: 40px;
  height: 20px;
  line-height: 20px;
}
.form-search {
  color: #203e66;
  text-align: right;
}
.button-icon {
  background: url("../../assets/images/agentManage/icon-Agent.png");
  display: inline-block;
  height: 16px;
  width: 16px;
  background-size: cover !important;
  vertical-align: sub;
}
.button-update {
  background-position: 0px 129px;
}
.btn-style:hover .button-update {
  background-position: 0px 89px;
}
.btn-dialog:first-child {
  margin-right: 10px;
}
.btn-dialog {
  padding: 0 20px;
}
.network-btn {
  padding: 0;
}
.icon-detail {
  cursor: pointer;
  color: #1e85e5;
}
.icon-detail:hover {
  color: #409eff;
}

.status-describe {
  height: 100%;
}

.status-describe > span {
  display: inline-block;
  line-height: 26px;
  cursor: pointer;
  margin: 0 5px;
}

.status-describe > span > span > span {
  text-decoration: underline;
}
</style>