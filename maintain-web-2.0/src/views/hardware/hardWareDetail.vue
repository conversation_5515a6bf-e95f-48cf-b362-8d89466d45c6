<!-- 硬件系统详情 -->
<template>
  <div class="hard-detail" v-loading.fullscreen="fullscreenLoading">
    <div class="detail-content">
      <el-row>
        <el-col :span="12">
          <div class="top-title">硬件系统详情</div>
        </el-col>
        <el-col :span="12" class="return-col">
          <button @click.prevent="returnWeb" class="btn-return btn-style">
            <div class="button-icon button-return"></div>
            <span>返回</span>
          </button>
        </el-col>
      </el-row>
      <el-row class="content-row">
        <el-row class="host-row">
          <el-row class="host-content">
            <div class="host-header">
              <div class="host-text">主机信息</div>
            </div>
            <el-row class="hostForm-row">
              <el-form class="host-form">
                <el-row>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <div class="button-icon button-ip"></div>
                        <span>IP</span>
                      </div>
                      <span>{{ hostForm.ip }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="CPU使用率">
                      <div slot="label">
                        <div class="button-icon button-usedCpu"></div>
                        <span>CPU使用率</span>
                      </div>
                      <template slot-scope="scope">
                        <span :style="{ color: CurrencyStatus(hostForm.cpuStatus) }">
                          {{ hostForm.usedCpu }}
                        </span>
                      </template>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="内存">
                      <div slot="label">
                        <div class="button-icon button-usedMemory"></div>
                        <span>内存</span>
                      </div>
                      <template slot-scope="scope">
                        <span :style="{
                          color: CurrencyStatus(hostForm.memoryStatus),
                        }">
                          {{ hostForm.usedMemory + "/" + hostForm.totalMemory }}
                        </span>
                      </template>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="操作系统">
                      <div slot="label">
                        <div class="button-icon button-osName"></div>
                        <span>操作系统</span>
                      </div>
                      <span>{{ hostForm.osName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item>
                      <div slot="label">
                        <div class="button-note"></div>
                        <span>备注</span>
                      </div>
                      <el-input v-if="noteForm.isSet" v-model="noteForm.value" :autosize="true" :show-word-limit="true"
                        maxlength="255" type="textarea" class="noteInput">
                      </el-input>
                      <el-popover class="popoverSpan note-popover" trigger="hover" placement="top-start" v-else>
                        <p class="popoverContent">{{ hostForm.note }}</p>
                        <div slot="reference" class="name-wrapper">
                          <span>{{ hostForm.note || "-" }}</span>
                        </div>
                      </el-popover>
                      <el-button type="text" class="text-button" @click="noteSure(noteForm.buttonText)">
                        {{ noteForm.buttonText }}
                      </el-button>
                      <el-button type="text" class="text-button" v-if="noteForm.isSet" @click="noteQuit">
                        取消
                      </el-button>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="是否开启防火墙">
                      <div slot="label">
                        <div class="button-icon button-firewallStatus"></div>
                        <el-button type="text" class="fireBtn" @click.stop="firewallBtn()">防火墙</el-button>
                      </div>
                      <span v-if="hostForm.firewallStatus == 1">开启</span>
                      <span v-else-if="hostForm.firewallStatus == 0">关闭</span>
                      <!-- <el-switch 
                                                disabled 
                                                v-model="hostForm.firewallStatus" 
                                                class="inactiveFirewall" 
                                                :active-value='1' :inactive-value="0" 
                                                active-color="#25ce88" inactive-color="#f93846"  
                                                @click.native='switchClick(hostForm.firewallStatus)'>
                                            </el-switch> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="接收字节数/秒">
                      <div slot="label">
                        <div class="iconfont-icon">
                          <i class="iconfont icon-accept-byte"></i>
                        </div>
                        <span> 接收字节数/秒</span>
                      </div>
                      <span>{{ this.bytesToSize(hostForm.receiveBytePerSecond || 0, 1) }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="发送字节数/秒">
                      <div slot="label">
                        <div class="iconfont-icon">
                          <i class="iconfont icon-send-byte"></i>
                        </div>
                        <span> 发送字节数/秒</span>
                      </div>
                      <span>{{ this.bytesToSize(hostForm.sendBytePerSecond || 0, 1) }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-row>
          </el-row>
        </el-row>
        <el-row class="network-row">
          <el-row class="host-content">
            <div class="host-header">
              <div class="host-text">资源图表</div>
            </div>
            <el-row class="form-time">
              <el-radio-group v-model.number="timeChart" size="mini" @change="timeChange">
                <el-radio-button class="proce-radio" v-for="item in timeOptions" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </el-row>
            <el-row class="chart-row">
              <div class="statistics">
                <el-row class="chart-content">
                  <div class="chartWrap"></div>
                </el-row>
              </div>
              <div class="statistics">
                <el-row class="chart-content">
                  <div class="chartWrap"></div>
                </el-row>
              </div>
              <div class="statistics">
                <el-row class="chart-content">
                  <div class="chartWrap"></div>
                </el-row>
              </div>
            </el-row>
          </el-row>
        </el-row>
        <el-row class="network-row">
          <el-row class="host-content">
            <div class="host-header">
              <div class="host-text">资源进程（TOP3）</div>
            </div>
            <el-row class="chart-row">
              <div class="statistics">
                <el-row class="assets-top">
                  <el-table :data="hostForm.cpuTopProcessInfo" :cell-class-name="cellClass" max-height="300"
                    :header-cell-style="tableHeader" :header-row-class-name="headerClass">
                    <el-table-column prop="name" label="名称"/>
                    <el-table-column prop="cpuPercent" label="CPU使用率">
                      <template slot-scope="scope">
                        <span>{{ scope.row.cpuPercent.toFixed(2) }}%</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="memRssSize" label="常驻内存">
                      <template slot-scope="scope">
                        <span>{{ converToFlow(scope.row.memRssSize) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="command" label="启动参数" show-overflow-tooltip/>
                    <el-table-column prop="capTime" label="采集时间" show-overflow-tooltip/>
                  </el-table>
                  <el-table :data="hostForm.memTopProcessInfo" :cell-class-name="cellClass" max-height="300"
                    :header-cell-style="tableHeader" :header-row-class-name="headerClass">
                    <el-table-column prop="name" label="名称"/>
                    <el-table-column prop="cpuPercent" label="CPU使用率">
                      <template slot-scope="scope">
                        <span>{{ scope.row.cpuPercent.toFixed(2) }}%</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="memRssSize" label="常驻内存">
                      <template slot-scope="scope">
                        <span>{{ converToFlow(scope.row.memRssSize) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="command" label="启动参数" show-overflow-tooltip/>
                    <el-table-column prop="capTime" label="采集时间" show-overflow-tooltip/>
                  </el-table>
                </el-row>
              </div>
            </el-row>
          </el-row>
        </el-row>
        <el-row class="network-row">
          <el-row class="host-content">
            <div class="host-header">
              <div class="host-text">网卡信息</div>
            </div>
            <el-row class="table-row">
              <el-table :data="hostForm.nicInfo" :cell-class-name="cellClass" max-height="300"
                :header-cell-style="tableHeader" :header-row-class-name="headerClass">
                <el-table-column prop="cpuAvg" label="名称">
                  <template slot-scope="scope">
                    <span>{{ scope.row.netInterfaceConfig.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="cpuTop" label="描述">
                  <template slot-scope="scope">
                    <el-popover trigger="hover" placement="top-start" width="300">
                      <p class="popoverContent">
                        {{ scope.row.netInterfaceConfig.description }}
                      </p>
                      <div slot="reference" class="startParam-popover">
                        {{ scope.row.netInterfaceConfig.description }}
                      </div>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column prop="IP" label="IP">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.netInterfaceConfig.address || "-"
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="usedMemory" label="MAC地址">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.netInterfaceConfig.hwaddr || "-"
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="speed" label="带宽" :formatter="formatSpeed"></el-table-column>
              </el-table>
            </el-row>
          </el-row>
        </el-row>
        <el-row class="network-row">
          <el-row class="host-content">
            <div class="host-header">
              <div class="host-text">硬盘使用情况</div>
            </div>
            <el-row class="table-row">
              <el-table :data="hostForm.diskInfo" max-height="300" :cell-style="cellStyle"
                :header-cell-style="tableHeader" :header-row-class-name="headerClass">
                <el-table-column prop="name" label="盘符"></el-table-column>
                <el-table-column prop="total" label="总空间"></el-table-column>
                <el-table-column prop="used" label="使用空间"></el-table-column>
                <el-table-column prop="avail" label="可用空间"></el-table-column>
                <el-table-column prop="usedPercent" label="使用率"></el-table-column>
              </el-table>
            </el-row>
          </el-row>
        </el-row>
        <el-row class="procedure-row">
          <el-row class="host-content">
            <div class="host-header">
              <div class="host-text">历史健康状况</div>
            </div>
            <el-row class="healthTable-row">
              <el-row class="health-row">
                <el-table :data="healthList" border :row-class-name="bodyClass" :header-cell-style="healthHeader"
                  :header-row-class-name="headerClass" max-height="300">
                  <el-table-column prop="status" label="状态">
                    <template slot-scope="scope">
                      <el-popover trigger="hover" placement="top-start" v-if="scope.row.content != '良好'">
                        <p class="popoverContent">
                          {{ scope.row.description }}
                        </p>
                        <el-tag slot="reference" class="status-tag" size="small" :color="scope.row.status">
                          {{ scope.row.content }}
                        </el-tag>
                      </el-popover>
                      <div v-else>
                        <el-tag size="small" class="status-tag" :color="scope.row.status">
                          {{ scope.row.content }}
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="时间"></el-table-column>
                  <el-table-column prop="description" label="原因"></el-table-column>
                </el-table>
              </el-row>
              <el-row class="pagination-row">
                <el-col :span="24" class="table-health">
                  <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="form.pn" :page-sizes="[20, 50, 100]" :page-size="form.ps"
                    layout="total,sizes,prev,pager,next,jumper" :total="total">
                  </el-pagination>
                </el-col>
              </el-row>
            </el-row>
          </el-row>
        </el-row>
      </el-row>
    </div>
    <el-dialog title="防火墙开放策略" :visible.sync="portFormVisible" :close-on-click-modal="false" class="dialog-border"
      width="500px">
      <div class="port-body" v-loading="firewallVisible">
        <p v-for="(item, index) in firewallList" :key="index">
          {{ item }}
        </p>
      </div>

      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="portFormVisible = false" class="btn-style port-btn">
          <span>关闭</span>
        </button>
      </div>
    </el-dialog>

    <webSocketSend v-if="socketShow" @socketReturn="closeDialog" :socketData="socketData"></webSocketSend>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import { converToFlow } from '../procedureManage/formatflow'
import { parseTime } from "@/utils/index.js";
import {
  headerRowClassName,
  bodyRowClassName,
  tableHeaderStyle,
} from "@/utils/tableStyle.js";
import webSocketSend from "@/components/webSocketSend.vue";

export default {
  name: "hard-detail",
  components: { webSocketSend },
  data() {
    return {
      converToFlow,
      flowCpu: "",
      flowMemory: "",
      twoRow: null,
      pageTimer: null,
      total: 0,
      timeChart: 1,

      fullscreenLoading: false,
      socketShow: false,
      portFormVisible: false,
      firewallVisible: false,
      hostForm: {},
      chartForm: {
        startTime: "",
        endTime: "",
      },
      form: {
        pn: 1, //当前页
        ps: 20, //当前每页条数
      },
      btn: {
        firewall: {
          name: "开启防火墙",
          value: false,
        },
      },
      noteForm: {
        isSet: false,
        buttonText: "修改",
        value: "",
      },

      healthList: [],
      firewallList: [],
      timeOptions: [
        {
          label: "1小时前",
          value: 1,
        },
        {
          label: "2小时前",
          value: 2,
        },
        {
          label: "6小时前",
          value: 6,
        },
        {
          label: "12小时前",
          value: 12,
        },
        {
          label: "1天前",
          value: 24,
        },
        {
          label: "7天前",
          value: 168,
        },
      ],
    };
  },
  methods: {
    init() {
      this.twoRow = Number(this.$route.query.detailData);

      getPermission("服务器管理", this.btn);
      this.getDataList(false, this.twoRow, this.form);
      this.getStatus();
      this.requestInterval();
      this.timeChange(this.timeChart);
    },

    //请求数据函数
    getDataList(IntervalLoading, data, pageForm) {
      let _this = this;

      _this.fullscreenLoading = IntervalLoading ? false : true;
      _this.$http
        .post("hardware/detail_" + data + ".json", _this.qs.stringify(pageForm))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.hostForm = res.data.data;
            if (res.data.data.hardwareHistoryHealthList) {
              _this.healthList = res.data.data.hardwareHistoryHealthList.list;
              _this.total = res.data.data.hardwareHistoryHealthList.total;
              _this.formatStatus(_this.healthList);
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    firewallBtn() {
      let _this = this,
        data = {};

      if (_this.hostForm.ip) {
        _this.portFormVisible = true;
        _this.firewallVisible = true;
        data.ip = _this.hostForm.ip;

        _this.$http
          .post("hardware/firewall-rule.json", _this.qs.stringify(data))
          .then((res) => {
            _this.firewallVisible = false;
            if (res.data.code == 0) {
              let dataHtml = '<div class="noData-div">暂无数据</div>';
              _this.firewallList = res.data.data || [];
            } else {
              res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
            }
          })
          .catch((error) => {
            _this.firewallVisible = false;
            _this.erFn();
            console.log(error);
          });
      } else {
        _this.waFn("暂无详情");
      }
    },
    getEchart(data, chartForm) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post(
          "hardware/history_resource_" + data + ".json",
          _this.qs.stringify(chartForm)
        )
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (JSON.stringify(res.data.data) != "{}") {
              _this.drawLine(res.data.data.historyResource);
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    saveNote(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("hardware/note", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.getDataList(false, this.twoRow, this.form);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    requestInterval() {
      if (this.pageTimer) {
        clearInterval(this.pageTimer);
      } else {
        this.pageTimer = setInterval(() => {
          this.getDataList(true, this.twoRow, this.form);
        }, 60000);
      }
    },

    //处理数据函数
    CurrencyStatus(value) {
      switch (value) {
        case "YELLOW":
          value = "#fb843b";
          break;
        case "RED":
          value = "#f93846";
          break;
      }
      if (value == "GREEN") value = "";
      return value;
    },
    formatStatus(data) {
      data.forEach((item) => {
        switch (item.status) {
          case "GREEN":
            item.status = "#25ce88";
            item.content = "良好";
            break;
          case "YELLOW":
            item.status = "#fb843b";
            item.content = "警告";
          case "RED":
            item.status = "#f93846";
            item.content = "错误";
        }
      });
    },
    formatSpeed(row, column, val) {
      val =
        val && val.indexOf("M") == -1
          ? (Number(val) / 1024 / 1024).toFixed(2) + "Mb/s"
          : "-";
      return val;
    },

    //功能函数
    closeDialog(val) {
      this.socketData = {};
      this.socketShow = false;
      val === "SUCCESS" && this.getDataList(false, this.twoRow, this.form);
    },
    returnWeb() {
      this.$router.go(-1);
    },
    switchClick(val) {
      // let _this = this,
      //     data = {
      //         ip:'',
      //         operateType:1
      //     };
      // if(val == 1) {
      //     return;
      // }
      // if(this.btn.firewall.value != true) {
      //     this.waFn('暂无权限');
      //     return;
      // }
      // _this.$confirm('您确定要开启防火墻吗？','提示',{
      //     cancelButtonText:'关闭',
      //     confirmButtonText:'确定',
      //     confirmButtonClass:'confirm-success',
      // }).then(() =>{
      //     _this.socketData = {
      //         operateType:1,
      //         param:JSON.stringify({
      //             ip:_this.hostForm.ip
      //         }),
      //         service:'hardware',
      //         title:'开启防火墙过程',
      //     }
      //     _this.socketShow = true;
      // }).catch(() =>{
      // })
    },
    handleSizeChange(val) {
      this.form.ps = val;
      this.getDataList(false, this.twoRow, this.form);
    },
    handleCurrentChange(val) {
      this.form.pn = val;
      this.getDataList(false, this.twoRow, this.form);
    },
    timeChange(val) {
      window.onresize = "";
      let endTime = Date.parse(new Date());
      let startTime = endTime - val * 60 * 60 * 1000;

      this.chartForm.endTime = parseTime(endTime, "{y}-{m}-{d} {h}:{i}");
      this.chartForm.startTime = parseTime(startTime, "{y}-{m}-{d} {h}:{i}");
      this.flowCpu && this.flowCpu.dispose();
      this.flowMemory && this.flowMemory.dispose();
      this.getEchart(this.twoRow, this.chartForm);
    },
    drawLine(data) {
      let chartDom = document.getElementsByClassName("chartWrap");
      let option1 = {
        title: "使用率",
        legend: ["CPU使用率", "内存使用率"],
        xData: data.cpuResource.x,
        series: [
          {
            name: "CPU使用率",
            type: "line",
            data: data.cpuResource.y,
            smooth: true,
            itemStyle: {
              normal: {
                color: "rgb(99,223,246)",
              },
              emphasis: {
                borderColor: "red",
              },
            },
          },
          {
            name: "内存使用率",
            type: "line",
            data: data.memoryResource.y,
            smooth: true,
            itemStyle: {
              normal: {
                color: "rgb(138, 167, 244)",
              },
              emphasis: {
                borderColor: "red",
              },
            },
          },
        ],
      };

      let option2 = {
        title: "字节数",
        legend: ["接收字节数", "发送字节数"],
        xData: data.networkReceiveResource.x,
        series: [
          {
            name: "接收字节数",
            type: "line",
            data: data.networkReceiveResource.y,
            smooth: true,
            itemStyle: {
              normal: {
                color: "rgb(99,223,246)",
              },
              emphasis: {
                borderColor: "red",
              },
            },
          },
          {
            name: "发送字节数",
            type: "line",
            data: data.networkSendResource.y,
            smooth: true,
            itemStyle: {
              normal: {
                color: "rgb(138, 167, 244)",
              },
              emphasis: {
                borderColor: "red",
              },
            },
          },
        ],
      };
      console.log(data)
      let option3 = {
        title: "使用率",
        xData: data.diskResource.x,
        legend: null,
        series: [
          {
            name: "磁盘使用率",
            type: "line",
            data: data.diskResource.y,
            smooth: true,
            itemStyle: {
              normal: {
                color: "rgb(99,223,246)",
              },
              emphasis: {
                borderColor: "red",
              },
            },
          }
        ],
      };
      this.buildEchart(option1, chartDom[0]);
      this.buildEchart(option2, chartDom[1]);
      this.buildEchart(option3, chartDom[2]);

      // this.flowMemory
      // this.flowCpu
    },

    buildEchart(config, dom) {
      let option = {
        title: {
          //   text: "图表",
          textStyle: {
            fontSize: 14,
            color: "#1e85e6",
          },
        },
        legend: {
          data: config.legend,
        },
        tooltip: {
          trigger: "axis",
          formatter: (obj, e, i) => {
            let html = `<p>${obj[0].name}</p>`
            for (let i = 0; i < obj.length; i++) {
              const { marker, seriesName, value } = obj[i];
              html += `<p>
                ${marker + seriesName}：${this.getChartValue(value, config.title)}
                </p>`
            }
            return html;
          },
          axisPointer: {
            lineStyle: { type: "dashed" },
          },
        },
        toolbox: {
          show: true,
          feature: {
            magicType: { show: true, type: ["line", "bar"] },
          },
        },
        calculable: true,
        grid: {
          top: "16%",
          bottom: "20%",
          left: "20%",
          right: "3%",
        },
        xAxis: {
          type: "category",
          boundaryGap: ["10%", "10%"],
          axisLine: {
            lineStyle: { color: "#e6e9f1" },
          },
          axisLabel: {
            color: "#203e66",
            length: 7,
            formatter: (val) => {
              let str = val.split(" ");
              return str.join("\n");
            },
          },
          data: config.xData,
        },
        yAxis: [
          {
            type: "value",
            axisTick: { show: false }, //坐标轴刻度
            axisLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#203e66",
              },
              formatter: (val) => {
                return this.getChartValue(val, config.title);
              },
            },
            splitLine: {
              lineStyle: { color: "#e6e9f1" },
            },
          },
        ],
        dataZoom: [
          {
            show: true,
            type: "slider",
            height: 20,
            borderColor: "transparent",
            backgroundColor: "#ebeef5",
            start: 0,
            end: 100,
            bottom: 8,
            handleSize: "100%",
            handleStyle: {
              shadowColor: "rgba(0,0,0,0.6)",
              color: "#606266",
              shadowBlur: 6,
              shadowOffsetX: 3,
              shadowOffsetY: 3,
            },
          },
        ],
        series: config.series,
      };

      let charts = this.$echarts.init(dom);
      charts.setOption(option);

      window.onresize = function () {
        charts.resize();
      };
    },
    getChartValue(val, title) {
      return title == "使用率" ? val + "%" : this.bytesToSize(val, 1);
    },
    bytesToSize(fileSize, num, type) {
      if (!fileSize || fileSize == 0) return 0;
      if ((fileSize < 1024 && !type) || type == "B") {
        return fileSize + "B";
      } else if ((fileSize < 1024 * 1024 && !type) || type == "KB") {
        return (fileSize / 1024).toFixed(num) + "KB";
      } else if ((fileSize < 1024 * 1024 * 1024 && !type) || type == "MB") {
        return (fileSize / 1024 / 1024).toFixed(num) + "MB";
      } else {
        return (fileSize / 1024 / 1024 / 1024).toFixed(num) + "GB";
      }
    },
    noteSure(val) {
      let data = {};

      switch (val) {
        case "修改":
          this.noteForm.value = this.hostForm.note;
          this.noteForm.isSet = true;
          this.noteForm.buttonText = "保存";
          break;
        case "保存":
          this.noteForm.isSet = false;
          this.noteForm.buttonText = "修改";
          data = {
            agentId: this.hostForm.agentId,
            note: this.noteForm.value,
          };
          this.saveNote(data);
          break;
      }
    },
    noteQuit() {
      this.noteForm.isSet = false;
      this.noteForm.buttonText = "修改";
    },
    tableHeader() {
      return "border:0;";
    },
    cellStyle() {
      return "border:0;";
    },
    headerClass() {
      return headerRowClassName();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    healthHeader() {
      return tableHeaderStyle();
    },
    tableRowClassName({ row, rowIndex }) {
      let value = "";

      switch (row.diskStatus) {
        case "RED":
          value = "warning-row";
          break;
        case "YELLOW":
          value = "alarm-row";
          break;
      }
      return value;
    },
    cellClass({ row, column, rowIndex, columnIndex }) {
      switch (columnIndex) {
        case 1:
          return "overflowColumn";
      }
    },
  },
  mounted() {
    this.init();
  },
  destroyed() {
    clearInterval(this.pageTimer);
  },
  filters: {
    myCurrency(value) {
      if (value) {
        value = Number(value.split("%")[0]);
        return Number(value);
      }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.hard-detail {
  height: 100%;
  width: 100%;
  /* overflow-y: auto; */
}

.detail-content {
  width: 98%;
  margin: 0 1% 1%;
  height: 100%;
}

.top-title {
  text-align: left;
  text-indent: 15px;
  color: #1e85e6;
  font-size: 15px;
  font-weight: bold;
  height: 40px;
  line-height: 40px;
  display: inline-block;
  margin-top: 10px;
}

.content-row {
  width: 100%;
  height: calc(100% - 60px);
  background-color: #ffffff;
  border: 1px solid #e2e2e2;
  -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  overflow: auto;
}

.host-row {
  margin: 10px 10px 0 10px;
  min-height: 130px;
}

.host-header {
  height: 30px;
  line-height: 36px;
  font-size: 14px;
  border-bottom: 1px solid #cdd0d3;
}

.host-text {
  display: inline-block;
  height: 30px;
  line-height: 36px;
  border-bottom: 2px solid #5174b4;
  padding: 0 3px;
  color: #203e66;
  font-weight: bold;
}

.hostForm-row {
  padding: 30px 0;
}

.host-content {
  padding: 0px 15px;
}

.host-form {
  margin: 5px 0 0 50px;
}

.network-row {
  margin: 0 10px;
}

.procedure-row {
  margin: 0 10px;
}

.btn-row {
  height: 26px;
  margin: 0 10px 10px;
  text-align: right;
}

.table-row {
  padding: 40px 40px;
}

.healthTable-row {
  padding: 40px 40px 0;
}

.table-health {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-row {
  height: 30px;
  margin: 12px;
}

.title-row:not(:nth-child(1)) {
  margin-top: 30px;
}

.iconfont-icon {
  width: 23px;
  height: 21px;
  display: inline-block;
  margin-left: 3px;
}

.iconfont-icon .iconfont {
  font-size: 21px;
  font-weight: 500;
  position: absolute;
}

.button-icon {
  display: inline-block;
  height: 21px;
  width: 25px;
  background-size: cover !important;
  vertical-align: sub;
  background: url("../../assets/images/hardWare/icon-detail.png");
}

.button-ip {
  background-position: 0px -109px;
}

.button-usedCpu {
  background-position: 0px -9px;
}

.button-usedMemory {
  background-position: 0px -58px;
}

.button-osName {
  background-position: 0px -159px;
}

.button-firewallStatus {
  background-position: 0px -219px;
}

.button-note {
  display: inline-block;
  height: 20px;
  width: 25px;
  background-size: cover !important;
  vertical-align: sub;
  background: url("../../assets/images/procedureManage/icon-detail.png");
  background-position: 0px -1075px;
}

.return-col {
  text-align: right;
  padding-right: 20px;
  height: 40px;
  line-height: 40px;
  margin-top: 10px;
}

.btn-return {
  background-color: #1e85e6;
  color: #fff;
  padding-left: 5px;
}

.button-return {
  background-position: 0 26px;
  height: 16px;
}

.btn-return:hover {
  background-color: #2d91ef;
  color: #fff;
}

.status-tag {
  color: #fff;
  border: 0;
  width: 40px;
  height: 20px;
  line-height: 20px;
}

.port-btn {
  padding: 0 20px;
}

.port-body {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  color: #203e66;
  line-height: 20px;
}

.chart-row {
  width: calc(100% - 80px);
  height: 300px;
  margin: 20px 40px;
  display: flex;
}

.chart-content {
  height: 100%;
}

.chartWrap {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-time {
  margin: 10px 0 0 10px;
  height: 40px;
  text-align: right;
}

.statistics {
  flex: 1;
  height: 100%;
}
.statistics .assets-top {
  display: flex;
}
.statistics .assets-top .el-table {
  margin-left: 10px;
}
.statistics .assets-top .el-table:nth-of-type(1) {
  margin-left: 0;
}
.text-button:nth-of-type(2) {
  margin-left: 0;
  padding: 0 !important;
}

.noteInput {
  max-width: 50%;
}

.fireBtn {
  padding: 0;
}
</style>