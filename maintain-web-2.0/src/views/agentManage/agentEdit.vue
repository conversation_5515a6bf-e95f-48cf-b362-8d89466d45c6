<!-- 编辑Agent -->
<template>
  <div class="agentEdit-content" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      title="编辑Agent"
      :visible.sync="editFormVisible"
      width="600px"
      top="10vh"
      class="dialog-border"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-row type="flex" class="row-bg">
        <el-form
          label-width="100px"
          :model="editForm"
          :rules="rules"
          ref="editForm"
          class="agentEdit-form"
        >
          <el-col :span="24">
            <el-form-item label="主机IP:" prop="ip">
              <el-input v-model="editForm.ip" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作系统:" prop="os">
              <el-select v-model="editForm.os" disabled>
                <el-option
                  v-for="item in systemOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="端口:" prop="port">
              <el-select v-model.number="editForm.port" disabled>
                <el-option
                  v-for="item in portOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="管理员名称:" prop="name">
              <el-input v-model="editForm.name" disabled></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
                        <el-form-item label="管理员密码:" prop="pswd">
                            <el-input v-model="editForm.pswd" disabled show-password></el-input>
                        </el-form-item>
                    </el-col> -->
          <!-- <el-col :span="24" v-if="rootShow">
                        <el-form-item label="root密码:" prop="rootPswd">
                            <el-input v-model="editForm.rootPswd" disabled show-password></el-input>
                        </el-form-item>
                    </el-col> -->
          <el-col :span="24">
            <el-form-item label="JDK版本:">
              <el-input disabled v-model="JDK">JDK1.8版本</el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-row>
              <el-form-item label="角色:" prop="roleIds">
                <el-checkbox-group v-model="editForm.roleIds">
                  <el-col :span="6" v-for="item in roleOptions" :key="item.id">
                    <el-checkbox
                      class="batchAdd"
                      :label="item.id"
                      :title="item.name"
                      @change="roleChange($event, item.name)"
                    >
                      {{ item.name }}
                    </el-checkbox>
                  </el-col>
                </el-checkbox-group>
              </el-form-item>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否配置VPN:">
              <el-radio-group v-model="editForm.existsVpn">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
                        <el-form-item label="NTP服务:">
                            <el-radio-group v-model="editForm.autoNTP">
                                <el-radio :label="1" :disabled='openDisabled'>开启</el-radio>
                                <el-radio :label="0">关闭</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col> -->
        </el-form>
      </el-row>

      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="editFormVisible = false" class="btn-style">
          关闭
        </button>
        <button
          @click.prevent="saveBtn"
          v-loading.fullscreen.lock="fullscreenLoading"
          class="btn-style primary-btn"
        >
          确定
        </button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkIPNotEnablePort } from "@/utils/validate.js";

export default {
  name: "agentEdit-content",
  props: { editData: Object },
  data() {
    return {
      JDK: "JDK1.8版本",

      fullscreenLoading: false,
      openDisabled: false,
      editFormVisible: true,
      rootShow: false,

      roleOptions: [],
      roleName: [],
      systemOptions: [
        {
          label: "Windows",
          value: 1,
        },
        {
          label: "Linux",
          value: 0,
        },
      ],
      portOptions: [
        {
          label: 22222,
          value: 22222,
        },
        {
          label: 22,
          value: 22,
        },
        {
          label: 36535,
          value: 36535,
        },
      ],

      editForm: {
        name: "Administrator",
        pswd: "",
        ip: "",
        rootPswd: "",
        note: "",
        port: 22222,
        os: 1,
        autoNTP: 1,
        autoWatchDog: 1,
        existsVpn: 0,
        roleIds: [],
      },
      rules: {
        ip: [
          {
            trigger: "blur",
            required: true,
            validator: (rule, value, callback) => {
              if (value == "") {
                callback(new Error("请输入IP"));
              } else {
                if (checkIPNotEnablePort(value)) {
                  callback();
                } else {
                  callback(new Error("IP格式不正确"));
                }
              }
            },
          },
        ],
        port: [
          {
            required: true,
            message: "请选择端口",
          },
          {
            type: "number",
            message: "端口必须为数字值",
          },
        ],
        os: [
          {
            required: true,
            message: "请选择操作系统",
          },
        ],
        rootPswd: [
          {
            required: true,
            message: "请输入root密码",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入管理员名称",
          },
        ],
        pswd: [
          {
            required: true,
            message: "请输入管理员密码",
          },
        ],
        roleIds: [
          {
            type: "array",
            required: true,
            message: "请至少选择一种角色",
          },
        ],
      },
    };
  },
  methods: {
    //请求数据函数
    getDataList() {
      let _this = this;

      _this.$http
        .post("agent/roles.json")
        .then((res) => {
          if (res.data.code == 0) {
            _this.roleOptions = res.data.data;
            _this.initData();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          console.log(error);
          _this.erFn();
        });
    },
    saveData(data) {
      let _this = this;

      _this.$http
        .post("agent/update", _this.qs.stringify(data))
        .then((res) => {
          if (res.data.code == 0) {
            this.suFn("操作成功");
            this.resetForm("SUCCESS");
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
        });
    },

    //功能函数
    saveBtn() {
      let _this = this,
        data = {};

      _this.$refs["editForm"].validate((valid) => {
        if (valid) {
          data = { ..._this.editForm };
          _this.formatRole(true, data.roleIds);
          data.role = _this.formatRole(true, data.roleIds).join(",");
          _this.saveData(data);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(val) {
      this.$refs["editForm"].resetFields();
      this.editFormVisible = false;
      this.$emit("returnMain", val);
    },
    roleChange(status, val) {
      let roleName = this.roleName;
      let roleflag = false;

      if (status) {
        roleName.push(val);
      } else {
        for (let i = 0; i < roleName.length; i++) {
          if (roleName[i] == val) {
            roleName.splice(i, 1);
          }
        }
      }
      // this.formatNtp(roleName);
    },
    formatNtp(roleName) {
      let roleflag = false;

      for (let i = 0; i < roleName.length; i++) {
        if (
          roleName[i].indexOf("CDH") > -1 ||
          roleName[i].indexOf("cdh") > -1
        ) {
          this.editForm.autoNTP = 0;
          roleflag = true;
          break;
        }
      }
      roleflag ? (this.openDisabled = true) : (this.openDisabled = false);
    },
    formatRole(formatString, roleName) {
      let data = [];

      roleName = roleName || [];
      $.each(this.roleOptions, (index, item) => {
        if (roleName && roleName.length > 0) {
          roleName.forEach((nameItem, nameIndex) => {
            if (nameItem == item.name) {
              !formatString && data.push(item.id);
            }
            if (nameItem == item.id) {
              formatString && data.push(item.name);
            }
          });
        }
      });
      return data;
    },
    initData() {
      this.roleName = this.editData.role.split(",");
      // this.formatNtp(this.roleName);

      $.each(this.editData, (index, item) => {
        $.each(this.editForm, (formIndex, formItem) => {
          formIndex == index && (this.editForm[formIndex] = item);
        });
      });

      this.editForm.roleIds = this.formatRole(false, this.roleName);
      if (this.editForm.name != "root" && this.editForm.os == 0) {
        this.rootShow = true;
      }
    },
  },
  mounted() {
    this.getDataList();
    // this.editForm = { ...this.editData };
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.agentEdit-content {
  height: 100%;
  overflow-y: auto;
}
.agentEdit-form {
  width: 90%;
  margin: auto;
}
.btn-style:first-child {
  margin-right: 10px;
}
.btn-style {
  padding: 0 20px;
}
.batchAdd {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>