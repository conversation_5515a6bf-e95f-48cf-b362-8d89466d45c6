<!-- Agent管理 -->
<template>
  <div class="agent-content">
    <el-row class="row-content" v-loading.fullscreen="fullscreenLoading">
      <el-col :span="24" class="content-form">
        <el-form label-width="4rem" class="agent-form">
          <el-row type="flex" class="row-bg">
            <el-col :span="24" class="form-btn">
              <el-col :span="12" class="status-row">
                <StatusMsg
                  v-if="statusData != null"
                  :statusData="statusData"
                  :statusChange="statusChange"
                  :statusOption="statusOption"
                ></StatusMsg>
                <div>
                  <span>ntpServer:</span>
                  <span>{{ ntpServer || "-" }}</span>
                </div>
              </el-col>
              <el-col :span="12" class="agent-btn">
                <button
                  @click.prevent="addBtn"
                  class="btn-style"
                  v-if="btn.addAgent.value"
                >
                  <div class="button-icon button-add"></div>
                  <span>添加</span>
                </button>
                <button
                  @click.prevent="importBtn"
                  class="btn-style"
                  v-if="btn.importBtn.value"
                >
                  <i class="iconfont icon-import"></i>
                  <span>导入</span>
                </button>
                <button
                  @click.prevent="updateAllBtn"
                  class="btn-style"
                  v-if="btn.updateAgent.value"
                >
                  <i class="iconfont icon-update"></i>
                  <span>更新Agent</span>
                </button>
                <el-dropdown
                  @command="handelCommand"
                  @visible-change="dropdownChange"
                >
                  <div class="dropdownContent">
                    <span>一键操作</span>
                    <i
                      :class="[
                        dropdownShow
                          ? 'el-icon-arrow-up'
                          : 'el-icon-arrow-down',
                        'el-icon--right',
                      ]"
                    ></i>
                  </div>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-for="(item, index) in buttonGroup"
                      :key="index"
                      :command="item"
                      class="dropdownItem"
                    >
                      {{ item }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </el-col>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
      <el-col :span="24" class="content-table">
        <el-table
          :data="dataList"
          border
          v-loading="tableLoading"
          :row-class-name="bodyClass"
          :header-cell-style="tableHeader"
          :header-row-class-name="headerClass"
          @cell-click="cellClick"
          height="100%"
          class="table-border"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
          ></el-table-column>
          <el-table-column label="状态" width="60">
            <template slot-scope="scope">
              <el-popover
                :trigger="scope.row.content != '良好' ? 'hover' : 'manual'"
                placement="top-start"
              >
                <p class="popoverContent">{{ scope.row.description }}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag
                    size="small"
                    class="status-tag"
                    :color="scope.row.status"
                  >
                    {{ scope.row.content }}
                  </el-tag>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="ip"
            label="服务器IP"
            width="127"
            sortable
          ></el-table-column>
          <el-table-column
            prop="os"
            label="操作系统"
            width="100"
            :formatter="formatOs"
          ></el-table-column>
          <el-table-column
            prop="role"
            label="角色"
            sortable
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="note"
            label="备注"
            min-width="90"
            class-name="overflowColumn"
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.editShow"
                :ref="scope.row.refName"
                v-model="scope.row.inputVal"
                @blur="noteBlur(scope.row)"
                :autosize="true"
                :show-word-limit="true"
                maxlength="255"
                type="textarea"
                rows="1"
                class="noteInput"
              >
              </el-input>
              <el-popover
                class="popoverSpan"
                trigger="hover"
                placement="top-start"
                v-else
              >
                <p class="popoverContent">{{ scope.row.note || "-" }}</p>
                <span slot="reference" class="name-wrapper edit-span">
                  {{ scope.row.note || "-" }}
                </span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            width="145"
            sortable
            label="创建时间"
          ></el-table-column>
          <el-table-column
            prop="lastHeartbeatTime"
            width="145"
            sortable
            label="最近心跳时间"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.lastHeartbeatTime || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="状态" width="100">
            <template slot-scope="scope">
              <el-switch
                disabled
                v-model="scope.row.switchValue"
                active-color="#25ce88"
                inactive-color="#cfcfcf"
                :active-text="scope.row.switchText"
                @click.native="switchClick(scope.row)"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="310">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="text-button"
                v-if="btn.editAgent.value"
                @click="editBtn(scope.row)"
              >
                <div class="agentButton button-edit"></div>
                编辑
              </el-button>
              <el-button
                type="text"
                class="text-button"
                v-if="btn.deleteAgent.value"
                @click="deleteBtn(scope.row)"
              >
                <div class="agentButton button-delete"></div>
                删除
              </el-button>
              <el-button
                type="text"
                class="text-button form-btn"
                v-if="btn.updateAgent.value"
                @click="updateBtn(scope.row)"
              >
                <!-- <div class="agentButton button-update"></div> -->
                <i class="iconfont icon-update"></i>
                更新
              </el-button>
              <el-button
                type="text"
                class="text-button form-btn"
                @click="synchronousBtn(scope.row)"
              >
                <i class="iconfont icon-update"></i>
                时间同步
              </el-button>
              <span v-if="!btn.editAgent.value && !btn.deleteAgent.value"
                >-</span
              >
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <agentAdd
      v-if="addFormVisible"
      @returnMain="closeDialog('添加', $event)"
    ></agentAdd>
    <agentEdit
      v-if="editFormVisible"
      @returnMain="closeDialog('编辑', $event)"
      :editData="editData"
    ></agentEdit>
    <importFile
      v-if="importFileShow"
      @returnMain="closeDialog('导入', $event)"
      :importData="importData"
    ></importFile>
    <webSocketSend
      v-if="socketShow"
      @socketReturn="closeDialog('webSocket', $event)"
      :socketData="socketData"
    ></webSocketSend>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import agentAdd from "./agentAdd.vue";
import agentEdit from "./agentEdit.vue";
import importFile from "@/components/importFile.vue";
import webSocketSend from "@/components/webSocketSend.vue";
import StatusMsg from "@/views/common/statusMsg";
import {
  tableHeaderStyle,
  bodyRowClassName,
  headerRowClassName,
} from "@/utils/tableStyle.js";
import { requestInterval } from "@/utils/request.js";
import { mapGetters } from "vuex";
import { getStore } from "@/utils/store.js";

export default {
  name: "agent-content",
  components: { agentAdd, agentEdit, importFile, webSocketSend, StatusMsg },
  data() {
    return {
      ntpServer: "",
      pageTimer: null,
      addFormVisible: false,
      editFormVisible: false,
      openDisabled: false,
      tableLoading: false,
      fullscreenLoading: false,
      socketShow: false,
      importFileShow: false,
      dropdownShow: false,

      dataList: [],
      buttonGroup: ["一键时间同步", "一键开启平台", "一键关闭平台"],
      btn: {
        addAgent: {
          name: "添加Agent",
          value: false,
        },
        importBtn: {
          name: "导入Agent",
          value: false,
        },
        updateAgent: {
          name: "更新Agent",
          value: false,
        },
        operateAgent: {
          name: "操作Agent",
          value: false,
        },
        deleteAgent: {
          name: "删除Agent",
          value: true,
        },
        editAgent: {
          name: "编辑Agent",
          value: true,
        },
      },
      statusOption: {
        GRAY: { isShow: true },
      },
      statusData: null,
      editData: {},
      importData: {
        requestUrl: "",
        downFile: "",
      },
      form: {
        status: "",
      },
    };
  },
  methods: {
    init() {
      this.getDataList();
      getPermission(this.$route.name, this.btn);
      this.getStatus();
      this.requestInterval();
    },

    //请求数据函数
    getDataList(IntervalLoading) {
      let _this = this;

      _this.tableLoading = IntervalLoading ? false : true;
      _this.ntpServer = "";
      _this.$http
        .post("agent/list.json", _this.qs.stringify(_this.form))
        .then((res) => {
          _this.tableLoading = false;
          if (res.data.code == 0) {
            _this.ntpServer = res.data.data.ntpServer;
            //设置状态数量
            this.statusData = res.data.data.statusMap;
            res.data.data.page && _this.formatStatus(res.data.data.page);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    statusChange(item) {
      if (item.num == "0") {
        this.waFn("暂无数据");
        return false; //不执行选中的样式
      }
      this.form.status = item.field;
      this.getDataList();
    },

    saveNote(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("agent/note", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.getDataList(false);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    requestInterval() {
      if (this.pageTimer) {
        clearInterval(this.pageTimer);
      } else {
        this.pageTimer = setInterval(() => {
          this.getDataList(true);
        }, 60000);
      }
    },
    updateAllBtn() {
      let _this = this;

      _this.$http
        .post("agent/prepareUpdate.json")
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            res.data.data && this.updateAll(res.data.data);
          } else {
            _this.waFn(res.data.msg);
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn(error);
        });
    },
    deleteData(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("agent/delete", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("删除成功");
            _this.getDataList();
          } else {
            _this.waFn("操作失败");
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
        });
    },

    //处理数据函数
    formatOs(row, column, value) {
      switch (value) {
        case 0:
          value = "Linux";
          break;
        case 1:
          value = "Windows";
          break;
      }
      return value;
    },
    formatStatus(data) {
      data.forEach((item, index) => {
        switch (item.status) {
          case "GREEN":
            item.status = "#25ce88";
            item.content = "良好";
            break;
          case "YELLOW":
            item.status = "#fb843b";
            item.content = "警告";
            break;
          case "RED":
            item.status = "#f93846";
            item.content = "错误";
            break;
          case "GRAY":
            item.status = "#cdd0d3";
            item.content = "关闭";
            break;
        }
        if (item.programStatus) {
          switch (item.programStatus) {
            case "OPEN":
              item.switchValue = true;
              item.switchText = "开启";
              break;
            case "CLOSE":
              item.switchValue = false;
              item.switchText = "停止";
              break;
          }
        }
        item.editShow = false;
        item.inputVal = item.note || "";
        item.refName = "noteInput" + index;
      });
      this.dataList = [...data];
    },

    //功能函数
    switchClick(row) {
      if (this.btn.operateAgent.value != true) {
        this.waFn("暂无权限");
        return;
      }
      row.switchValue ? this.closeBtn(row) : this.openBtn(row);
    },
    cellClick(row, column, cell, event) {
      if (column.label == "备注" && row.editShow == false) {
        row.inputVal = row.note || "";
        row.editShow = true;
        this.$nextTick(() => {
          this.$refs[row.refName].focus();
        });
      }
    },
    noteBlur(row) {
      let data = {};

      row.inputVal == undefined && (row.inputVal = "");
      if (row.inputVal == row.note) {
        row.editShow = false;
        return;
      }
      data = {
        ip: row.ip,
        note: row.inputVal,
      };
      this.saveNote(data);
    },
    addBtn() {
      this.addFormVisible = true;
    },
    importBtn() {
      this.importData.requestUrl = "agent/import";
      this.importData.downFile = "agent";
      this.importFileShow = true;
    },
    closeDialog(name, val) {
      switch (name) {
        case "添加":
          this.addFormVisible = false;
          break;
        case "编辑":
          this.editFormVisible = false;
          break;
        case "导入":
          this.importFileShow = false;
          break;
        case "webSocket":
          this.socketData = {};
          this.socketShow = false;
          break;
      }
      val === "SUCCESS" && this.getDataList();
    },
    openBtn(value) {
      this.$confirm("您确定要开启该服务器Agent吗？", "提示", {
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      })
        .then(() => {
          this.socketData = {
            operateType: 1,
            param: JSON.stringify({
              id: value.id,
            }),
            service: "agent",
            title: "Agent开启过程",
          };
          this.socketShow = true;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    closeBtn(value) {
      this.$confirm("您确定要关闭该服务器Agent吗？", "提示", {
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      })
        .then(() => {
          this.socketData = {
            operateType: 0,
            param: JSON.stringify({
              id: value.id,
            }),
            service: "agent",
            title: "Agent关闭过程",
          };
          this.socketShow = true;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    updateAll(val) {
      let _this = this;

      if (_this.dataList.length == 0) {
        _this.msgFn("请先获取表格数据");
        return;
      }
      _this
        .$confirm(
          "已在" + val + "路径下发现MaintainAgent程序，您确定要更新Agent吗？",
          "提示",
          {
            cancelButtonText: "关闭",
            confirmButtonText: "确定",
            confirmButtonClass: "confirm-success",
          }
        )
        .then(() => {
          this.socketData = {
            operateType: 3,
            service: "agent",
            title: "Agent更新过程",
          };
          this.socketShow = true;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    synchronousBtn(row) {
      this.$confirm("您确定要同步时间吗？", "提示", {
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      })
        .then(() => {
          this.socketData = {
            operateType: 7,
            service: "agent",
            title: "时间同步过程",
          };
          row &&
            (this.socketData.param = JSON.stringify({
              ip: row.ip,
            }));
          this.socketShow = true;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    editBtn(row) {
      this.editFormVisible = true;
      this.editData = row;
    },
    deleteBtn(row) {
      let _this = this,
        data = {};

      this.$confirm("您确定要删除该Agent吗？", "提示", {
        roundBUtton: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      })
        .then(() => {
          data = {
            ip: row.ip,
          };
          _this.deleteData(data);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    updateBtn(row) {
      let _this = this;

      _this
        .$confirm("您确定要更新当前服务器的Agent吗？", "提示", {
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
        })
        .then(() => {
          this.socketData = {
            operateType: 3,
            service: "agent",
            param: JSON.stringify({
              ip: row.ip,
            }),
            title: "Agent更新过程",
          };
          this.socketShow = true;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    dropdownChange(val) {
      this.dropdownShow = val;
    },
    handelCommand(val) {
      switch (val) {
        case "一键时间同步":
          this.synchronousBtn();
          break;
        case "一键开启平台":
          let userName = "",
            template = "";

          userName = this.operationName || getStore({ name: "operationName" });
          template = `<p>用户名为：${userName}，请输入该用户密码：</p>`;

          this.$prompt(template, "请输入用户信息验证", {
            dangerouslyUseHTMLString: true,
            cancelButtonText: "关闭",
            confirmButtonText: "确定",
            closeOnPressEscape: false,
            confirmButtonClass: "confirm-success",
            customClass: "userPrompt",
            inputType: "password",
            inputPlaceholder: "请输入密码",
            beforeClose: (action, instance, done) => {
              if (action == "cancel") {
                done();
                return;
              }
              let data = {
                username: userName,
                password: instance.inputValue,
              };
              this.fullscreenLoading = true;
              this.$http
                .post("user/pass/confirm", this.qs.stringify(data))
                .then((res) => {
                  this.fullscreenLoading = false;
                  if (res.data.code == 0) {
                    done();
                  } else {
                    this.waFn("密码输入错误");
                  }
                })
                .catch((error) => {
                  this.fullscreenLoading = false;
                  console.log(error);
                });
            },
          })
            .then(() => {
              this.socketData = {
                operateType: 11,
                service: "agent",
                title: "一键开启过程",
              };
              this.socketShow = true;
            })
            .catch(() => {});

          break;
        case "一键关闭平台":
          var userName = "",
            template = "";

          userName = this.operationName || getStore({ name: "operationName" });
          template = `<p>用户名为：${userName}，请输入该用户密码：</p>`;

          this.$prompt(template, "请输入用户信息验证", {
            dangerouslyUseHTMLString: true,
            cancelButtonText: "关闭",
            confirmButtonText: "确定",
            closeOnPressEscape: false,
            confirmButtonClass: "confirm-success",
            customClass: "userPrompt",
            inputType: "password",
            inputPlaceholder: "请输入密码",
            beforeClose: (action, instance, done) => {
              if (action == "cancel") {
                done();
                return;
              }
              let data = {
                username: userName,
                password: instance.inputValue,
              };
              this.fullscreenLoading = true;
              this.$http
                .post("user/pass/confirm", this.qs.stringify(data))
                .then((res) => {
                  this.fullscreenLoading = false;
                  if (res.data.code == 0) {
                    done();
                  } else {
                    this.waFn("密码输入错误");
                  }
                })
                .catch((error) => {
                  this.fullscreenLoading = false;
                  console.log(error);
                });
            },
          })
            .then(() => {
              this.socketData = {
                operateType: 10,
                service: "agent",
                title: "一键关闭过程",
              };
              this.socketShow = true;
            })
            .catch(() => {});
          break;
      }
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
  },
  computed: {
    ...mapGetters(["operationName"]),
  },
  mounted() {
    this.init();
  },
  destroyed() {
    clearInterval(this.pageTimer);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.agent-content {
  height: 100%;
  width: 98%;
  margin: 0 1%;
}
.agent-form {
  margin: 1.5rem;
  /* padding-right:1rem; */
}
.btn-style:not(:last-child) {
  margin-right: 10px;
}
.button-icon {
  background: url("../../assets/images/agentManage/icon-Agent.png");
  display: inline-block;
  height: 16px;
  width: 16px;
  background-size: cover !important;
  vertical-align: sub;
}
.button-add {
  background-position: 0px -102px;
}
.btn-style:hover .button-add {
  background-position: 0px -152px;
}
.status-tag {
  color: #fff;
  border: 0;
  width: 40px;
  height: 20px;
  line-height: 20px;
}
.agent-btn {
  text-align: right;
  line-height: 25px;
}
.noteInput {
  width: 100%;
  max-width: 100%;
}
.agentButton {
  display: inline-block;
  height: 16px;
  width: 18px;
  background-size: cover !important;
  background: url("../../assets/images/procedureManage/icon-deploy.png");
  vertical-align: sub;
}
.button-edit {
  background-position: 0px -475px;
}
.text-button {
  margin-left: 0px;
  padding: 0 5px;
}
.text-button:hover .button-edit {
  background-position: 0px -525px;
}
.button-delete {
  background-position: 0px -365px;
}
.text-button:hover .button-delete {
  background-position: 0px -425px;
}
.button-update {
  background-position: 0px -365px;
}
.text-button:hover .button-update {
  background-position: 0px -425px;
}
.status-row > div {
  display: inline-block;
  margin-right: 10px;
}
.dropdownContent {
  height: 26px;
  padding: 0 10px;
  background: #fff;
  border: 1px solid #e2e2e2;
  color: #203e66;
  transition: 0.1s;
  white-space: nowrap;
  cursor: pointer;
  border-radius: 3px;
}
.el-icon-arrow-down,
.dropdownItem {
  color: #203e66;
}

.status-describe {
  height: 100%;
}

.status-describe > span {
  display: inline-block;
  line-height: 26px;
  cursor: pointer;
  margin: 0 5px;
}

.status-describe > span > span > span {
  text-decoration: underline;
}
</style>