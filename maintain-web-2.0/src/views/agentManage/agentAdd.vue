<!-- 添加Agent -->
<template>
  <div class="agentAdd-content" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      title="添加Agent"
      :visible.sync="addFormVisible"
      width="600px"
      top="10vh"
      class="dialog-border"
      custom-class="scrollDialog"
      :close-on-click-modal="false"
      v-dialogDrag
      @close="resetForm"
    >
      <el-row type="flex" class="row-bg">
        <el-form
          label-width="100px"
          :model="addForm"
          :rules="rules"
          ref="addForm"
          class="agentAdd-form"
        >
          <el-col :span="24">
            <el-form-item label="主机IP:" prop="ip">
              <el-input v-model="addForm.ip"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作系统:" prop="os">
              <el-select v-model="addForm.os" @change="osChange">
                <el-option
                  v-for="item in systemOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="端口:" prop="port">
              <el-select v-model.number="addForm.port">
                <el-option
                  v-for="item in portOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="管理员名称:" prop="name">
              <el-input
                v-model="addForm.name"
                @blur="rootBlur($event)"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="管理员密码:" prop="pswd">
              <el-input v-model="addForm.pswd" show-password></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="rootShow">
            <el-form-item label="root密码:" prop="rootPswd">
              <el-input v-model="addForm.rootPswd" show-password></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="JDK版本:">
              <el-input disabled v-model="JDK">JDK1.8版本</el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-row>
              <el-form-item label="角色:" prop="roleIds">
                <el-checkbox-group v-model="addForm.roleIds">
                  <el-col :span="6" v-for="item in roleOptions" :key="item.id">
                    <el-checkbox
                      class="batchAdd"
                      :label="item.id"
                      :title="item.name"
                      @change="roleChange($event, item.name)"
                    >
                      {{ item.name }}
                    </el-checkbox>
                  </el-col>
                </el-checkbox-group>
              </el-form-item>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-form-item label="NTP服务:">
              <el-radio-group v-model="addForm.autoNtp">
                <el-radio :label="1" :disabled="openDisabled">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="是否配置VPN:">
              <el-radio-group v-model="addForm.existsVpn">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="addFormVisible = false" class="btn-style">
          关闭
        </button>
        <button
          @click.prevent="saveBtn"
          v-loading.fullscreen.lock="fullscreenLoading"
          class="btn-style primary-btn"
        >
          确定
        </button>
      </div>
    </el-dialog>
    <webSocketSend
      v-if="socketShow"
      @socketReturn="closeDialog"
      :socketData="socketData"
    ></webSocketSend>
  </div>
</template>

<script>
import webSocketSend from "@/components/webSocketSend.vue";
import { checkIPNotEnablePort } from "@/utils/validate.js";

export default {
  name: "agentAdd-content",
  components: { webSocketSend },
  data() {
    return {
      JDK: "JDK1.8版本",

      fullscreenLoading: false,
      openDisabled: false,
      addFormVisible: true,
      socketShow: false,
      rootShow: false,

      roleOptions: [],
      roleName: [],
      systemOptions: [
        {
          label: "Windows",
          value: 1,
        },
        {
          label: "Linux",
          value: 0,
        },
      ],
      portOptions: [
        {
          label: 22222,
          value: 22222,
        },
        {
          label: 22,
          value: 22,
        },
        {
          label: 36535,
          value: 36535,
        },
      ],

      addForm: {
        name: "Administrator",
        pswd: "",
        ip: "",
        rootPswd: "",
        port: 22222,
        os: 1,
        autoNtp: 1,
        existsVpn: 0,
        autoWatchDog: 1,
        roleIds: [],
      },
      rules: {
        ip: [
          {
            trigger: "blur",
            required: true,
            validator: (rule, value, callback) => {
              if (value == "") {
                callback(new Error("请输入IP"));
              } else {
                if (checkIPNotEnablePort(value)) {
                  callback();
                } else {
                  callback(new Error("IP格式不正确"));
                }
              }
            },
          },
        ],
        port: [
          {
            required: true,
            message: "请选择端口",
          },
          {
            type: "number",
            message: "端口必须为数字值",
          },
        ],
        os: [
          {
            required: true,
            message: "请选择操作系统",
          },
        ],
        rootPswd: [
          {
            required: true,
            message: "请输入root密码",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入管理员名称",
          },
        ],
        pswd: [
          {
            required: true,
            message: "请输入管理员密码",
          },
        ],
        roleIds: [
          {
            type: "array",
            required: true,
            message: "请至少选择一种角色",
          },
        ],
      },
    };
  },

  methods: {
    //请求数据函数
    getDataList() {
      let _this = this;

      _this.$http
        .post("agent/roles.json")
        .then((res) => {
          if (res.data.code == 0) {
            _this.roleOptions = res.data.data;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
        });
    },

    //功能函数
    closeDialog(val) {
      this.socketData = {};
      this.socketShow = false;
      val === "SUCCESS" && this.resetForm(val);
    },
    saveBtn() {
      let _this = this,
        data = {};

      _this.$refs["addForm"].validate((valid) => {
        if (valid) {
          for (let item in _this.addForm) {
            data[item] = _this.addForm[item];
          }
          _this.socketData = {
            operateType: 4,
            param: JSON.stringify(data),
            service: "agent",
            title: "添加Agent过程",
          };
          _this.socketShow = true;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(val) {
      this.$refs["addForm"].resetFields();
      this.addFormVisible = false;
      this.$emit("returnMain", val);
    },
    osChange(value) {
      switch (value) {
        case 0:
          this.addForm.autoWatchDog = 0;
          this.addForm.port = 22;
          this.addForm.name = "root";
          break;
        case 1:
          this.addForm.autoWatchDog = 1;
          this.addForm.port = 22222;
          this.addForm.name = "Administrator";
          this.rootShow = false;
          this.addForm.rootPswd = "";
          break;
      }
    },
    roleChange(status, val) {
      let roleName = this.roleName;
      let roleflag = false;

      if (status) {
        roleName.push(val);
      } else {
        for (let i = 0; i < roleName.length; i++) {
          if (roleName[i] == val) {
            roleName.splice(i, 1);
          }
        }
      }
      for (let i = 0; i < roleName.length; i++) {
        if (
          roleName[i].indexOf("CDH") > -1 ||
          roleName[i].indexOf("cdh") > -1
        ) {
          this.addForm.autoNtp = 0;
          roleflag = true;
        }
      }
      roleflag ? (this.openDisabled = true) : (this.openDisabled = false);
    },
    rootBlur(e) {
      if (e.target.value != "root" && this.addForm.os == 0) {
        this.rootShow = true;
      } else {
        this.rootShow = false;
        this.addForm.rootPswd = "";
      }
    },
  },
  mounted() {
    this.getDataList();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.agentAdd-content {
  height: 100%;
  overflow-y: auto;
}
.agentAdd-form {
  width: 90%;
  margin: auto;
}
.btn-style:first-child {
  margin-right: 10px;
}
.btn-style {
  padding: 0 20px;
}
.batchAdd {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>