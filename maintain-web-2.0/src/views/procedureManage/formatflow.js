import { isNumber } from 'lodash-es'
/**
 * @description: 把数字转换为计算机储存单位
 * @param {Number} number 数量
 * @param {Number} limit 保留小数点后几位（默认两位）
 */
export function converToFlow(number, limit = 2) {
	if (!isNumber(number)) {
		return number;
	}
	var size = '',
		unit = '',
		k = 1024,
		m = 1024 * 1024,
		g = 1024 * 1024 * 1024,
		t = 1024 * 1024 * 1024 * 1024;
	if (number < k) {
		//如果小于1KB转化成B
		size = number.toFixed(limit);
		unit = 'B';
	} else if (number < m) {
		//如果小于1MB转化成KB
		size = (number / k).toFixed(limit);
		unit = 'K';
	} else if (number < g) {
		//如果小于1GB转化成MB
		size = (number / m).toFixed(limit);
		unit = 'M';
	} else if (number < t) {
		//如果小于1TB转化成GB
		size = (number / g).toFixed(limit);
		unit = 'G';
	} else {
		size = (number / t).toFixed(limit);
		unit = 'T';
	}
	var sizestr = size + '';
	var len = sizestr.indexOf('.');
	var dec = sizestr.substr(len + 1, 2);
	if (dec == '00') {
		//当小数点后为00时 去掉小数部分
		return sizestr.substring(0, len) + sizestr.substr(len + 3, 2) + unit;
	}
	return sizestr + unit;
}
