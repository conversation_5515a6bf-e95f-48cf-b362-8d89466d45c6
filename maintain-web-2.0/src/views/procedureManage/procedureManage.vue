<!-- 程序管理 -->
<template>
  <div class="procedure-content" v-loading.fullscreen="fullscreenLoading">
    <div class="collapse-content">
      <el-row class="collapse-row">
        <el-col class="content-form">
          <el-form label-width="4rem" class="procedure-form">
            <el-row type="flex" class="row-bg">
              <el-col :span="24" class="form-btn">
                <el-col :span="8" class="status-row">
                  <StatusMsg
                    :statusData="statusData"
                    :statusOption="statusMsgOption"
                    :statusChange="statusClick"
                  ></StatusMsg>
                </el-col>
                <el-col :span="16" class="deploy-Btn">
                  <el-input
                    v-model="form.name"
                    class="procedure-input"
                    clearable
                    placeholder="请输入程序名称"
                    @keyup.enter.once="searchBtn"
                  >
                    <el-select
                      slot="prepend"
                      v-model="form.matchType"
                      @change="fuzzyChange"
                    >
                      <el-option label="包含" :value="0"></el-option>
                      <el-option label="等于" :value="1"></el-option>
                      <el-option label="不包含" :value="2"></el-option>
                    </el-select>
                  </el-input>
                  <button
                    @click.prevent="searchBtn"
                    class="btn-style"
                    v-if="btn.addProcedure.value"
                  >
                    <div class="operateButton button-search"></div>
                    <span>查询</span>
                  </button>
                  <button
                    @click.prevent="addBtn('添加程序')"
                    class="btn-style"
                    v-if="btn.addProcedure.value"
                  >
                    <div class="operateButton button-add"></div>
                    <span>添加程序</span>
                  </button>
                  <button
                    @click.prevent="addBtn('批量添加程序')"
                    class="btn-style"
                    v-if="btn.addProcedure.value"
                  >
                    <div class="operateButton button-add"></div>
                    <span>批量添加</span>
                  </button>
                  <button
                    @click.prevent="getUpdateStatus"
                    class="btn-style"
                    v-if="btn.deployAll.value"
                  >
                    <div class="hardButton hard-button"></div>
                    <span>一键部署</span>
                  </button>
                  <el-dropdown
                    @command="handelCommand"
                    @visible-change="dropdownChange"
                  >
                    <div class="dropdownContent">
                      <span>一键操作</span>
                      <i
                        :class="[
                          dropdownShow
                            ? 'el-icon-arrow-up'
                            : 'el-icon-arrow-down',
                          'el-icon--right',
                        ]"
                      ></i>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        v-for="(item, index) in buttonGroup"
                        :key="index"
                        :command="item"
                        class="dropdownItem"
                      >
                        {{ item }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-dropdown
                    @command="handelCommand"
                    @visible-change="logDropdownChange"
                  >
                    <div class="dropdownContent">
                      <span>日志采集</span>
                      <i
                        :class="[
                          logDropdownShow
                            ? 'el-icon-arrow-up'
                            : 'el-icon-arrow-down',
                          'el-icon--right',
                        ]"
                      ></i>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        v-for="(item, index) in logButtonGroup"
                        :key="index"
                        :command="item"
                        class="dropdownItem"
                      >
                        {{ item }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </el-col>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
        <el-col class="content-collapse">
          <el-collapse class="procedure-collapse" v-model="activeName">
            <el-collapse-item
              :name="index"
              v-for="(item, index) in data"
              :key="item.value"
            >
              <template slot="title">
                <div class="collapse-text">
                  {{ index }}
                  <div
                    class="small-round collapse-round"
                    :style="{ background: formatStatus(item) }"
                  ></div>
                </div>
                <div class="title-icon"></div>
              </template>
              <div class="collapse-table">
                <el-table
                  :data="item.dataList"
                  border
                  :key="item.value"
                  :span-method="arraySpanMethod"
                  :cell-class-name="tableRowClassName"
                  @cell-mouse-leave="cellMouseLeave"
                  @cell-mouse-enter="cellMouseEnter"
                  :row-class-name="bodyClass"
                  :header-cell-style="tableHeader"
                  class="procedure-table"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    min-width="70"
                  ></el-table-column>
                  <el-table-column prop="status" label="状态" width="70">
                    <template slot-scope="scope">
                      <el-popover
                        class="popoverSpan"
                        trigger="hover"
                        placement="top-start"
                        v-if="scope.row.content != '良好'"
                      >
                        <p class="popoverContent">
                          {{ scope.row.description }}
                        </p>
                        <div slot="reference" class="name-wrapper">
                          <el-tag
                            size="small"
                            class="status-tag"
                            :color="formatStatus(scope.row)"
                          >
                            {{ scope.row.content }}
                          </el-tag>
                        </div>
                      </el-popover>
                      <div slot="reference" class="name-wrapper" v-else>
                        <el-tag
                          size="small"
                          class="status-tag"
                          :color="formatStatus(scope.row)"
                        >
                          {{ scope.row.content }}
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="程序" width="180">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        class="cell-button text-button"
                        v-if="btn.procedureDetail.value"
                        :title="scope.row.name"
                        @click="showDetail(scope.row)"
                      >
                        {{ scope.row.name }}
                      </el-button>
                      <span v-else>{{ scope.row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="serverIp" label="IP" width="120">
                    <template slot-scope="scope">
                      <span>{{ scope.row.serverIp }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="restartCount"
                    label="重启次数"
                    width="70"
                  ></el-table-column>
                  <el-table-column
                    prop="note"
                    label="备注"
                    min-width="90"
                    class-name="overflowColumn"
                  >
                    <template slot-scope="scope">
                      <el-popover
                        class="popoverSpan"
                        trigger="hover"
                        placement="top-start"
                        v-if="scope.row.note"
                      >
                        <p class="popoverContent">{{ scope.row.note }}</p>
                        <div slot="reference" class="name-wrapper">
                          <span>{{ scope.row.note }}</span>
                        </div>
                      </el-popover>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="version"
                    label="版本号"
                    width="90"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="lastHeartbeatTime"
                    label="心跳时间"
                    width="145"
                  >
                    <template slot-scope="scope">
                      <span>{{ scope.row.lastHeartbeatTime || "-" }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="守护状态" width="100">
                    <template slot-scope="scope">
                      <el-switch
                        disabled
                        v-model="scope.row.switchValue"
                        active-color="#25ce88"
                        inactive-color="#cfcfcf"
                        :active-text="scope.row.switchText"
                        @click.native="switchClick(scope.row)"
                      >
                      </el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column label="心跳监控状态" width="110">
                    <template slot-scope="scope">
                      <el-switch
                        disabled
                        v-model="scope.row.heartMonitor"
                        active-color="#25ce88"
                        inactive-color="#cfcfcf"
                        :active-text="scope.row.heartText"
                        @click.native="switchHeart(scope.row)"
                      >
                      </el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="175">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        class="text-button"
                        v-if="btn.editProcedure.value"
                        @click="editBtn(scope.row.id)"
                      >
                        <div class="hardButton button-edit"></div>
                        编辑
                      </el-button>
                      <el-button
                        type="text"
                        class="text-button"
                        v-if="btn.deleteProcedure.value"
                        @click="deleteBtn(scope.row)"
                      >
                        <div class="hardButton button-delete"></div>
                        删除
                      </el-button>
                      <el-button
                        type="text"
                        class="text-button"
                        v-if="btn.editProcedure.value"
                        @click="noteBtn(scope.row)"
                      >
                        <div class="hardButton button-edit"></div>
                        备注
                      </el-button>
                      <span
                        v-if="
                          !btn.editProcedure.value && !btn.deleteProcedure.value
                        "
                        >-</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="一键操作"
                    align="center"
                    v-if="oneKeyShow"
                    min-width="90"
                  >
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        class="text-button"
                        v-if="btn.clearCount.value"
                        @click="clearCount(scope.row)"
                      >
                        <div class="hardButton button-clear"></div>
                        清空次数
                      </el-button>
                      <el-button
                        type="text"
                        class="text-button"
                        v-if="btn.operateProcedure.value"
                        @click="operateProcedure('开启程序', scope.row)"
                      >
                        <div class="hardButton button-open"></div>
                        开启程序
                      </el-button>
                      <el-button
                        type="text"
                        class="text-button"
                        v-if="btn.operateProcedure.value"
                        @click="operateProcedure('关闭程序', scope.row)"
                      >
                        <div class="hardButton button-close"></div>
                        关闭程序
                      </el-button>
                      <span
                        v-if="
                          !btn.editProcedure.value && !btn.deleteProcedure.value
                        "
                        >-</span
                      >
                    </template>
                  </el-table-column>
                  <!-- <el-table-column
                    label="logstash操作"
                    align="center"
                    v-if="oneKeyShow"
                    min-width="100"
                  >
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        class="text-button form-btn"
                        v-if="btn.distributeLogstash.value"
                        @click="uploadLogstash(scope.row)"
                      >
                        <div class="hardButton button-open"></div>
                        <span class="iconfont icon-upload"></span>
                        上传程序
                      </el-button>
                      <el-button
                        type="text"
                        class="text-button form-btn"
                        v-if="btn.operateProcedure.value"
                        @click="updateLogstash(scope.row)"
                      >
                        <div class="hardButton button-close"></div>
                        <span class="iconfont icon-update"></span>
                        更新配置
                      </el-button>
                      <span
                        v-if="
                          !btn.distributeLogstash.value &&
                          !btn.operateProcedure.value
                        "
                        >-</span
                      >
                    </template>
                  </el-table-column> -->
                </el-table>
              </div>
            </el-collapse-item>
          </el-collapse>
          <div class="noData-div" v-show="dataShow">暂无数据</div>
        </el-col>
      </el-row>
    </div>

    <DeployAll
      v-if="hideDeployAll"
      @returnMain="closeDeploy"
      :setpData="setpData"
    ></DeployAll>

    <ProcedureDialog
      v-if="hideDialog"
      @returnMain="closeProdureDialog"
      :dialogData="dialogData"
    ></ProcedureDialog>

    <WebSocketSend
      v-if="socketShow"
      @socketReturn="closeDialog"
      :socketData="socketData"
    ></WebSocketSend>

    <editFile
      v-if="editFileShow"
      @editFileReturn="editFileReturn"
      :editData="editData"
    >
      <el-col slot="promptSlot">
        <p class="promptText">说明：</p>
        <p class="promptText">
          1."***各角色机的本机IP***",代表待分发目标主机ip,
          在分发程序时将根据各agent角色机ip进行替换, 请确保角色机ip正确。
        </p>
        <p class="promptText">
          2.基础环境配置, 默认值取运维系统application.yml配置。
        </p>
      </el-col>
    </editFile>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import DeployAll from "../hardware/deployAll.vue";
import ProcedureDialog from "./procedureDialog.vue";
import WebSocketSend from "@/components/webSocketSend.vue";
import editFile from "@/components/editFile.vue";
import StatusMsg from "@/views/common/statusMsg";

import { mapMutations, mapGetters } from "vuex";
import { getStore } from "@/utils/store.js";
import { bodyRowClassName, tableHeaderStyle } from "@/utils/tableStyle.js";

export default {
  name: "procedure-content",
  components: {
    DeployAll,
    WebSocketSend,
    ProcedureDialog,
    editFile,
    StatusMsg,
  },
  data() {
    return {
      rowIndex: "-1",
      role: "",
      description: "",
      searchName: "",
      pageTimer: null,

      hideDeployAll: false,
      hideDialog: false,
      fullscreenLoading: false,
      socketShow: false,
      dropdownShow: false,
      logDropdownShow: false,
      oneKeyShow: false,
      dataShow: true,
      editFileShow: false,

      setpData: {}, //一键部署弹出框配置

      data: {},
      detailData: {},
      dialogData: {},
      configData: {},
      editData: {},
      form: {
        name: "",
        status: "",
        matchType: 0,
      },
      statusData: null,
      statusMsgOption: {
        GRAY: { isShow: true },
        // TOTAL: { isShow: false },
      },
      btn: {
        addProcedure: {
          name: "添加程序",
          value: false,
        },
        editProcedure: {
          name: "编辑程序",
          value: false,
        },
        deleteProcedure: {
          name: "删除程序",
          value: false,
        },
        operateProcedure: {
          name: "操作程序",
          value: false,
        },
        procedureDetail: {
          name: "程序详情",
          value: false,
        },
        deployAll: {
          name: "一键部署",
          value: false,
        },
        clearCount: {
          name: "重启次数置零",
          value: false,
        },
        distributeLogstash: {
          name: "分发logstash",
          value: false,
        },
        updateLogstash: {
          name: "更新logstash配置文件",
          value: false,
        },
        heartMonitor: {
          name: "心跳监控",
          value: false,
        },
      },
      buttonGroup: [
        "开启搜索程序",
        "关闭搜索程序",
        "清空搜索程序重启次数",
        "批量编辑公共配置文件",
      ],
      logButtonGroup: ["分发logstash", "更新logstash配置文件"],
      activeName: [],
      OrderArr: [],
      hoverArr: [],
    };
  },
  methods: {
    ...mapMutations(["saveCacheName"]),
    init() {
      this.getDataList();
      getPermission(this.$route.name, this.btn);
      this.getStatus();
      this.requestInterval();
    },

    //请求数据函数
    getDataList(IntervalLoading) {
      let _this = this;

      _this.fullscreenLoading = IntervalLoading ? false : true;
      _this.$http
        .post("software/list.json", _this.qs.stringify(_this.form))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            this.statusData = res.data.data.statusMap || {
              RED: 0,
              YELLOW: 0,
              GREEN: 0,
              GRAY: 0,
            };
            if (
              res.data.data.softwares &&
              JSON.stringify(res.data.data.softwares) != "{}"
            ) {
              _this.form.name
                ? (_this.oneKeyShow = false)
                : (_this.oneKeyShow = true);
              _this.dataShow = false;
              _this.data = res.data.data.softwares;
              _this.getTable(_this.data);
            } else {
              _this.dataShow = true;
              _this.data = {};
              _this.msgFn("暂无数据");
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    operateSever(data) {
      let _this = this;
      let item = {};

      if (data.pid) item.pid = data.pid;
      _this.fullscreenLoading = true;

      _this.$http
        .post(
          "software/operate_" + data.id + "_" + data.operate + ".json",
          _this.qs.stringify(item)
        )
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            setTimeout(() => {
              _this.suFn("操作成功");
              _this.getDataList();
            }, 3000);
          } else {
            _this.waFn("操作失败");
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
        });
    },
    operaHeart(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("software/heart_monitor", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.getDataList();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    deleteBtn(row) {
      let _this = this,
        data = {};

      this.$confirm("您确定要删除该服务吗？", "提示", {
        roundBUtton: true,
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          data = {
            id: row.id,
            serverIp: row.serverIp,
          };

          _this.fullscreenLoading = true;
          _this.$http
            .post("software/delete_soft", _this.qs.stringify(data))
            .then((res) => {
              _this.fullscreenLoading = false;
              if (res.data.code == 0) {
                _this.suFn("删除成功");
                _this.getDataList();
              } else {
                _this.waFn("操作失败");
              }
            })
            .catch((error) => {
              _this.fullscreenLoading = false;
              _this.erFn();
            });
        })
        .catch(() => {});
    },
    editBtn(val) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("software/soft_" + val + ".json")
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.dialogData = { ...res.data.data };
            _this.dialogData.dialogName = "编辑程序";
            _this.hideDialog = true;
          } else {
            _this.waFn("操作失败");
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
        });
    },
    clearRestartCount(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("software/clear_restart_count", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("清除成功");
            _this.getDataList();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getUpdateStatus() {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .get("software/checkUpgradePackage")
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (res.data.data) {
              if (res.data.data.checkResult) {
                this.deployBtn();
              } else {
                this.msgFn("程序包已经是最新版本");
              }
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          // _this.erFn();
          console.log(error);
        });
    },
    requestInterval() {
      if (this.pageTimer) {
        clearInterval(this.pageTimer);
      } else {
        this.pageTimer = setInterval(() => {
          this.getDataList(true);
        }, 60000);
      }
    },
    //处理数据函数
    formatStatus(row) {
      let status = "";

      status = row.totalStatus ? row.totalStatus : row.status;
      switch (status) {
        case "GREEN":
          status = "#25ce88";
          row.content = "良好";
          break;
        case "YELLOW":
          status = "#fb843b";
          row.content = "警告";
          break;
        case "RED":
          status = "#f93846";
          row.content = "错误";
          break;
        case "GRAY":
          status = "#cdd0d3";
          row.content = "关闭";
          break;
      }
      if (row.programStatus) {
        switch (row.programStatus) {
          case "OPEN":
            row.switchValue = true;
            row.switchText = "开启";
            break;
          case "CLOSE":
            row.switchValue = false;
            row.switchText = "停止";
            break;
        }
      }
      switch (row.heartMonitor) {
        case true:
          row.heartText = "开启";
          break;
        case false:
          row.heartText = "停止";
          break;
      }

      return status;
    },
    //功能函数
    addCacheName(flag) {
      let arr = [],
        indexOf = null;

      arr = [...this.cacheName];
      indexOf = arr.indexOf("procedure-content");
      if (flag) {
        !(indexOf > -1) && arr.push("procedure-content");
      } else {
        indexOf > -1 && arr.splice(indexOf, 1);
      }
      return arr;
    },
    clearCount(row) {
      let _this = this,
        data = {
          host: row.serverIp,
        },
        confirmContent = `确认清空搜索结果中 ${row.serverIp} 下所有的程序？`;

      this.$confirm(confirmContent, "提示", {
        closeOnClickModal: false,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      })
        .then(() => {
          this.clearRestartCount(data);
        })
        .catch(() => {});
    },
    dropdownChange(val) {
      this.dropdownShow = val;
    },
    logDropdownChange(val) {
      this.logDropdownShow = val;
    },
    handelCommand(val) {
      switch (val) {
        case "分发logstash":
          this.logstatshBtn("分发");
          break;
        case "更新logstash配置文件":
          this.logstatshBtn("修改");
          break;
        case "清空搜索程序重启次数":
          let data = {
            software: this.form.name,
            status: this.form.status,
            fuzzy: this.form.matchType,
          };
          this.clearRestartCount(data);
          break;
        case "开启搜索程序":
          this.socketData = {
            operateType: 1,
            param: JSON.stringify({
              software: this.form.name,
              status: this.form.status,
              fuzzy: this.form.matchType,
            }),
            service: "software",
            title: "程序开启过程",
          };
          this.socketShow = true;
          break;
        case "关闭搜索程序":
          let userName = "",
            template = "";

          userName = this.operationName || getStore({ name: "operationName" });
          template = `<p>用户名为：${userName}，请输入该用户密码：</p>`;

          this.$prompt(template, "请输入用户信息验证", {
            dangerouslyUseHTMLString: true,
            cancelButtonText: "关闭",
            confirmButtonText: "确定",
            closeOnPressEscape: false,
            confirmButtonClass: "confirm-success",
            customClass: "userPrompt",
            inputType: "password",
            inputPlaceholder: "请输入密码",
            beforeClose: (action, instance, done) => {
              if (action == "cancel") {
                done();
                return;
              }
              let data = {
                username: userName,
                password: instance.inputValue,
              };
              this.fullscreenLoading = true;
              this.$http
                .post("user/pass/confirm", this.qs.stringify(data))
                .then((res) => {
                  this.fullscreenLoading = false;
                  if (res.data.code == 0) {
                    done();
                  } else {
                    this.waFn("密码输入错误");
                  }
                })
                .catch((error) => {
                  this.fullscreenLoading = false;
                  console.log(error);
                });
            },
          })
            .then(() => {
              this.socketData = {
                operateType: 0,
                param: JSON.stringify({
                  software: this.form.name,
                  status: this.form.status,
                  fuzzy: this.form.matchType,
                }),
                service: "software",
                title: "程序关闭过程",
              };
              this.socketShow = true;
            })
            .catch(() => {});
          break;
        case "批量编辑公共配置文件":
          var userName = "",
            template = "";

          userName = this.operationName || getStore({ name: "operationName" });
          template = `<p>用户名为：${userName}，请输入该用户密码：</p>`;

          this.$prompt(template, "请输入用户信息验证", {
            dangerouslyUseHTMLString: true,
            cancelButtonText: "关闭",
            confirmButtonText: "确定",
            closeOnPressEscape: false,
            confirmButtonClass: "confirm-success",
            customClass: "userPrompt",
            inputType: "password",
            inputPlaceholder: "请输入密码",
            beforeClose: (action, instance, done) => {
              if (action == "cancel") {
                done();
                return;
              }
              let data = {
                username: userName,
                password: instance.inputValue,
              };
              this.fullscreenLoading = true;
              this.$http
                .post("user/pass/confirm", this.qs.stringify(data))
                .then((res) => {
                  this.fullscreenLoading = false;
                  if (res.data.code == 0) {
                    done();
                  } else {
                    this.waFn("密码输入错误");
                  }
                })
                .catch((error) => {
                  this.fullscreenLoading = false;
                  console.log(error);
                });
            },
          })
            .then(() => {
              this.editFileShow = true;
            })
            .catch(() => {});
          break;
      }
    },
    statusClick(item) {
      // if (val == "0") {
      //   this.waFn("暂无数据");
      //   return;
      // }
      // switch (className) {
      //   case "num-red":
      //     this.form.status = "RED";
      //     break;
      //   case "num-yellow":
      //     this.form.status = "YELLOW";
      //     break;
      //   case "num-green":
      //     this.form.status = "GREEN";
      //     break;
      //   case "num-gray":
      //     this.form.status = "GRAY";
      //     break;
      // }
      // this.getDataList();

      if (item.num == "0") {
        this.waFn("暂无数据");
        return false; //不执行选中的样式
      }
      this.form.status = item.field;
      this.getDataList();
    },
    editFileReturn(data) {
      if (data) {
        let Base64 = require("js-base64").Base64;

        this.socketData = {
          operateType: 9,
          service: "software",
          title: "分发过程",
          param: JSON.stringify({
            content: Base64.encode(data.configContent),
          }),
        };
        this.socketShow = true;
      } else {
        this.editFileShow = false;
      }
    },
    uploadLogstash(row) {
      this.$confirm(
        "您确定要上传" + row.serverIp + "服务器下的logstash吗？",
        "提示",
        {
          closeOnClickModal: false,
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
        }
      )
        .then(() => {
          this.socketData = {
            operateType: 5,
            service: "software",
            param: JSON.stringify({
              ip: row.serverIp,
            }),
            title: "分发logstash过程",
          };
          this.socketShow = true;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    updateLogstash(row) {
      this.$confirm(
        "您确定要更新" + row.serverIp + "服务器下的logstash吗？",
        "提示",
        {
          closeOnClickModal: false,
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
        }
      )
        .then(() => {
          this.socketData = {
            operateType: 6,
            service: "software",
            param: JSON.stringify({
              ip: row.serverIp,
            }),
            title: "更新logstash配置文件过程",
          };
          this.socketShow = true;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    closeDeploy(val) {
      this.hideDeployAll = false;
      val === "SUCCESS" && this.getDataList();
    },
    closeProdureDialog(val) {
      this.hideDialog = false;
      val === "SUCCESS" && this.getDataList();
    },
    switchHeart(row) {
      let data = {};

      if (this.btn.heartMonitor.value != true) {
        this.waFn("暂无权限");
        return;
      }
      switch (row.heartMonitor) {
        case true:
          data = {
            id: row.id,
            heartMonitor: "false",
          };
          break;
        case false:
          data = {
            id: row.id,
            heartMonitor: "true",
          };
          break;
      }
      this.operaHeart(data);
    },
    switchClick(row) {
      if (this.btn.operateProcedure.value != true) {
        this.waFn("暂无权限");
        return;
      }
      row.switchValue ? this.closeBtn(row) : this.openBtn(row);
    },
    fuzzyChange() {
      this.form.name = "";
    },
    addBtn(val) {
      switch (val) {
        case "添加程序":
          this.dialogData.dialogName = "添加程序";
          this.hideDialog = true;
          break;
        case "批量添加程序":
          this.dialogData.dialogName = "批量添加程序";
          this.hideDialog = true;
          break;
      }
    },
    deployBtn() {
      let _this = this;
      _this.fullscreenLoading = true;

      //判断 一键部署的程序是否被中断过
      _this.$http
        .get("software/is_continue_upgrade")
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (res.data.data) {
              this.getCommonConfig();
            } else {
              this.$confirm("您需要修改公共配置文件吗？", "提示", {
                roundBUtton: true,
                closeOnClickModal: false,
                distinguishCancelAndClose: true,
                cancelButtonText: "不需要",
                confirmButtonText: "需要",
                confirmButtonClass: "confirm-success",
              })
                .then(() => {
                  this.setpData.isNew = true;
                  this.setpData.edidCommonConfig = true;
                  //设置公共配置
                  this.setCommonConfig(1);
                  //输入解压密码
                  this.comperssFile();
                })
                .catch((action) => {
                  if (action == "cancel") {
                    this.setpData.edidCommonConfig = false;
                    this.setpData.isNew = true;
                    //设置公共配置
                    this.setCommonConfig(0);
                    //输入解压密码
                    this.comperssFile();
                  }
                });
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
        });
    },
    //设置公共配置
    setCommonConfig(isModify) {
      this.$http
        .post(
          "software/recordIsModifyCommonConfig",
          this.qs.stringify({
            isModifyCommonConfig: isModify,
          })
        )
        .then((res) => {
          if (res.data.code == 0) {
          } else {
            res.data.msg ? this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {});
    },
    //获取公共配置
    getCommonConfig() {
      this.$http
        .get("software/getIsModifyCommonConfig")
        .then((res) => {
          if (res.data.code == 0) {
            this.setpData.isNew = false;
            this.setpData.edidCommonConfig = res.data.data.isModifyCommonConfig;
            this.hideDeployAll = true;
          } else {
            res.data.msg ? this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {});
    },
    //解压文件密码
    comperssFile() {
      this.$prompt("请输入文件解压密码", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputType: "password",
      })
        .then(({ value }) => {
          this.fullscreenLoading = true;
          //验证解压密码是否正确
          this.$http
            .post(
              "software/unzip_pwd.json",
              this.qs.stringify({
                pwd: value,
              })
            )
            .then((res) => {
              if (res.data.code == 0) {
                this.hideDeployAll = true;
              } else {
                res.data.msg ? this.waFn(res.data.msg) : _this.erFn();
              }
            })
            .catch((error) => {
              this.fullscreenLoading = true;
            });
        })
        .catch(() => {
          this.hideDeployAll = false;
        });
    },
    searchBtn(e) {
      this.getDataList();
    },
    openBtn(value) {
      this.$confirm("您确定要开启该服务吗？", "提示", {
        closeOnClickModal: false,
        roundBUtton: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          this.socketData = {
            operateType: 1,
            param: JSON.stringify({
              softwareId: value.id,
            }),
            service: "software",
            title: "程序开启过程",
          };
          this.socketShow = true;
        })
        .catch(() => {});
    },
    closeBtn(value) {
      let template = `<p>您确定要关闭该服务吗？</p>
                            <div class='prompt-remark'>
                                <span>操作备注：</span>
                            </div>`;

      this.$prompt(template, "提示", {
        dangerouslyUseHTMLString: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        customClass: "procedurePrompt",
      })
        .then((data) => {
          this.socketData = {
            operateType: 0,
            param: JSON.stringify({
              softwareId: value.id,
              msg: data.value,
            }),
            service: "software",
            title: "程序关闭过程",
          };
          this.socketShow = true;
        })
        .catch(() => {});
    },
    showDetail(row) {
      this.$router.push({
        path: "procedureDetail",
        query: {
          id: row.id,
          pid: row.pid,
          name: row.name,
          serverIp: row.serverIp,
        },
      });
    },
    logstatshBtn(value) {
      let type = null,
        title = "",
        confirmContent = "";

      switch (value) {
        case "分发":
          type = 5;
          title = "分发logstash过程";
          confirmContent =
            "只在第一次安装时需要分发，若安装完毕，无需分发。您确定要分发logstash吗？";
          break;
        case "修改":
          type = 6;
          title = "更新logstash配置文件过程";
          confirmContent = "您确定要更新logstash配置文件吗？";
          break;
      }

      this.$confirm(confirmContent, "提示", {
        cancelButtonText: "关闭",
        closeOnClickModal: false,
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
      })
        .then(() => {
          this.socketData = {
            operateType: type,
            service: "software",
            title: title,
          };
          this.socketShow = true;
        })
        .catch(() => {});
    },
    operateProcedure(val, row) {
      let type = null,
        title = "",
        host = "",
        confirmContent = "";

      switch (val) {
        case "开启程序":
          type = 1;
          title = "程序开启过程";
          host = row.serverIp;

          confirmContent = `确认开启搜索结果中 ${host} 下所有的程序？`;
          break;
        case "关闭程序":
          type = 0;
          title = "程序关闭过程";
          host = row.serverIp;
          confirmContent = `确认关闭搜索结果中 ${host} 下所有的程序？`;
          break;
      }

      if (host) {
        this.$confirm(confirmContent, "提示", {
          closeOnClickModal: false,
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
        })
          .then(() => {
            this.socketData = {
              operateType: type,
              param: JSON.stringify({
                host: host,
              }),
              service: "software",
              title: title,
            };
            this.socketShow = true;
          })
          .catch(() => {});
      } else {
        this.waFn("ip不存在");
      }
    },
    closeDialog(val) {
      this.socketData = {};
      this.socketShow = false;
      this.editFileShow = false;
      val === "SUCCESS" && this.getDataList();
    },
    getTable(data) {
      this.activeName = [];
      // this.activeName.push(data[0]);
      for (let i in data) {
        this.getOrderNumber(data[i].dataList);
        if (this.activeName.length == 0) this.activeName.push(i);
      }
    },
    getOrderNumber(data) {
      let OrderObj = {},
        OrderArr = [];
      data.forEach((item, index) => {
        item.rowIndex = index;
        if (OrderObj[item.serverIp]) {
          OrderObj[item.serverIp].push(item.id);
        } else {
          OrderObj[item.serverIp] = [];
          OrderObj[item.serverIp].push(item.id);
        }
      });
      for (let i in OrderObj) {
        if (OrderObj[i].length > 1) {
          OrderArr.push(OrderObj[i]);
        }
      }
      data.forEach((item, index) => {
        item.OrderArr = OrderArr;
      });
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (
        column.label === "IP" ||
        column.label === "一键操作" ||
        column.label === "logstash操作"
      ) {
        if (row.OrderArr.length != 0) {
          for (let i = 0; i < row.OrderArr.length; i++) {
            let element = row.OrderArr[i];
            for (let j = 0; j < element.length; j++) {
              let item = element[j];
              if (row.id == item) {
                if (j == 0) {
                  return {
                    rowspan: element.length,
                    colspan: 1,
                  };
                } else if (j != 0) {
                  return {
                    rowspan: 0,
                    colspan: 0,
                  };
                }
              }
            }
          }
        }
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.role == this.role) {
        let arr = this.hoverArr;
        if (arr && arr.length != 0) {
          for (let i = 0; i < arr.length; i++) {
            if (row.id == arr[i]) {
              return "hovered-row";
            }
          }
        }
      }
    },
    cellMouseEnter(row, column, cell, event) {
      this.rowIndex = row.id;
      this.role = row.role;
      this.hoverArr = [];
      row.OrderArr.forEach((item) => {
        if (item.indexOf(this.rowIndex) >= 0) {
          this.hoverArr = item;
        }
      });
    },
    cellMouseLeave(row, column, cell, event) {
      this.rowIndex = "-1";
      this.role = "";
      this.hoverArr = [];
    },
    noteBtn(row) {
      this.$prompt("", "备注编辑", {
        dangerouslyUseHTMLString: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        closeOnPressEscape: false,
        confirmButtonClass: "confirm-success",
        customClass: "userPrompt",
        inputType: "textarea",
        inputErrorMessage: "备注信息最多只能为255个字符",
        inputValidator: (val) => {
          return val.length < 255;
        },
        inputValue: row.note,
        beforeClose: (action, instance, done) => {
          if (action == "cancel") {
            done();
            return;
          }
          let data = {
            serverId: row.serverId,
            name: row.name,
            note: instance.inputValue,
          };
          this.fullscreenLoading = true;
          this.$http
            .post("software/note", this.qs.stringify(data))
            .then((res) => {
              this.fullscreenLoading = false;
              if (res.data.code == 0) {
                row.note = instance.inputValue;
                this.suFn("修改成功");
                done();
              } else {
                this.waFn("修改失败");
              }
            })
            .catch((error) => {
              this.fullscreenLoading = false;
              console.log(error);
            });
        },
      })
        .then(() => {})
        .catch(() => {});
    },
    bodyClass() {
      return bodyRowClassName();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
  },
  computed: {
    ...mapGetters(["cacheName", "operationName"]),
  },
  beforeRouteEnter(to, form, next) {
    next((vm) => {
      let CacheName = vm.addCacheName(true);
      vm.saveCacheName(CacheName);
    });
  },
  beforeRouteLeave(to, form, next) {
    if (to.name == "程序详情") {
      let contentScroll = document.querySelector(".content-collapse").scrollTop;
      this.$route.meta.scrollTop = contentScroll;
    } else {
      let CacheName = this.addCacheName(false);
      this.saveCacheName(CacheName);
    }
    next();
  },
  activated() {
    let $content = document.querySelector(".content-collapse");
    $content.scrollTop = this.$route.meta.scrollTop;
    this.requestInterval();
  },
  deactivated() {
    clearInterval(this.pageTimer);
    this.pageTimer = null;
  },
  mounted() {
    this.init();
  },
  destroyed() {
    clearInterval(this.pageTimer);
    this.pageTimer = null;
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.procedure-content {
  height: 100%;
  width: 100%;
}
.collapse-content {
  width: 98%;
  margin: 0 1% 1%;
  height: 100%;
}
.row-card {
  width: 80%;
  height: calc(100% - 20px);
}
.procedure-form {
  margin: 1rem;
  /* padding-right:1rem; */
}
.content-collapse {
  width: 100%;
  height: calc(99% - 5rem);
  overflow: auto;
  margin-bottom: 1%;
}
.collapse-row {
  height: 100%;
  width: 100%;
}
.collapse-round {
  position: absolute;
  top: 6px;
  display: inline-block;
  right: -8px;
}
.collapse-text {
  margin-left: 15px;
  position: relative;
  height: 40px;
}
.el-collapse-item {
  position: relative;
  margin-bottom: 15px;
  border: 1px solid #e4e7ed;
  /* box-shadow: rgba(0,0,0,.2) 0px 0px 5px; */
}
.cell-button {
  text-align: left;
  padding: 0 !important;
  text-decoration: underline;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
.collapse-table {
  margin: 20px;
  width: calc(100% - 40px);
}
.procedure-collapse {
  border: 0;
}
.title-icon {
  height: 16px;
  width: 20px;
  position: absolute;
  right: 14px;
  top: 11px;
  background-size: cover !important;
  background: url("../../assets/images/procedureManage/icon-procedure.png");
}
.el-collapse-item.is-active .title-icon {
  background-position: 0px -132px;
}
.el-collapse-item .title-icon {
  background-position: 0px -12px;
}
.btn-style:not(:last-child),
.el-dropdown:not(:last-child) {
  margin-right: 10px;
}
.hardButton {
  display: inline-block;
  height: 16px;
  width: 18px;
  background-size: cover !important;
  background: url("../../assets/images/procedureManage/icon-deploy.png");
  vertical-align: sub;
}
.hard-button {
  background-position: 0px -12px;
}
.btn-style:hover .hard-button {
  background-position: 0px -72px;
}
.distribute-button {
  background-position: 0px -133px;
}
.btn-style:hover .distribute-button {
  background-position: 0px -193px;
}
.update-button {
  background-position: 0px -254px;
}
.btn-style:hover .update-button {
  background-position: 0px -304px;
}
.button-edit {
  background-position: 0px -475px;
}
.text-button:hover .button-edit {
  background-position: 0px -525px;
}
.button-delete {
  background-position: 0px -365px;
}
.text-button:hover .button-delete {
  background-position: 0px -425px;
}
.button-clear {
  background-position: 0px -577px;
}
.text-button:hover .button-clear {
  background-position: 0px -629px;
}
.button-open {
  background-position: 0px -684px;
}
.text-button:hover .button-open {
  background-position: 0px -744px;
}
.button-close {
  background-position: 0px -801px;
}
.text-button:hover .button-close {
  background-position: 0px -861px;
}
.operateButton {
  display: inline-block;
  height: 16px;
  width: 16px;
  background-size: cover !important;
  background: url("../../assets/images/agentManage/icon-Agent.png");
  vertical-align: sub;
}
.button-search {
  background-position: 0px 129px;
}
.btn-style:hover .button-search {
  background-position: 0px 89px;
}
.button-add {
  background-position: 0px -102px;
}
.btn-style:hover .button-add {
  background-position: 0px -152px;
}
.status-tag {
  color: #fff;
  border: 0;
  width: 40px;
  height: 20px;
  line-height: 20px;
}
.procedure-table {
  border: 1px solid #e2e2e2;
  border-right: 0;
}
.deploy-Btn {
  text-align: right;
  line-height: 25px;
}
.text-button + .text-button {
  margin-left: 0px;
}
.procedure-input {
  width: 260px;
  margin-right: 10px;
}
.procedure-input /deep/ .el-input-group__prepend {
  width: 85px;
}
.procedure-select {
  width: 150px;
  margin-right: 10px;
}
.dropdownContent {
  height: 26px;
  padding: 0 10px;
  background: #fff;
  border: 1px solid #e2e2e2;
  color: #203e66;
  transition: 0.1s;
  white-space: nowrap;
  cursor: pointer;
  border-radius: 3px;
}
.el-icon-arrow-down,
.dropdownItem {
  color: #203e66;
}
.status-describe span {
  cursor: pointer;
  text-decoration: underline;
  margin: 0 5px;
}
.collapse-table button {
  padding: 0;
}
.procedure-table .el-table__body-wrapper {
  max-height: none;
}
</style>