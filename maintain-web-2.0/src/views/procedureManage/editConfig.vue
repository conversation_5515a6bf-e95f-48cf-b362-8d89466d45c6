<!-- 编辑配置文件 -->
<template>
  <div class="config-content" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      title="配置文件列表"
      :visible.sync="listFormVisible"
      width="1000px"
      class="dialog-border"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-row type="flex" class="row-bg dialog-table">
        <el-table
          :data="configList"
          max-height="400"
          :header-row-class-name="headerClass"
          v-loading="tableLoading"
        >
          <el-table-column prop="name" label="名称"></el-table-column>
          <el-table-column prop="time" label="修改时间"></el-table-column>
          <el-table-column prop="size" label="文件大小"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="text-button"
                v-if="btn.editConfig.value"
                @click="editBtn(scope.row)"
              >
                <div class="button-icon button-edit"></div>
                <span>编辑</span>
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-dialog
        append-to-body
        title="编辑配置文件"
        :visible.sync="editFormVisible"
        class="dialog-border"
        width="1000px"
        :close-on-click-modal="false"
        @close="editClose"
      >
        <el-row class="dialog-table">
          <el-row>
            <el-col :span="12">
              <p class="configTitle">{{ editForm.configName }}:</p>
            </el-col>
            <!-- <el-col :span="12" class="button-col">
                            <el-button type="text" class="text-button" @click="checkJson">
                                <div class="button-icon button-json"></div>
                                <span>JSON格式化</span>
                            </el-button>
                        </el-col> -->
          </el-row>
          <el-row class="edit-row">
            <el-col :span="24">
              <el-input
                type="textarea"
                v-model="editForm.configContent"
                rows="20"
              ></el-input>
            </el-col>
          </el-row>
        </el-row>
        <div slot="footer" class="dialog-footer footer-button">
          <button @click.prevent="editFormVisible = false" class="btn-style">
            关闭
          </button>
          <button @click.prevent="saveBtn" class="btn-style primary-btn">
            确定
          </button>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="listFormVisible = false" class="btn-style">
          <span>关闭</span>
        </button>
      </div>
    </el-dialog>

    <webSocketSend
      v-if="socketShow"
      @socketReturn="closeSocketDialog"
      :socketData="socketData"
    ></webSocketSend>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import webSocketSend from "@/components/webSocketSend.vue";
import { headerRowClassName } from "@/utils/tableStyle.js";

export default {
  name: "config-content",
  props: { configData: Object },
  components: { webSocketSend },
  data() {
    return {
      fullscreenLoading: false,
      tableLoading: false,
      editFormVisible: false,
      listFormVisible: true,
      socketShow: false,

      editForm: {
        configName: "",
        configContent: "",
      },
      btn: {
        editConfig: {
          name: "修改程序配置文件",
          value: false,
        },
      },
      returnData: {
        name: "editConfig",
        value: "",
      },

      configList: [],
    };
  },
  methods: {
    //请求数据函数
    getDataList() {
      let _this = this;
      let data = {
        ip: this.configData.serverIp,
        process: this.configData.name,
      };
      _this.tableLoading = true;
      _this.$http
        .get("software/configs?" + _this.qs.stringify(data))
        .then((res) => {
          _this.tableLoading = false;
          if (res.data.code == 0) {
            this.configList = res.data.data;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    saveBtn() {
      let _this = this;
      let Base64 = require("js-base64").Base64;
      let data = {
        ip: this.configData.serverIp,
        process: this.configData.name,
        config: this.editForm.configName,
        content: Base64.encode(this.editForm.configContent),
      };

      _this.fullscreenLoading = true;
      _this.$http
        .post("software/config", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("保存成功");
            _this.editFormVisible = false;
            _this.restartConfirm();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    editBtn(row) {
      let _this = this;
      let data = {
        ip: this.configData.serverIp,
        process: this.configData.name,
        config: row.name,
      };
      let Base64 = require("js-base64").Base64;

      _this.editForm.configName = row.name;
      _this.editFormVisible = true;
      _this.fullscreenLoading = true;

      _this.$http
        .get("software/config?" + _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            this.editForm.configContent = Base64.decode(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    restartBtn() {
      let _this = this;
      let data = {
        id: this.configData.id,
        pid: this.configData.pid,
        operate: 2,
      };

      if (data.pid) {
        let item = {};
        item.pid = data.pid;
        _this.fullscreenLoading = true;
        _this.$http
          .post(
            "software/operate_" + data.id + "_" + data.operate + ".json",
            _this.qs.stringify(item)
          )
          .then((res) => {
            _this.fullscreenLoading = false;
            res.data.code == 0
              ? _this.suFn("重启成功")
              : _this.waFn(res.data.msg);
          })
          .catch((error) => {
            _this.fullscreenLoading = false;
            _this.erFn();
          });
      } else {
        _this.waFn("程序并未开启");
      }
    },

    //功能函数
    restartConfirm() {
      let _this = this;

      _this
        .$confirm("是否需要重启当前程序？", "提示", {
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
        })
        .then(() => {
          this.socketShow = true;
          this.socketData = {
            operateType: 2,
            param: JSON.stringify({
              softwareId: this.configData.id,
              pid: this.configData.pid,
            }),
            service: "software",
            title: "重启程序过程",
          };
        })
        .catch(() => {
          this.getDataList();
        });
    },
    checkJson() {
      let configContent = this.editForm.configContent;

      try {
        let obj = JSON.parse(configContent);
        if (typeof obj == "object" && obj) {
          this.editForm.configContent = JSON.stringify(
            JSON.parse(this.editForm.configContent),
            null,
            4
          );
          this.suFn("json格式成功");
        } else {
          this.waFn("json格式错误");
        }
      } catch (e) {
        console.log(e);
        this.waFn("json格式错误");
      }
    },
    editClose() {
      this.editForm = {
        configName: "",
        configContent: "",
      };
    },
    resetForm() {
      this.configList = {};
      this.$emit("returnMain", this.returnData);
    },
    closeSocketDialog(val) {
      this.socketShow = false;
      this.socketData = {};
      if (val === "SUCCESS") {
        this.returnData.value = val;
        this.listFormVisible = false;
      }
    },
    headerClass() {
      return headerRowClassName();
    },
  },
  mounted() {
    getPermission("程序管理", this.btn);
    this.getDataList();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.config-content {
  height: 100%;
  overflow-y: auto;
}
.configTitle {
  color: #203e66;
  font-weight: bold;
  padding: 5px 0 0 5px;
}
.button-icon {
  display: inline-block;
  height: 18px;
  width: 20px;
  background-size: cover !important;
  vertical-align: sub;
  background: url("../../assets/images/procedureManage/icon-dialog.png");
}
.button-edit {
  background-position: 0px -51px;
}
.button-json {
  background-position: 0px -198px;
}
.button-col {
  text-align: right;
}
.text-button:hover .button-edit {
  background-position: 0px -151px;
}
.text-button:hover .button-json {
  background-position: 0px -242px;
}
.btn-style {
  padding: 0 20px;
}
.btn-style:first-child {
  margin-right: 10px;
}
</style>