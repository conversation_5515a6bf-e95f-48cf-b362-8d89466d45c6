<!-- 程序详情 -->
<template>
    <div class="procedrue-detail">
        <div class="detail-content" v-loading.fullscreen="fullscreenLoading">
            <el-row>
                <el-col :span="16">
                    <div class="top-title">程序详情</div>
                    <!-- <el-button v-if='btn.updateSoft.value'>更新程序</el-button> -->
                    <el-button class="proceBtn" type="text" v-if="btn.restartSoft.value"
                        @click="dialogBtn(btn.restartSoft.name)">
                        <div class="button-icon button-restart"></div>
                        <span>重启程序</span>
                    </el-button>
                    <el-button class="proceBtn" type="text" v-if="btn.updateLog.value"
                        @click="dialogBtn(btn.updateLog.name)">
                        <div class="button-icon button-update"></div>
                        <span>下载日志</span>
                    </el-button>
                    <el-button class="proceBtn" type="text" v-if="btn.editConfig.value"
                        @click="dialogBtn(btn.editConfig.name)">
                        <div class="button-icon button-edit"></div>
                        <span>编辑配置文件</span>
                    </el-button>
                    <!-- <el-button 
                        class="proceBtn" type="text" 
                        v-if='btn.editConfig.value' 
                        @click='logBtn'>
                        <div class="button-icon button-stack"></div>
                        <span>堆栈分析</span>
                    </el-button>-->
                </el-col>
                <el-col :span="8" class="return-col">
                    <button @click.prevent="returnWeb" class="btn-style primary-btn">
                        <div class="button-icon button-return"></div>
                        <span>返回</span>
                    </button>
                </el-col>
            </el-row>

            <el-row class="content-row">
                <el-row class="host-row">
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">程序信息</div>
                        </div>
                        <el-row class="hostForm-row">
                            <el-col :span="softwareInfo.status == 'GRAY' ? 24 : 18">
                                <el-form class="host-form">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-name"></div>
                                                    <span>程序名称</span>
                                                </div>
                                                <span data-name="name">{{ softwareInfo.name }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-ip"></div>
                                                    <span>程序IP</span>
                                                </div>
                                                <span data-name="host">{{ softwareInfo.host }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="heartMonitor">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-processPid"></div>
                                                    <span>进程号</span>
                                                </div>
                                                <template>
                                                    <span data-name="processPid">
                                                        {{
                                                            softwareInfo.processPid || "-"
                                                        }}
                                                    </span>
                                                </template>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="heartMonitor">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-cpuPercent"></div>
                                                    <span>当前cpu占用</span>
                                                </div>
                                                <span data-name="cpuPercent">
                                                    {{
                                                        softwareInfo.cpuPercent || "-"
                                                    }}
                                                </span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="heartMonitor">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-memoryPercent"></div>
                                                    <span>内存</span>
                                                </div>
                                                <span data-name="memoryPercent">
                                                    {{ softwareInfo.usedMemory || '-' }}
                                                </span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="heartMonitor">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-disk"></div>
                                                    <span>磁盘占用</span>
                                                </div>
                                                <span data-name="programSize">
                                                    {{
                                                        softwareInfo.programSize | myCurrency
                                                    }}
                                                </span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-time"></div>
                                                    <span>启动时间</span>
                                                </div>
                                                <span data-name="createTime">
                                                    {{
                                                        softwareInfo.createTime || "-"
                                                    }}
                                                </span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-runningTime"></div>
                                                    <span>{{ timeContent }}</span>
                                                </div>
                                                <span data-name="runningTime">
                                                    {{
                                                        softwareInfo.runningTime || "-"
                                                    }}
                                                </span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-restartTime"></div>
                                                    <span>最近重启时间</span>
                                                </div>
                                                <span data-name="restartTime">
                                                    {{
                                                        softwareInfo.restartTime || "-"
                                                    }}
                                                </span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-startType"></div>
                                                    <span>启动方式</span>
                                                </div>
                                                <span data-name="startType">
                                                    {{
                                                        softwareInfo.startType | currencyStartType()
                                                    }}
                                                </span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-count"></div>
                                                    <span>重启次数</span>
                                                </div>
                                                <span data-name="restartCount">
                                                    {{
                                                        softwareInfo.restartCount != "" &&
                                                        softwareInfo.restartCount != undefined
                                                        ? softwareInfo.restartCount
                                                        : "-"
                                                    }}
                                                </span>
                                                <el-button type="text"
                                                    v-if="softwareInfo.restartCount && softwareInfo.restartCount != 0 && btn.clearCount.value"
                                                    class="text-button" @click="clearCount">清空</el-button>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="heartMonitor">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-port"></div>
                                                    <span>端口占用情况</span>
                                                </div>
                                                <template>
                                                    <el-button type="text" v-if="portShow" class="text-button"
                                                        @click="portBtn()">详情</el-button>
                                                </template>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-heartMonitor"></div>
                                                    <span>心跳监控</span>
                                                </div>
                                                <el-switch disabled v-model="softwareInfo.heartMonitor"
                                                    active-color="#25ce88" inactive-color="#cfcfcf"
                                                    @click.native="switchClick(softwareInfo)"></el-switch>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-note"></div>
                                                    <span>备注</span>
                                                </div>
                                                <el-input v-if="noteForm.isSet" v-model="noteForm.value" :autosize="true"
                                                    :show-word-limit="true" maxlength="255" type="textarea"
                                                    class="noteInput"></el-input>
                                                <el-popover class="popoverSpan note-popover" trigger="hover"
                                                    placement="top-start" v-else>
                                                    <p class="popoverContent">{{ softwareInfo.note || "-" }}</p>
                                                    <div slot="reference" class="name-wrapper">
                                                        <span>{{ softwareInfo.note || "-" }}</span>
                                                    </div>
                                                </el-popover>
                                                <el-button type="text" class="text-button note-button"
                                                    @click="noteSure('noteForm', noteForm.buttonText)">{{
                                                        noteForm.buttonText }}</el-button>
                                                <el-button type="text" class="text-button note-button" v-if="noteForm.isSet"
                                                    @click="noteQuit('noteForm')">取消</el-button>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="heartMonitor">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-startParam"></div>
                                                    <span>启动参数</span>
                                                </div>
                                                <template>
                                                    <el-popover trigger="hover" placement="top-start" width="500"
                                                        v-if="softwareInfo.startParam">
                                                        <p class="popoverContent">{{ softwareInfo.startParam }}</p>
                                                        <p slot="reference" class="startParam-popover">{{
                                                            softwareInfo.startParam || "-" }}</p>
                                                    </el-popover>
                                                    <span v-else>-</span>
                                                </template>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="!softwareInfo.config">
                                            <el-form-item>
                                                <div slot="label">
                                                    <div class="button-icon button-heapSize"></div>
                                                    <span>初始堆-最大堆(MB)</span>
                                                </div>

                                                <div v-if="heapForm.isSet">
                                                    <el-input v-model="heapForm.minHeapSize"
                                                        @keyup.enter.native="noteSure('heapForm', heapForm.buttonText)"
                                                        class="noteInput"></el-input>
                                                    <span>-</span>
                                                    <el-input v-model="heapForm.maxHeapSize"
                                                        @keyup.enter.native="noteSure('heapForm', heapForm.buttonText)"
                                                        class="noteInput"></el-input>
                                                </div>
                                                <span v-else>{{ softwareInfo.minHeapSize && softwareInfo.maxHeapSize ?
                                                    (softwareInfo.minHeapSize + '-' + softwareInfo.maxHeapSize) : "未设置"
                                                }}</span>
                                                <el-button type="text" class="text-button note-button"
                                                    @click="noteSure('heapForm', heapForm.buttonText)">{{
                                                        heapForm.buttonText }}</el-button>
                                                <el-button type="text" class="text-button note-button" v-if="heapForm.isSet"
                                                    @click="noteQuit('heapForm')">取消</el-button>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </el-col>
                            <el-col :span="6" class="status-content" v-if="softwareInfo.status != 'GRAY'">
                                <div :class="{ 'proce-status': proceStatus }" :style="{
                                    backgroundPosition:
                                        '0px ' + formatStatus(softwareInfo.status),
                                }"></div>
                            </el-col>
                        </el-row>
                    </el-row>
                </el-row>

                <el-row class="network-row" v-if="heartMonitor">
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">资源图表</div>
                        </div>
                        <el-row class="form-time">
                            <el-radio-group v-model.number="timeChart" size="mini" @change="timeChange">
                                <el-radio-button class="proce-radio" v-for="item in timeOptions" :key="item.value"
                                    :label="item.value">{{ item.label }}</el-radio-button>
                            </el-radio-group>
                        </el-row>
                        <el-row class="chart-row">
                            <div class="noData-div" v-show="chartShow">暂无数据</div>
                            <div class="statistics" v-for="(index, item) in chartGroup" :key="item">
                                <el-row class="chart-content">
                                    <div class="chartWrap"></div>
                                </el-row>
                            </div>
                        </el-row>
                    </el-row>
                </el-row>
                <!-- <el-row class="procedure-row" v-if="reportShow">
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">业务汇报</div>
                        </div>
                        <el-row class="table-row">
                            <el-row class="health-row">
                                <el-table :data="healthList" border height="100%" :span-method="arraySpanMethod"
                                    :row-class-name="bodyClass" :header-cell-style="tableHeader"
                                    :header-row-class-name="headerClass" :cell-class-name="tableRowClassName"
                                    @cell-mouse-leave="cellMouseLeave" @cell-mouse-enter="cellMouseEnter">
                                    <el-table-column prop="startTime" label="监控开始时间" width="150"></el-table-column>
                                    <el-table-column prop="endTime" label="监控结束时间" width="150"></el-table-column>
                                    <el-table-column prop="module" label="模块名" width="140"></el-table-column>
                                    <el-table-column prop="content" label="业务内容" min-width="280"
                                        class-name="overflowColumn">
                                        <template slot-scope="scope">
                                            <el-popover class="popoverSpan" trigger="hover" placement="top-start"
                                                v-if="formatJson(scope.row.content)">
                                                <p class="popoverContent" v-html="formatJson(scope.row.content)"></p>
                                                <div slot="reference" class="name-wrapper">
                                                    <span>{{ formatJson(scope.row.content) }}</span>
                                                </div>
                                            </el-popover>
                                            <span v-else>-</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="description" label="说明" min-width="300"
                                        class-name="overflowColumn">
                                        <template slot-scope="scope">
                                            <el-popover class="popoverSpan" trigger="hover" placement="top-start"
                                                v-if="formatJson(scope.row.description)">
                                                <p class="popoverContent" v-html="formatJson(scope.row.description)"></p>
                                                <div slot="reference" class="name-wrapper">
                                                    <span>{{ formatJson(scope.row.description) }}</span>
                                                </div>
                                            </el-popover>
                                            <span v-else>-</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-row>
                            <el-row class="pagination-row">
                                <el-col :span="24" class="table-business">
                                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                        :current-page="form.pn" :page-sizes="[20, 50, 100]" :page-size="form.ps"
                                        layout="total,sizes,prev,pager,next,jumper" :total="form.total"></el-pagination>
                                </el-col>
                            </el-row>
                        </el-row>
                    </el-row>
                </el-row> -->

                <el-row class="procedure-row">
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">历史重启记录</div>
                        </div>
                        <el-row class="table-row">
                            <el-row class="health-row">
                                <el-table :data="restartList" border height="100%" :row-class-name="bodyClass"
                                    :header-cell-style="tableHeader" :header-row-class-name="headerClass"
                                    :cell-class-name="tableRowClassName">
                                    <el-table-column prop="status" label="状态">
                                        <template slot-scope="scope">
                                            <el-popover trigger="hover" placement="top-start"
                                                v-if="scope.row.content != '良好'">
                                                <p class="popoverContent">{{ scope.row.description }}</p>
                                                <el-tag slot="reference" class="status-tag" size="small"
                                                    :color="scope.row.status">{{ scope.row.content }}</el-tag>
                                            </el-popover>
                                            <div v-else>
                                                <el-tag size="small" class="status-tag" :color="scope.row.status">{{
                                                    scope.row.content }}</el-tag>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="createTime" label="时间"></el-table-column>
                                    <el-table-column prop="description" label="原因"></el-table-column>
                                </el-table>
                            </el-row>
                            <el-row class="pagination-row">
                                <el-col :span="24" class="table-business">
                                    <el-pagination @size-change="restartSizeChange" @current-change="restartCurrentChange"
                                        :current-page="restartForm.pn" :page-sizes="[20, 50, 100]"
                                        :page-size="restartForm.ps" layout="total,sizes,prev,pager,next,jumper"
                                        :total="restartForm.total"></el-pagination>
                                </el-col>
                            </el-row>
                        </el-row>
                    </el-row>
                </el-row>
            </el-row>

            <el-dialog title="端口详情" :visible.sync="portFormVisible" :close-on-click-modal="false" class="dialog-border"
                width="500px">
                <div class="port-body">
                    <p v-for="item in portInfo" :key="item.value">{{ item }}</p>
                </div>

                <div slot="footer" class="dialog-footer footer-button">
                    <button @click.prevent="portFormVisible = false" class="btn-style port-btn">
                        <span>关闭</span>
                    </button>
                </div>
            </el-dialog>

            <editConfig v-if="configShow" @returnMain="closeDialog" :configData="configData"></editConfig>
            <updateLog v-if="logShow" @returnMain="closeDialog" :logData="logData"></updateLog>
            <webSocketSend v-if="socketShow" @socketReturn="closeSocketDialog" :socketData="socketData"></webSocketSend>
        </div>
    </div>
</template>

<script>
import editConfig from "./editConfig.vue";
import updateLog from "./updateLog.vue";
import webSocketSend from "@/components/webSocketSend.vue";
import { parseTime } from "@/utils/index.js";
import getPermission from "@/utils/permissions.js";
import {
    bodyRowClassName,
    tableHeaderStyle,
    headerRowClassName
} from "@/utils/tableStyle.js";
import { isJson } from "@/utils/validate.js";
import { converToFlow } from './formatflow'
export default {
    name: "procedrue-detail",
    components: { editConfig, updateLog, webSocketSend },
    data() {
        return {
            timeChart: 1,
            pageTimer: null,
            rowIndex: "-1",
            timeContent: "运行时长",
            flowCpu: "",
            flowMemory: "",
            flowHard: "",

            fullscreenLoading: false,
            portFormVisible: false,
            configShow: false,
            logShow: false,
            socketShow: false,
            portShow: false,
            proceStatus: false,
            reportShow: false,
            heartMonitor: false,
            chartShow: true,

            twoRow: {},
            softwareInfo: {},
            chartForm: {
                startTime: "",
                endTime: ""
            },
            btn: {
                updateSoft: {
                    name: "添加/更新程序",
                    value: false
                },
                restartSoft: {
                    name: "重启程序",
                    value: false
                },
                updateLog: {
                    name: "程序日志列表",
                    value: false
                },
                editConfig: {
                    name: "读取程序配置文件",
                    value: false
                },
                clearCount: {
                    name: "重启次数置零",
                    value: false
                },
                heartMonitor: {
                    name: "心跳监控",
                    value: false
                }
            },
            form: {
                pn: 1, //当前页
                ps: 20, //当前每页条数
                total: 0 //总条数
            },
            restartForm: {
                pn: 1,
                ps: 20,
                total: 0
            },
            noteForm: {
                isSet: false,
                buttonText: "修改",
                value: ""
            },
            heapForm: {
                isSet: false,
                buttonText: "修改",
                maxHeapSize: null,
                minHeapSize: null
            },

            chartGroup: ["CPU使用率", "内存使用率", "硬盘使用率"],
            timeOptions: [
                {
                    label: "1小时前",
                    value: 1
                },
                {
                    label: "2小时前",
                    value: 2
                },
                {
                    label: "6小时前",
                    value: 6
                },
                {
                    label: "12小时前",
                    value: 12
                },
                {
                    label: "1天前",
                    value: 24
                },
                {
                    label: "7天前",
                    value: 168
                }
            ],
            healthList: [],
            logList: [],
            restartList: [],
            portInfo: [],
            OrderArr: [],
            hoverArr: []
        };
    },
    methods: {
        init() {
            this.getDataList(false, this.twoRow, this.form);
            this.getRestartList(this.twoRow, this.restartForm);
            getPermission("程序管理", this.btn);
            this.getStatus();
            this.requestInterval();
        },

        //请求数据函数
        getDataList(IntervalLoading, data, pageForm) {
            let _this = this;

            _this.fullscreenLoading = IntervalLoading ? false : true;
            pageForm.startTime = this.chartForm.startTime;
            pageForm.endTime = this.chartForm.endTime;
            _this.chartShow = true;

            _this.$http
                .post(
                    "software/detail_" + data.id + ".json",
                    _this.qs.stringify(pageForm)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        _this.formatData(res.data.data);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getRestartList(data, restartForm) {
            let _this = this;

            _this.$http
                .post(
                    "software/restart_history_" + data.id + ".json",
                    _this.qs.stringify(restartForm)
                )
                .then(res => {
                    if (res.data.code == 0) {
                        res.data.data.restartHistory &&
                            _this.formatRestart(res.data.data.restartHistory);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.erFn();
                    console.log(error);
                });
        },
        getEchart(data, chartForm) {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.chartShow = true;

            _this.$http
                .post(
                    "software/history_resource_" + data.id + ".json",
                    _this.qs.stringify(chartForm)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (JSON.stringify(res.data.data) != "{}") {
                            _this.drawLine(res.data.data.historyResource);
                            _this.chartShow = false;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        clearCount() {
            let _this = this,
                data = {};

            _this.fullscreenLoading = true;
            data = {
                id: _this.softwareInfo.id,
                host: _this.softwareInfo.host,
                name: _this.softwareInfo.name
            };

            _this.$http
                .post("software/clear_restart_count", _this.qs.stringify(data))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        _this.suFn("清除成功");
                        _this.getDataList(false, this.twoRow, this.form);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        saveNote(url, data) {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .post(url, _this.qs.stringify(data))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        _this.suFn("修改成功");
                        _this.getDataList(false, this.twoRow, this.form);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        requestInterval() {
            if (this.pageTimer) {
                clearInterval(this.pageTimer);
            } else {
                this.pageTimer = setInterval(() => {
                    this.getDataList(true, this.twoRow, this.form);
                }, 60000);
            }
        },
        operaHeart(data) {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .post("software/heart_monitor", _this.qs.stringify(data))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        _this.softwareInfo.heartMonitor = res.data.data;
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        initData() {
            this.twoRow = this.$route.query;
            this.chartForm.endTime = parseTime(
                Date.parse(new Date()),
                "{y}-{m}-{d} {h}:{i}"
            );
            this.chartForm.startTime = parseTime(
                Date.parse(new Date()) - 60 * 60 * 1000,
                "{y}-{m}-{d} {h}:{i}"
            );
        },
        formatJson(value) {
            let content = "";

            let obj = {
                initData(value) {
                    let content = "",
                        num = 0;

                    $.each(value, (index, item) => {
                        num++;
                        if (
                            Object.prototype.toString.call(item) ==
                            "[object Object]" ||
                            Object.prototype.toString.call(item) ==
                            "[object Array]"
                        ) {
                            item = JSON.stringify(item);
                        }
                        if (num % 3 == 0) {
                            num == Object.keys(value).length
                                ? (content += index + "为" + item)
                                : (content += index + "为" + item + "<br/>");
                        } else {
                            num == Object.keys(value).length
                                ? (content += index + "为" + item)
                                : (content += index + "为" + item + "，");
                        }
                    });
                    return content;
                }
            };
            if (value && Object.keys(value).length != 0) {
                isJson(value) && (value = obj.initData(JSON.parse(value)));
                return value;
            } else {
                return "";
            }
        },
        formatData(data) {
            let _this = this;
            _this.softwareInfo = data.softwareInfo;
            _this.heartMonitor = data.softwareInfo.heartMonitor;
            _this.portShow = true;
            _this.portInfo = data.portInfo || [];
            _this.proceStatus = _this.softwareInfo.status ? true : false;
            _this.softwareInfo.runningTime > 0
                ? (_this.timeContent = "运行时长")
                : (_this.timeContent = "停止时长");

            _this.softwareInfo.runningTime = Math.abs(
                _this.softwareInfo.runningTime
            );
            _this.softwareInfo.runningTime > 86400000
                ? (_this.softwareInfo.runningTime =
                    parseInt(_this.softwareInfo.runningTime / 86400000) +
                    "天")
                : (_this.softwareInfo.runningTime =
                    (_this.softwareInfo.runningTime / 3600000).toFixed(2) +
                    "小时");

            if (_this.timeChart == 1) {
                if (data.historyResource) {
                    _this.chartShow = false;
                    _this.drawLine(data.historyResource);
                }
            } else {
                _this.timeChange(_this.timeChart);
            }

            if (data.businessMonitor) {
                _this.reportShow = true;
                _this.formatHeath(data.businessMonitor.list);
                _this.form.total = data.businessMonitor.total;
            }
            this.softwareInfo.alarmTypeMap && this.initAlarmContent();
        },
        initAlarmContent() {
            let alarmDom = "";

            this.$nextTick(() => {
                alarmDom = $(".host-form .el-form-item__content span");
                $.each(this.softwareInfo.alarmTypeMap, (index, item) => {
                    $.each(alarmDom, (i, obj) => {
                        switch (item) {
                            case "GREEN":
                                item = "#25ce88";
                                break;
                            case "YELLOW":
                                item = "#fb843b";
                                break;
                            case "RED":
                                item = "#f93846";
                                break;
                        }
                        if (index == $(obj).attr("data-name")) {
                            $(obj).css("color", item);
                        }
                    });
                });
            });
        },
        formatRestart(data) {
            data.list.forEach(item => {
                switch (item.status) {
                    case 0:
                        item.status = "#25ce88";
                        item.content = "良好";
                        break;
                    case 1:
                        item.status = "#fb843b";
                        item.content = "警告";
                        break;
                    case 2:
                        item.status = "#f93846";
                        item.content = "错误";
                        break;
                }
            });
            this.restartList = data.list;
            this.restartForm.total = data.total;
        },
        formatStatus(val) {
            switch (val) {
                case "GREEN":
                    val = "-248px";
                    break;
                case "YELLOW":
                    val = "-127px";
                    break;
                case "RED":
                    val = "-14px";
                    break;
            }
            return val;
        },
        formatSpeed(row, column, val) {
            val &&
                val.indexOf("M") == -1 &&
                (val = (Number(val) / 1024 / 1024).toFixed(2) + "Mb/s");
            return val;
        },
        drawLine(data) {
            this.$nextTick(() => {
                let chartDom = document.getElementsByClassName("chartWrap");
                let _this = this;
                $.each(data, (index, item) => {
                    if (item.x && item.x.length != 0) {
                        let options = {};
                        options = {
                            title: {
                                text: item.content,
                                textStyle: {
                                    fontSize: 14,
                                    color: "#1e85e6"
                                }
                            },
                            tooltip: {
                                trigger: "axis",
                                formatter: obj => {
                                    let content = "",
                                        val = "";
                                    switch (index) {
                                        case "cpuResource":
                                            val = obj[0].value + "%";
                                            break;
                                        case "usedMemoryResource":
                                            val = obj[0].value + "G";
                                            break;
                                        case "diskResource":
                                            val = converToFlow(obj[0].value);
                                            break;
                                    }
                                    content =
                                        obj[0].name +
                                        "<br/>" +
                                        obj[0].marker +
                                        obj[0].seriesName +
                                        "：" +
                                        val;
                                    return content;
                                },
                                axisPointer: {
                                    lineStyle: { type: "dashed" }
                                }
                            },
                            calculable: true,
                            grid: {
                                top: "16%",
                                bottom: "20%",
                                left: "20%",
                                right: "3%"
                            },
                            xAxis: {
                                type: "category",
                                boundaryGap: ["10%", "10%"],
                                axisLine: {
                                    lineStyle: { color: "#e6e9f1" }
                                },
                                axisLabel: {
                                    color: "#203e66",
                                    length: 7,
                                    formatter: val => {
                                        let str = val.split(" ");
                                        return str.join("\n");
                                    }
                                },
                                data: item.x
                            },
                            yAxis: [
                                {
                                    type: "value",
                                    axisTick: { show: false }, //坐标轴刻度
                                    axisLine: {
                                        lineStyle: { color: "#e6e9f1" }
                                    },
                                    axisLabel: {
                                        show: true,
                                        textStyle: {
                                            color: "#203e66"
                                        },
                                        formatter: val => {
                                            switch (index) {
                                                case "cpuResource":
                                                    val = val + "%";
                                                    break;
                                                case "usedMemoryResource":
                                                    val = val + "G";
                                                    break;
                                                case "diskResource":
                                                    val = converToFlow(val);
                                                    break;
                                            }
                                            return val;
                                        }
                                    },
                                    splitLine: {
                                        lineStyle: { color: "#e6e9f1" }
                                    }
                                }
                            ],
                            series: [
                                {
                                    name: item.content,
                                    type: "line",
                                    smooth: true,
                                    itemStyle: {
                                        normal: {
                                            color: "rgb(99,223,246)"
                                        },
                                        emphasis: {
                                            borderColor: "red"
                                        }
                                    },
                                    data: item.y
                                }
                            ]
                        };

                        switch (index) {
                            case "cpuResource":
                                this.flowCpu = this.$echarts.init(chartDom[0]);
                                this.flowCpu.setOption(options);
                                break;
                            case "usedMemoryResource":
                                this.flowMemory = this.$echarts.init(
                                    chartDom[1]
                                );
                                this.flowMemory.setOption(options);
                                break;
                            case 'diskResource':
                                this.flowHard = this.$echarts.init(chartDom[2]);
                                this.flowHard.setOption(options);
                                break;
                        }
                    } else {
                        this.chartShow = true;
                    }
                });
                window.onresize = function () {
                    _this.flowCpu && _this.flowCpu.resize();
                    _this.flowMemory && _this.flowMemory.resize();
                    //   _this.flowHard.resize()
                };
            });
        },
        formatHeath(data) {
            let list = [];
            $.each(data, (index, item) => {
                for (let i in item.data) {
                    item.data[i].startTime = item.startTime;
                    item.data[i].endTime = item.endTime;
                    list.push(item.data[i]);
                }
            });
            this.healthList = list;
            this.getOrderNumber(this.healthList);
        },

        //功能函数
        timeChange(val) {
            window.onresize = "";
            let endTime = Date.parse(new Date());
            let startTime = endTime - val * 60 * 60 * 1000;

            this.chartForm.endTime = parseTime(endTime, "{y}-{m}-{d} {h}:{i}");
            this.chartForm.startTime = parseTime(
                startTime,
                "{y}-{m}-{d} {h}:{i}"
            );
            this.flowCpu && this.flowCpu.dispose();
            this.flowMemory && this.flowMemory.dispose();
            this.getEchart(this.twoRow, this.chartForm);
        },
        noteSure(formName, val) {
            let data = {},
                url = "",
                value = "";
            let reg = /(-1)|^[1-9]\d*$/;

            switch (formName) {
                case "noteForm":
                    data = {
                        serverId: this.softwareInfo.serverId,
                        name: this.softwareInfo.name,
                        note: this[formName].value
                    };
                    url = "software/note";
                    val == "修改" &&
                        (this[formName].value = this.softwareInfo.note);
                    break;
                case "heapForm":
                    data = {
                        id: this.softwareInfo.id,
                        ip: this.softwareInfo.serverIp,
                        name: this.softwareInfo.name,
                        minHeapSize: Number(this[formName].minHeapSize),
                        maxHeapSize: Number(this[formName].maxHeapSize)
                    };
                    url = "software/heap";
                    val == "修改" &&
                        ((this[
                            formName
                        ].minHeapSize = this.softwareInfo.minHeapSize),
                            (this[
                                formName
                            ].maxHeapSize = this.softwareInfo.maxHeapSize));
                    break;
            }

            switch (val) {
                case "修改":
                    this[formName].isSet = true;
                    this[formName].buttonText = "保存";
                    break;
                case "保存":
                    if (formName == "heapForm") {
                        if (
                            !this[formName].minHeapSize ||
                            !this[formName].maxHeapSize ||
                            (!reg.test(this[formName].minHeapSize) ||
                                !reg.test(this[formName].maxHeapSize))
                        ) {
                            this.waFn("请输入堆大小，且必须为正整数");
                            break;
                        }
                        if (
                            this[formName].maxHeapSize <
                            this[formName].minHeapSize
                        ) {
                            this.waFn("最大堆值需大于等于初始堆值");
                            break;
                        }
                    }
                    this[formName].isSet = false;
                    this[formName].buttonText = "修改";
                    this.saveNote(url, data);
                    break;
            }
        },
        noteQuit(formName) {
            this[formName].isSet = false;
            this[formName].buttonText = "修改";
        },
        handleSizeChange(val) {
            this.form.ps = val;
            this.getDataList(false, this.twoRow, this.form);
        },
        handleCurrentChange(val) {
            this.form.pn = val;
            this.getDataList(false, this.twoRow, this.form);
        },
        restartSizeChange(val) {
            this.restartForm.ps = val;
            this.getRestartList(this.twoRow, this.restartForm);
        },
        restartCurrentChange(val) {
            this.restartForm.pn = val;
            this.getRestartList(this.twoRow, this.restartForm);
        },
        dialogBtn(val) {
            switch (val) {
                case "读取程序配置文件":
                    this.configShow = true;
                    this.configData = this.twoRow;
                    break;
                case "程序日志列表":
                    this.logShow = true;
                    this.logData = this.twoRow;
                    break;
                case "重启程序":
                    this.$confirm("您确定要重启该程序吗？", "提示", {
                        closeOnClickModal: false,
                        cancelButtonText: "关闭",
                        confirmButtonText: "确定",
                        confirmButtonClass: "confirm-success"
                    })
                        .then(() => {
                            this.socketShow = true;
                            this.socketData = {
                                operateType: 2,
                                param: JSON.stringify({
                                    softwareId: this.twoRow.id,
                                    pid: this.twoRow.pid
                                }),
                                service: "software",
                                title: "重启程序过程"
                            };
                        })
                        .catch(() => { });
                    break;
            }
        },
        restartBtn() {
            let _this = this;
            let data = {
                id: this.twoRow.id,
                pid: this.twoRow.pid,
                operate: 2
            };

            if (data.pid) {
                let item = {};
                item.pid = data.pid;
                _this.fullscreenLoading = true;

                _this.$http
                    .post(
                        "software/operate_" +
                        data.id +
                        "_" +
                        data.operate +
                        ".json",
                        _this.qs.stringify(item)
                    )
                    .then(res => {
                        _this.fullscreenLoading = false;
                        res.data.code == 0
                            ? _this.suFn("重启成功")
                            : _this.waFn(res.data.msg);
                    })
                    .catch(error => {
                        _this.fullscreenLoading = false;
                        _this.erFn();
                    });
            } else {
                _this.waFn("程序并未开启");
            }
        },
        portBtn() {
            this.portInfo.length != 0
                ? (this.portFormVisible = true)
                : this.waFn("暂无详情");
        },
        logBtn() {
            this.$router.push({
                path: "logAnalyze",
                query: {
                    id: this.twoRow.id
                }
            });
        },
        returnWeb() {
            this.$router.go(-1);
        },
        closeDialog(row) {
            switch (row.name) {
                case "editConfig":
                    this.configShow = false;
                    this.configData = {};
                    row.value === "SUCCESS" &&
                        this.getDataList(false, this.twoRow, this.form);
                    break;
                case "updateLog":
                    this.logShow = false;
                    this.logData = {};
                    break;
            }
        },
        closeSocketDialog(val) {
            this.socketShow = false;
            this.socketData = {};
            val === "SUCCESS" &&
                this.getDataList(false, this.twoRow, this.form);
        },
        switchClick(row) {
            let data = {};

            if (this.btn.heartMonitor.value != true) {
                this.waFn("暂无权限");
                return;
            }
            switch (row.heartMonitor) {
                case true:
                    data = {
                        id: row.id,
                        heartMonitor: "false"
                    };
                    break;
                case false:
                    data = {
                        id: row.id,
                        heartMonitor: "true"
                    };
                    break;
            }
            this.operaHeart(data);
        },
        getOrderNumber(data) {
            let OrderObj = {};

            data.forEach((item, index) => {
                item.rowIndex = index;
                if (OrderObj[item.startTime]) {
                    if (OrderObj[item.startTime][item.endTime]) {
                        OrderObj[item.startTime][item.endTime].push(
                            item.rowIndex
                        );
                    } else {
                        OrderObj[item.startTime][item.endTime] = [];
                        OrderObj[item.startTime][item.endTime].push(
                            item.rowIndex
                        );
                    }
                } else {
                    OrderObj[item.startTime] = {};
                    OrderObj[item.startTime][item.endTime] = [];
                    OrderObj[item.startTime][item.endTime].push(item.rowIndex);
                }
            });
            for (let i in OrderObj) {
                for (let j in OrderObj[i]) {
                    if (OrderObj[i][j].length > 1) {
                        this.OrderArr.push(OrderObj[i][j]);
                    }
                }
            }
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 0 || columnIndex === 1) {
                if (this.OrderArr.length != 0) {
                    for (let i = 0; i < this.OrderArr.length; i++) {
                        let element = this.OrderArr[i];
                        for (let j = 0; j < element.length; j++) {
                            let item = element[j];
                            if (rowIndex == item) {
                                if (j == 0) {
                                    return {
                                        rowspan: element.length,
                                        colspan: 1
                                    };
                                } else if (j != 0) {
                                    return {
                                        rowspan: 0,
                                        colspan: 0
                                    };
                                }
                            }
                        }
                    }
                }
            }
        },
        tableRowClassName({ row, rowIndex }) {
            let arr = this.hoverArr;
            if (arr && arr.length != 0) {
                for (let i = 0; i < arr.length; i++) {
                    if (row.rowIndex == arr[i]) {
                        return "hovered-row";
                    }
                }
            }
        },
        cellMouseEnter(row, column, cell, event) {
            this.rowIndex = row.rowIndex;
            this.hoverArr = [];
            this.OrderArr.forEach(item => {
                item.indexOf(this.rowIndex) >= 0 && (this.hoverArr = item);
            });
        },
        cellMouseLeave(row, column, cell, event) {
            this.rowIndex = "-1";
            this.hoverArr = [];
        },
        bodyClass() {
            return bodyRowClassName();
        },
        headerClass() {
            return headerRowClassName();
        },
        tableHeader() {
            return tableHeaderStyle();
        }
    },
    mounted() {
        this.initData();
        this.init();
    },
    destroyed() {
        window.onresize = "";
        clearInterval(this.pageTimer);
    },
    filters: {
        myCurrency(value) {
            let CurrencyValue = "";

            if (value) {
                CurrencyValue =
                    value <= 1073741824
                        ? (value / 1048576).toFixed(2) + "MB"
                        : (value / 1073741824).toFixed(2) + "GB";
            }
            value || (CurrencyValue = "-");
            return CurrencyValue;
        },
        currencyStartType(value) {
            switch (value) {
                case "AUTO":
                    value = "自动";
                    break;
                case "MANUAL":
                    value = "手动";
                    break;
                default:
                    value = "-";
                    break;
            }
            return value;
        }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.procedrue-detail {
    height: 100%;
    width: 100%;
    /* overflow-y: auto; */
}

.detail-content {
    width: 98%;
    margin: 0 1% 1%;
    height: 100%;
}

.top-title {
    text-align: left;
    text-indent: 15px;
    color: #1e85e6;
    font-size: 15px;
    font-weight: bold;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    margin-top: 10px;
}

.proceBtn {
    color: #203e66;
    position: relative;
    margin: 0;
}

.button-icon {
    display: inline-block;
    height: 20px;
    width: 25px;
    background-size: cover !important;
    vertical-align: sub;
    background: url("../../assets/images/procedureManage/icon-detail.png");
}

.proceBtn:hover {
    color: #409eff;
}

.proceBtn:nth-child(2) {
    margin-left: 20px;
}

.proceBtn:not(:nth-child(2)):after {
    content: "";
    display: inline-block;
    width: 1px;
    height: 13px;
    position: absolute;
    left: -2.5px;
    top: 6px;
    background-color: #c1c5c5;
}

.button-edit {
    background-position: 0px 2px;
}

.proceBtn:hover .button-edit {
    background-position: 0px -48px;
}

.button-restart {
    background-position: 0px -99px;
}

.proceBtn:hover .button-restart {
    background-position: 0px -149px;
}

.button-update {
    background-position: 0px -198px;
}

.proceBtn:hover .button-update {
    background-position: 0px -248px;
}

.button-stack {
    background-position: 0px 72px;
}

.proceBtn:hover .button-stack {
    background-position: 0px 29px;
}

.button-name {
    background-position: 0px -299px;
}

.button-processPid {
    background-position: 0px -349px;
}

.button-startParam {
    background-position: 0px -399px;
}

.button-cpuPercent {
    background-position: 0px -450px;
}

.button-memoryPercent {
    background-position: 0px -500px;
}

.button-port {
    background-position: 0px -560px;
}

.button-ip {
    background-position: 0px -662px;
}

.button-time {
    background-position: 0px -716px;
}

.button-disk {
    background-position: 0px -760px;
}

.button-runningTime {
    background-position: 0px -893px;
}

.button-count {
    background-position: 0px -940px;
    height: 22px;
}

.button-restartTime {
    background-position: 0px -989px;
}

.button-startType {
    background-position: 0px -1032px;
}

.button-heartMonitor {
    background-position: 0px -1132px;
}

.button-note {
    background-position: 0px -1075px;
}

.button-heapSize {
    background-position: 0px -1185px;
}

.status-content {
    height: 153px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.proce-status {
    display: inline-block;
    height: 85px;
    width: 142px;
    background-size: cover !important;
    background: url("../../assets/images/index/icon-totalStatus.png");
}

.content-row {
    width: 100%;
    height: calc(100% - 60px);
    background-color: #ffffff;
    border: 1px solid #e2e2e2;
    -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
    overflow: auto;
}

.chart-row {
    width: calc(100% - 80px);
    height: 300px;
    margin: 20px 40px;
    display: flex;

}

.statistics {
    flex: 1;
    height: 100%;
    margin-left: 10px;
}

.statistics:nth-of-type(1) {
    margin-left: 0;
}

.host-row {
    margin: 10px 10px 0 10px;
    min-height: 130px;
}

.host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}

.host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}

.host-content {
    padding: 0px 15px;
}

.hostForm-row {
    padding: 30px 0;
}

.host-form {
    margin: 5px 0 0 50px;
}

.el-form-item {
    margin-bottom: 5px !important;
}

.network-row {
    margin: 0 10px;
    min-height: 400px;
}

.procedure-row {
    margin: 0 10px;
    min-height: 400px;
}

.table-row {
    padding: 40px 40px 0;
    min-height: 400px;
}

.health-row {
    height: 300px;
}

.table-business {
    display: flex;
    align-items: center;
    justify-content: center;
}

.chartWrap {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-row {
    height: 30px;
    margin: 12px;
}

.el-dialog__body {
    max-height: 400px;
}

.port-body {
    max-height: 400px;
    overflow-y: auto;
    color: #203e66;
}

.port-body p {
    margin: 5px 0;
}

.chart-content {
    height: 100%;
}

.form-time {
    margin: 10px 0 0 10px;
    height: 40px;
    text-align: right;
}

.startParam-popover {
    overflow: hidden;
    text-overflow: ellipsis;
}

.return-col {
    text-align: right;
    padding-right: 20px;
    height: 40px;
    line-height: 40px;
    margin-top: 10px;
}

.button-return {
    background-position: 0 -614px;
    height: 16px;
}

.port-btn {
    padding: 0 20px;
}

.primary-btn {
    padding-left: 5px;
}

.status-tag {
    color: #fff;
    border: 0;
    width: 40px;
    height: 20px;
    line-height: 20px;
}

.note-button:nth-of-type(2) {
    margin-left: 0;
    padding: 0 !important;
}

.noteInput {
    max-width: 45%;
}
</style>