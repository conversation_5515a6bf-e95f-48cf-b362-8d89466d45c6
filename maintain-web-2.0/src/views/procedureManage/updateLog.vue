<!-- 下载日志文件 -->
<template>
    <div class="config-content">
        <el-dialog 
            title="下载日志" 
            :visible.sync="logFormVisible"
            width="700px" 
            @close="resetLog" 
            :close-on-click-modal="false"
            class="dialog-border">
            <el-row type="flex" class="row-bg dialog-table">
                <el-table 
                    :data="logList" 
                    max-height='400' 
                    v-loading='tableLogLoading' 
                    :header-row-class-name="headerClass">
                    <el-table-column prop="name" label="名称" min-width="150px"></el-table-column>
                    <el-table-column prop="time" label="修改时间" min-width="120px"></el-table-column>
                    <el-table-column prop="size" label="文件大小" min-width="80px"></el-table-column>
                    <el-table-column align="center" label="操作" min-width="100px">
                        <template slot-scope="scope">
                            <el-button 
                                type="text" 
                                class="text-button" 
                                v-if="btn.updateLog.value"
                                @click="updateLog(scope.row)">
                                <div class="button-icon button-update"></div>
                                <span>下载</span>
                            </el-button>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <el-row class="pagination-row">
                    <el-col :span="24" class="table-health">
                        <el-pagination 
                            @size-change="handleSizeChange" 
                            @current-change="handleCurrentChange" 
                            :current-page="logForm.pn" 
                            :page-sizes="[20,50,100]"
                            :page-size="logForm.ps"
                            layout="total,sizes,prev,pager,next,jumper"
                            :total="logForm.total">
                        </el-pagination>
                    </el-col>
                </el-row>

            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="logFormVisible = false" class="btn-style">
                    <span>关闭</span>
                </button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import { headerRowClassName } from "@/utils/tableStyle.js";

export default {
    name: "config-content",
    props: { logData: Object },
    data() {
        return {
            logFormVisible: true,
            tableLogLoading: false,

            logForm: {
                pn: 1,
                ps: 20,
                total: 0
            },
            btn: {
                updateLog: {
                    name: "下载程序日志",
                    value: false
                }
            },
            returnData: {
                name: "updateLog",
                value: ""
            },

            logList: []
        };
    },
    methods: {
        //请求数据函数
        handleSizeChange(val) {
            this.logForm.ps = val;
            this.logBtn();
        },
        handleCurrentChange(val) {
            this.logForm.pn = val;
            this.logBtn();
        },
        logBtn() {
            let _this = this;
            let data = {
                ip: this.logData.serverIp,
                process: this.logData.name,
                pn: this.logForm.pn,
                ps: this.logForm.ps
            };

            _this.tableLogLoading = true;
            _this.$http
                .get("software/logs?" + _this.qs.stringify(data))
                .then(res => {
                    _this.tableLogLoading = false;
                    if (res.data.code == 0) {
                        _this.logList = res.data.data.list;
                        _this.logForm.total = res.data.data.total;
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.tableLogLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        updateLog(row) {
            let _this = this;
            let data = {
                ip: this.logData.serverIp,
                process: this.logData.name,
                log: row.name
            };
            window.location =
                this.$http.defaults.baseURL +
                "software/log?" +
                _this.qs.stringify(data);
        },

        //功能函数
        resetLog() {
            this.logList = [];
            this.logForm.total = 0;
            this.$emit("returnMain", this.returnData);
        },
        headerClass() {
            return headerRowClassName();
        }
    },
    mounted() {
        getPermission("程序管理", this.btn);
        this.logBtn();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.pagination-row {
    height: 30px;
    margin-top: 10px;
}
.table-health {
    display: flex;
    align-items: center;
    justify-content: center;
}
.button-icon {
    display: inline-block;
    height: 20px;
    width: 20px;
    background-size: cover !important;
    vertical-align: sub;
    background: url("../../assets/images/procedureManage/icon-dialog.png");
}
.button-update {
    background-position: 0px 2px;
}
.text-button:hover .button-update {
    background-position: 0px -98px;
}
.btn-style {
    padding: 0 20px;
}
</style>