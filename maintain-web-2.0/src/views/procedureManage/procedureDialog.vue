<!-- 添加程序 -->
<template>
  <div class="procedure-content" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      :title="dialogData.dialogName"
      :visible.sync="addFormVisible"
      :close-on-click-modal="false"
      width="600px"
      top="10vh"
      class="dialog-border"
      custom-class="procedureDialog"
      @close="resetForm"
      v-dialogDrag
    >
      <el-row type="flex" class="row-bg">
        <el-form
          label-width="100px"
          :model="addForm"
          :rules="rules"
          ref="addForm"
          class="procedure-form"
        >
          <el-row v-if="dialogData.dialogName == '批量添加程序'">
            <el-col :span="24">
              <el-form-item label="主机IP:" prop="serverIp">
                <el-select
                  v-model="addForm.serverIp"
                  @change="ipChange"
                  placeholder="请选择主机IP"
                >
                  <el-option
                    v-for="item in ipOptions"
                    :key="item.ip"
                    :label="item.ip"
                    :value="item.ip"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="pathShow">
              <el-form-item label="程序目录:" prop="directory">
                <el-select
                  v-model="addForm.directory"
                  @change="directoryChange"
                  placeholder="请选择程序目录"
                >
                  <el-option
                    v-for="(item, index) in pathOptions"
                    :key="index"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="softShow">
              <el-form-item label="程序名称:" prop="softName">
                <el-col :span="24">
                  <el-checkbox
                    :indeterminate="isIndeterminate"
                    v-model="checkAll"
                    @change="handelCheckAllChange"
                  >
                    全选
                  </el-checkbox>
                </el-col>
                <el-checkbox-group v-model="addForm.softName">
                  <el-col
                    :span="8"
                    v-for="(item, index) in softNameOptions"
                    :key="index"
                  >
                    <el-checkbox
                      class="batchAdd"
                      :label="item"
                      :title="item"
                      @change="handelNameChange"
                    >
                    </el-checkbox>
                  </el-col>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-else>
            <el-col :span="24" v-if="dialogData.dialogName == '添加程序'">
              <el-form-item label="主机IP:" prop="selectIP">
                <el-select
                  multiple
                  collapse-tags
                  v-model="addForm.selectIP"
                  @change="ipChange"
                  placeholder="请选择主机IP"
                  class="multipleIP"
                >
                  <el-option
                    v-for="item in ipOptions"
                    :key="item.ip"
                    :label="
                      item.os ? item.ip + '（Windows）' : item.ip + '（Linux）'
                    "
                    :value="item.ip"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-else>
              <el-form-item label="主机IP:" prop="serverIp">
                <el-input v-model="addForm.serverIp" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="程序名称:" prop="name">
                <el-input v-model="addForm.name">
                  <el-button type="text" slot="append" @click="defaultBtn"
                    >默认配置</el-button
                  >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="进程数:" prop="processCount">
                <el-input
                  v-model="addForm.processCount"
                  :show-word-limit="true"
                  maxlength="2"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="程序路径:" prop="realDir">
                <el-input v-model="addForm.realDir"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
                            <el-form-item label="目录:" prop="baseDir">
                                <el-input v-model="addForm.baseDir"></el-input>
                            </el-form-item>
                        </el-col> -->
            <el-col :span="24">
              <el-form-item label="心跳监控:" prop="heartMonitor">
                <el-radio-group v-model="addForm.heartMonitor">
                  <el-radio :label="true">开启</el-radio>
                  <el-radio :label="false">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="手动配置程序:" prop="config">
                <el-radio-group v-model="addForm.config">
                  <el-radio :label="true">开启</el-radio>
                  <el-radio :label="false">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="addForm.config">
              <el-col :span="24">
                <el-form-item label="程序关键字:" prop="keys">
                  <el-input
                    v-model="addForm.keys"
                    placeholder="识别程序的关键字 以,隔开"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="启动命令目录:" prop="scriptPath">
                  <el-input
                    v-model="addForm.scriptPath"
                    placeholder="执行启动命令的目录，如bin（默认为程序根目录）"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="启动命令:" prop="script">
                  <el-input
                    v-model="addForm.script"
                    placeholder="执行启动程序命令，如sh start.sh"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="关闭命令:" prop="closeScript">
                  <el-input
                    v-model="addForm.closeScript"
                    placeholder="执行关闭程序命令，如service DaemonXServer stop"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="日志路径:">
                  <el-input
                    v-model="addForm.logPath"
                    placeholder="请输入日志路径"
                    :maxlength="200"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="日志后缀:">
                  <el-input
                    v-model="addForm.logSuffix"
                    placeholder="请输入日志后缀"
                    :maxlength="200"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="配置文件路径:">
                  <el-input
                    v-model="addForm.configPath"
                    placeholder="请输入配置文件路径"
                    :maxlength="200"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
        </el-form>
      </el-row>

      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="addFormVisible = false" class="btn-style">
          关闭
        </button>
        <button
          @click.prevent="saveBtn"
          v-loading.fullscreen.lock="fullscreenLoading"
          class="btn-style primary-btn"
        >
          确定
        </button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkChinese } from "@/utils/validate.js";

export default {
  name: "procedure-content",
  props: { dialogData: Object },
  data() {
    return {
      fullscreenLoading: false,
      softShow: false,
      pathShow: false,
      addFormVisible: true,
      isIndeterminate: true,
      checkAll: false,

      ipOptions: [],
      softNameOptions: [],
      pathOptions: [],
      selsectValue: [],
      selsectOs: [],
      selectLastOs: [],

      addForm: {
        serverIp: "",
        directory: "",
        heartMonitor: true,
        config: false,
        name: "",
        processCount: null,
        realDir: "",
        baseDir: "",
        keys: "",
        script: "",
        closeScript: "",
        scriptPath: "",
        selectIP: [],
        softName: [],
        addForm: '',
        logSuffix: '',
        configPath: ''
      },
      rules: {
        serverIp: [
          {
            required: true,
            trigger: "change",
            message: "请选择IP",
          },
        ],
        directory: [
          {
            required: true,
            trigger: "change",
            message: "请选择程序目录",
          },
        ],
        selectIP: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个IP",
          },
        ],
        softName: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个程序",
          },
        ],
        heartMonitor: [
          {
            required: true,
            trigger: "change",
            message: "请选择是否开启心跳监控",
          },
        ],
        config: [
          {
            required: true,
            trigger: "change",
            message: "请选择是否手动配置启动程序",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入程序名称",
          },
        ],
        keys: [
          {
            required: true,
            message: "请输入程序关键字",
          },
        ],
        script: [
          {
            required: true,
            message: "请输入启动命令",
          },
        ],
        closeScript: [
          {
            message: "请输入关闭命令",
          },
        ],
        scriptPath: [
          {
            // required:true,
            message: "请输入启动命令目录",
          },
        ],
        processCount: [
          {
            required: true,
            trigger: "blur",
            validator: (rule, value, callback) => {
              let vin = /^\+?[1-9][0-9]*$/;
              let vin1 = /^[\d]/g;
              if (value == "" || value == null) {
                callback(new Error("请输入进程数"));
              } else {
                if (vin.test(value) && vin1.test(value)) {
                  callback();
                } else {
                  callback(new Error("进程数只能为正整数"));
                }
              }
            },
          },
        ],
        realDir: [
          {
            trigger: "blur",
            required: true,
            validator: (rule, value, callback) => {
              if (value == "") {
                callback(new Error("请输入真实路径"));
              } else {
                if (checkChinese(value)) {
                  callback();
                } else {
                  callback(new Error("真实路径格式不正确"));
                }
              }
            },
          },
        ],
        baseDir: [
          {
            trigger: "blur",
            required: true,
            validator: (rule, value, callback) => {
              if (value == "") {
                callback(new Error("请输入目录"));
              } else {
                if (checkChinese(value)) {
                  callback();
                } else {
                  callback(new Error("目录格式不正确"));
                }
              }
            },
          },
        ],
      },
    };
  },
  methods: {
    init() {
      if (this.dialogData.dialogName != "编辑程序") {
        this.getIP();
      } else {
        this.addForm = { ...this.dialogData };
      }
    },

    //请求函数
    getIP() {
      let _this = this;

      _this.$http
        .post("software/hosts")
        .then((res) => {
          if (res.data.code == 0) {
            _this.ipOptions = res.data.data;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
          console.log(error);
        });
    },
    getDirectory(data) {
      let _this = this;

      _this.$http
        .post("software/directory", _this.qs.stringify(data))
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data && res.data.data.length != 0) {
              _this.pathOptions = [...res.data.data];
              _this.pathShow = true;
            } else {
              _this.pathShow = false;
              _this.msgFn("该IP暂无可添加的目录");
            }
          } else {
            _this.pathShow = false;
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
          _this.pathShow = false;
          console.log(error);
        });
    },
    getNames(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("software/names", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (res.data.data && res.data.data.length != 0) {
              res.data.data && (_this.softNameOptions = [...res.data.data]);
              _this.softShow = true;
            } else {
              _this.softShow = false;
              _this.msgFn("该IP目录下暂无可添加的程序");
            }
          } else {
            _this.softShow = false;
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          _this.softShow = false;
          console.log(error);
        });
    },
    getDefaultConfig() {
      let _this = this,
        data = {
          ip: _this.addForm.selectIP.join(","),
          name: _this.addForm.name,
        };

      _this.$http
        .post("software/default.json", _this.qs.stringify(data))
        .then((res) => {
          if (res.data.code == 0) {
            this.addForm = Object.assign(this.addForm, res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
          console.log(error);
        });
    },
    addSoft(data, url, text) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post(url, _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn(text);
            _this.resetForm("SUCCESS");
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //功能函数
    addData(obj) {
      let _this = this,
        data = {},
        url = "",
        text = "";

      switch (_this.dialogData.dialogName) {
        case "添加程序":
          let isShow = true,
            value = "";
          if (_this.selectOs.length > 1) {
            value = _this.selectOs[_this.selectOs.length - 1];
            $.each(_this.selectOs, (index, item) => {
              if (value !== item) {
                _this.waFn("禁止选择不同操作系统下的IP!");
                isShow = false;
                return false;
              }
            });
          }
          if (isShow) {
            url = "software/add_software";
            text = "添加成功";
            data = { ...obj };
            data.batch = false;
            data.serverIp = data.selectIP.join(",");
            delete data["selectIP"];
            _this.addSoft(data, url, text);
          }
          break;
        case "批量添加程序":
          url = "software/add_software";
          text = "添加成功";
          data = {
            batch: true,
            serverIp: obj.serverIp,
            baseDir: this.addForm.directory,
            name: obj.softName.join(","),
          };
          _this.addSoft(data, url, text);
          break;
        case "编辑程序":
          url = "software/update_soft";
          text = "编辑成功";
          data = { ...obj };
          _this.addSoft(data, url, text);
          break;
      }
    },
    defaultBtn() {
      if (!(this.addForm.name && this.addForm.selectIP.length > 0)) {
        this.waFn("请先填写IP和程序名称");
        return;
      }
      this.getDefaultConfig();
    },
    ipChange(val) {
      let obj = {},
        data = {};

      if (this.dialogData.dialogName == "批量添加程序") {
        this.softShow = false;
        this.addForm.softName = [];
        this.addForm.directory = "";

        obj = this.ipOptions.find((item) => {
          return item.ip === val;
        });
        data = {
          os: obj.os,
        };
        this.getDirectory(data);
      } else {
        let value = "";
        this.selectOs = [];
        val.forEach((item) => {
          $.each(this.ipOptions, (index, obj) => {
            if (item == obj.ip) {
              value = obj.os;
              this.selectOs.push(obj.os);
              return false;
            }
          });
        });

        if (val.length > 1) {
          value = this.selectOs[this.selectOs.length - 1];
          $.each(this.selectOs, (index, item) => {
            if (value !== item) {
              this.waFn("禁止选择不同操作系统下的IP!");
              return false;
            }
          });
        }
      }
    },
    directoryChange(val) {
      let data = {};

      this.addForm.softName = [];
      data = {
        directory: val,
        ip: this.addForm.serverIp,
      };
      this.getNames(data);
    },
    handelCheckAllChange(val) {
      this.addForm.softName = val ? this.softNameOptions : [];
      this.isIndeterminate = false;
    },
    handelNameChange(val) {
      let checkedCount = val.length;
      this.checkAll = checkedCount === this.softNameOptions.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.softNameOptions.length;
    },
    saveBtn() {
      let _this = this;

      _this.$refs["addForm"].validate((valid) => {
        if (valid) {
          this.addData(this.addForm);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(val) {
      this.$refs["addForm"].resetFields();
      this.addFormVisible = false;
      this.$emit("returnMain", val);
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.procedure-content {
  height: 100%;
  overflow-y: auto;
}
.procedure-form {
  width: 90%;
  margin: auto;
}
.btn-style:first-child {
  margin-right: 10px;
}
.btn-style {
  padding: 0 20px;
}
.multipleIP {
  width: 100%;
}
.batchAdd {
  width: 100%;
  display: flex;
  height: 20px;
  margin-bottom: 5px;
}
</style>
<style>
.batchAdd .el-checkbox__label {
  overflow: hidden;
  text-overflow: ellipsis;
  width: calc(100% - 20px);
  display: inline-block;
  height: 100%;
  line-height: 100%;
}
</style>