<!-- 日志管理 -->
<template>
    <div class="logAnalyze-content" v-loading.fullscreen = 'fullscreenLoading'>
            <div class="logAnalyze-row left-row">
                <el-row class="search-log">
                    <!-- <el-col :span='16'>
                        <el-input placeholder="请输入程序名称" v-model="name" clearable></el-input>
                    </el-col>
                    <el-col :span="8">
                        <button class="btn-style btn-search primary-btn" @click="searchBtn">
                            <span>搜索</span>
                        </button>
                    </el-col> -->
                    <el-col :span='24' class="search-col">
                        <el-input placeholder="请输入关键词" v-model="filterText" clearable></el-input>
                    </el-col>
                </el-row>
                <el-row class="log-content">
                    <el-row class="log-row">
                        <el-tree 
                            :highlight-current="true" 
                            ref="serviceTree" node-key="node" 
                            :default-checked-keys="[defaultNode]" 
                            :default-expanded-keys="defaultArr" 
                            class="log-tree serviceTree" 
                            :data="logOptions" 
                            @node-click="handleNodeClick"
                            :filter-node-method="filterNode">
                        </el-tree>
                    </el-row>
                    <el-row class="log-row">
                        <el-tree 
                            :highlight-current="true" 
                            ref="esTree" node-key="node" 
                            :props='logProps'
                            :load="loadNode"
                            lazy
                            class="log-tree" 
                            @node-click="handleESNodeClick"
                            :filter-node-method="filterNode">
                        </el-tree>
                    </el-row>
                    <el-row class="log-row">
                        <el-tree 
                            :highlight-current="true" 
                            :data="serviceSideOptions" 
                            ref="serviceSideTree" node-key="node" 
                            class="log-tree" 
                            @node-click="handleServiceSideClick"
                            :filter-node-method="filterNode">
                        </el-tree>
                    </el-row>
                </el-row>
                
            </div>
            <div class="logAnalyze-row right-row">
                <logDetail :twoData="twoData" :twoId='twoId' v-if="logDetailShow"></logDetail>
                <logService :twoData="twoData" :twoId='twoId' v-if="logServiceShow"></logService>
                <logES :twoData="esData" v-if="logEsShow"></logES>
                <logserviceSide v-if="serviceSideShow"></logserviceSide>
                <serviceInterface v-if="serviceInterfaceShow"></serviceInterface>
            </div>
    </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import logDetail from "./logService/logDetail.vue";
import logService from "./logService/logService.vue";
import logES from "./logES/logES.vue";
import logserviceSide from "./logOverview/logServiceSide.vue";
import serviceInterface from "./logOverview/serviceInterface.vue";

export default {
    name: "logAnalyze-content",
    components: {
        logDetail,
        logService,
        logES,
        logserviceSide,
        serviceInterface
    },
    data() {
        return {
            name: "",
            filterText: "",
            defaultNode: 2,
            twoId: null,
            passId: null,
            passNode: null,

            fullscreenLoading: false,
            logDetailShow: false,
            logServiceShow: false,
            logEsShow: false,
            serviceSideShow: false,
            serviceInterfaceShow: false,

            dataList: [],
            esOptions: [],
            esSlowOptions: [],
            logOptions: [
                {
                    label: "程序日志",
                    children: [],
                    type: 1
                }
            ],
            serviceSideOptions: [
                {
                    label: "总览",
                    children: [
                        {
                            label: "调用关系"
                        },
                        {
                            label: "慢接口服务"
                        }
                    ],
                    type: 1
                }
            ],
            defaultArr: [0, 1],

            twoData: {},
            esData: {},
            logProps: {
                isLeaf: "leaf"
            }
        };
    },
    methods: {
        init() {
            this.passId = this.$route.query.id || null;
            this.getDataList(1);
            this.getStatus();
        },

        //请求数据函数
        getDataList(type, resolve) {
            let _this = this;

            type == 1 && (_this.fullscreenLoading = true);
            _this.$http
                .get("log/stack_analyze.json?type=" + type)
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        this.formatData(type, res.data.data, resolve);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        searchBtn() {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .get("software/search_software.json?name=" + _this.name)
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        res.data.data && _this.formatData(1, res.data.data);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        formatData(type, data, resolve) {
            switch (type) {
                case 1:
                    data.softwareInfo &&
                        this.initServiceData(data.softwareInfo);
                    break;
                case 2:
                    data.esInfo && this.initEsData(data.esInfo, type, resolve);
                    break;
                case 3:
                    data.esInfo && this.initEsData(data.esInfo, type, resolve);
                    break;
            }
        },
        initServiceData(data) {
            let transformData = {},
                transformArr = [],
                num = 0,
                id = 1,
                expandedArr = [];

            this.passNode = null;
            for (let key in data) {
                transformData.node = num;
                transformData.label = key;
                transformData.children = [];
                num++;
                data[key].dataList.forEach(item => {
                    transformData.children.push({
                        label: id + "、" + item.name,
                        node: num,
                        children: [
                            {
                                label: "日志分析",
                                id: item.id,
                                node: num + 1
                            },
                            {
                                label: "服务治理",
                                id: item.id,
                                node: num + 2
                            }
                        ]
                    });
                    if (!this.passNode) {
                        if (this.passId == item.id) {
                            expandedArr.push(num);
                            expandedArr.push(transformData.node);
                            this.defaultArr = expandedArr;
                            this.passNode = num + 1;
                        }
                    }
                    num += 3;
                    id++;
                });
                transformArr.push(transformData);
                transformData = {};
            }
            this.logOptions[0].children = JSON.parse(
                JSON.stringify(transformArr)
            );
            this.$nextTick(() => {
                this.$refs.serviceTree.setCurrentKey(
                    this.passNode ||
                        this.logOptions[0].children[0].children[0].children[0]
                            .node
                );
            });
            this.twoData.id =
                this.passId ||
                this.logOptions[0].children[0].children[0].children[0].id;
            this.twoData.label = this.logOptions[0].children[0].children[0].children[0].label;
            this.logDetailShow = true;
        },
        initEsData(data, type, resolve) {
            let arr = [];
            let text = "";

            text = type == 2 ? "a" : "b";
            data.forEach((item, index) => {
                arr.push({
                    label: item,
                    type: type,
                    leaf: true,
                    detail: true,
                    sign: text + index
                });
            });
            return resolve(arr);
        },

        //功能函数
        loadNode(node, resolve) {
            if (node.level === 0) {
                return resolve([
                    {
                        label: "es日志"
                    }
                ]);
            }
            if (node.level === 1) {
                return resolve([
                    {
                        label: "日志查询",
                        type: 2
                    },
                    {
                        label: "慢日志查询",
                        type: 3
                    }
                ]);
            }
            if (node.level === 2) {
                switch (node.data.label) {
                    case "日志查询":
                        setTimeout(() => {
                            this.getDataList(node.data.type, resolve);
                        }, 300);
                        break;
                    case "慢日志查询":
                        setTimeout(() => {
                            this.getDataList(node.data.type, resolve);
                        }, 300);
                        break;
                }
            }
            if (node.level > 2) {
                return resolve([]);
            }
        },
        handleNodeClick(data) {
            this.$refs["esTree"].setCurrentKey(null);
            this.$refs["serviceSideTree"].setCurrentKey(null);
            if (!data.children) {
                switch (data.label) {
                    case "日志分析":
                        this.twoData.id = data.id;
                        this.twoData.label = data.label;
                        this.twoId = data.id;
                        this.logDetailShow = true;
                        this.logServiceShow = false;
                        this.logEsShow = false;
                        this.serviceInterfaceShow = false;
                        this.serviceSideShow = false;
                        break;
                    case "服务治理":
                        this.twoData.id = data.id;
                        this.twoData.label = data.label;
                        this.twoId = data.id;
                        this.logDetailShow = false;
                        this.logEsShow = false;
                        this.serviceSideShow = false;
                        this.serviceInterfaceShow = false;
                        this.logServiceShow = true;
                        break;
                }
            }
        },
        handleESNodeClick(data) {
            let type = "";

            this.$refs["serviceTree"].setCurrentKey(null);
            this.$refs["serviceSideTree"].setCurrentKey(null);
            if (data.detail) {
                this.esData.type = data.type;
                this.esData.esip = data.label;
                this.$set(this.esData, "sign", data.sign);
                this.logEsShow = true;
                this.serviceSideShow = false;
                this.logDetailShow = false;
                this.logServiceShow = false;
                this.serviceInterfaceShow = false;
            }
        },
        handleServiceSideClick(data) {
            let label = data.label;
            this.$refs["serviceTree"].setCurrentKey(null);
            this.$refs["esTree"].setCurrentKey(null);

            switch (label) {
                case "调用关系":
                    this.logDetailShow = false;
                    this.logEsShow = false;
                    this.logServiceShow = false;
                    this.serviceInterfaceShow = false;
                    this.serviceSideShow = true;
                    break;
                case "慢接口服务":
                    this.serviceInterfaceShow = true;
                    this.logDetailShow = false;
                    this.logEsShow = false;
                    this.logServiceShow = false;
                    this.serviceSideShow = false;
                    break;
            }
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        }
    },
    watch: {
        filterText(val) {
            this.$refs["serviceTree"].filter(val);
            this.$refs["esTree"].filter(val);
            this.$refs["serviceSideTree"].filter(val);
            this.$nextTick(() => {
                let treeDom = $(".log-row .log-tree .el-tree__empty-block");

                switch (treeDom.length) {
                    case 1:
                        $(treeDom[0]).css("display", "none");
                        break;
                    case 2:
                        $(treeDom[0]).css("display", "none");
                        $(treeDom[1]).css("display", "none");
                        break;
                    case 3:
                        $(treeDom[0]).css("display", "block");
                        $(treeDom[1]).css("display", "none");
                        $(treeDom[2]).css("display", "none");
                }
            });
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.logAnalyze-content {
    display: flex;
}
.logAnalyze-row {
    display: inline-block;
}
.left-row {
    width: 17%;
    background: #fff;
    height: calc(100% - 16px);
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    margin: 8px;
}
.right-row {
    width: calc(83% - 8px);
    margin: 8px;
    height: calc(100% - 16px);
}
.search-log {
    padding: 8px 0;
    margin: 0 8px;
    border-bottom: 1px solid #cdd0d3;
    height: 55px;
    display: flex;
    align-items: center;
}
.log-content {
    height: calc(100% - 55px);
    overflow-y: auto;
}
.btn-search {
    margin-left: 8px;
}
.log-tree {
    padding: 8px;
}
.logCategory {
    padding: 5px 20px;
}
.logName {
    padding: 5px 0;
    cursor: pointer;
}
.log-row:not(:nth-of-type(2)) .log-tree {
    padding: 0 8px;
}
.log-row:nth-of-type(2) .log-tree {
    padding-bottom: 0;
    padding-top: 0;
}
.serviceTree .el-tree__empty-block {
    display: none;
}
.search-col {
    padding: 5px;
}
</style>