<!-- 服务调用关系 -->
<template>
  <el-row class="serviceSide-content" v-loading.fullscreen = 'fullscreenLoading'>
    <el-row class="detail-row">
        <el-row class='form-time'>
            <el-col :span="6">
                <el-form class="level-form">
                    <el-form-item label="服务方:" prop="level">
                        <el-select 
                            v-model="serviceForm.name" 
                            @change="serviceChange" 
                            class="logSelect">
                            <el-option 
                                v-for="(item,index) in serviceOptions" 
                                :key="index" 
                                :label="item" 
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-row class="detailContent-row">
            <el-row class="chart-row">
                <div class="host-header">
                    <div class="host-text">调用关系</div>
                </div>
                <el-row class="log-chart">
                    <div class="noData-div" v-show="chartShow">暂无数据</div>
                    <div id="levelChart"></div>
                </el-row>
            </el-row>
        </el-row>
    </el-row>
  </el-row>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import { parseTime } from "@/utils/index.js";

export default {
    name: "serviceSide-content",
    data() {
        return {
            flowLevel: "",
            fullscreenLoading: false,
            chartShow: true,

            serviceOptions: [],
            serviceForm: {
                name: ""
            }
        };
    },
    methods: {
        init() {
            this.getOptions();
            this.getStatus();
        },

        //请求数据函数
        getOptions() {
            this.fullscreenLoading = true;
            this.$http
                .post("log/software")
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.data && res.data.data.length != 0) {
                            this.serviceOptions = res.data.data;
                            this.serviceForm.name = this.serviceOptions[0];
                            this.getInvoke(this.serviceOptions[0]);
                        } else {
                            this.fullscreenLoading = false;
                        }
                    } else {
                        this.fullscreenLoading = false;
                        res.data.msg ? this.waFn(res.data.msg) : this.erFn();
                    }
                })
                .catch(error => {
                    this.fullscreenLoading = false;
                    console.log(error);
                });
        },
        getInvoke(name) {
            let data = {
                name: name
            };

            this.fullscreenLoading = true;
            this.$http
                .post("log/invoke", this.qs.stringify(data))
                .then(res => {
                    this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data && res.data.data.length != 0) {
                            this.formatInvoke(res.data.data);
                            this.chartShow = false;
                        } else {
                            this.chartShow = true;
                        }
                    } else {
                        res.data.msg ? this.waFn(res.data.msg) : this.erFn();
                    }
                })
                .catch(error => {
                    this.fullscreenLoading = false;
                    console.log(error);
                });
        },

        //处理数据函数
        formatInvoke(data) {
            let invokeData = {
                name: this.serviceForm.name,
                children: []
            };
            data.forEach((item, index) => {
                invokeData.children.push({
                    name: item.cname,
                    children: [
                        {
                            name: "调用次数:" + item.count
                        }
                    ]
                });
                if (item.cip && item.cip.length != 0) {
                    item.cip.forEach(obj => {
                        invokeData.children[index].children.push({
                            name: "调用者IP:" + obj
                        });
                    });
                }
            });
            this.drawLine(invokeData);
        },

        //功能函数
        drawLine(data) {
            let chartDom = document.getElementById("levelChart");
            let _this = this,
                options = {};

            _this.chartShow = false;
            options = {
                title: {
                    textStyle: {
                        fontSize: 12,
                        color: "#1e85e6"
                    }
                },
                tooltip: {
                    trigger: "item",
                    formatter: "{b0}",
                    triggerOn: "mousemove"
                },
                calculable: true,
                series: [
                    {
                        type: "tree",
                        data: [data],
                        symbolSize: 7,
                        left: "10%",
                        right: "15%",
                        too: 0,
                        label: {
                            position: "left",
                            verticalAlign: "middle",
                            align: "right",
                            fontSize: 12,
                            color: "#203e66"
                        },
                        leaves: {
                            label: {
                                position: "right",
                                verticalAlign: "middle",
                                align: "left"
                            }
                        },
                        expandAndCollapse: true,
                        animationDuration: 550,
                        animationDurationUpdate: 750
                    }
                ]
            };
            _this.flowLevel = _this.$echarts.init(chartDom);
            _this.flowLevel.setOption(options);

            window.onresize = function() {
                _this.flowLevel.resize();
            };
        },
        serviceChange(val) {
            this.flowLevel && this.flowLevel.dispose();
            window.onresize = null;
            this.getInvoke(val);
        }
    },

    mounted() {
        this.init();
    },
    destroyed() {
        window.onresize = null;
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.serviceSide-content {
    height: 100%;
}
.detail-row {
    height: 100%;
    widows: 100%;
}
.chart-row {
    height: 100%;
    background: #fff;
    padding: 10px 30px 20px 20px;
}
.form-time {
    height: 40px;
    padding: 10px 0 0px 10px;
}
.level-form > div {
    margin-bottom: 0;
    width: 300px;
}
.logSelect {
    width: 230px;
}
.form-time:nth-of-type(1) div {
    display: inline-block;
    line-height: 27px;
}
.detailContent-row {
    margin: 10px 0 0;
    height: calc(100% - 50px);
    background-color: #fff;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}
.host-header {
    height: 29px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 28px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-num {
    margin-left: 20px;
    display: inline-block;
    color: #203e66;
}
.log-chart {
    height: calc(100% - 30px);
    margin: 10px 20px;
}
#levelChart {
    height: 100%;
    width: 100%;
}
</style>