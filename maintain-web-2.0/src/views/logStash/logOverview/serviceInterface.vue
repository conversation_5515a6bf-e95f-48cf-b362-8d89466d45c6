<!-- 慢接口服务 -->
<template>
  <el-row class="serviceInterface-content" v-loading.fullscreen = 'fullscreenLoading'>
    <el-row class="detail-row">
        <el-row class="detailContent-row">
            <el-row class="chart-row">
                <div class="host-header">
                    <div class="host-text">慢接口服务</div>
                </div>
                <el-row class="log-table">
                    <el-table 
                        :data="dataList" 
                        :row-class-name="bodyClass" 
                        :header-cell-style="tableHeader" 
                        :header-row-class-name="headerClass" 
                        height="100%" border>
                        <el-table-column type="index" min-width="50" align="center"></el-table-column>
                        <el-table-column prop="cost" label="接口耗时">
                            <template slot-scope="scope">
                                <span>{{scope.row.cost}}ms</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="ctm"  min-width="130" label="调用时间"></el-table-column>
                        <el-table-column prop="sname" label="服务名称" min-width="100" class-name="overflowColumn">
                            <template slot-scope="scope">
                                <el-popover trigger="hover" placement="top-start">
                                    <p class="popoverContent">{{scope.row.sname}}</p>
                                    <div slot="reference" class="startParam-popover">{{scope.row.sname}}</div>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column prop="flag" label="请求flag"></el-table-column>
                        <el-table-column prop="inpara" label="接口入参" class-name="overflowColumn">
                            <template slot-scope="scope">
                                <el-popover trigger="hover" placement="top-start" width="500">
                                    <p class="popoverContent" v-html="scope.row.inpara"></p>
                                    <div slot="reference" class="startParam-popover">{{scope.row.inpara}}</div>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column prop="rescode" label="返回状态码"></el-table-column>
                        <el-table-column prop="outpara" label="接口出参" class-name="overflowColumn">
                            <template slot-scope="scope">
                                <el-popover trigger="hover" placement="top-start">
                                    <p class="popoverContent">{{scope.row.outpara}}</p>
                                    <div slot="reference" class="startParam-popover">{{scope.row.outpara}}</div>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column prop="cname" label="调用者" min-width="100" class-name="overflowColumn">
                            <template slot-scope="scope">
                                <el-popover trigger="hover" placement="top-start">
                                    <p class="popoverContent">{{scope.row.cname}}</p>
                                    <div slot="reference" class="startParam-popover">{{scope.row.cname}}</div>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column prop="cip" label="调用者IP" min-width="100" class-name="overflowColumn">
                            <template slot-scope="scope">
                                <el-popover trigger="hover" placement="top-start">
                                    <p class="popoverContent">{{scope.row.cip | transformIP}}</p>
                                    <div slot="reference" class="startParam-popover">{{scope.row.cip | transformIP}}</div>
                                </el-popover>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="sip" label="服务方IP" min-width="100" class-name="overflowColumn">
                            <template slot-scope="scope">
                                <el-popover trigger="hover" placement="top-start">
                                    <p class="popoverContent">{{scope.row.sip | transformIP}}</p>
                                    <div slot="reference" class="startParam-popover">{{scope.row.sip | transformIP}}</div>
                                </el-popover>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>
            </el-row>
        </el-row>
    </el-row>
  </el-row>
</template>

<script>
import {
    bodyRowClassName,
    tableHeaderStyle,
    headerRowClassName
} from "@/utils/tableStyle.js";
import getPermission from "@/utils/permissions.js";
import { parseTime } from "@/utils/index.js";

export default {
    name: "serviceInterface-content",
    props: {
        twoData: Object
    },
    data() {
        return {
            fullscreenLoading: false,

            dataList: []
        };
    },
    methods: {
        init() {
            this.getSlow();
            this.getStatus();
        },

        //请求数据函数
        getSlow() {
            this.fullscreenLoading = true;
            this.$http
                .post("log/slow")
                .then(res => {
                    this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        res.data.data && (this.dataList = res.data.data);
                    } else {
                        res.data.msg ? this.waFn(res.data.msg) : this.erFn();
                    }
                })
                .catch(error => {
                    this.fullscreenLoading = false;
                    console.log(error);
                });
        },

        //处理数据函数

        //功能函数
        bodyClass() {
            return bodyRowClassName();
        },
        headerClass() {
            return headerRowClassName();
        },
        tableHeader() {
            return tableHeaderStyle();
        }
    },

    filters: {
        transformIP(val) {
            Array.isArray(val) && (val = val.join(", "));
            return val;
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.serviceInterface-content {
    height: 100%;
}
.detail-row {
    height: 100%;
    widows: 100%;
}
.chart-row {
    height: 100%;
    background: #fff;
    padding: 10px 20px 20px;
}
.form-time {
    height: 40px;
    padding: 10px 0 0px 10px;
}
.level-form > div {
    margin-bottom: 0;
    width: 270px;
}
.form-time:nth-of-type(1) div {
    display: inline-block;
    line-height: 27px;
}
.detailContent-row {
    height: 100%;
    background-color: #fff;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}
.host-header {
    height: 29px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 28px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-num {
    margin-left: 20px;
    display: inline-block;
    color: #203e66;
}
.log-table {
    height: calc(100% - 30px);
    padding: 20px 10px 10px;
}
</style>