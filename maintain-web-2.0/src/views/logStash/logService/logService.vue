<!-- 服务治理 -->
<template>
    <el-row class="logService-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-row class="detail-row">
            <el-row class='form-time'>
                <el-col :span="6">
                    <el-form class="level-form">
                        <el-form-item label="调用者:" prop="name">
                            <el-select 
                                v-model="chartForm.cName" 
                                @change="nameChange" 
                                clearable 
                                class="logSelect">
                                <el-option 
                                    v-for="item in nameOptions" 
                                    :key="item.value" 
                                    :label="item.cName" 
                                    :value="item.cName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </el-col>
                <el-col :span="12">
                    <el-radio-group 
                        v-model.number="timeChart" 
                        size="mini" 
                        @change="timeChange" 
                        class="logRadio">
                        <el-radio-button 
                            class="proce-radio" 
                            v-for="item in timeOptions" 
                            :key="item.value" 
                            :label="item.value">
                            {{item.label}}
                        </el-radio-button>
                    </el-radio-group>
                </el-col>
            </el-row>
            <el-row class="detailContent-row">
                <el-row class="chart-row">
                    <div class="host-header">
                        <div class="host-text">接口耗时</div>
                    </div>
                    <el-row class="log-chart">
                        <div class="noData-div" v-show="chartShow">暂无数据</div>
                        <div id="levelChart"></div>
                    </el-row>
                </el-row>
                <el-row class="table-row">
                    <div class="host-header">
                        <div class="host-text">接口详情</div>
                        <div class="host-num">接口成功 
                            <span class="levelNum">{{successNum}}</span> 条，
                            失败 <span class="levelNum">{{failNum}}</span> 条</div>
                    </div>
                    <el-row class='table-form'>
                        <el-col :span="12" class="tableSearch-row">
                            <el-row>
                                <el-col :span='12' :offset="offsetNum">
                                    <el-input 
                                        placeholder="请输入关键词" 
                                        v-model="chartForm.content" 
                                        clearable 
                                        @keyup.enter.native="tableSearch">
                                        <el-select 
                                            placeholder="请选择" 
                                            class="input-select" 
                                            slot="prepend" 
                                            v-model="searchName" 
                                            @change="keyChange">
                                            <el-option 
                                                v-for="item in searchOptions" 
                                                :key="item.value" 
                                                :label="item.label" 
                                                :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-input>
                                </el-col>
                                <el-col :span="1" v-if="searchName == 'cost'" class="costLine">
                                    <span>-</span>
                                </el-col>
                                <el-col :span='6' v-if="searchName == 'cost'">
                                    <el-input 
                                        placeholder="请输入关键词" 
                                        v-model="chartForm.costTime" 
                                        @keyup.enter.native="tableSearch"
                                        clearable></el-input>
                                </el-col>
                                <el-col :span='4'>
                                    <button class="btn-style btn-search primary-btn" @click="tableSearch">
                                        <i class="iconfont icon-search"></i>
                                        <span>搜索</span>
                                    </button>
                                </el-col>
                            </el-row>
                        </el-col>
                    </el-row>
                    <el-row class="log-table">
                        <el-col :span="24" class="content-table">
                            <el-table 
                                :data="dataList" 
                                @sort-change="tableSortChange" 
                                :row-class-name="bodyClass" 
                                :header-cell-style="tableHeader" 
                                :header-row-class-name="headerClass" 
                                height="100%" border>

                                <el-table-column prop="ctm"  min-width="120" label="调用时间" sortable="custom"></el-table-column>
                                <el-table-column prop="sname" label="服务名称"></el-table-column>
                                <el-table-column prop="flag" label="请求flag" sortable="custom"></el-table-column>
                                <el-table-column prop="inpara" label="接口入参" class-name="overflowColumn">
                                    <template slot-scope="scope">
                                        <el-popover trigger="hover" placement="top-start" width="500">
                                            <p class="popoverContent" v-html="scope.row.inpara"></p>
                                            <div slot="reference" class="startParam-popover">{{scope.row.inpara}}</div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="rescode" label="接口返回状态码" min-width="110px"></el-table-column>
                                <el-table-column prop="outpara" label="接口出参" class-name="overflowColumn">
                                    <template slot-scope="scope">
                                        <el-popover trigger="hover" placement="top-start">
                                            <p class="popoverContent">{{scope.row.outpara}}</p>
                                            <div slot="reference" class="startParam-popover">{{scope.row.outpara}}</div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="cname" label="调用者"></el-table-column>
                                <el-table-column prop="cip" label="调用者IP" class-name="overflowColumn">
                                    <template slot-scope="scope">
                                        <el-popover trigger="hover" placement="top-start">
                                            <p class="popoverContent">{{scope.row.cip | transformIP}}</p>
                                            <div slot="reference" class="startParam-popover">{{scope.row.cip | transformIP}}</div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="cost" label="接口耗时" sortable="custom">
                                    <template slot-scope="scope">
                                        <span>{{scope.row.cost}}ms</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="sip" label="服务方IP" min-width="100" class-name="overflowColumn">
                                    <template slot-scope="scope">
                                        <el-popover trigger="hover" placement="top-start">
                                            <p class="popoverContent">{{scope.row.sip | transformIP}}</p>
                                            <div slot="reference" class="startParam-popover">{{scope.row.sip | transformIP}}</div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                        <el-col :span="24" class="table-page">
                            <el-pagination 
                            @size-change="handleSizeChange" 
                            @current-change="handleCurrentChange" 
                            :current-page="chartForm.pn" 
                            :page-sizes="[20,50,100]"
                            :page-size="chartForm.ps"
                            layout="total,sizes,prev,pager,next,jumper"
                            :total="form.total"
                            background
                            popper-class="pagination-popper">
                        </el-pagination>
                        </el-col>
                    </el-row>
                </el-row>
            </el-row>
        </el-row>
    </el-row>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import {
    bodyRowClassName,
    tableHeaderStyle,
    headerRowClassName
} from "@/utils/tableStyle.js";
import { parseTime } from "@/utils/index.js";

export default {
    name: "logService-content",
    props: {
        twoData: Object,
        twoId: Number
    },
    data() {
        return {
            flowLevel: "",
            backName: "",
            searchName: "flag",
            pageTimer: null,
            timeChart: 0,
            successNum: 0,
            failNum: 0,
            offsetNum: 8,

            fullscreenLoading: false,
            levelChart: false,
            chartShow: true,

            searchOptions: [
                {
                    label: "请求flag",
                    value: "flag"
                },
                {
                    label: "返回状态码",
                    value: "status"
                },
                // {
                //     label:'服务方IP',
                //     value:'sip'
                // },
                // {
                //     label:'调用者IP',
                //     value:'cIp'
                // },
                {
                    label: "接口耗时",
                    value: "cost"
                }
            ],
            timeOptions: [
                {
                    label: "当天",
                    value: 0
                },
                {
                    label: "过去3天",
                    value: 3
                },
                {
                    label: "过去5天",
                    value: 5
                }
            ],
            dataList: [],
            nameOptions: [],

            form: {
                total: 0, //总条数
                error: 0,
                debug: 0,
                info: 0
            },
            chartForm: {
                sort: [],
                cName: "",
                id: null,
                startTime: "",
                endTime: "",
                content: "",
                costTime: "",
                pn: 1, //当前页
                ps: 20 //当前每页条数
            }
        };
    },
    methods: {
        init() {
            this.requestInterval();
            this.getStatus();
        },

        //请求数据函数
        getDataList(IntervalLoading, data, chartData) {
            let _this = this;

            _this.fullscreenLoading = IntervalLoading ? false : true;
            !chartData && (_this.chartShow = true);
            _this.successNum = 0;
            _this.failNum = 0;
            _this.$http
                .post("log/service_manage.json", _this.qs.stringify(data))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (!chartData) {
                            if (JSON.stringify(res.data.data) != "{}") {
                                _this.drawLine(res.data.data);
                            }
                        }
                        _this.initData(res.data.data);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        requestInterval() {
            if (this.pageTimer) {
                clearInterval(this.pageTimer);
            } else {
                this.pageTimer = setInterval(() => {
                    this.getDataList(true, this.chartForm);
                }, 60000);
            }
        },

        //处理数据函数
        initData(data) {
            let _this = this;
            _this.dataList = data.table;
            _this.form.total = data.total;
            _this.nameOptions = data.cName;
            $.each(data.resultCode, (index, item) => {
                index == 0 || index == 200
                    ? (this.successNum += item)
                    : (this.failNum += item);
            });
        },
        drawLine(data) {
            let chartDom = document.getElementById("levelChart");
            let _this = this;

            if (data.x.length != 0) {
                let options = {};

                _this.chartShow = false;
                options = {
                    tooltip: {
                        trigger: "axis",
                        // formatter:'{b0}</br>{a0}: {c0}ms',
                        axisPointer: {
                            lineStyle: {
                                type: "dashed",
                                width: 1
                            }
                        }
                    },
                    legend: {
                        data: ["最大值", "平均值"]
                    },
                    calculable: true,
                    grid: {
                        top: "16%",
                        bottom: "20%",
                        left: "10%",
                        right: "3%"
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: ["10%", "10%"],
                        axisLine: {
                            lineStyle: { color: "#e6e9f1" }
                        },
                        axisLabel: {
                            color: "#203e66",
                            length: 7,
                            formatter: val => {
                                let str = val.split(" ");
                                return str.join("\n");
                            }
                        },
                        data: data.x
                    },
                    yAxis: [
                        {
                            type: "value",
                            axisTick: { show: false }, //坐标轴刻度
                            axisLine: {
                                lineStyle: { color: "#e6e9f1" }
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#203e66"
                                },
                                formatter: "{value}ms"
                            },
                            splitLine: {
                                lineStyle: { color: "#e6e9f1" }
                            }
                        }
                    ],
                    series: [
                        {
                            name: "平均值",
                            type: "line",
                            smooth: true,
                            itemStyle: {
                                normal: {
                                    color: "rgb(99,223,246)"
                                },
                                emphasis: {
                                    borderColor: "red"
                                }
                            },
                            data: data.avg
                        },
                        {
                            name: "最大值",
                            type: "line",
                            smooth: true,
                            itemStyle: {
                                normal: {
                                    color: "#7C50D6"
                                },
                                emphasis: {
                                    borderColor: "red"
                                }
                            },
                            data: data.max
                        }
                    ]
                };
                _this.flowLevel = _this.$echarts.init(chartDom);
                _this.flowLevel.setOption(options);
            } else {
                _this.chartShow = true;
            }

            window.onresize = function() {
                _this.flowLevel && _this.flowLevel.resize();
            };
        },

        //功能函数
        handleSizeChange(val) {
            this.chartForm.ps = val;
            this.getDataList(false, this.chartForm, true);
        },
        handleCurrentChange(val) {
            this.chartForm.pn = val;
            this.getDataList(false, this.chartForm, true);
        },
        tableSearch() {
            if (isNaN(this.chartForm.content)) {
                this.waFn("请输入整数");
                return;
            }
            if (this.searchName == "cost") {
                if (this.chartForm.costTime || this.chartForm.content) {
                    if (!this.chartForm.costTime || !this.chartForm.content) {
                        this.waFn("请完整输入耗时时间");
                        return;
                    }
                }

                if (isNaN(this.chartForm.costTime)) {
                    this.waFn("请输入整数");
                    return;
                }
                this.chartForm[this.searchName] = [];
                this.chartForm.content &&
                    this.chartForm[this.searchName].push(
                        this.chartForm.content
                    );
                this.chartForm.costTime &&
                    this.chartForm[this.searchName].push(
                        this.chartForm.costTime
                    );
                this.chartForm[this.searchName] =
                    this.chartForm[this.searchName].length != 0
                        ? this.chartForm[this.searchName].join(",")
                        : [];
            } else {
                this.chartForm[this.searchName] = this.chartForm.content;
            }

            if (this.backName != this.searchName && this.backName)
                delete this.chartForm[this.backName];
            this.dataList = [];
            this.chartForm.pn = 1;
            this.chartForm.ps = 50;
            this.form.total = 0;
            this.backName = this.searchName;
            this.getDataList(false, this.chartForm, true);
        },
        nameChange(val) {
            this.flowLevel && this.flowLevel.dispose();
            this.dataList = [];
            this.chartForm.cName = val;
            this.getDataList(false, this.chartForm);
        },
        timeChange(val) {
            let endTime = Date.parse(new Date());
            let startTime = endTime - val * 24 * 60 * 60 * 1000;

            window.onresize = "";
            if (this.backName != this.searchName && this.backName)
                delete this.chartForm[this.backName];
            this.chartForm.endTime = parseTime(endTime, "{y}-{m}-{d} 23:59:59");
            this.chartForm.startTime = parseTime(
                startTime,
                "{y}-{m}-{d} 00:00:00"
            );
            this.flowLevel && this.flowLevel.dispose();
            this.getDataList(false, this.chartForm);
        },
        tableSortChange(column, prop, order) {
            this.chartForm.sort = [];
            this.chartForm.sort.push(column.prop + ":" + column.order);
            this.chartForm.sort = this.chartForm.sort.join(",");
            this.getDataList(false, this.chartForm);
        },
        keyChange(val) {
            this.chartForm.content = "";
            this.chartForm.costTime = "";
            this.offsetNum = val == "cost" ? 1 : 8;
        },
        bodyClass() {
            return bodyRowClassName();
        },
        headerClass() {
            return headerRowClassName();
        },
        tableHeader() {
            return tableHeaderStyle();
        },
        resetData() {
            this.flowLevel && this.flowLevel.dispose();
            this.dataList = [];
            this.form.total = 0;
            this.chartForm.sort = [];
            this.backName = "";
            this.chartForm = {
                sort: [],
                cName: "",
                id: null,
                startTime: "",
                endTime: "",
                content: "",
                costTime: "",
                pn: 1,
                ps: 50
            };
        }
    },
    watch: {
        twoData: {
            handler(val, oldVal) {
                this.resetData();
                this.chartForm.id = val.id;
                this.chartForm.endTime = parseTime(
                    Date.parse(new Date()),
                    "{y}-{m}-{d} 23:59:59"
                );
                this.chartForm.startTime = parseTime(
                    Date.parse(new Date()),
                    "{y}-{m}-{d} 00:00:00"
                );
                this.getDataList(false, this.chartForm);
            },
            immediate: true,
            deep: true
        },
        twoId(val) {
            this.resetData();
            this.chartForm.id = val;
            this.chartForm.endTime = parseTime(
                Date.parse(new Date()),
                "{y}-{m}-{d} 23:59:59"
            );
            this.chartForm.startTime = parseTime(
                Date.parse(new Date()),
                "{y}-{m}-{d} 00:00:00"
            );
            this.timeChart = 0;
            this.getDataList(false, this.chartForm);
        }
    },
    mounted() {
        this.init();
    },
    destroyed() {
        window.onresize = "";
        clearInterval(this.pageTimer);
    },
    filters: {
        transformIP(val) {
            Array.isArray(val) && (val = val.join(", "));
            return val;
        },
        transformJson(val) {
            // Array.isArray(val) && (val = val.join(', '));
            return val;
            // isJson(value) && (val = )
        }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.logService-content {
    height: 100%;
}
.chart-row {
    height: 45%;
    background: #fff;
    padding: 10px 30px 20px 20px;
}
.form-time {
    height: 40px;
    padding: 10px 0 0px 10px;
}
.form-time:nth-of-type(1) div {
    display: inline-block;
    line-height: 27px;
}
/* .form-time div{
    display: inline-block;
} */
.table-form {
    border: 0;
    height: 40px;
}
.table-row {
    height: 55%;
    background: #fff;
    padding: 0px 30px 0px 20px;
}
.level-form {
    width: 100%;
}
.level-form > div {
    margin-bottom: 0;
    width: 270px;
    float: right;
}
.log-chart {
    height: calc(100% - 30px);
    margin: 10px 20px;
}
#levelChart {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.log-table {
    height: calc(100% - 70px);
    padding: 0 10px;
}
.content-table {
    height: calc(100% - 5rem);
}
.levelNum {
    color: #1e85e6;
}
.tableSearch-row {
    float: right;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}
.tableSearch-row button {
    margin-left: 5px;
}
.tableSearch-row .iconfont {
    font-size: 14px;
}
.detail-row {
    height: 100%;
    width: 100%;
}
.detailContent-row {
    margin: 10px 0 0;
    height: calc(100% - 50px);
    background-color: #fff;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}
.host-header {
    height: 29px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 28px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-num {
    margin-left: 20px;
    display: inline-block;
    color: #203e66;
}
.input-select {
    width: 120px;
}
.costLine {
    height: 26px;
    line-height: 26px;
    text-align: center;
}
</style>