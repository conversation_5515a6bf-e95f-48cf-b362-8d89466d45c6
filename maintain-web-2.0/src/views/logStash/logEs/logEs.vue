<!-- es日志 -->
<template>
  <el-row class="Es-content" v-loading.fullscreen = 'fullscreenLoading'>
    <el-row class="detail-row">
        <el-row class='form-time'>
            <el-col :span="6">
                <el-form class="level-form">
                    <el-form-item label="日志级别:" prop="level">
                        <el-select 
                            v-model="chartForm.level" 
                            @change="levelChange" 
                            class="logSelect">
                            <el-option 
                                v-for="item in levelOptions" 
                                :key="item.value" 
                                :label="item.label" 
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :span="12">
                <el-radio-group 
                    v-model.number="timeChart" 
                    size="mini" 
                    @change="timeChange" 
                    class="logRadio">
                    <el-radio-button 
                        class="proce-radio" 
                        v-for="item in timeOptions" 
                        :key="item.value" 
                        :label="item.value">
                        {{item.label}}
                    </el-radio-button>
                </el-radio-group>
            </el-col>
        </el-row>
        <el-row class="detailContent-row">
            <div v-if="proceStatus" class="proce-status" :style="{backgroundPosition:'0px ' +formatStatus(statusContent) }"></div>
            <el-row class="chart-row">
                <div class="host-header">
                    <div class="host-text">日志走势</div>
                </div>
                <el-row class="log-chart">
                    <div class="noData-div" v-show="chartShow">暂无数据</div>
                    <div id="levelChart"></div>
                </el-row>
            </el-row>
            
            <el-row class="table-row">
                <div class="host-header">
                    <div class="host-text">日志详情</div>
                    <div 
                        v-for="(item,key,index) in logLevel" 
                        :key="index" class="host-num">
                        <span v-if="index == 0" class>日志</span>{{key}} 
                        <span class="levelNum">{{item}}</span> 条
                        <span v-if="index != Object.keys(logLevel).length - 1">，</span>
                    </div>
                </div>
                
                <el-row class='table-form'>
                    <el-col :span="12" class="tableSearch-row">
                        <el-row>
                            <el-col :span='12' :offset="offsetNum">
                                <el-input 
                                    placeholder="请输入关键词" 
                                    v-model="chartForm.inputVal" 
                                    clearable
                                    @keyup.enter.native="tableSearch">
                                    <el-select 
                                        placeholder="请选择" 
                                        class="input-select" 
                                        slot="prepend" 
                                        v-model="searchName" 
                                        @change="keyChange">
                                        <el-option 
                                            v-for="item in searchOptions" 
                                            :key="item.value" 
                                            :label="item.label" 
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-input>
                            </el-col>
                            <el-col :span="1" v-if="searchName == 'tookmillis'" class="costLine">
                                <span>-</span>
                            </el-col>
                            <el-col :span='6' v-if="searchName == 'tookmillis'">
                                <el-input 
                                    placeholder="请输入关键词" 
                                    v-model="chartForm.costTime"
                                    @keyup.enter.native="tableSearch"
                                    clearable></el-input>
                            </el-col>
                            <el-col :span='4'>
                                <button class="btn-style btn-search primary-btn" @click="tableSearch">
                                    <span>搜索</span>
                                </button>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
                <el-row class="log-table">
                    <el-row class="content-table">
                        <el-table 
                            :data="dataList" 
                            @sort-change="tableSortChange" 
                            :row-class-name="bodyClass" 
                            :header-cell-style="tableHeader" 
                            :header-row-class-name="headerClass" 
                            height="100%" border>

                            <el-table-column prop="tm" label="日志时间" sortable="custom"></el-table-column>
                            <el-table-column prop="ip" label="日志ip"></el-table-column>
                            <el-table-column prop="node" label="日志节点"></el-table-column>
                            <el-table-column prop="level" label="日志级别" sortable="custom" min-width='70'></el-table-column>
                            <el-table-column prop="thread" label="线程名" min-width='100'></el-table-column>
                            <el-table-column 
                                prop="tookmillis" 
                                label="查询耗时" 
                                min-width='60' 
                                sortable="custom"
                                v-if="twoData.type == 3">
                                <template slot-scope="scope">
                                    <span>{{scope.row.tookmillis + 'ms'}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="msg" label="具体日志" class-name="overflowColumn">
                                <template slot-scope="scope">
                                    <el-popover trigger="hover" placement="top-start" width="500">
                                        <p class="popoverContent">{{scope.row.msg}}</p>
                                        <div slot="reference" class="startParam-popover">{{scope.row.msg}}</div>
                                    </el-popover>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-row>
                    <el-row :span="24" class="table-page">
                        <el-pagination 
                        @size-change="handleSizeChange" 
                        @current-change="handleCurrentChange" 
                        :current-page="chartForm.pn" 
                        :page-sizes="[20,50,100]"
                        :page-size="chartForm.ps"
                        layout="total,sizes,prev,pager,next,jumper"
                        :total="form.total"
                        background
                        popper-class="pagination-popper">
                    </el-pagination>
                    </el-row>
                </el-row>
            </el-row>

        </el-row>
    </el-row>
  </el-row>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import {
    bodyRowClassName,
    tableHeaderStyle,
    headerRowClassName
} from "@/utils/tableStyle.js";
import { parseTime } from "@/utils/index.js";

export default {
    name: "Es-content",
    props: {
        twoData: Object
    },
    data() {
        return {
            flowLevel: "",
            statusContent: "",
            chartName: "DEBUG",
            searchName: "node",
            pageTimer: null,
            timeChart: 0,
            offsetNum: 8,

            fullscreenLoading: false,
            levelChart: false,
            chartShow: true,
            proceStatus: false,

            dataList: [],
            searchOptions: [
                {
                    label: "节点",
                    value: "node"
                },
                {
                    label: "具体日志",
                    value: "content"
                },
                {
                    label: "查询耗时",
                    value: "tookmillis"
                }
            ],
            timeOptions: [
                {
                    label: "当天",
                    value: 0
                },
                {
                    label: "过去3天",
                    value: 3
                },
                {
                    label: "过去5天",
                    value: 5
                }
            ],
            levelOptions: [
                {
                    label: "ERROR",
                    value: 1
                },
                {
                    label: "WARN",
                    value: 2
                },
                {
                    label: "INFO",
                    value: 3
                },
                {
                    label: "DEBUG",
                    value: 4
                },
                {
                    label: "TRACE",
                    value: 5
                }
                // {
                //     label:'ALL',
                //     value:6
                // },
            ],

            form: {
                total: 0, //总条数
                error: 0,
                debug: 0,
                info: 0
            },
            logLevel: {},
            chartForm: {
                sort: [],
                level: 4,
                type: null,
                esip: "",
                startTime: "",
                endTime: "",
                inputVal: "",
                costTime: "",
                pn: 1, //当前页
                ps: 20 //当前每页条数
            }
        };
    },
    methods: {
        init() {
            this.requestInterval();
            this.getStatus();
        },

        //请求数据函数
        getDataList(IntervalLoading, data, chartData) {
            let _this = this;

            _this.fullscreenLoading = IntervalLoading ? false : true;
            !chartData && (_this.chartShow = true);
            _this.$http
                .post("log/stack_analyze.json", _this.qs.stringify(data))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (!chartData) {
                            if (res.data.data.logInfo.graph) {
                                _this.drawLine(res.data.data.logInfo.graph);
                            }
                        }
                        _this.dataList = res.data.data.logInfo.table;
                        _this.form.total = res.data.data.logInfo.total;
                        _this.logLevel = res.data.data.logInfo.level;
                        _this.proceStatus = res.data.data.status ? true : false;
                        _this.statusContent = res.data.data.status;
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        requestInterval() {
            if (this.pageTimer) {
                clearInterval(this.pageTimer);
            } else {
                this.pageTimer = setInterval(() => {
                    this.getDataList(true, this.chartForm);
                }, 60000);
            }
        },

        //处理数据函数
        drawLine(data) {
            let chartDom = document.getElementById("levelChart");
            let _this = this;

            if (data.x.length != 0) {
                let options = {};

                _this.chartShow = false;
                options = {
                    title: {
                        text: _this.chartName + "日志",
                        textStyle: {
                            fontSize: 12,
                            color: "#1e85e6"
                        }
                    },
                    tooltip: {
                        trigger: "axis",
                        formatter: "{b0}</br>{a0}: {c0}",
                        axisPointer: {
                            lineStyle: { type: "dashed" }
                        }
                    },
                    calculable: true,
                    grid: {
                        top: "16%",
                        bottom: "20%",
                        left: "10%",
                        right: "3%"
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: ["10%", "10%"],
                        axisLine: {
                            lineStyle: { color: "#e6e9f1" }
                        },
                        axisLabel: {
                            color: "#203e66",
                            length: 7,
                            formatter: val => {
                                let str = val.split(" ");
                                return str.join("\n");
                            }
                        },
                        data: data.x
                    },
                    yAxis: [
                        {
                            type: "value",
                            axisTick: { show: false }, //坐标轴刻度
                            axisLine: {
                                lineStyle: { color: "#e6e9f1" }
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#203e66"
                                },
                                formatter: "{value}"
                            },
                            splitLine: {
                                lineStyle: { color: "#e6e9f1" }
                            }
                        }
                    ],
                    series: [
                        {
                            name: _this.chartName + "日志",
                            type: "line",
                            smooth: true,
                            itemStyle: {
                                normal: {
                                    color: "rgb(99,223,246)"
                                },
                                emphasis: {
                                    borderColor: "red"
                                }
                            },
                            data: data.y
                        }
                    ]
                };
                _this.flowLevel = _this.$echarts.init(chartDom);
                _this.flowLevel.setOption(options);
            } else {
                _this.chartShow = true;
            }
            window.onresize = function() {
                _this.flowLevel.resize();
            };
        },
        formatStatus(val) {
            switch (val) {
                case "GREEN":
                    val = "-217px";
                    break;
                case "YELLOW":
                    val = "-111px";
                    break;
                case "RED":
                    val = "-3px";
                    break;
            }
            return val;
        },
        formatTookmillis() {
            if (this.twoData.type == 2) {
                this.searchOptions.forEach((item, index) => {
                    if (item.label == "查询耗时") {
                        this.searchOptions.splice(index, 1);
                    }
                });
            } else {
                let isPush = true;

                this.searchOptions.forEach((item, index) => {
                    if (item.label == "查询耗时") {
                        isPush = false;
                    }
                });
                if (isPush) {
                    this.searchOptions.push({
                        label: "查询耗时",
                        value: "tookmillis"
                    });
                }
            }
        },

        //功能函数
        handleSizeChange(val) {
            this.chartForm.ps = val;
            this.getDataList(false, this.chartForm, true);
        },
        handleCurrentChange(val) {
            this.chartForm.pn = val;
            this.getDataList(false, this.chartForm, true);
        },
        tableSearch() {
            if (this.searchName == "tookmillis") {
                if (this.chartForm.costTime || this.chartForm.inputVal) {
                    if (!this.chartForm.costTime || !this.chartForm.inputVal) {
                        this.waFn("请完整输入查询时间");
                        return;
                    }
                }

                if (
                    isNaN(this.chartForm.costTime) ||
                    isNaN(this.chartForm.inputVal)
                ) {
                    this.waFn("请输入整数");
                    return;
                }
                this.chartForm[this.searchName] = [];
                this.chartForm.inputVal &&
                    this.chartForm[this.searchName].push(
                        this.chartForm.inputVal
                    );
                this.chartForm.costTime &&
                    this.chartForm[this.searchName].push(
                        this.chartForm.costTime
                    );
                this.chartForm[this.searchName] =
                    this.chartForm[this.searchName].length != 0
                        ? this.chartForm[this.searchName].join(",")
                        : [];
            } else {
                this.chartForm[this.searchName] = this.chartForm.inputVal;
            }

            if (this.backName != this.searchName && this.backName)
                delete this.chartForm[this.backName];
            this.dataList = [];
            this.chartForm.pn = 1;
            this.chartForm.ps = 50;
            this.form.total = 0;
            this.backName = this.searchName;
            this.getDataList(false, this.chartForm, true);
        },
        levelChange(val) {
            let obj = {};

            obj = this.levelOptions.find(item => {
                return item.value === val;
            });
            this.chartName = obj.label;
            this.flowLevel && this.flowLevel.dispose();
            this.dataList = [];
            this.chartForm.level = val;
            this.getDataList(false, this.chartForm);
        },
        timeChange(val) {
            let endTime = Date.parse(new Date());
            let startTime = endTime - val * 24 * 60 * 60 * 1000;

            window.onresize = "";
            this.chartForm.endTime = parseTime(endTime, "{y}-{m}-{d} 23:59:59");
            this.chartForm.startTime = parseTime(
                startTime,
                "{y}-{m}-{d} 00:00:00"
            );
            this.flowLevel && this.flowLevel.dispose();
            this.getDataList(false, this.chartForm);
        },
        tableSortChange(column, prop, order) {
            this.chartForm.sort = [];
            this.chartForm.sort.push(column.prop + ":" + column.order);
            this.chartForm.sort = this.chartForm.sort.join(",");
            this.getDataList(false, this.chartForm);
        },
        keyChange(val) {
            this.chartForm.inputVal = "";
            this.chartForm.costTime = "";
            this.offsetNum = val == "tookmillis" ? 1 : 8;
        },
        bodyClass() {
            return bodyRowClassName();
        },
        headerClass() {
            return headerRowClassName();
        },
        tableHeader() {
            return tableHeaderStyle();
        },
        resetData() {
            this.flowLevel && this.flowLevel.dispose();
            this.dataList = [];
            this.chartForm.sort = [];
            this.form.total = 0;
            this.chartShow = true;
            this.chartForm = {
                sort: [],
                level: 4,
                type: null,
                startTime: "",
                esip: "",
                endTime: "",
                inputVal: "",
                costTime: "",
                pn: 1,
                ps: 50
            };
        }
    },
    watch: {
        twoData: {
            handler(val, oldval) {
                this.resetData();
                this.chartForm.type = val.type;
                this.chartForm.esip = val.esip;
                this.chartName = "DEBUG";
                this.chartForm.endTime = parseTime(
                    Date.parse(new Date()),
                    "{y}-{m}-{d} 23:59:59"
                );
                this.chartForm.startTime = parseTime(
                    Date.parse(new Date()),
                    "{y}-{m}-{d} 00:00:00"
                );
                this.getDataList(false, this.chartForm);
                this.formatTookmillis();
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.init();
    },
    destroyed() {
        window.onresize = "";
        clearInterval(this.pageTimer);
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.Es-content {
    height: 100%;
}
.chart-row {
    height: 45%;
    background: #fff;
    padding: 10px 30px 20px 20px;
}
.form-time {
    height: 40px;
    padding: 10px 0 0px 10px;
}
.form-time:nth-of-type(1) div {
    display: inline-block;
    line-height: 27px;
}
.table-form {
    border: 0;
    height: 40px;
}
.table-row {
    height: 55%;
    background: #fff;
    padding: 0px 30px 0px 20px;
}
.level-form {
    width: 100%;
}
.level-form > div {
    margin-bottom: 0;
    width: 270px;
}
.log-chart {
    height: calc(100% - 30px);
    margin: 10px 20px;
}
#levelChart {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.log-table {
    height: calc(100% - 70px);
    padding: 0 10px;
}
.content-table {
    height: calc(100% - 5rem);
}
.levelNum {
    color: #1e85e6;
}
.tableSearch-row button {
    margin-left: 5px;
}
.tableSearch-row {
    float: right;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}
.detail-row {
    height: 100%;
    width: 100%;
}
.detailContent-row {
    margin: 10px 0 0;
    height: calc(100% - 50px);
    background-color: #fff;
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}
.host-header {
    height: 29px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 28px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-num {
    margin-left: 20px;
    display: inline-block;
    color: #203e66;
}
.proce-status {
    height: 80px;
    width: 50px;
    background-size: cover !important;
    background: url("../../../assets/images/logManage/icon-tag.png");
    position: absolute;
    right: -4px;
    z-index: 10;
}
.input-select {
    width: 100px;
}
.costLine {
    height: 26px;
    line-height: 26px;
    text-align: center;
}
</style>