<template>
    <div class="cdhHref">
        <form :action="loginUrl" ref="cdhSubmit" method="post">
            <input type="text" name="j_username" v-model="userName"/><br>
            <input type="password" name="j_password" v-model="password"/><br>
            <!-- <input type="submit"> -->
        </form>
    </div>
</template>

<script>
export default {
    name: "cdhHref",
    data() {
        return {
            loginUrl: "",
            userName: "",
            password: ""
        };
    },
    mounted() {
        document.title = "跳转中...";
        this.userName = this.$route.query.userName;
        this.password = this.$route.query.password;
        this.loginUrl = this.$route.query.loginUrl;
        this.$nextTick(() => {
            this.$refs["cdhSubmit"].submit();
        });
        console.log(this.loginUrl);
    }
};
</script>

<style scoped>
.cdhHref {
    visibility: hidden;
}
</style>