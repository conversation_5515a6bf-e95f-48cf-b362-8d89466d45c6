<!-- 集群管理 -->
<template>
  <div class="colony-content">
    <div class="tab-content" v-loading.fullscreen="fullscreenLoading">
      <el-tabs
        type="card"
        class="colony-header"
        v-model="activeName"
        @tab-click="handelClick"
      >
        <el-tab-pane
          v-for="(item) in tabOptions"
          :key="item.id"
          :label="item.name"
          :name="(item.id).toString()"
        >
        </el-tab-pane>
      </el-tabs>
      <es-manage :id="activeName"></es-manage>
    </div>
  </div>
</template>

<script>
import esManage from "./esManage.vue";
export default {
  name: "colony-content",
  components: { esManage },
  data() {
    return {
      activeName: "",
      fullscreenLoading: false,
      tabOptions: [],
      numForm: {
        policeNum: 0,
        alarmNum: 0,
        goodNum: 0,
        iframeSrc: "",
        groupId: null,
      },
    };
  },
  methods: {
    //请求数据函数
    getDataList() {
      this.activeName = "";
      let _this = this;
      _this.fullscreenLoading = true;
      _this.$http
        .get("group/services")
        .then((res) => {
          const { code, data, msg } = res.data;
          _this.fullscreenLoading = false;
          if (code == 0) {
            this.tabOptions = data || [];
            if (data.length !== 0) {
              this.activeName = data[0].id.toString()
            }
          } else {
            msg ? _this.waFn(msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    handelClick(tab,a) {
      console.log(tab,a)
    },
  },
  mounted() {
    this.getDataList();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.colony-content {
  height: calc(100% - 30px);
  width: calc(100% - 30px);
  /* border: 1px solid #ccc; */
  margin: 15px;
}
iframe {
  height: 100%;
  width: 100%;
  position: absolute;
}
.tab-content {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}
.el-tabs {
  width: 100%;
  height: 40px;
  flex-shrink: 0;
}
.el-tabs .el-tabs__content .el-tab-pane {
  height: 100%;
  width: 100%;
}
.el-tabs /deep/ .el-tabs--card>.el-tabs__header  .el-tabs__item {
  border-left: none;
}
.el-tabs  /deep/ .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  border-left: 1px solid #E4E7ED;
}
 .el-tabs /deep/ .el-tabs__content {
  display: none;
}
.el-tabs .el-tabs__content .el-tab-pane .errorMsg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #f53749;
  line-height: 20px;
  font-size: 14px;
}
.tab-round {
  background-color: red;
  position: absolute;
  z-index: 100;
  left: 65px;
  top: 5px;
}
.el-tabs--border-card {
  border: 0;
}
.radio-content {
  width: 100%;
  text-align: center;
  margin: 7px;
}
.monitor-content {
  height: 100%;
}
.form-time {
  margin: 20px 10px 0;
  height: 40px;
  text-align: right;
}
.detail-row {
  height: 40px;
  margin-top: 30px;
}
.chart-content {
  width: 100%;
  height: 100%;
}
.chart-row {
  width: 100%;
  height: calc(100% - 200px);
  padding-top: 30px;
}
.cdh-row {
  height: calc(100% - 90px);
  padding-top: 0;
}
.cdh-row .chartWrap {
  height: 100%;
}
.table-row {
  height: calc(100% - 30px);
  padding: 20px 20px 10px;
}
.btn-style:not(:first-of-type) {
  margin-left: 5px;
}
.colony-status {
  text-align: left;
  padding-left: 30px;
  line-height: 30px;
}
.colony-Btn {
  padding-right: 15px;
}
.colony-detail {
  position: absolute;
  top: 7px;
  right: 15px;
}
.status-describe {
  display: inline-block;
  margin-right: 40px;
}
.host-row {
  padding: 10px 10px 0 10px;
  min-height: 200px;
  height: 40%;
}
.host-header {
  height: 30px;
  line-height: 36px;
  font-size: 14px;
  border-bottom: 1px solid #cdd0d3;
}
.host-text {
  display: inline-block;
  height: 30px;
  line-height: 36px;
  border-bottom: 2px solid #5174b4;
  padding: 0 3px;
  color: #203e66;
  font-weight: bold;
}
.host-content {
  padding: 0px 15px;
  height: 100%;
}
.network-row {
  margin: 0 10px;
  /* min-height: 400px; */
  height: 60%;
}
.popoverContent {
  color: #203e66;
  line-height: 20px;
}
.host-form {
  margin: 18px 0 0 50px;
}
.hostForm-row {
  min-height: 170px;
  display: flex;
  align-items: center;
  height: calc(100% - 30px);
}
.queue-row {
  padding: 10px 10px 0 10px;
  min-height: 150px;
  height: 30%;
}
.queueForm-row {
  min-height: 120px;
  display: flex;
  align-items: center;
  height: calc(100% - 30px);
}
.queueChart-row {
  margin: 0 10px;
  /* min-height: 400px; */
  height: 70%;
}
</style>