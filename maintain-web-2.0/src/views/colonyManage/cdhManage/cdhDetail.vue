<!-- CDH监控详情 -->
<template>
    <div class="cdh-detail" v-loading.fullscreen = 'fullscreenLoading'>
        <div class="detail-content">
            <el-row>
                <el-col :span="12">
                    <div class="top-title">CDH管理监控详情</div>
                    <div class="instruct-form">
                        <span>操作指令：</span>
                        <el-select v-model="instructName" @change="instructChange">
                            <el-option 
                                v-for="(item,index) in instructOptions" 
                                :key='index' 
                                :value="item" 
                                :label="index">
                            </el-option>
                        </el-select>
                    </div>
                    <el-button 
                        class="proceBtn" type="text" 
                        v-if='buttonShow' 
                        @click="detailBtn">
                        <!-- <div class="button-icon button-update"></div> -->
                        <span>{{buttonName}}</span>
                    </el-button>
                            
                </el-col>
                <el-col :span="12" class="return-col">
                    <button @click.prevent="returnWeb" class="btn-return btn-style">
                        <div class="button-icon button-return"></div>
                        <span>返回</span>
                    </button>
                </el-col> 
            </el-row>
            <el-row class="content-row">
                <el-row class="host-row">
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">HDFS空间使用情况</div>
                        </div>
                        <el-row class='form-time'>
                            <el-date-picker 
                                v-model="hdfsTime"
                                value-format="yyyy-MM-dd"
                                type="daterange" 
                                align="right" 
                                range-separator='至' 
                                start-placeholder='开始日期' 
                                end-placeholder='结束日期'
                                @change="hdfsChange"
                                :clearable="false">
                            </el-date-picker>
                        </el-row>
                        <el-row class="hostForm-row">
                            <div class="noData-div" v-if="hdfsShow">暂无数据</div>
                            <el-row class="chart-content"></el-row>
                        </el-row>
                    </el-row>
                </el-row>
                
                <el-row class='network-row'>
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">HBASE表统计</div>
                        </div>
                        <el-row class='form-time'>
                            <el-col :span="12" class="hbase-select">
                                <el-form>
                                    <el-form-item label="表名:">
                                        <el-select v-model="hbaseName" @change="tableNameChange">
                                            <el-option 
                                                v-for="(item,index) in hbaseOptions" 
                                                :key='index' 
                                                :value="index" 
                                                :label="index">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                            <el-col :span="12">
                                <el-date-picker 
                                    v-model="hbaseTime"
                                    value-format="yyyy-MM-dd"
                                    type="daterange" 
                                    align="right" 
                                    range-separator='至' 
                                    start-placeholder='开始日期' 
                                    end-placeholder='结束日期'
                                    @change="hbaseChange"
                                    :clearable="false">
                                </el-date-picker>
                            </el-col>
                        </el-row>
                        <el-row class="hostForm-row">
                            <div class="noData-div" v-if="hbaseShow">暂无数据</div>
                            <el-row class="chart-content"></el-row>
                        </el-row>
                    </el-row>
                </el-row>
            </el-row>
        </div>
        <el-dialog 
            title="操作指令结果" 
            :visible.sync="addFormVisible" 
            width="680px" top="10vh" 
            :close-on-click-modal="false"
            class="dialog-border">
            <div class="socket-body">
                <p class="socket-message" v-for="(value,index) in textOptions" :key="index">{{value}}</p>
            </div>
            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="addFormVisible = false" class="btn-style">关闭</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    headerRowClassName,
    bodyRowClassName,
    tableHeaderStyle
} from "@/utils/tableStyle.js";
import { parseTime } from "@/utils/index.js";

export default {
    name: "cdh-detail",
    data() {
        return {
            hdfsTime: "",
            hbaseTime: "",
            hdfsChart: "",
            hbaseChart: "",
            hbaseName: "",
            instructName: "",
            buttonName: "",
            newSrc: "",

            twoRow: null,
            fullscreenLoading: false,
            addFormVisible: false,
            hdfsShow: true,
            hbaseShow: true,
            buttonShow: false,

            hbaseData: [],
            textOptions: [],

            instructOptions: {},
            hbaseOptions: {},
            hdfsForm: {
                requestCode: 1,
                requestName: "hdfsSpace",
                params: {
                    beginTime: "",
                    endTime: ""
                }
            },
            hbaseForm: {
                requestCode: 2,
                requestName: "hbaseTable",
                params: {
                    beginTime: "",
                    endTime: ""
                }
            }
        };
    },
    methods: {
        initTime() {
            let endTime = parseTime(Date.parse(new Date()), "{y}-{m}-{d}");
            let startTime = parseTime(
                Date.parse(new Date()) - 60 * 60 * 1000 * 24 * 7,
                "{y}-{m}-{d}"
            );

            this.hdfsTime = [startTime, endTime];
            this.hbaseTime = [startTime, endTime];
            this.hdfsForm.params.beginTime = startTime;
            this.hdfsForm.params.endTime = endTime;
            this.hbaseForm.params.beginTime = startTime;
            this.hbaseForm.params.endTime = endTime;

            this.getHdfsData(this.hdfsForm);
            this.getHbaseData(this.hbaseForm);
            this.getStructOption();
            this.getStatus();
        },

        //请求数据函数
        getHdfsData(data) {
            let _this = this;
            let searchData = {};

            searchData = {
                message: JSON.stringify(data)
            };
            this.hdfsShow = false;
            _this.fullscreenLoading = true;

            _this.$http
                .post(
                    "plugin/plugin_business/CdhPlugin/hdfsSpace",
                    _this.qs.stringify(searchData)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        res.data.data != "[]"
                            ? _this.initHdfsData(JSON.parse(res.data.data))
                            : (this.hdfsShow = true);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getHbaseData(data) {
            let _this = this,
                resData = [];
            let searchData = {};

            searchData = {
                message: JSON.stringify(data)
            };
            this.hbaseShow = false;
            _this.fullscreenLoading = true;

            _this.$http
                .post(
                    "plugin/plugin_business/CdhPlugin/hbaseTable",
                    _this.qs.stringify(searchData)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data != "[]" && res.data.data) {
                            resData = JSON.parse(res.data.data);
                            _this.hbaseData = resData;
                            _this.hbaseOptions = JSON.parse(
                                resData[0].hbase_table_increase
                            );
                            _this.hbaseName = Object.keys(
                                _this.hbaseOptions
                            )[0];
                            _this.initHbaseData(resData, _this.hbaseName);
                        } else {
                            _this.hbaseShow = true;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getStructData(data) {
            let _this = this;
            let searchData = {},
                requestName = "";

            searchData = {
                name: "CdhPlugin",
                message: data
            };
            requestName = JSON.parse(data).requestName;

            _this.fullscreenLoading = true;
            _this.$http
                .post(
                    "plugin/plugin_business/CdhPlugin/" + requestName,
                    _this.qs.stringify(searchData)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            this.textOptions = JSON.parse(res.data.data);
                            this.addFormVisible = true;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getStructOption() {
            let _this = this;
            let searchData = {};

            searchData = {
                message: JSON.stringify({
                    requestCode: 3,
                    requestName: "allcheck"
                })
            };

            _this.$http
                .post(
                    "plugin/plugin_business/CdhPlugin/allcheck",
                    _this.qs.stringify(searchData)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            _this.instructOptions = JSON.parse(
                                res.data.data
                            ).cmd;
                            _this.buttonName = Object.keys(
                                JSON.parse(res.data.data).url
                            )[0];
                            _this.newSrc = JSON.parse(
                                JSON.parse(res.data.data).url[_this.buttonName]
                            ).url;
                            _this.buttonShow = true;
                        }
                        res.data.data &&
                            (this.instructOptions = JSON.parse(
                                res.data.data
                            ).cmd);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        initHdfsData(data) {
            let form = {
                time: [],
                total_space: [],
                used_space: [],
                free_space: []
            };

            data.forEach(item => {
                form.time.push(item.increse_day);
                form.free_space.push(item.free_space);
                form.used_space.push(item.used_space);
            });

            this.initHDFS(form);
        },
        initHbaseData(data, hbaseName) {
            let form = {
                time: [],
                num: [],
                sum: []
            };

            data.forEach(item => {
                form.time.push(item.increse_day);
                $.each(JSON.parse(item.hbase_table_increase), (index, obj) => {
                    hbaseName == index && form.num.push(obj);
                });
                $.each(JSON.parse(item.hbase_table_sum), (index, obj) => {
                    hbaseName == index && form.sum.push(obj);
                });
            });

            this.initHBASE(form);
        },
        initHDFS(data) {
            let chartDom = document.getElementsByClassName("chart-content")[0];
            let options = {};
            let _this = this;

            options = {
                title: {
                    textStyle: {
                        fontSize: 14,
                        color: "#1e85e6"
                    }
                },
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        lineStyle: { type: "dashed" }
                    },
                    formatter: obj => {
                        let content = "",
                            freeVal = "",
                            useVal = "";

                        useVal =
                            (obj[0].value / 1024 / 1024 / 1024).toFixed(2) +
                            "G";
                        freeVal =
                            (obj[1].value / 1024 / 1024 / 1024).toFixed(2) +
                            "G";
                        content =
                            obj[0].name +
                            "<br/>" +
                            obj[0].marker +
                            obj[0].seriesName +
                            "：" +
                            useVal +
                            "<br/>" +
                            obj[0].marker +
                            obj[1].seriesName +
                            "：" +
                            freeVal;
                        return content;
                    }
                },
                calculable: true,
                grid: {
                    top: "16%",
                    bottom: "20%",
                    left: "10%",
                    right: "5%"
                },
                xAxis: {
                    type: "category",
                    boundaryGap: ["10%", "10%"],
                    axisLine: {
                        lineStyle: { color: "#e6e9f1" }
                    },
                    axisLabel: {
                        color: "#203e66",
                        length: 7
                    },
                    data: data.time
                },
                yAxis: [
                    {
                        type: "value",
                        axisTick: { show: false }, //坐标轴刻度
                        axisLine: {
                            lineStyle: { color: "#e6e9f1" }
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#203e66"
                            },
                            formatter: val => {
                                val =
                                    (val / 1024 / 1024 / 1024).toFixed(2) + "G";
                                return val;
                            }
                        },

                        splitLine: {
                            lineStyle: { color: "#e6e9f1" }
                        }
                    }
                ],
                legend: {
                    data: ["使用空间", "剩余空间"],
                    textStyle: {
                        color: "#203e66"
                    }
                    // left:'left'
                },
                series: [
                    {
                        type: "bar",
                        barWidth: 20,
                        name: "使用空间",
                        smooth: true,
                        stack: "总量",
                        itemStyle: {
                            normal: {
                                color: "#d48265"
                            }
                        },
                        label: {
                            show: true,
                            formatter: obj => {
                                let content = "";
                                content =
                                    (obj.value / 1024 / 1024 / 1024).toFixed(
                                        2
                                    ) + "G";
                                return content;
                            }
                        },
                        data: data.used_space
                    },
                    {
                        type: "bar",
                        barWidth: 20,
                        name: "剩余空间",
                        smooth: true,
                        stack: "总量",
                        itemStyle: {
                            normal: {
                                color: "#9fdabf"
                            }
                        },
                        label: {
                            show: true,
                            formatter: obj => {
                                let content = "";
                                content =
                                    (obj.value / 1024 / 1024 / 1024).toFixed(
                                        2
                                    ) + "G";
                                return content;
                            }
                        },
                        data: data.free_space
                    }
                ]
            };
            this.hdfsChart = this.$echarts.init(chartDom);
            this.hdfsChart.setOption(options);
            window.addEventListener("resize", this.hdfsResize);
        },
        initHBASE(data) {
            let chartDom = document.getElementsByClassName("chart-content")[1];
            let options = {};
            let _this = this;

            options = {
                title: {
                    textStyle: {
                        fontSize: 14,
                        color: "#1e85e6"
                    }
                },
                tooltip: {
                    trigger: "axis",
                    // formatter:'{b0}</br>{a0}: {c0}个',
                    axisPointer: {
                        lineStyle: { type: "dashed" }
                    }
                },
                legend: {
                    data: ["表总数", "表每日新增"]
                },
                calculable: true,
                grid: {
                    top: "16%",
                    bottom: "20%",
                    left: "10%",
                    right: "5%"
                },
                xAxis: {
                    type: "category",
                    boundaryGap: ["10%", "10%"],
                    axisLine: {
                        lineStyle: { color: "#e6e9f1" }
                    },
                    axisLabel: {
                        color: "#203e66",
                        length: 7
                    },
                    data: data.time
                },
                yAxis: [
                    {
                        type: "value",
                        axisTick: { show: false }, //坐标轴刻度
                        axisLine: {
                            lineStyle: { color: "#e6e9f1" }
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#203e66"
                            }
                        },

                        splitLine: {
                            lineStyle: { color: "#e6e9f1" }
                        }
                    }
                ],
                series: [
                    {
                        type: "line",
                        name: "表每日新增",
                        smooth: true,
                        itemStyle: {
                            normal: {
                                color: "rgb(99,223,246)"
                            },
                            emphasis: {
                                borderColor: "red"
                            }
                        },
                        data: data.num
                    },
                    {
                        type: "line",
                        name: "表总数",
                        smooth: true,
                        itemStyle: {
                            normal: {
                                color: "#7C50D6"
                            },
                            emphasis: {
                                borderColor: "red"
                            }
                        },
                        data: data.sum
                    }
                ]
            };
            this.hbaseChart = this.$echarts.init(chartDom);
            this.hbaseChart.setOption(options);
            window.addEventListener("resize", this.hbaseResize);
        },

        //功能函数
        returnWeb() {
            this.$router.go(-1);
        },
        hbaseChange(val) {
            window.removeEventListener("resize", this.hbaseResize);
            this.hbaseChart && this.hbaseChart.dispose();
            this.hbaseForm.params.beginTime = val[0];
            this.hbaseForm.params.endTime = val[1];

            this.getHbaseData(this.hbaseForm);
        },
        hdfsChange(val) {
            window.removeEventListener("resize", this.hdfsResize);
            this.hdfsChart && this.hdfsChart.dispose();
            if (val) {
                this.hdfsForm.params.beginTime = val[0];
                this.hdfsForm.params.endTime = val[1];
            }
            this.getHdfsData(this.hdfsForm);
        },
        tableNameChange(val) {
            window.removeEventListener("resize", this.hbaseResize);
            this.hbaseChart && this.hbaseChart.dispose();
            this.initHbaseData(this.hbaseData, val);
        },
        instructChange(val) {
            this.getStructData(val);
        },
        hdfsResize() {
            this.hdfsChart && this.hdfsChart.resize();
        },
        hbaseResize() {
            this.hbaseChart && this.hbaseChart.resize();
        },
        detailBtn() {
            window.open(this.newSrc);
        }
    },
    mounted() {
        this.twoRow = Number(this.$route.query.detailData);

        this.initTime();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.cdh-detail {
    height: 100%;
    width: 100%;
    /* overflow-y: auto; */
}
.detail-content {
    height: 100%;
    width: 98%;
    margin: 0 1%;
}
.top-title {
    text-align: left;
    text-indent: 15px;
    color: #1e85e6;
    font-size: 15px;
    font-weight: bold;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    margin-top: 10px;
}
.content-row {
    width: 100%;
    height: calc(100% - 60px);
    background-color: #ffffff;
    border: 1px solid #e2e2e2;
    -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
    overflow: auto;
}
.host-row {
    margin: 10px 10px 0 10px;
    min-height: 130px;
}
.host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.hostForm-row {
    padding: 30px 0;
}
.host-content {
    padding: 0px 15px;
}
.host-form {
    margin: 5px 0 0 50px;
}
.network-row {
    margin: 0 10px;
}
.button-icon {
    display: inline-block;
    height: 21px;
    width: 25px;
    background-size: cover !important;
    vertical-align: sub;
    background: url("../../../assets/images/hardWare/icon-detail.png");
}
.return-col {
    text-align: right;
    padding-right: 20px;
    height: 40px;
    line-height: 40px;
    margin-top: 10px;
}
.btn-return {
    background-color: #1e85e6;
    color: #fff;
    padding-left: 5px;
}
.button-return {
    background-position: 0 26px;
    height: 16px;
}
.btn-return:hover {
    background-color: #2d91ef;
    color: #fff;
}
.el-date-editor .el-range-separator {
    line-height: 20px;
}
.form-time {
    text-align: right;
    padding: 10px;
}
.chart-content {
    height: 300px;
    width: 100%;
}
.hbase-select {
    text-align: left;
}
.instruct-form {
    display: inline-block;
    margin-left: 20px;
}
.socket-body {
    max-height: 400px;
    overflow-y: auto;
}
.socket-message {
    margin: 5px 0;
    word-break: break-all;
    color: #203e66;
}
.instruct-form span {
    color: #203e66;
}
.proceBtn {
    color: #203e66;
    position: relative;
    margin: 0;
}
</style>