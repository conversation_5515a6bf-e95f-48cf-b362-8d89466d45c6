<!-- kafka监控详情 -->
<template>
    <div class="kafka-detail">
        <div class="detail-content">
            <el-row>
                <el-col :span="12">
                    <div class="top-title">kafka监控详情</div>
                    <el-button 
                        class="proceBtn" 
                        type="text" 
                        v-if='buttonShow' 
                        @click="detailBtn">
                        <!-- <div class="button-icon button-update"></div> -->
                        <span>{{buttonName}}</span>
                    </el-button>
                </el-col>
                <el-col :span="12" class="return-col">
                    <button @click.prevent="returnWeb" class="btn-return btn-style">
                        <div class="button-icon button-return"></div>
                        <span>返回</span>
                    </button>
                </el-col> 
            </el-row>
            <el-row class="content-row">
                <el-row class="host-row">
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">Brokers</div>
                        </div>
                        <el-row class="table-row">
                            <el-table 
                                :data="brokersList" 
                                :cell-class-name="cellClass" 
                                max-height="300" 
                                :header-cell-style="tableHeader" 
                                :header-row-class-name="headerClass"
                                v-loading='brokerLoading'>

                                <el-table-column prop="host" label="Host"></el-table-column>
                                <el-table-column prop="port" label="Port"></el-table-column>
                                <el-table-column prop="jmx_port" label="Jmx_port"></el-table-column>
                                <el-table-column prop="endpoints" label="Endpoints"></el-table-column>
                                <!-- <el-table-column prop="timestamp" label="timestamp"></el-table-column> -->
                                <el-table-column prop="version" label="Version"></el-table-column>
                            </el-table>
                        </el-row>
                    </el-row>
                </el-row>
                
                <el-row class='network-row'>
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">Topic</div>
                        </div>
                        <el-row class="table-row">
                            <el-table 
                            :data="topicList" 
                            :cell-class-name="cellClass" 
                            :header-cell-style="tableHeader" 
                            :header-row-class-name="headerClass" max-height='420'
                            v-loading='topicLoading'
                            @expand-change='topicChange'>

                                <el-table-column type="expand">
                                    <template slot-scope="scope">
                                        <el-table 
                                        :data="scope.row.partitionList" 
                                        class="partition-table" border 
                                        :row-class-name="bodyClass" 
                                        :header-cell-style="healthHeader" 
                                        :header-row-class-name="headerClass" max-height='300'
                                        v-loading='partitionLoading'>
                                            <el-table-column prop="topic" label="Topic" min-width="100"></el-table-column>
                                            <el-table-column prop="partition" label="Partition"></el-table-column>
                                            <el-table-column prop="leader" label="Leader"></el-table-column>
                                            <el-table-column prop="replicas" label="Replicas"></el-table-column>
                                            <el-table-column prop="ISR" label="Isr"></el-table-column>
                                        </el-table>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="topic" label="Topic" min-width="100"></el-table-column>
                                <el-table-column prop="partition" label="Partitions">
                                    <template slot-scope="scope">
                                        <span>{{scope.row.partition && scope.row.partition + ' 个'}}</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-row>
                    </el-row>
                </el-row>
                <el-row class='network-row'>
                    <el-row class="host-content">
                        <div class="host-header">
                            <div class="host-text">Consumers</div>
                        </div>
                        <el-row class='form-time'>
                            <el-col :span="12">
                                <el-form>
                                    <el-form-item label="Consumers:"  class="consumers-formItem">
                                        <el-select 
                                            v-model="consumersName" 
                                            @change="consumersNameChange" 
                                            class="consumers-select">
                                            <el-option 
                                                v-for="(item,index) in consumersOptions" 
                                                :key='index' :value="item" 
                                                :label="item">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>
                        <el-row class="consumers-table">
                            <el-table 
                                :data="consumersList" border 
                                :row-class-name="bodyClass" 
                                :header-cell-style="healthHeader" 
                                :header-row-class-name="headerClass" max-height='300'
                                v-loading='consumersLoading'>

                                <el-table-column label="Topic" prop="TOPIC" min-width="120"></el-table-column>
                                <el-table-column label="Partition" prop="PARTITION"></el-table-column>
                                <el-table-column label="Consumer-Id" prop="CONSUMER-ID" min-width="150" class-name="overflowColumn">
                                    <template slot-scope="scope">
                                        <el-popover 
                                            trigger="hover" placement="top-start" 
                                            width="300" >
                                            <p class="popoverContent">{{scope.row['CONSUMER-ID']}}</p>
                                            <div 
                                                slot="reference" 
                                                class="startParam-popover">
                                                {{scope.row['CONSUMER-ID']}}
                                            </div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column label="Client-Id" prop="CLIENT-ID" min-width="100"></el-table-column>
                                <el-table-column label="Host" prop="HOST"></el-table-column>
                                <el-table-column label="Current-Offset" prop="CURRENT-OFFSET"></el-table-column>
                                <el-table-column label="Log-End-Offset" prop="LOG-END-OFFSET"></el-table-column>
                                <el-table-column label="Lag" prop="LAG"></el-table-column>
                            </el-table>
                        </el-row>
                    </el-row>
                </el-row>
            </el-row>
        </div>
    </div>
</template>

<script>
import {
    headerRowClassName,
    bodyRowClassName,
    tableHeaderStyle
} from "@/utils/tableStyle.js";

export default {
    name: "kafka-detail",
    data() {
        return {
            brokerLoading: false,
            topicLoading: false,
            partitionLoading: false,
            consumersLoading: false,
            buttonShow: false,

            consumersName: "",
            buttonName: "",
            newSrc: "",

            brokersList: [],
            topicList: [],
            consumersList: [],
            consumersOptions: []
        };
    },
    methods: {
        initMethods() {
            this.getStatus();
            this.getAllTopicsInfo();
            this.getBrokerInfo();
            this.getAllConsumers();
            this.getUrl();
        },

        //请求数据函数
        getAllTopicsInfo() {
            let _this = this;
            let searchData = {};

            searchData = {
                name: "KafkaPlugin",
                message: JSON.stringify({
                    requestCode: 2,
                    requestName: "getAllTopicsInfo"
                })
            };
            _this.topicLoading = true;

            _this.$http
                .post(
                    "plugin/plugin_business/KafkaPlugin/getAllTopicsInfo",
                    _this.qs.stringify(searchData),
                    { timeout: null }
                )
                .then(res => {
                    _this.topicLoading = false;
                    if (res.data.code == 0) {
                        res.data.data &&
                            (_this.topicList = JSON.parse(res.data.data));
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.topicLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getTopicsInfo(data) {
            let _this = this;
            let searchData = {};

            searchData = {
                name: "KafkaPlugin",
                message: JSON.stringify({
                    requestCode: 3,
                    requestName: "getTopicsInfo",
                    params: data.topic
                })
            };
            _this.partitionLoading = true;

            _this.$http
                .post(
                    "plugin/plugin_business/KafkaPlugin/getTopicsInfo",
                    _this.qs.stringify(searchData),
                    { timeout: null }
                )
                .then(res => {
                    _this.partitionLoading = false;
                    if (res.data.code == 0) {
                        res.data.data &&
                            (data.partitionList = JSON.parse(res.data.data));
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.partitionLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getAllConsumers() {
            let _this = this;
            let searchData = {};

            searchData = {
                name: "KafkaPlugin",
                message: JSON.stringify({
                    requestCode: 4,
                    requestName: "getAllConsumers"
                })
            };

            _this.$http
                .post(
                    "plugin/plugin_business/KafkaPlugin/getAllConsumers",
                    _this.qs.stringify(searchData),
                    { timeout: null }
                )
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            _this.consumersOptions = JSON.parse(res.data.data);
                            _this.consumersName = _this.consumersOptions[0];
                            _this.getConsumersInfo(_this.consumersName);
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.erFn();
                    console.log(error);
                });
        },
        getConsumersInfo(data) {
            let _this = this;
            let searchData = {};

            searchData = {
                name: "KafkaPlugin",
                message: JSON.stringify({
                    requestCode: 6,
                    requestName: "getConsumersInfo",
                    params: data
                })
            };
            _this.consumersLoading = true;

            _this.$http
                .post(
                    "plugin/plugin_business/KafkaPlugin/getConsumersInfo",
                    _this.qs.stringify(searchData),
                    { timeout: null }
                )
                .then(res => {
                    _this.consumersLoading = false;
                    if (res.data.code == 0) {
                        res.data.data &&
                            (_this.consumersList = JSON.parse(res.data.data));
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.consumersLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getBrokerInfo() {
            let _this = this;
            let searchData = {};

            searchData = {
                name: "KafkaPlugin",
                message: JSON.stringify({
                    requestCode: 7,
                    requestName: "getBrokerInfo"
                })
            };
            _this.brokerLoading = true;

            _this.$http
                .post(
                    "plugin/plugin_business/KafkaPlugin/getBrokerInfo",
                    _this.qs.stringify(searchData),
                    { timeout: null }
                )
                .then(res => {
                    _this.brokerLoading = false;
                    if (res.data.code == 0) {
                        res.data.data &&
                            (_this.brokersList = JSON.parse(res.data.data));
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.brokerLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getUrl() {
            let _this = this;
            let searchData = {};

            searchData = {
                name: "KafkaPlugin",
                message: JSON.stringify({
                    requestCode: 8,
                    requestName: "getUrl"
                })
            };

            _this.$http
                .post(
                    "plugin/plugin_business/KafkaPlugin/getUrl",
                    _this.qs.stringify(searchData),
                    { timeout: null }
                )
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            _this.buttonShow = true;
                            _this.buttonName = Object.keys(
                                JSON.parse(res.data.data).url
                            )[0];
                            _this.newSrc = JSON.parse(
                                JSON.parse(res.data.data).url[_this.buttonName]
                            ).url;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.erFn();
                    console.log(error);
                });
        },

        //功能函数
        cellClass({ row, column, rowIndex, columnIndex }) {
            switch (columnIndex) {
                case 1:
                    return "overflowColumn";
            }
        },
        tableHeader() {
            return "border:0;";
        },
        cellStyle() {
            return "border:0;";
        },
        headerClass() {
            return headerRowClassName();
        },
        bodyClass() {
            return bodyRowClassName();
        },
        healthHeader() {
            return tableHeaderStyle();
        },
        returnWeb() {
            this.$router.go(-1);
        },
        consumersNameChange(val) {
            this.consumersList = [];
            this.getConsumersInfo(val);
        },
        topicChange(row, expandRows) {
            this.$nextTick(() => {
                row.partitionList || this.getTopicsInfo(row);
            });
        },
        detailBtn() {
            window.open(this.newSrc);
        }
    },
    mounted() {
        this.initMethods();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.kafka-detail {
    height: 100%;
    width: 100%;
    /* overflow-y: auto; */
}
.detail-content {
    width: 98%;
    margin: 0 1% 1%;
    height: 100%;
}
.top-title {
    text-align: left;
    text-indent: 15px;
    color: #1e85e6;
    font-size: 15px;
    font-weight: bold;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    margin-top: 10px;
}
.content-row {
    width: 100%;
    height: calc(100% - 60px);
    background-color: #ffffff;
    border: 1px solid #e2e2e2;
    -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
    overflow: auto;
}
.host-row {
    margin: 10px 10px 0 10px;
    min-height: 200px;
}
.host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-content {
    padding: 0px 15px;
}
.network-row {
    margin: 0 10px;
    /* min-height: 200px; */
}
.table-row {
    padding: 30px;
}
.consumers-table {
    padding: 15px;
}
.button-icon {
    display: inline-block;
    height: 21px;
    width: 25px;
    background-size: cover !important;
    vertical-align: sub;
    background: url("../../../assets/images/hardWare/icon-detail.png");
}
.return-col {
    text-align: right;
    padding-right: 20px;
    height: 40px;
    line-height: 40px;
    margin-top: 10px;
}
.btn-return {
    background-color: #1e85e6;
    color: #fff;
    padding-left: 5px;
}
.button-return {
    background-position: 0 26px;
    height: 16px;
}
.btn-return:hover {
    background-color: #2d91ef;
    color: #fff;
}
.form-time {
    text-align: left;
    padding: 10px;
}
.consumers-select {
    width: 230px;
}
.consumers-formItem {
    margin-bottom: 0;
}
.partition-table {
    margin: 20px;
    width: calc(100% - 40px);
}
.proceBtn {
    color: #203e66;
    position: relative;
    /* margin: 0; */
    margin-left: 20px;
}
</style>