<template>
    <div class="codisManage">
        <div class="colony-detail" v-if="!chartShow">
            <div class="status-describe">
                集群管理错误<span class="num-red"> {{baseForm.policeNum}} </span>个，
                警告<span class="num-yellow"> {{baseForm.alarmNum}} </span>个，
                良好<span class="num-green"> {{baseForm.goodNum}} </span>个
            </div>
            <button 
                class="btn-style" 
                v-if="btn.CodisDetail.value"
                @click="detailBtn">
                <span>Codis管理详情</span>
            </button>

        </div>
        <el-row class="monitor-content">
            <el-row class="queue-row">
                <el-row class="host-content">
                    <div class="host-header">
                        <div class="host-text">队列信息</div>
                    </div>
                    <el-row class="queueForm-row">
                        <el-col :span="24">
                            <el-form class="host-form">
                                <el-row>
                                    <el-col :span="6" v-for="(item,index) in TaskData" :key="index">
                                        <el-form-item class="label-color">
                                            <div slot="label">
                                                <!-- <div class="button-icon button-ip"></div> -->
                                                <span>{{index}}</span>
                                            </div>
                                            <span>{{(!item && item != 0) ? '-' : item}}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </el-col>
                        <div class="noData-div queue-div" v-if="queueShow">暂无数据</div>
                    </el-row>
                </el-row>
            </el-row>

            <el-row class='queueChart-row'>
                <el-row class="host-content">
                    <div class="host-header">
                        <div class="host-text">历史信息</div>
                    </div>
                    <el-row class='form-time' v-if="!chartShow">
                        <el-col :span="24" class="colony-Btn">
                            <el-radio-group v-model.number="timeChart" size="mini" @change="timeChange">
                                <el-radio-button 
                                    class="proce-radio" 
                                    v-for="item in timeOptions" 
                                    :key="item.value" 
                                    :label="item.value">
                                    {{item.label}}
                                </el-radio-button>
                            </el-radio-group>
                        </el-col>
                    </el-row>
                    <el-row class="chart-row cdh-row" v-loading='chartLoading'>
                        <Echart class="chart-content" v-if="contentShow" :chartData='chartData'></Echart>
                    </el-row>
                    <div class="noData-div" v-show="chartShow">暂无数据</div>
                </el-row>
            </el-row>
        </el-row>
        
    </div>
</template>

<script>
import { parseTime } from "@/utils/index.js";
import Echart from "./echart.vue";
import getPermission from "@/utils/permissions.js";

export default {
    name: "codisManage",
    props: { numForm: Object },
    components: { Echart },
    data() {
        return {
            timeChart: 7,

            activeName: "Codis管理",
            contentShow: false,
            chartLoading: false,
            queueShow: true,
            chartShow: true,
            visible: false,

            timeOptions: [
                {
                    label: "当天",
                    value: 1
                },
                {
                    label: "过去3天",
                    value: 3
                },
                {
                    label: "过去5天",
                    value: 5
                },
                {
                    label: "过去7天",
                    value: 7
                }
            ],

            chartForm: {
                startTime: "",
                endTime: "",
                groupId: null
            },
            btn: {
                CodisDetail: {
                    name: "Codis管理详情",
                    value: false
                }
            },
            baseForm: {
                policeNum: 0,
                alarmNum: 0,
                goodNum: 0,
                iframeSrc: ""
            },
            chartData: {},
            commonData: {},
            TaskData: {}
        };
    },
    methods: {
        initData() {
            this.baseForm = { ...this.numForm };
            this.chartForm.groupId = this.numForm.groupId;

            this.getTaskQueueLen();
            this.timeChange(this.timeChart);
            getPermission(this.$route.name, this.btn);
        },

        //请求数据函数
        getTaskQueueLen(data) {
            let _this = this;

            _this.queueShow = false;
            _this.$http
                .post(
                    "dynamicAndStatic/getTaskQueueLen",
                    _this.qs.stringify(data)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        res.data.data
                            ? (_this.TaskData = res.data.data)
                            : (_this.queueShow = true);
                    } else {
                        _this.queueShow = true;
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getChartData(data) {
            let _this = this;

            _this.chartLoading = true;
            _this.$http
                .post("group/list.json", _this.qs.stringify(data))
                .then(res => {
                    _this.chartLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            _this.commonData = res.data.data;
                            _this.formatEchartData(
                                _this.commonData,
                                _this.activeName
                            );
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.chartLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        formatEchartData(data, label) {
            this.chartData = data;
            this.chartData.content = label;
            if (!this.chartData.content) {
                this.contentShow = false;
                this.chartShow = true;
            } else {
                this.contentShow = true;
                this.chartShow = false;
            }
        },

        //功能函数
        timeChange(val) {
            let endTime = Date.parse(new Date());
            let startTime = endTime - val * 24 * 60 * 60 * 1000;

            window.onresize = "";
            this.chartForm.endTime = parseTime(endTime, "{y}-{m}-{d} 23:59:59");
            this.chartForm.startTime = parseTime(
                startTime,
                "{y}-{m}-{d} 00:00:00"
            );
            this.getChartData(this.chartForm);
        },
        detailBtn() {
            this.baseForm.iframeSrc && window.open(this.baseForm.iframeSrc);
        }
    },
    mounted() {
        this.initData();
    }
};
</script>

<style scoped>
.codisManage {
    height: 100%;
    width: 100%;
}
.host-form {
    margin: 18px 0 0 50px;
}
.queue-row {
    padding: 10px 10px 0 10px;
    min-height: 150px;
    height: 30%;
}
.queueForm-row {
    min-height: 120px;
    display: flex;
    align-items: center;
    height: calc(100% - 30px);
}
.queueChart-row {
    margin: 0 10px;
    /* min-height: 400px; */
    height: 70%;
}
.host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-content {
    padding: 0px 15px;
    height: 100%;
}
.queue-div {
    top: 55%;
}
.form-time {
    margin: 20px 10px 0;
    height: 40px;
    text-align: right;
}
.colony-Btn {
    padding-right: 15px;
}
.chart-content {
    width: 100%;
    height: 100%;
}
.chart-row {
    width: 100%;
    height: calc(100% - 90px);
    padding-top: 0;
}
.monitor-content {
    height: 100%;
}
.colony-detail {
    position: absolute;
    top: 7px;
    right: 15px;
}
.status-describe {
    display: inline-block;
    margin-right: 40px;
}
</style>