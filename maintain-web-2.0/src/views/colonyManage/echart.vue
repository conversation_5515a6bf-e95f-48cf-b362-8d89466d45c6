<!-- 操作过程 -->
<template>
    <el-row class="chart-content">
        <div class="chartWrap">

        </div>
    </el-row>
</template>

<script>
export default {
    name: "chart-content",
    props: { chartData: Object },
    data() {
        return {
            flowCpu: "",
            Height: "80%"
        };
    },
    methods: {
        drawLine(data) {
            let chartDom = "",
                _this = this,
                options = {};

            chartDom =
                this.chartData.content == "ES管理2"
                    ? document.getElementsByClassName("chartWrap")[
                    this.chartData.index
                    ]
                    : document.getElementsByClassName("chartWrap")[0];

            options = {
                dataZoom: [
                    {
                        type: "slider",
                        show: true,
                        bottom: this.chartData.content == "ES管理2" ? 5 : 10,
                        textStyle: {
                            color: "#203e66"
                        },
                        height: 8,
                        realtime: false,
                        showDetail: true,
                        showDataShadow: false,
                        preventDefaultMouseMove: false,
                        borderColor: "transparent",
                        backgroundColor: "#f2f2f2",
                        handleSize: 14,
                        handleStyle: { color: "#00a8ff" },
                        fillerColor: "#77cdf9", // 选中范围的填充颜色
                        handleIcon:
                            "M512 512m-512 0a500 500 0 1 0 1024 0 500 500 0 1 0-1024 0Z"
                    }
                ],
                tooltip: {
                    trigger: "axis",
                    formatter: (obj, val) => {
                        let content = "",
                            status = "",
                            objIndex = null;

                        try {
                            obj.forEach((item, index) => {
                                if (item.data != null) {
                                    objIndex = index;
                                    throw new Error("find");
                                }
                            });
                        } catch (e) {
                            if (e.message != "find") throw e;
                        }

                        if (objIndex != null) {
                            switch (obj[objIndex].data) {
                                case 0:
                                    status = "良好";
                                    break;
                                case 1:
                                    status = "告警";
                                    break;
                                case 2:
                                    status = "错误";
                                    break;
                            }
                            content =
                                obj[objIndex].name +
                                "<br/>" +
                                obj[objIndex].marker +
                                "健康状态：" +
                                status +
                                "<br/>" +
                                obj[objIndex].marker +
                                "说明：" +
                                data.description[obj[0].dataIndex];
                        }
                        return content;
                    },
                    axisPointer: {
                        lineStyle: { type: "dashed" }
                    }
                },
                calculable: true,
                grid: {
                    top: 45,
                    bottom: this.chartData.content == "ES管理2" ? "22%" : "20%",
                    left: "10%",
                    right: "10%",
                    splitLine: {
                        show: false
                    }
                },
                xAxis: {
                    type: "category",
                    name: "时间",
                    nameTextStyle: {
                        color: "#203e66"
                    },
                    boundaryGap: ["10%", "10%"],
                    axisLine: {
                        onZero: false,
                        lineStyle: { color: "#e6e9f1" }
                    },
                    axisLabel: {
                        color: "#203e66",
                        length: 7,
                        formatter: val => {
                            let str = val.split(" ");
                            return str.join("\n");
                        }
                    },
                    data: data.x
                },
                yAxis: [
                    {
                        type: "value",
                        name: "状态",
                        nameGap: 30,
                        nameTextStyle: {
                            color: "#203e66"
                        },
                        min: -1,
                        max: 2,
                        splitNumber: 3,
                        axisTick: { show: false }, //坐标轴刻度
                        axisLine: {
                            lineStyle: { color: "#e6e9f1" }
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#203e66"
                            },
                            formatter: val => {
                                switch (val) {
                                    case -1:
                                        val = "";
                                        break;
                                    case 0:
                                        val = "良好";
                                        break;
                                    case 1:
                                        val = "告警";
                                        break;
                                    case 2:
                                        val = "错误";
                                        break;
                                }
                                return val;
                            }
                        },
                        splitLine: {
                            show: false
                        }
                    }
                ],
                legend: {
                    textStyle: {
                        color: "#203e66"
                    },
                    icon: "rect",
                    itemWidth: 10,
                    itemHeight: 10,
                    itemGap: 30,
                    left: "12%",
                    top: 5,
                    padding: [0, 0, 0, 30],
                    data: [
                        {
                            name: "良好(" + data.GREEN.num + ")"
                        },
                        {
                            name: "告警(" + data.YELLOW.num + ")"
                        },
                        {
                            name: "报警(" + data.RED.num + ")"
                        }
                    ]
                },
                series: [
                    {
                        name: "良好(" + data.GREEN.num + ")",
                        type: "line",
                        showSymbol: true,
                        symbol: "circle",
                        symbolSize: 4,
                        smooth: true,
                        itemStyle: {
                            normal: {
                                color: "#21db8d"
                            },
                            emphasis: {
                                borderColor: "#21db8d"
                            }
                        },
                        lineStyle: {
                            width: 4
                        },
                        data: data.GREEN.chart
                    },
                    {
                        name: "告警(" + data.YELLOW.num + ")",
                        type: "line",
                        smooth: true,
                        showSymbol: true,
                        symbol: "circle",
                        symbolSize: 4,
                        itemStyle: {
                            normal: {
                                color: "#fc833d"
                            },
                            emphasis: {
                                borderColor: "#fc833d"
                            }
                        },
                        lineStyle: {
                            width: 4
                        },
                        data: data.YELLOW.chart
                    },
                    {
                        name: "报警(" + data.RED.num + ")",
                        type: "line",
                        smooth: true,
                        showSymbol: true,
                        symbol: "circle",
                        symbolSize: 4,
                        itemStyle: {
                            normal: {
                                color: "#f53749"
                            },
                            emphasis: {
                                borderColor: "#f53749"
                            }
                        },
                        lineStyle: {
                            width: 4
                        },
                        data: data.RED.chart
                    }
                ]
            };

            this.flowCpu = this.$echarts.init(chartDom);
            this.flowCpu.setOption(options);
            window.addEventListener("resize", this.bindResize);
        },
        bindResize() {
            this.flowCpu && this.flowCpu.resize();
        }
    },
    watch: {
        chartData: {
            handler(val) {
                console.log(val)
                this.drawLine(val);
            },
            deep: true
        }
    },
    destroyed() {
        window.removeEventListener("resize", this.bindResize);
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.chart-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chartWrap {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>