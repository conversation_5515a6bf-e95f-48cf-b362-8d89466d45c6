<template>
    <div class="alarmManage">
        <el-row class="monitor-content">
            <el-row class="host-row">
                <el-row class="host-content">
                    <div class="host-header">
                        <div class="host-text">告警信息</div>
                    </div>
                    <el-row class="table-row">
                        <el-table 
                            :data="errorList" 
                            v-loading='errorLoading'
                            height="100%" 
                            :cell-style="cellStyle" 
                            :header-cell-style="tableHeader" 
                            :header-row-class-name="headerClass">

                            <el-table-column prop="metric_type" label="服务类型" min-width="100"></el-table-column>
                            <el-table-column prop="warn_message" label="告警信息" min-width="500" class-name="overflowColumn">
                                <template slot-scope="scope">
                                    <el-popover class="popoverSpan" trigger="hover" placement="top-start">
                                        <p class="popoverContent" v-html="formatArr(scope.row.warn_message, true)"></p>
                                        <div slot="reference" class="name-wrapper">
                                            <span>{{formatArr(scope.row.warn_message, false)}}</span>
                                        </div>
                                    </el-popover>
                                </template>
                            </el-table-column>
                            <el-table-column prop="create_time" label="告警时间" min-width="150"></el-table-column>
                            <el-table-column align="center" label="操作" min-width="100">
                                <template slot-scope="scope">
                                    <el-button 
                                        type="text" 
                                        class="text-button form-btn"
                                        @click="updateBtn(scope.row.metric_type)">
                                        <i class="iconfont icon-update"></i>
                                        刷新
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-row>
                </el-row>
            </el-row>
        </el-row>
    </div>
</template>

<script>
import { headerRowClassName } from "@/utils/tableStyle.js";
export default {
    name: "alarmManage",
    data() {
        return {
            errorLoading: false,

            errorList: []
        };
    },
    methods: {
        //请求数据函数
        getDataList() {
            let _this = this;

            _this.errorLoading = true;
            _this.$http
                .post("warn/getAllGroupWarnInfo")
                .then(res => {
                    _this.errorLoading = false;
                    if (res.data.code == 0) {
                        res.data.data &&
                            (this.errorList = JSON.parse(res.data.data));
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.errorLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getRefreshData(data) {
            let _this = this;
            let name = "",
                requestName = "";

            name = data.name;
            requestName = JSON.parse(data.message).requestName;
            _this.errorLoading = true;

            _this.$http
                .post(
                    "plugin/plugin_business/" + name + "/" + requestName,
                    _this.qs.stringify(data),
                    { timeout: null }
                )
                .then(res => {
                    _this.errorLoading = false;
                    if (res.data.code == 0) {
                        let resData = JSON.parse(res.data.data);
                        _this.errorList.forEach((item, index) => {
                            if (item.metric_type == resData[0].metric_type) {
                                _this.$set(_this.errorList, index, resData[0]);
                            }
                        });
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.errorLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        //处理数据函数
        formatArr(data, show) {
            let content = "";

            if (data && data.length != 0) {
                data = JSON.parse(data);
                if (Object.prototype.toString.call(data) == "[object Array]") {
                    data.forEach((item, index) => {
                        index != data.length - 1 && show
                            ? (content += item + "</br>")
                            : (content += item);
                    });
                    return content;
                } else {
                    return data;
                }
            } else {
                return "-";
            }
        },

        //功能函数
        updateBtn(value) {
            let data = {};

            switch (value) {
                case "HBase":
                    data = {
                        name: "HbasePlugin",
                        message: JSON.stringify({
                            requestName: "refreshHBaseWarn"
                        })
                    };
                    this.getRefreshData(data);
                    break;
                case "HDFS":
                    data = {
                        name: "HbasePlugin",
                        message: JSON.stringify({
                            requestName: "refreshHdfsWarn"
                        })
                    };
                    this.getRefreshData(data);
                    break;
                case "Kafka":
                    data = {
                        name: "KafkaPlugin",
                        message: JSON.stringify({
                            requestName: "refreshKafkaWarn"
                        })
                    };
                    this.getRefreshData(data);
                    break;
                case "CDH":
                    data = {
                        name: "CdhPlugin",
                        message: JSON.stringify({
                            requestName: "refreshCDHWarn"
                        })
                    };
                    this.getRefreshData(data);
                    break;
                case "ES":
                    data = {
                        name: "EsPlugin",
                        message: JSON.stringify({
                            requestName: "refreshEsWarn"
                        })
                    };
                    this.getRefreshData(data);
                    break;
            }
        },
        tableHeader() {
            return "border:0;";
        },
        cellStyle() {
            return "border:0;";
        },
        headerClass() {
            return headerRowClassName();
        }
    },
    mounted() {
        this.getDataList();
    }
};
</script>

<style scoped>
.alarmManage,
.monitor-content {
    height: 100%;
    width: 100%;
}
.host-row {
    padding: 10px 10px 0 10px;
    min-height: 200px;
    height: 100%;
}
.table-row {
    height: calc(100% - 30px);
    padding: 20px 20px 10px;
}
.host-form {
    margin: 18px 0 0 50px;
}
.host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-content {
    padding: 0px 15px;
    height: 100%;
}
</style>