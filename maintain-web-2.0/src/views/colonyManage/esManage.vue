<template>
  <div class="esManage" v-loading.fullscreen="fullscreenLoading">
    <el-row class="monitor-content">
      <el-row class="form-time">
        <el-col :span="24" class="colony-Btn">
          <el-radio-group v-model.number="timeChart" size="mini" @change="timeChange">
            <el-radio-button class="proce-radio" v-for="item in timeOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-col>
      </el-row>
      <el-row class="chart-row" v-loading="chartLoading" v-if="Object.keys(chartData)">
        <Echart class="chart-content" :chartData="chartData"></Echart>
      </el-row>
      <div class="noData-div" v-else>暂无数据</div>
    </el-row>
  </div>
</template>

<script>
import { parseTime } from "@/utils/index.js";
import Echart from "./echart.vue";

export default {
  name: "esManage",
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  components: { Echart },
  data() {
    return {
      timeChart: 7,
      esGroup: "",
      activeName: "ES管理",
      moreShow: false,
      contentShow: false,
      contentShow1: false,
      chartLoading: false,
      chartLoading1: false,
      chartShow: true,
      chartShow1: true,
      isShow: false,
      firstShow: false,
      fullscreenLoading: false,
      timeOptions: [
        {
          label: "当天",
          value: 0,
        },
        {
          label: "最近3天",
          value: 3,
        },
        {
          label: "最近5天",
          value: 5,
        },
        {
          label: "最近7天",
          value: 7,
        },
      ],
      baseForm: {
        policeNum: 0,
        alarmNum: 0,
        goodNum: 0,
        iframeSrc: "",
      },
      chartForm: {
        startTime: "",
        endTime: "",
        groupId: null,
      },
      chartData: {},
    };
  },
  watch: {
    id: {
      handler(val) {
        if (val && val >= 0) {
          this.chartForm.groupId = val;
          this.timeChange()
        }
      },
      immediate: true
    }
  },
  methods: {
    //请求数据函数
    getChartData() {
      this.fullscreenLoading = true
      this.$http
        .post("group/list.json", this.qs.stringify(this.chartForm))
        .then((res) => {
          const { data } = res.data;
          this.chartData = data;
          this.fullscreenLoading = false
        })
        .catch((error) => {
          this.fullscreenLoading = false
          th.erFn();
        });
    },
    timeChange(val) {
      let endTime = Date.parse(new Date());
      let startTime = endTime - val * 24 * 60 * 60 * 1000;
      this.chartForm.endTime = parseTime(endTime, "{y}-{m}-{d} 23:59:59");
      this.chartForm.startTime = parseTime(startTime, "{y}-{m}-{d} 00:00:00");
      this.getChartData()
    },
  }
};
</script>

<style scoped>
.esManage {
  height: 100%;
  width: 100%;
  flex: 1;
  background-color: white;
  box-shadow: 0px 4px 10px #ccc;
  border-radius: 0 10px 10px;
}

.form-time {
  margin: 20px 10px 0;
  height: 40px;
  text-align: right;
}

.form-time.hasTwo {
  margin-top: 5px;
  height: 30px;
}

.colony-Btn {
  padding-right: 15px;
}

.chart-content {
  width: 100%;
  height: 100%;
}

.chart-row {
  width: 100%;
  height: calc(100% - 90px);
  padding-top: 0;
}

.chart-row.hasTwo {
  height: calc(100% - 65px);
}

.monitor-content {
  height: 100%;
  padding: 10px 10px 0px;
}

.colony-detail {
  position: absolute;
  top: 7px;
  right: 15px;
}

.status-describe {
  display: inline-block;
  margin-right: 40px;
}

.historyChart-row {
  height: 50%;
}

.host-header {
  height: 30px;
  line-height: 36px;
  font-size: 14px;
  border-bottom: 1px solid #cdd0d3;
}

.host-text {
  display: inline-block;
  height: 30px;
  line-height: 36px;
  border-bottom: 2px solid #5174b4;
  padding: 0 3px;
  color: #203e66;
  font-weight: bold;
}

.host-content {
  padding: 0px 15px;
  height: 100%;
}
</style>