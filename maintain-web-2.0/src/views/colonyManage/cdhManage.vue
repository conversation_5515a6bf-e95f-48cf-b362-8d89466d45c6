<template>
    <div class="cdhManage">
        <div class="colony-detail" v-if="!chartShow">
            <div class="status-describe">
                集群管理错误<span class="num-red"> {{baseForm.policeNum}} </span>个，
                警告<span class="num-yellow"> {{baseForm.alarmNum}} </span>个，
                良好<span class="num-green"> {{baseForm.goodNum}} </span>个
            </div>
            <button 
                class="btn-style" 
                v-if="btn.cdhDetail.value"
                @click="detailBtn">
                <span>CDH管理详情</span>
            </button>
            <button 
                class="btn-style" 
                type="text" 
                v-if='kafkaButtonShow' 
                @click="kafkaDetailBtn">
                <span>{{kafkaButtonName}}</span>
            </button>
            <!-- <button 
                class="btn-style" 
                v-if="btn.cdhMonitorDetail.value"
                @click="detailMonitorBtn('CDH管理监控详情')">
                <span>CDH管理监控详情</span>
            </button>
            <button 
                class="btn-style" 
                v-if="btn.kafkaMonitorDetail.value"
                @click="detailMonitorBtn('kafka监控详情')">
                <span>kafka监控详情</span>
            </button> -->

        </div>
        <el-row class="monitor-content">
            <el-row class='network-row'>
                <el-row class="host-content">
                    <div class="host-header">
                        <div class="host-text">历史信息</div>
                    </div>
                    <el-row class='form-time' v-if="!chartShow">
                        <el-col :span="24" class="colony-Btn">
                            <el-radio-group v-model.number="timeChart" size="mini" @change="timeChange">
                                <el-radio-button 
                                    class="proce-radio" 
                                    v-for="item in timeOptions" 
                                    :key="item.value" 
                                    :label="item.value">
                                    {{item.label}}
                                </el-radio-button>
                            </el-radio-group>
                        </el-col>
                    </el-row>
                    <el-row class="chart-row cdh-row" v-loading='chartLoading'>
                        <Echart class="chart-content" v-if="contentShow" :chartData='chartData'></Echart>
                    </el-row>
                    <div class="noData-div" v-show="chartShow">暂无数据</div>
                </el-row>
            </el-row>
        </el-row>
    </div>
</template>

<script>
import { parseTime } from "@/utils/index.js";
import Echart from "./echart.vue";
import { headerRowClassName } from "@/utils/tableStyle.js";
import getPermission from "@/utils/permissions.js";

export default {
    name: "cdhManage",
    props: { numForm: Object },
    components: { Echart },
    data() {
        return {
            timeChart: 7,
            href: "",
            kafkaUrl: "",
            kafkaButtonName: "",

            activeName: "CDH管理",
            contentShow: false,
            chartLoading: false,
            chartShow: true,
            kafkaButtonShow: false,

            timeOptions: [
                {
                    label: "当天",
                    value: 1
                },
                {
                    label: "过去3天",
                    value: 3
                },
                {
                    label: "过去5天",
                    value: 5
                },
                {
                    label: "过去7天",
                    value: 7
                }
            ],

            chartForm: {
                startTime: "",
                endTime: "",
                groupId: null
            },
            btn: {
                cdhDetail: {
                    name: "CDH管理详情",
                    value: false
                },
                cdhMonitorDetail: {
                    name: "CDH管理监控详情",
                    value: false
                },
                kafkaMonitorDetail: {
                    name: "kafka监控详情",
                    value: false
                }
            },
            baseForm: {
                policeNum: 0,
                alarmNum: 0,
                goodNum: 0,
                iframeSrc: ""
            },
            chartData: {},
            commonData: {}
        };
    },
    methods: {
        initData() {
            this.baseForm = { ...this.numForm };
            this.chartForm.groupId = this.numForm.groupId;
            console.log(this.numForm);
            this.timeChange(this.timeChart);
            this.getkafkaUrl();
            getPermission(this.$route.name, this.btn);
        },

        //请求数据函数
        getChartData(data) {
            let _this = this;

            _this.chartLoading = true;
            _this.$http
                .post("group/list.json", _this.qs.stringify(data))
                .then(res => {
                    _this.chartLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            _this.commonData = res.data.data;
                            _this.formatEchartData(
                                _this.commonData,
                                _this.activeName
                            );
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.chartLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        detailBtn() {
            let _this = this;
            const tempage = window.open("", "_blank");

            _this.$http
                .post("home/redirect.json")
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            const { href } = _this.$router.resolve({
                                name: "cdhHref",
                                query: {
                                    loginUrl: res.data.data.loginUrl,
                                    userName: res.data.data.userName,
                                    password: res.data.data.password
                                }
                            });
                            href && (_this.href = href);
                            tempage.location = href;
                        } else {
                            _this.baseForm.iframeSrc &&
                                window.open(_this.baseForm.iframeSrc);
                        }
                    } else {
                        // res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                        _this.baseForm.iframeSrc &&
                            window.open(_this.baseForm.iframeSrc);
                    }
                })
                .catch(error => {
                    // _this.erFn();
                    this.baseForm.iframeSrc &&
                        window.open(this.baseForm.iframeSrc);
                    console.log(error);
                });
        },
        kafkaDetailBtn() {
            window.open(this.kafkaUrl);
        },
        getkafkaUrl() {
            let _this = this;
            let searchData = {};

            searchData = {
                name: "KafkaPlugin",
                message: JSON.stringify({
                    requestCode: 8,
                    requestName: "getUrl"
                })
            };

            _this.$http
                .post(
                    "plugin/plugin_business/KafkaPlugin/getUrl",
                    _this.qs.stringify(searchData),
                    { timeout: null }
                )
                .then(res => {
                    if (res.data.code == 0) {
                        if (res.data.data) {
                            _this.kafkaButtonShow = true;
                            _this.kafkaButtonName = Object.keys(
                                JSON.parse(res.data.data).url
                            )[0];
                            _this.kafkaUrl = JSON.parse(
                                JSON.parse(res.data.data).url[
                                    _this.kafkaButtonName
                                ]
                            ).url;
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        formatEchartData(data, label) {
            this.chartData = data;
            this.chartData.content = label;
            if (!this.chartData.content) {
                this.contentShow = false;
                this.chartShow = true;
            } else {
                this.contentShow = true;
                this.chartShow = false;
            }
        },

        //功能函数
        detailMonitorBtn(name) {
            switch (name) {
                case "kafka监控详情":
                    this.$router.push({
                        path: "/kafkaDetail"
                    });
                    break;
                case "CDH管理监控详情":
                    this.$router.push({
                        path: "/cdhDetail"
                    });
                    break;
            }
        },
        timeChange(val) {
            let endTime = Date.parse(new Date());
            let startTime = endTime - val * 24 * 60 * 60 * 1000;

            window.onresize = "";
            this.chartForm.endTime = parseTime(endTime, "{y}-{m}-{d} 23:59:59");
            this.chartForm.startTime = parseTime(
                startTime,
                "{y}-{m}-{d} 00:00:00"
            );
            this.getChartData(this.chartForm);
        }
    },
    watch: {
        // href(){
        //     window.open(this.href,'_blank');
        // }
    },
    mounted() {
        this.initData();
    }
};
</script>

<style scoped>
.cdhManage {
    height: 100%;
    width: 100%;
}
.network-row {
    margin: 0 10px;
    /* min-height: 400px; */
    height: 100%;
}
.host-form {
    margin: 18px 0 0 50px;
}
.host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.host-content {
    padding: 10px 15px 0 15px;
    height: 100%;
}
.form-time {
    margin: 20px 10px 0;
    height: 40px;
    text-align: right;
}
.colony-Btn {
    padding-right: 15px;
}
.chart-content {
    width: 100%;
    height: 100%;
}
.chart-row {
    width: 100%;
    height: calc(100% - 90px);
    padding-top: 0;
}
.monitor-content {
    height: 100%;
}
.colony-detail {
    position: absolute;
    top: 7px;
    right: 15px;
}
.status-describe {
    display: inline-block;
    margin-right: 40px;
}
</style>