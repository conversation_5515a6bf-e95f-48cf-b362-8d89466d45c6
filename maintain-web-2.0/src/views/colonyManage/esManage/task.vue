<!-- task管理 -->
<template>
    <div class="agent-content">
        <el-row class="row-content">
            <el-col :span="24" class="content-form">
                <el-col :span="24" class="form-btn">
                    <button 
                        @click.prevent="updateBtn" 
                        class="btn-style">
                        <div class="button-icon button-update"></div>
                        <span>刷新</span>
                    </button>
                    <button @click.prevent="returnWeb" class="btn-return btn-style">
                        <div class="button-return"></div>
                        <span>返回</span>
                    </button>
                </el-col>
            </el-col>
            <el-col :span="24"  class="content-table">
                <el-table
                    :data="dataList" 
                    v-loading='tableLoading' 
                    :row-class-name="bodyClass" 
                    :header-cell-style="tableHeader" 
                    :header-row-class-name="headerClass" 
                    height="100%" class="table-border"
                    v-scroll='loadMore'
                    ref="table">

                    <el-table-column type="expand">
                        <template slot-scope="scope">
                            <el-table 
                            :data="scope.row.children" border
                            class="partition-table"  
                            :header-cell-style="detailHeader" 
                            :header-row-class-name="headerClass"
                            max-height="400">
                                <el-table-column prop='node' label="Node" min-width="180"></el-table-column>
                                <el-table-column prop="id" label="Id" min-width="100"></el-table-column>
                                <el-table-column prop="type" label="Type" min-width="100"></el-table-column>
                                <el-table-column prop="action" label="Action" class-name="overflowColumn" min-width="200">
                                    <template slot-scope="scope">
                                        <el-popover 
                                            :trigger="scope.row.action ? 'hover' : 'manual'" 
                                            placement="top-start">
                                            <p class="popoverContent">{{scope.row.action}}</p>
                                            <div 
                                                slot="reference" 
                                                class="startParam-popover">
                                                {{scope.row.action || '--'}}
                                            </div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="description" label="Description" class-name="overflowColumn" min-width="150">
                                    <template slot-scope="scope">
                                        <el-popover 
                                            :trigger="scope.row.description ? 'hover' : 'manual'" 
                                            placement="top-start" 
                                            width="700" >
                                            <p class="popoverContent">{{scope.row.description}}</p>
                                            <div 
                                                slot="reference" 
                                                class="startParam-popover">
                                                {{scope.row.description || '--'}}
                                            </div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="start_time_in_millis" label="start_time_in_millis" min-width="150" :formatter="formatStartTime"></el-table-column>
                                <el-table-column prop="running_time_in_nanos" label="running_time_in_nanos" min-width="150" :formatter="formatRunning"></el-table-column>
                                <el-table-column prop="cancellable" label="cancellable" min-width="100" align="center">
                                    <template slot-scope="scope">
                                        <el-button 
                                            type="text" 
                                            v-if="scope.row.cancellable">
                                            取消
                                        </el-button>
                                        <span v-else>-</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-table-column>
                    <el-table-column prop='node' label="Node" min-width="180"></el-table-column>
                    <el-table-column prop="id" label="Id" min-width="100"></el-table-column>
                    <el-table-column prop="type" label="Type" min-width="100"></el-table-column>
                    <el-table-column prop="action" label="Action" class-name="overflowColumn" min-width="200">
                        <template slot-scope="scope">
                            <el-popover 
                                :trigger="scope.row.action ? 'hover' : 'manual'" 
                                placement="top-start">
                                <p class="popoverContent">{{scope.row.action}}</p>
                                <div 
                                    slot="reference" 
                                    class="startParam-popover">
                                    {{scope.row.action || '--'}}
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                    <el-table-column prop="description" label="Description" class-name="overflowColumn" min-width="150">
                        <template slot-scope="scope">
                            <el-popover 
                                :trigger="scope.row.description ? 'hover' : 'manual'" 
                                placement="top-start" 
                                width="700">
                                <p class="popoverContent">{{scope.row.description}}</p>
                                <div 
                                    slot="reference" 
                                    class="startParam-popover">
                                    {{scope.row.description || '--'}}
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                    <el-table-column prop="start_time_in_millis" label="start_time_in_millis" min-width="150" :formatter="formatStartTime"></el-table-column>
                    <el-table-column prop="running_time_in_nanos" label="running_time_in_nanos" min-width="150" :formatter="formatRunning"></el-table-column>
                    <el-table-column prop="cancellable" label="cancellable" min-width="100" align="center">
                        <template slot-scope="scope">
                            <el-button 
                                type="text" 
                                v-if="scope.row.cancellable"
                                @click="quitBtn(scope.row)">
                                取消
                            </el-button>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import {
    tableHeaderStyle,
    bodyRowClassName,
    headerRowClassName
} from "@/utils/tableStyle.js";

export default {
    name: "agent-content",
    data() {
        return {
            tableLoading: false,
            currentPage: 0,
            page: 0,
            total: 0,

            dataList: [],
            dataAllList: []
        };
    },
    methods: {
        //请求数据函数
        getDataList() {
            let _this = this;

            _this.tableLoading = true;
            _this.$http
                .get("/api/_tasks?pretty&detailed&group_by=parents")
                .then(res => {
                    _this.tableLoading = false;
                    if (res.status == 200) {
                        _this.total = 0;
                        $.each(res.data.tasks, (index, item) => {
                            _this.total++;
                            _this.dataAllList.push(item);
                        });

                        if (_this.total > 99) {
                            this.page = 1;
                            this.loadMore();
                        } else {
                            _this.dataList = _this.dataAllList;
                        }
                    } else {
                        res.statusText
                            ? _this.waFn(res.statusText)
                            : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.tableLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        deteleRow(data) {
            let _this = this;

            _this.tableLoading = true;
            _this.$http
                .post("/api/_tasks/" + data.node + ":" + data.id + "/_cancel")
                .then(res => {
                    _this.tableLoading = false;
                    if (res.status == 200) {
                        Object.keys(res.data.nodes).length != 0
                            ? _this.suFn("取消成功")
                            : _this.waFn("取消失败");
                    } else {
                        res.statusText
                            ? _this.waFn(res.statusText)
                            : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.tableLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        loadMore() {
            let _this = this;
            if (this.page > this.currentPage) {
                let num = 0,
                    val = _this.currentPage * 100;
                for (let i = val; i < val + 100; i++) {
                    num = i;
                    if (_this.dataAllList[i]) {
                        _this.dataList.push(_this.dataAllList[i]);
                    } else {
                        break;
                    }
                }
                _this.currentPage++;
                if (num == val + 99) {
                    this.page++;
                }
            }
        },
        formatStartTime(row, column, value) {
            if (value) {
                value = new Date(value);
                let YY = value.getFullYear(),
                    MM = value.getMonth() + 1,
                    dd = value.getDate(),
                    HH = value.getHours(),
                    mm = value.getMinutes(),
                    ss = value.getSeconds();

                value =
                    YY +
                    "-" +
                    (MM < 10 ? "0" + MM : MM) +
                    "-" +
                    (dd < 10 ? "0" + dd : dd) +
                    " " +
                    (HH < 10 ? "0" + HH : HH) +
                    ":" +
                    (mm < 10 ? "0" + mm : mm) +
                    ":" +
                    (ss < 10 ? "0" + ss : ss);
                return value;
            } else {
                return "-";
            }
        },
        formatRunning(row, column, value) {
            if (value) {
                value = (value / 1000000).toFixed(2);
                if (value >= 0 && value < 1000) {
                    value = value + "ms";
                } else if (value >= 1000 && value < 3600000) {
                    value = (value / 1000).toFixed(2) + "s";
                } else if (value >= 60000 && value < 3600000) {
                    value = (value / 60000).toFixed(2) + "mins";
                } else if (value >= 3600000 && value < 216000000) {
                    value = (value / 3600000).toFixed(2) + "hours";
                } else if (value >= 216000000 && value < 5184000000) {
                    value = (value / 216000000).toFixed(2) + "days";
                }
                return value;
            } else {
                return "-";
            }
        },

        //功能函数
        updateBtn() {
            this.dataList = [];
            this.dataAllList = [];
            this.page = 0;
            this.currentPage = 0;
            this.getDataList();
        },
        quitBtn(row) {
            row && this.deteleRow(row);
        },
        returnWeb() {
            this.$router.go(-1);
        },
        tableHeader() {
            return "border:0;";
        },
        detailHeader() {
            return tableHeaderStyle();
        },
        bodyClass({ row, index }) {
            return !row.children ? "expand" : "";
        },
        headerClass() {
            return headerRowClassName();
        },
        healthHeader() {
            return tableHeaderStyle();
        }
    },
    directives: {
        scroll: {
            bind: function(el, binding) {
                let selectWarp = el.querySelector(".el-table__body-wrapper");

                selectWarp.addEventListener("scroll", () => {
                    if (
                        selectWarp.scrollHeight -
                            selectWarp.clientHeight -
                            selectWarp.scrollTop <=
                        100
                    ) {
                        let fnc = binding.value;
                        typeof fnc == "function" && fnc();
                    }
                });
            }
        }
    },
    mounted() {
        this.getDataList();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.agent-content {
    width: 98%;
    margin: 0 1% 1%;
    height: 100%;
}
.agent-form {
    margin: 1.5rem;
    padding-right: 1rem;
}
.status-tag {
    color: #fff;
    border: 0;
    width: 40px;
    height: 20px;
    line-height: 20px;
}
.agent-btn {
    text-align: right;
}
.partition-table {
    margin: 20px;
    width: calc(100% - 40px);
}
.form-btn {
    line-height: 5rem;
    text-align: right;
    padding-right: 2.5rem;
}
.button-icon {
    background: url("../../../assets/images/agentManage/icon-Agent.png");
    display: inline-block;
    height: 16px;
    width: 16px;
    background-size: cover !important;
    vertical-align: sub;
}
.button-update {
    background-position: 0px -2px;
}
.btn-style:hover .button-update {
    background-position: 0px -52px;
}
.btn-return {
    background-color: #1e85e6;
    color: #fff;
    padding-left: 5px;
    margin-left: 10px;
}
.btn-return:hover {
    background-color: #2d91ef;
    color: #fff;
}
.button-return {
    display: inline-block;
    height: 16px;
    width: 25px;
    background-size: cover !important;
    vertical-align: sub;
    background: url("../../../assets/images/hardWare/icon-detail.png");
    background-position: 0 26px;
}
</style>