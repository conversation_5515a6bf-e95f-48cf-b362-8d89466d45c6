<!-- ES监控详情 -->
<template>
  <div class="es-detail" v-loading.fullscreen="fullscreenLoading">
    <div class="detail-content">
      <el-row>
        <el-col :span="20">
          <el-col :span="4">
            <div class="top-title">ES管理监控详情</div>
          </el-col>
        </el-col>
        <el-col :span="4" class="return-col">
          <button @click.prevent="returnWeb" class="btn-return btn-style">
            <div class="button-icon button-return"></div>
            <span>返回</span>
          </button>
        </el-col>
      </el-row>
      <el-row class="content-row">
        <template>
          <el-tabs v-model="activeName" class="es-tabs" @tab-click="tabClick">
            <el-tab-pane name="base" label="基本信息" class="es-tanPane">
              <el-row class="tab-row">
                <el-row class="hostForm-row">
                  <el-col :span="24">
                    <el-form class="host-form">
                      <el-row class="base-row">
                        <el-row>
                          <h4>关闭索引</h4>
                        </el-row>
                        <el-row class="base-content">
                          <el-col :span="8">
                            <el-form-item class="label-color">
                              <div slot="label">
                                <!-- <div class="button-icon button-ip"></div> -->
                                <span>关闭索引状态</span>
                              </div>
                              <el-switch
                                v-model="form.closeStatus"
                                :disabled="!btn.closeIndexStatus.value"
                                active-color="#25ce88"
                                inactive-color="#cfcfcf"
                                class="font-switch"
                                v-if="
                                  form.closeStatus == true ||
                                  form.closeStatus == false
                                "
                                @click.native="
                                  operateStatus(btn.closeIndexStatus)
                                "
                              >
                              </el-switch>
                              <span v-else>-</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="8">
                            <el-form-item class="label-color">
                              <div slot="label">
                                <!-- <div class="button-icon button-ip"></div> -->
                                <span>关闭索引天数</span>
                              </div>
                              <el-input
                                class="indexInput"
                                v-model="form.closeDay"
                                clearable
                              ></el-input>
                              <el-button type="text" @click="editClose"
                                >修改</el-button
                              >
                              <!-- <span>{{staticForm.monitorTime || '-'}}</span> -->
                            </el-form-item>
                          </el-col>
                          <el-col :span="8">
                            <el-form-item class="label-color">
                              <div slot="label">
                                <!-- <div class="button-icon button-ip"></div> -->
                                <span>关闭索引任务</span>
                              </div>
                              <el-button type="text" @click="ruleClose"
                                >立即执行</el-button
                              >
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-row>
                      <el-row class="base-row">
                        <el-row>
                          <h4>删除索引</h4>
                        </el-row>
                        <el-row class="base-content">
                          <el-col :span="16" class="explain-col">
                            <el-form-item class="label-color delete-status">
                              <div slot="label">
                                <!-- <div class="button-icon button-ip"></div> -->
                                <span>删除索引状态</span>
                              </div>
                              <el-switch
                                v-model="form.deleteStatus"
                                :disabled="!btn.deleteIndexStatus.value"
                                active-color="#25ce88"
                                inactive-color="#cfcfcf"
                                class="font-switch"
                                v-if="
                                  form.closeStatus == true ||
                                  form.closeStatus == false
                                "
                                @click.native="
                                  operateStatus(btn.deleteIndexStatus)
                                "
                              >
                              </el-switch>
                              <span v-else>-</span>
                            </el-form-item>
                            <p v-if="form.deleteRemark">
                              说明：{{ form.deleteRemark }}
                            </p>
                          </el-col>
                        </el-row>
                      </el-row>
                      <el-row class="base-row">
                        <el-row>
                          <h4>空间预算</h4>
                        </el-row>
                        <el-row class="base-content">
                          <el-col :span="8">
                            <el-form-item class="label-color">
                              <div slot="label">
                                <!-- <div class="button-icon button-ip"></div> -->
                                <span>索引剩余存放天数</span>
                              </div>
                              <span>
                                {{
                                  !form.indexFreeDay && form.indexFreeDay != 0
                                    ? "-"
                                    : form.indexFreeDay
                                }}
                              </span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="8">
                            <el-form-item class="label-color">
                              <div slot="label">
                                <!-- <div class="button-icon button-ip"></div> -->
                                <span>ES磁盘总占用</span>
                              </div>
                              <span>{{ form.esPercentage || "-" }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-row>
                    </el-form>
                  </el-col>
                </el-row>
              </el-row>
            </el-tab-pane>
            <el-tab-pane name="colony" label="集群信息" class="es-tanPane">
              <el-row class="tab-row">
                <el-row class="table-row">
                  <el-table
                    :data="clusterList"
                    border
                    :row-class-name="bodyClass"
                    :header-cell-style="healthHeader"
                    :header-row-class-name="headerClass"
                    height="100%"
                  >
                    <el-table-column prop="status" label="状态">
                      <template slot-scope="scope">
                        <el-tag
                          v-if="scope.row.status"
                          size="small"
                          class="status-tag"
                          :color="scope.row.status"
                        >
                          {{ scope.row.content }}
                        </el-tag>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="CLUSTER_NAME"
                      label="集群名称"
                    ></el-table-column>
                    <el-table-column
                      prop="NODE_NUMBER"
                      label="集群节点数量"
                    ></el-table-column>
                    <el-table-column
                      prop="NODE_NAME_LIST"
                      label="节点名称集合"
                      class-name="overflowColumn"
                    >
                      <template slot-scope="scope">
                        <el-popover
                          class="popoverSpan"
                          trigger="hover"
                          placement="top-start"
                        >
                          <p class="popoverContent">
                            {{ scope.row.NODE_NAME_LIST }}
                          </p>
                          <div slot="reference" class="name-wrapper">
                            <span>{{ scope.row.NODE_NAME_LIST || "-" }}</span>
                          </div>
                        </el-popover>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="INDEX_NUMBER"
                      label="索引数量"
                    ></el-table-column>
                    <el-table-column
                      prop="CLOSE_INDEX_NUMBER"
                      label="关闭索引数量"
                    ></el-table-column>
                    <el-table-column
                      prop="OPEN_INDEX_NUMBER"
                      label="打开索引数量"
                    ></el-table-column>
                    <el-table-column
                      prop="CHARDS_NUMBER"
                      label="分片数量"
                    ></el-table-column>
                    <el-table-column
                      prop="DOCS_NUMBER"
                      label="文档总数量"
                    ></el-table-column>
                    <el-table-column
                      prop="DOCS_DELETE_NUMBER"
                      label="文档删除总数量"
                    ></el-table-column>
                    <el-table-column
                      prop="CLUSTER_DATA_SIZE_BYTES"
                      label="占用磁盘空间"
                    >
                      <template slot-scope="scope">
                        <span>{{
                          currencyCapacity(
                            Number(scope.row.CLUSTER_DATA_SIZE_BYTES * 8)
                          )
                        }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-row>
              </el-row>
            </el-tab-pane>
            <el-tab-pane name="node" label="节点信息" class="es-tanPane">
              <el-row class="tab-row">
                <el-row class="table-row">
                  <el-table
                    :data="nodeList"
                    border
                    :row-class-name="bodyClass"
                    :header-cell-style="healthHeader"
                    :header-row-class-name="headerClass"
                    height="100%"
                  >
                    <el-table-column
                      prop="NODE_NAME"
                      label="节点名称"
                      class-name="overflowColumn"
                      min-width="100"
                    >
                      <template slot-scope="scope">
                        <el-popover
                          class="popoverSpan"
                          trigger="hover"
                          placement="top-start"
                        >
                          <p class="popoverContent">
                            {{ scope.row.NODE_NAME }}
                          </p>
                          <div slot="reference" class="name-wrapper">
                            <span>{{ scope.row.NODE_NAME || "-" }}</span>
                          </div>
                        </el-popover>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="NODE_ROLE"
                      label="节点角色"
                      class-name="overflowColumn"
                      min-width="100"
                    >
                      <template slot-scope="scope">
                        <el-popover
                          class="popoverSpan"
                          trigger="hover"
                          placement="top-start"
                        >
                          <p class="popoverContent">
                            {{ scope.row.NODE_ROLE }}
                          </p>
                          <div slot="reference" class="name-wrapper">
                            <span>{{ scope.row.NODE_ROLE || "-" }}</span>
                          </div>
                        </el-popover>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="HOST_ADDRESS"
                      label="服务器IP"
                      min-width="150"
                    ></el-table-column>
                    <el-table-column
                      prop="TCP_PORT"
                      label="TCP端口"
                      min-width="80"
                    ></el-table-column>
                    <el-table-column
                      prop="OS_NAME_AND_VERSION"
                      label="系统名称和版本"
                      class-name="overflowColumn"
                      min-width="100"
                    >
                      <template slot-scope="scope">
                        <el-popover
                          class="popoverSpan"
                          trigger="hover"
                          placement="top-start"
                        >
                          <p class="popoverContent">
                            {{ scope.row.OS_NAME_AND_VERSION }}
                          </p>
                          <div slot="reference" class="name-wrapper">
                            <span>{{
                              scope.row.OS_NAME_AND_VERSION || "-"
                            }}</span>
                          </div>
                        </el-popover>
                      </template>
                    </el-table-column>
                    <el-table-column label="内存剩余情况" min-width="180">
                      <template slot-scope="scope">
                        <div class="progress-text">
                          <div>
                            {{
                              scope.row.MEM_TOTAL && scope.row.MEM_TOTAL != 0
                                ? currencyCapacity(scope.row.MEM_FREE) +
                                  "/" +
                                  currencyCapacity(scope.row.MEM_TOTAL)
                                : "-"
                            }}
                          </div>
                          <span
                            v-if="
                              scope.row.MEM_TOTAL && scope.row.MEM_TOTAL != 0
                            "
                          >
                            {{
                              scope.row.memoryFreePercent &&
                              scope.row.memoryFreePercent + "%"
                            }}
                          </span>
                        </div>
                        <el-progress
                          v-if="scope.row.MEM_TOTAL && scope.row.MEM_TOTAL != 0"
                          :percentage="scope.row.memoryFreePercent"
                          :show-text="false"
                          color="#8aa7f4"
                        >
                        </el-progress>
                      </template>
                    </el-table-column>
                    <el-table-column label="硬盘剩余情况" min-width="180">
                      <template slot-scope="scope">
                        <div class="progress-text">
                          <div>
                            {{
                              scope.row.DISK_TOTAL && scope.row.DISK_TOTAL != 0
                                ? currencyCapacity(scope.row.DISK_AVAILABLE) +
                                  "/" +
                                  currencyCapacity(scope.row.DISK_TOTAL)
                                : "-"
                            }}
                          </div>
                          <span
                            v-if="
                              scope.row.DISK_TOTAL && scope.row.DISK_TOTAL != 0
                            "
                          >
                            {{
                              scope.row.monitorDirPercent &&
                              scope.row.monitorDirPercent + "%"
                            }}
                          </span>
                        </div>
                        <el-progress
                          v-if="
                            scope.row.DISK_TOTAL && scope.row.DISK_TOTAL != 0
                          "
                          :percentage="scope.row.monitorDirPercent"
                          :show-text="false"
                          color="#8aa7f4"
                        >
                        </el-progress>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="JVM_NAME"
                      label="jvm名称"
                      class-name="overflowColumn"
                      min-width="100"
                    >
                      <template slot-scope="scope">
                        <el-popover
                          class="popoverSpan"
                          trigger="hover"
                          placement="top-start"
                        >
                          <p class="popoverContent">{{ scope.row.JVM_NAME }}</p>
                          <div slot="reference" class="name-wrapper">
                            <span>{{ scope.row.JVM_NAME || "-" }}</span>
                          </div>
                        </el-popover>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="JVM_VERSION"
                      label="jvm版本号"
                      min-width="80"
                    ></el-table-column>
                    <el-table-column
                      prop="NODE_PLUGINS"
                      label="节点已安装插件"
                      class-name="overflowColumn"
                      min-width="120"
                    >
                      <template slot-scope="scope">
                        <el-popover
                          class="popoverSpan"
                          trigger="hover"
                          placement="top-start"
                        >
                          <p class="popoverContent">
                            {{ scope.row.NODE_PLUGINS }}
                          </p>
                          <div slot="reference" class="name-wrapper">
                            <span>{{ scope.row.NODE_PLUGINS || "-" }}</span>
                          </div>
                        </el-popover>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="NODE_CREATE_TIME"
                      label="节点创建时间"
                      :formatter="formatTime"
                      min-width="150"
                    ></el-table-column>
                    <el-table-column
                      prop="NODE_VERSION"
                      label="ES节点版本号"
                      min-width="100"
                    ></el-table-column>
                  </el-table>
                </el-row>
              </el-row>
            </el-tab-pane>
            <el-tab-pane name="index" label="索引信息" class="es-tanPane">
              <el-row class="tab-row">
                <el-row type="flex" class="index-form">
                  <el-col :span="24" class="form-search">
                    <el-col :span="6">
                      <span>索引名称:</span>
                      <el-input
                        v-model="diskForm.indexName"
                        clearable
                        class="diskInput"
                      ></el-input>
                    </el-col>
                    <el-col :span="6">
                      <span>索引状态:</span>
                      <el-select v-model="diskForm.indexStatus" clearable>
                        <el-option
                          v-for="(item, index) in recordOption"
                          :key="index"
                          :value="item.value"
                          :label="item.label"
                        >
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="9" class="disk-col">
                      <span>磁盘占用空间:</span>
                      <el-input
                        class="diskInput"
                        v-model="storeSizeGet"
                        clearable
                      >
                        <el-select
                          slot="append"
                          class="diskSelect"
                          v-model="selectStartValue"
                          @change="diskchange($event, 'selectStartValue')"
                        >
                          <el-option
                            v-for="(item, index) in diskSpaceOption"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </el-input>
                      <span>-</span>
                      <el-input
                        class="diskInput"
                        v-model="storeSizelet"
                        clearable
                      >
                        <el-select
                          slot="append"
                          class="diskSelect"
                          v-model="selectEndValue"
                          @change="diskchange($event, 'selectEndValue')"
                        >
                          <el-option
                            v-for="(item, index) in diskSpaceOption"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </el-input>
                    </el-col>
                    <el-col :span="3" class="button-col">
                      <button class="btn-style" @click="diskSearchBtn">
                        <div class="button-form button-search"></div>
                        <span>查询</span>
                      </button>
                      <!-- <button class="btn-style" @click.prevent="searchBtn">
                                                <div class="button-icon button-update"></div>
                                                <span>重置</span>
                                            </button> -->
                    </el-col>
                  </el-col>
                </el-row>
                <el-row class="indexTable-row">
                  <el-table
                    :data="indexList"
                    border
                    :row-class-name="bodyClass"
                    :header-cell-style="healthHeader"
                    :header-row-class-name="headerClass"
                    height="100%"
                  >
                    <el-table-column
                      prop="HEALTH"
                      label="索引状态"
                      min-width="100"
                    >
                      <template slot-scope="scope">
                        <el-tag
                          v-if="scope.row.status"
                          size="small"
                          class="status-tag"
                          :color="scope.row.status"
                        >
                          {{ scope.row.content }}
                        </el-tag>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column prop="HEALTH" label="索引状态"></el-table-column> -->
                    <el-table-column
                      prop="STATUS"
                      label="开启状态"
                      :formatter="formatOpen"
                      min-width="100"
                    ></el-table-column>
                    <el-table-column
                      prop="INDEX"
                      label="索引名称"
                      min-width="180"
                    ></el-table-column>
                    <el-table-column
                      prop="PRI"
                      label="索引分片"
                      min-width="100"
                    ></el-table-column>
                    <el-table-column
                      prop="REP"
                      label="分片的副本数"
                      min-width="100"
                    ></el-table-column>
                    <el-table-column
                      prop="DOCS_COUNT"
                      label="索引文档数"
                      min-width="100"
                    ></el-table-column>
                    <el-table-column
                      prop="DOCS_DELETE"
                      label="索引删除文档数"
                      min-width="120"
                    ></el-table-column>
                    <el-table-column
                      prop="STORE_SIZE"
                      label="索引总占用空间"
                      min-width="120"
                    >
                      <template slot-scope="scope">
                        <span>{{
                          currencyCapacity(Number(scope.row.STORE_SIZE * 8))
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="PRI_STORE_SIZE"
                      label="索引分片占用空间"
                      min-width="120"
                    >
                      <template slot-scope="scope">
                        <span>{{
                          currencyCapacity(Number(scope.row.PRI_STORE_SIZE * 8))
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="EAL_CAP_TIME"
                      label="最早捕获时间"
                      :formatter="formatTime"
                      min-width="180"
                    ></el-table-column>
                    <el-table-column
                      prop="LAL_CAP_TIME"
                      label="最近捕获时间"
                      :formatter="formatTime"
                      min-width="180"
                    ></el-table-column>
                    <el-table-column
                      prop="EAL_CREATE_TIME"
                      label="最早入库时间"
                      :formatter="formatTime"
                      min-width="180"
                    ></el-table-column>
                    <el-table-column
                      prop="LAL_CREATE_TIME"
                      label="最近入库时间"
                      :formatter="formatTime"
                      min-width="180"
                    ></el-table-column>
                    <el-table-column
                      prop="REMARE"
                      label="备注"
                      min-width="100"
                      class-name="overflowColumn"
                    >
                      <template slot-scope="scope">
                        <el-popover
                          class="popoverSpan"
                          trigger="hover"
                          placement="top-start"
                        >
                          <p class="popoverContent">{{ scope.row.REMARE }}</p>
                          <div slot="reference" class="name-wrapper">
                            <span>{{ scope.row.REMARE || "-" }}</span>
                          </div>
                        </el-popover>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="STATIS_SUCCESS"
                      label="索引统计完成情况"
                      :formatter="formatState"
                      min-width="120"
                    ></el-table-column>
                    <!-- <el-table-column prop="CREATE_TIME" label="节点信息创建时间" :formatter="formatTime"></el-table-column> -->
                  </el-table>
                </el-row>
                <el-row class="pagination-row">
                  <el-col :span="24" class="table-business">
                    <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="diskForm.pageNum"
                      :page-sizes="[20, 50, 100]"
                      :page-size="diskForm.size"
                      background
                      layout="total,sizes,prev,pager,next,jumper"
                      :total="diskForm.total"
                    >
                    </el-pagination>
                  </el-col>
                </el-row>
              </el-row>
            </el-tab-pane>
            <el-tab-pane name="operation" label="操作记录" class="es-tanPane">
              <el-row type="flex" class="index-form">
                <el-col :span="24" class="form-search">
                  <el-col :span="6">
                    <span>索引名称:</span>
                    <el-input
                      v-model="operationForm.indexName"
                      clearable
                      class="diskInput"
                    ></el-input>
                  </el-col>
                  <el-col :span="6">
                    <span>操作类型:</span>
                    <el-select v-model="operationForm.recordType" clearable>
                      <el-option
                        v-for="(item, index) in recordOption"
                        :key="index"
                        :value="item.value"
                        :label="item.label"
                      >
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="9">
                    <span>操作时间:</span>
                    <el-date-picker
                      v-model="operateTime"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholde="开始日期"
                      end-placeholde="结束日期"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      @change="operateChange"
                    >
                    </el-date-picker>
                  </el-col>
                  <el-col :span="3" class="button-col">
                    <button class="btn-style" @click="operateSearchBtn">
                      <div class="button-form button-search"></div>
                      <span>查询</span>
                    </button>
                    <!-- <button class="btn-style" @click.prevent="searchBtn">
                                            <div class="button-icon button-update"></div>
                                            <span>重置</span>
                                        </button> -->
                  </el-col>
                </el-col>
              </el-row>
              <el-row class="operationTable-row">
                <el-table
                  :data="operationList"
                  border
                  :row-class-name="bodyClass"
                  :header-cell-style="healthHeader"
                  :header-row-class-name="headerClass"
                  height="100%"
                >
                  <el-table-column
                    prop="INDEX_NAME"
                    label="索引名称"
                    min-width="100"
                  ></el-table-column>
                  <el-table-column
                    prop="RECORD_TYPE"
                    label="操作类型"
                    :formatter="formatOpen"
                    min-width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="CREATE_TIME"
                    label="创建时间"
                    :formatter="formatTime"
                    min-width="100"
                  ></el-table-column>
                  <el-table-column
                    prop="REMARK"
                    label="备注"
                    min-width="100"
                  ></el-table-column>
                </el-table>
              </el-row>
              <el-row class="pagination-row">
                <el-col :span="24" class="table-business">
                  <el-pagination
                    @size-change="operationSizeChange"
                    @current-change="operationCurrentChange"
                    :current-page="operationForm.pageNum"
                    :page-sizes="[20, 50, 100]"
                    :page-size="operationForm.size"
                    layout="total,sizes,prev,pager,next,jumper"
                    background
                    :total="operationForm.total"
                  >
                  </el-pagination>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </template>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  headerRowClassName,
  bodyRowClassName,
  tableHeaderStyle,
} from "@/utils/tableStyle.js";
import { parseTime } from "@/utils/index.js";
import getPermission from "@/utils/permissions.js";

export default {
  name: "es-detail",
  data() {
    return {
      activeName: "base",
      selectStartValue: 1024,
      selectEndValue: 1024,
      storeSizeGet: null,
      storeSizelet: null,

      fullscreenLoading: false,

      clusterList: [],
      nodeList: [],
      indexList: [],
      operationList: [],
      operateTime: [],
      diskSpaceOption: [
        {
          label: "KB",
          value: 1024,
        },
        {
          label: "MB",
          value: 1048579,
        },
        {
          label: "GB",
          value: 1073741824,
        },
      ],
      recordOption: [
        {
          label: "开启",
          value: "open",
        },
        {
          label: "关闭",
          value: "close",
        },
        {
          label: "删除",
          value: "delete",
        },
      ],

      form: {
        closeStatus: null,
        deleteStatus: null,
        deleteRemark: null,
        closeDay: null,
        deleteDay: null,
        indexFreeDay: "",
        esPercentage: "",
      },
      diskForm: {
        pageNum: 1,
        startNum: 0,
        size: 20,
        total: 0,
        indexStatus: null,
        indexName: null,
        storeSizeGet: null,
        storeSizelet: null,
      },
      operationForm: {
        pageNum: 1,
        startNum: 0,
        size: 20,
        total: 0,
        recordType: null,
        indexName: null,
        createGet: null,
        createlet: null,
      },
      btn: {
        closeIndexStatus: {
          name: "修改关闭索引状态",
          value: false,
        },
        deleteIndexStatus: {
          name: "修改删除索引状态",
          value: false,
        },
      },
    };
  },
  methods: {
    initMethods() {
      getPermission("集群管理", this.btn);
      this.getStatus();
      this.tabClick({
        name: this.activeName,
      });
    },

    //请求数据函数
    getDataList(data) {
      let _this = this;
      let name = "",
        requestName = "";

      name = data.name;
      requestName = JSON.parse(data.message).requestName;
      _this.fullscreenLoading = true;

      _this.$http
        .post(
          "plugin/plugin_business/" + name + "/" + requestName,
          _this.qs.stringify(data)
        )
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            res.data.data && _this.initData(data, res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //处理数据函数
    initData(req, res) {
      let requestCode = JSON.parse(req.message).requestCode;

      if (requestCode != 19 && requestCode != 20 && requestCode != 16) {
        res = JSON.parse(res);
      }
      switch (requestCode) {
        case 1:
          res.success && (this.form.closeDay = Number(res.number));
          break;
        case 2:
          res.success && (this.form.deleteDay = Number(res.number));
          break;
        case 3:
          if (res.result == 1) {
            this.suFn("修改成功");
          } else {
            this.waFn("修改失败");
          }
          break;
        case 4:
          res.result == 1 && this.suFn("修改成功");
          break;
        case 5:
          res && this.formatStatus(res, "CLUSTER_STATE");
          this.clusterList = res;
          break;
        case 6:
          res && this.formatPercent(res);
          this.nodeList = res;
          break;
        case 7:
          this.diskForm.total = res.total;
          if (res.data && res.data.length != 0) {
            this.formatStatus(res.data, "HEALTH");
            this.indexList = res.data;
          }
          break;
        case 10:
          if (res == true) {
            let data = {
              name: "EsPlugin",
              message: JSON.stringify({
                requestCode: 19,
                requestName: "getClosePowerStatus",
              }),
            };

            this.suFn("修改成功");
            this.getDataList(data);
          } else {
            this.waFn("修改失败");
          }
          break;
        case 13:
          if (res == true) {
            let data = {
              name: "EsPlugin",
              message: JSON.stringify({
                requestCode: 20,
                requestName: "getDeletePowerStatus",
              }),
            };

            this.suFn("修改成功");
            this.getDataList(data);
          } else {
            this.waFn("修改失败");
          }
          break;
        case 15:
          (res || res == 0) && (this.form.indexFreeDay = res);
          break;
        case 16:
          this.suFn(res);
          break;
        case 17:
          res && (this.form.esPercentage = res + "%");
          break;
        case 18:
          this.operationForm.total = res.total;
          res.data && (this.operationList = res.data);
          break;
        case 19:
          this.form.closeStatus = this.formatOperate(res);
          break;
        case 20:
          let data = res && JSON.parse(res);
          this.form.deleteStatus = this.formatOperate(data.power || "false");
          this.form.deleteRemark = data.message;
          break;
      }
    },
    formatOperate(data) {
      switch (data) {
        case "false":
          data = false;
          break;
        case "true":
          data = true;
          break;
        case "null":
          data = null;
          this.waFn("数据库暂无此选项");
          break;
        case "exception":
          data = null;
          this.waFn("数据库连接出错");
          break;
      }
      return data;
    },
    formatPercent(data) {
      data.forEach((item) => {
        item.MEM_TOTAL &&
          item.MEM_TOTAL != 0 &&
          (item.memoryFreePercent = Number(
            ((Number(item.MEM_FREE) / Number(item.MEM_TOTAL)) * 100).toFixed(2)
          ));
        item.DISK_TOTAL &&
          item.DISK_TOTAL != 0 &&
          (item.monitorDirPercent = Number(
            (
              (Number(item.DISK_AVAILABLE) / Number(item.DISK_TOTAL)) *
              100
            ).toFixed(2)
          ));
      });
    },
    formatStatus(data, name) {
      data.forEach((row) => {
        name == "HEALTH" && (row[name] = row[name].toUpperCase());
        switch (row[name]) {
          case "GREEN":
            row["status"] = "#25ce88";
            row["content"] = "良好";
            break;
          case "YELLOW":
            row["status"] = "#fb843b";
            row["content"] = "警告";
            break;
          case "RED":
            row["status"] = "#f93846";
            row["content"] = "错误";
            break;
        }
      });
      return data;
    },
    formatTime(row, column, value) {
      if (value) {
        value = new Date(value);
        let YY = value.getFullYear(),
          MM = value.getMonth() + 1,
          dd = value.getDate(),
          HH = value.getHours(),
          mm = value.getMinutes(),
          ss = value.getSeconds();

        value =
          YY +
          "-" +
          (MM < 10 ? "0" + MM : MM) +
          "-" +
          (dd < 10 ? "0" + dd : dd) +
          " " +
          (HH < 10 ? "0" + HH : HH) +
          ":" +
          (mm < 10 ? "0" + mm : mm) +
          ":" +
          (ss < 10 ? "0" + ss : ss);
        return value;
      } else {
        return "-";
      }
    },
    formatOpen(row, column, value) {
      switch (value) {
        case "open":
          value = "开启";
          break;
        case "close":
          value = "关闭";
          break;
        case "delete":
          value = "删除";
          break;
      }
      return value;
    },
    formatState(row, column, value) {
      switch (value) {
        case "true":
          value = "成功";
          break;
        case "false":
          value = "失败";
          break;
      }
      return value;
    },
    currencyCapacity(value) {
      if (value || value == 0) {
        if (value >= 0 && value < 8) {
          value = value + "bit";
        } else if (value >= 8 && value < 8192) {
          value = (value / 8).toFixed(2) + "bytes";
        } else if (value >= 8192 && value < 8388608) {
          value = (value / 8192).toFixed(2) + "KB";
        } else if (value >= 8388608 && value < 8589934592) {
          value = (value / 8388608).toFixed(2) + "MB";
        } else if (value >= 8589934592 && value < 8796093022208) {
          value = (value / 8589934592).toFixed(2) + "GB";
        } else if (value >= 8796093022208) {
          value = (value / 8796093022208).toFixed(2) + "TB";
        }
        return value;
      } else {
        return "-";
      }
    },
    checkDiskFree(value) {
      let vin = /^(\+?[0-9][0-9]{0,5})$/;
      let vin1 = /^[\d]/g;

      if (vin.test(value) && vin1.test(value)) {
        return true;
      } else {
        return false;
      }
    },
    formatRequestData(data) {
      $.each(data, (index, item) => {
        if (!item && item !== null && item !== 0) {
          data[index] = null;
        }
      });
      return data;
    },

    //功能函数
    returnWeb() {
      this.$router.go(-1);
    },
    operateStatus(row) {
      let data = {},
        confirmContent = "";

      if (row.value == true) {
        switch (row.name) {
          case "修改关闭索引状态":
            this.form.closeStatus = !this.form.closeStatus;
            data = {
              name: "EsPlugin",
              message: JSON.stringify({
                requestCode: 10,
                requestName: "closeIndexPower",
                param: String(!this.form.closeStatus),
              }),
            };
            confirmContent = "您确定要改变关闭索引的状态吗？";
            break;
          case "修改删除索引状态":
            this.form.deleteStatus = !this.form.deleteStatus;
            data = {
              name: "EsPlugin",
              message: JSON.stringify({
                requestCode: 13,
                requestName: "deleteIndexPower",
                param: String(!this.form.deleteStatus),
              }),
            };
            confirmContent = "您确定要改变删除索引的状态吗？";
            break;
        }

        this.$confirm(confirmContent, "提示", {
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
          closeOnClickModal: false,
        })
          .then(() => {
            this.getDataList(data);
          })
          .catch(() => {});
      } else {
        this.waFn("暂无权限");
      }
    },
    tabClick(obj) {
      let name = obj.name,
        data = {};

      switch (name) {
        case "base":
          this.getBaseForm();
          break;
        case "colony":
          data = {
            name: "EsPlugin",
            message: JSON.stringify({
              requestCode: 5,
              requestName: "getClusterState",
              param: 100,
            }),
          };
          this.getDataList(data);
          break;
        case "node":
          data = {
            name: "EsPlugin",
            message: JSON.stringify({
              requestCode: 6,
              requestName: "getNodeState",
            }),
          };
          this.getDataList(data);
          break;
        case "index":
          data = {
            name: "EsPlugin",
            message: JSON.stringify({
              requestCode: 7,
              requestName: "getIndexState",
              startNum: 0,
              size: 10,
            }),
          };
          this.resetIndex();
          this.getDataList(data);
          break;
        case "operation":
          data = {
            name: "EsPlugin",
            message: JSON.stringify({
              requestCode: 18,
              requestName: "getCloseOrOpenOrCloseRecord",
            }),
          };
          this.resetOperation();
          this.getDataList(data);
          break;
      }
    },
    getBaseForm() {
      let data = [
        {
          name: "EsPlugin",
          message: JSON.stringify({
            requestCode: 19,
            requestName: "getClosePowerStatus",
          }),
        },
        {
          name: "EsPlugin",
          message: JSON.stringify({
            requestCode: 20,
            requestName: "getDeletePowerStatus",
          }),
        },
        {
          name: "EsPlugin",
          message: JSON.stringify({
            requestCode: 1,
            requestName: "getIndexCloseNumber",
          }),
        },
        {
          name: "EsPlugin",
          message: JSON.stringify({
            requestCode: 2,
            requestName: "getIndexDeleteNumber",
          }),
        },
        {
          name: "EsPlugin",
          message: JSON.stringify({
            requestCode: 15,
            requestName: "indexStoageNumber",
          }),
        },
        {
          name: "EsPlugin",
          message: JSON.stringify({
            requestCode: 17,
            requestName: "getCalculatedPercentage",
          }),
        },
      ];
      data.forEach((item) => {
        this.getDataList(item);
      });
    },
    setBtn(val) {
      let data = {};

      switch (val) {
        case "close":
          if (this.checkDay(this.form.closeDay)) {
            data = {
              name: "EsPlugin",
              message: JSON.stringify({
                requestCode: 3,
                requestName: "updateIndexCloseNumber",
                param: this.form.closeDay,
              }),
            };
            this.getDataList(data);
          }
          break;
        case "delete":
          if (this.checkDay(this.form.deleteDay)) {
            data = {
              name: "EsPlugin",
              message: JSON.stringify({
                requestCode: 4,
                requestName: "updateIndexDeleteNumber",
                param: this.form.closeDay,
              }),
            };
            this.getDataList(data);
          }
          break;
      }
    },
    editClose() {
      let value = this.form.closeDay,
        data = {};

      if (value == "" || value == null) {
        this.waFn("请输入关闭索引天数");
      } else {
        if (this.checkDiskFree(value)) {
          data = {
            name: "EsPlugin",
            message: JSON.stringify({
              requestCode: 3,
              requestName: "updateIndexCloseNumber",
              param: Number(value),
            }),
          };
          this.getDataList(data);
        } else {
          this.waFn("关闭索引天数只能为0-999999的整数");
        }
      }
    },
    ruleClose() {
      let data = {
        name: "EsPlugin",
        message: JSON.stringify({
          requestCode: 16,
          requestName: "ruleCloseIndexRun",
        }),
      };

      this.getDataList(data);
    },
    checkDay(value) {
      let vin = /^\+?[1-9][0-9]*$/;
      let vin1 = /^[\d]/g;

      if (value == "" || value == null) {
        return false;
      } else {
        if (vin.test(value) && vin1.test(value)) {
          return true;
        } else {
          return false;
        }
      }
    },
    diskchange(val, name) {
      switch (name) {
        case "selectStartValue":
          this.storeSizeGet = null;
          break;
        case "selectEndValue":
          this.storeSizelet = null;
          break;
      }
    },
    diskSearchBtn() {
      let storeSizeGet = this.storeSizeGet;
      let storeSizelet = this.storeSizelet;
      let data = {};
      let message = {
        requestCode: 7,
        requestName: "getIndexState",
      };

      this.diskForm.storeSizeGet = storeSizeGet;
      this.diskForm.storeSizelet = storeSizelet;
      if (storeSizeGet || storeSizelet) {
        if (
          this.checkDiskFree(storeSizeGet) &&
          this.checkDiskFree(storeSizelet)
        ) {
          this.diskForm.storeSizeGet =
            Number(storeSizeGet) * this.selectStartValue;
          this.diskForm.storeSizelet =
            Number(storeSizelet) * this.selectEndValue;
        } else {
          this.waFn("磁盘占用空间只能输入0-999999整数");
          return;
        }
      }
      this.indexList = [];
      this.diskForm.total = 0;

      this.diskForm = this.formatRequestData(this.diskForm);
      Object.assign(message, this.diskForm);

      data = {
        name: "EsPlugin",
        message: JSON.stringify(message),
      };
      this.getDataList(data);
    },
    operateSearchBtn() {
      let data = {};
      let message = {
        requestCode: 18,
        requestName: "getCloseOrOpenOrCloseRecord",
      };

      this.operationList = [];
      this.operationForm.total = 0;
      this.operationForm = this.formatRequestData(this.operationForm);
      Object.assign(message, this.operationForm);

      data = {
        name: "EsPlugin",
        message: JSON.stringify(message),
      };
      this.getDataList(data);
    },
    operateChange(val) {
      if (val) {
        this.operationForm.createGet = val[0];
        this.operationForm.createlet = val[1];
      } else {
        this.operationForm.createGet = "";
        this.operationForm.createlet = "";
      }
    },
    handleSizeChange(val) {
      let data = {};
      let message = {
        requestCode: 7,
        requestName: "getIndexState",
      };

      this.diskForm.size = val;
      this.diskForm.startNum = (this.diskForm.pageNum - 1) * this.diskForm.size;
      Object.assign(message, this.diskForm);

      data = {
        name: "EsPlugin",
        message: JSON.stringify(message),
      };
      this.getDataList(data);
    },
    handleCurrentChange(val) {
      let data = {};
      let message = {
        requestCode: 7,
        requestName: "getIndexState",
      };

      this.diskForm.pageNum = val;
      this.diskForm.startNum = (this.diskForm.pageNum - 1) * this.diskForm.size;
      Object.assign(message, this.diskForm);

      data = {
        name: "EsPlugin",
        message: JSON.stringify(message),
      };
      this.getDataList(data);
    },
    operationSizeChange(val) {
      let data = {};
      let message = {
        requestCode: 18,
        requestName: "getCloseOrOpenOrCloseRecord",
      };

      this.operationForm.size = val;
      this.operationForm.startNum =
        (this.operationForm.pageNum - 1) * this.operationForm.size;
      Object.assign(message, this.operationForm);

      data = {
        name: "EsPlugin",
        message: JSON.stringify(message),
      };
      this.getDataList(data);
    },
    operationCurrentChange(val) {
      let data = {};
      let message = {
        requestCode: 18,
        requestName: "getCloseOrOpenOrCloseRecord",
      };

      this.operationForm.pageNum = val;
      this.operationForm.startNum =
        (this.operationForm.pageNum - 1) * this.operationForm.size;
      Object.assign(message, this.operationForm);

      data = {
        name: "EsPlugin",
        message: JSON.stringify(message),
      };
      this.getDataList(data);
    },
    resetIndex() {
      this.diskForm = {
        pageNum: 1,
        startNum: 0,
        size: 10,
        total: 0,
        indexStatus: null,
        indexName: null,
        storeSizeGet: null,
        storeSizelet: null,
      };
    },
    resetOperation() {
      this.operationForm = {
        pageNum: 1,
        startNum: 0,
        size: 10,
        total: 0,
        recordType: null,
        indexName: null,
        createGet: null,
        createlet: null,
      };
    },
    headerClass() {
      return headerRowClassName();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    healthHeader() {
      return tableHeaderStyle();
    },
  },
  mounted() {
    this.initMethods();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.es-detail {
  height: 100%;
  width: 100%;
  /* overflow-y: auto; */
}
.detail-content {
  width: 98%;
  margin: 0 1% 1%;
  height: 100%;
}
.es-tabs {
  width: 100%;
  height: 100%;
}
.es-tanPane {
  height: 100%;
}
.tab-row {
  height: 100%;
}
.hostForm-row {
  padding: 20px;
}
.indexInput {
  width: 100px;
}
.top-title {
  text-align: left;
  text-indent: 15px;
  color: #1e85e6;
  font-size: 15px;
  font-weight: bold;
  height: 40px;
  line-height: 40px;
  display: inline-block;
  margin-top: 10px;
}
.content-row {
  width: 100%;
  height: calc(100% - 60px);
  padding: 20px;
  background-color: #ffffff;
  border: 1px solid #e2e2e2;
  -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
  overflow: auto;
}
.button-icon {
  display: inline-block;
  height: 21px;
  width: 25px;
  background-size: cover !important;
  vertical-align: sub;
  background: url("../../../assets/images/hardWare/icon-detail.png");
}
.return-col {
  text-align: right;
  padding-right: 20px;
  height: 40px;
  line-height: 40px;
  margin-top: 10px;
}
.btn-return {
  background-color: #1e85e6;
  color: #fff;
  padding-left: 5px;
}
.button-return {
  background-position: 0 26px;
  height: 16px;
}
.btn-return:hover {
  background-color: #2d91ef;
  color: #fff;
}
.el-date-editor .el-range-separator {
  line-height: 20px;
}
.esDetail-form {
  height: 40px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 10;
}
.esDetail-formItem {
  margin-bottom: 0 !important;
}
.btn-set {
  margin-left: 20px;
}
.table-row {
  padding: 15px;
  height: 100%;
}
.indexTable-row,
.operationTable-row {
  padding: 15px;
  height: calc(100% - 56px);
}
.status-tag {
  color: #fff;
  border: 0;
  width: 40px;
  height: 20px;
  line-height: 20px;
}
.progress-text {
  color: #203e66;
  text-align: right;
}
.progress-text div {
  float: left;
}
.delete-status {
  margin-bottom: 10px;
}
.delete-status + p {
  color: red;
}
.index-form {
  padding: 0 15px;
}
.diskInput {
  width: 178px;
  margin: 0 5px;
}
.diskSelect {
  width: 70px;
}
.disk-col {
  display: flex;
  align-items: center;
}
.button-col {
  text-align: right;
}
.button-form {
  background: url("../../../assets/images/agentManage/icon-Agent.png");
  display: inline-block;
  height: 16px;
  width: 16px;
  background-size: cover !important;
  vertical-align: sub;
}
.button-search {
  background-position: 0px 129px;
}
.btn-style:hover .button-search {
  background-position: 0px 89px;
}
.pagination-row {
  height: 30px;
  text-align: center;
}
.base-row {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
}
.base-content {
  padding: 18px 18px 0;
}
.base-row h4 {
  font-size: 13px;
}
.base-row .explain-col {
  margin-bottom: 18px;
}
</style>