<!-- 审计管理 -->
<template>
  <div class="report-content">
    <el-row class="row-content">
      <el-col class="content-form">
        <el-form label-width="4rem" class="report-form">
          <el-row type="flex" class="row-bg">
            <el-col :span="24">
              <el-col :span="12">
                <button
                  class="btn-style form-btn"
                  @click.prevent="createShow = true"
                  v-if="btn.createReport.value"
                >
                  <i class="iconfont icon-create"></i>
                  <span>生成报告</span>
                </button>
                <span v-else class="spanShow">sss</span>
              </el-col>
              <el-col :span="12" class="form-search">
                <el-col :span="20">
                  <span>报告类型：</span>
                  <el-select v-model="form.type">
                    <el-option
                      v-for="(item, index) in reportOption"
                      :key="index"
                      :value="item.value"
                      :label="item.label"
                    >
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <button class="btn-style" @click.prevent="searchBtn">
                    <div class="button-icon button-search"></div>
                    <span>查询</span>
                  </button>
                </el-col>
              </el-col>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
      <el-col :span="24" class="content-table">
        <el-table
          class="table-border"
          :data="dataList"
          :row-class-name="bodyClass"
          :header-cell-style="tableHeader"
          :header-row-class-name="headerClass"
          height="100%"
          border
          v-loading="tableLoading"
        >
          <el-table-column prop="name" label="名称"></el-table-column>
          <el-table-column
            prop="size"
            label="文件大小"
            :formatter="currencyCapacity"
            sortable
          ></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="text-button"
                v-if="btn.updateReport.value"
                @click="updateLog(scope.row)"
              >
                <div class="button-icon button-update"></div>
                <span>下载</span>
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24" class="table-page">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="form.pn"
          :page-sizes="[20, 50, 100]"
          :page-size="form.ps"
          layout="total,sizes,prev,pager,next,jumper"
          :total="form.total"
          background
          popper-class="pagination-popper"
        >
        </el-pagination>
      </el-col>
    </el-row>

    <createReport v-if="createShow" @returnMain="closeCreate"></createReport>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import createReport from "./createReport.vue";
import {
  bodyRowClassName,
  tableHeaderStyle,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "report-content",
  components: { createReport },
  data() {
    return {
      pageTimer: null,
      tableLoading: false,
      createShow: false,

      dataList: [],
      reportOption: [
        {
          label: "日报",
          value: 1,
        },
        {
          label: "周报",
          value: 2,
        },
        {
          label: "月报",
          value: 3,
        },
      ],

      btn: {
        updateReport: {
          name: "下载报告",
          value: false,
        },
        createReport: {
          name: "生成报告",
          value: false,
        },
      },
      form: {
        type: 1,
        pn: 1, //当前页
        ps: 20, //当前每页条数
        total: 0, //总条数
      },
    };
  },
  methods: {
    init() {
      this.getDataList(false, this.form);
      getPermission(this.$route.name, this.btn);
      this.requestInterval();
      this.getStatus();
    },

    //请求数据函数
    getDataList(IntervalLoading, data) {
      let _this = this;

      _this.tableLoading = IntervalLoading ? false : true;
      _this.$http
        .post("report/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.tableLoading = false;
          if (res.status == 200) {
            if (res.data) {
              _this.dataList = res.data.list;
              _this.form.total = res.data.total;
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    updateLog(row) {
      let _this = this;
      let data = {
        path: row.path,
      };
      window.location =
        this.$http.defaults.baseURL + "report/down?" + _this.qs.stringify(data);
    },
    requestInterval() {
      if (this.pageTimer) {
        clearInterval(this.pageTimer);
      } else {
        this.pageTimer = setInterval(() => {
          this.getDataList(true, this.form);
        }, 60000);
      }
    },

    //处理数据函数
    formatUser(row, cell, value) {
      value = value || "--";
      return value;
    },
    currencyCapacity(row, column, value) {
      if (value && value != "未知") {
        value = Number(value);
        if (value > 0 && value < 1024) {
          value = value + "bytes";
        } else if (value >= 1024 && value < 1048576) {
          value = (value / 1024).toFixed(2) + "KB";
        } else if (value >= 1048576 && value < 1073741824) {
          value = (value / 1048576).toFixed(2) + "MB";
        } else if (value >= 1073741824) {
          value = (value / 1073741824).toFixed(2) + "GB";
        }
        return value;
      } else {
        return "-";
      }
    },

    //功能函数
    handleSizeChange(val) {
      this.form.ps = val;
      this.getDataList(false, this.form);
    },
    handleCurrentChange(val) {
      this.form.pn = val;
      this.getDataList(false, this.form);
    },
    searchBtn() {
      this.dataList = [];
      this.form.total = 0;
      this.getDataList(false, this.form);
    },
    closeCreate() {
      this.createShow = false;
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
  },
  mounted() {
    this.init();
  },
  destroyed() {
    clearInterval(this.pageTimer);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.report-content {
  height: 100%;
  width: 98%;
  margin: 0 1%;
}
.table-row {
  margin-top: 10px;
}
.report-form {
  margin: 1.5rem;
  /* padding-right:1rem; */
}
.title-row:not(:nth-child(1)) {
  margin-top: 30px;
}
.content-table {
  padding-bottom: 8px;
  height: calc(100% - 10rem);
}
.button-icon {
  display: inline-block;
  height: 20px;
  width: 20px;
  background-size: cover !important;
  vertical-align: sub;
  background: url("../../assets/images/procedureManage/icon-dialog.png");
}
.button-update {
  background-position: 0px 2px;
}
.text-button:hover .button-update {
  background-position: 0px -98px;
}
.form-search {
  color: #203e66;
  text-align: right;
}
.button-search {
  height: 16px;
  width: 16px;
  background: url("../../assets/images/agentManage/icon-Agent.png");
  background-position: 0px 129px;
}
.btn-style:hover .button-search {
  background-position: 0px 89px;
}
.spanShow {
  visibility: hidden;
}
</style>