<template>
    <div class="creatReport-content">
        <el-dialog 
            title="生成报告" 
            :visible.sync="addFormVisible" 
            width="600px" top="10vh" 
            class="dialog-border" 
            :close-on-click-modal="false"
            @close="resetForm">

            <el-row type="flex" class="row-bg">
                <el-form 
                    label-width="100px" 
                    :model="reportForm" 
                    :rules="rules" 
                    ref="reportForm" class="creatReport-form">
                    <el-col :span="24">
                        <el-form-item label="报告类型:" prop="type">
                            <el-select v-model="reportForm.type">
                                <el-option 
                                    v-for="(item,index) in reportOption"
                                    :key="index"
                                    :value="item.value"
                                    :label="item.label">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="报告时间:" prop="reportTime">
                            <el-date-picker 
                                v-model="reportForm.reportTime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange" 
                                range-separator='至' 
                                start-placeholder='开始日期' 
                                end-placeholder='结束日期'
                                @change="operateChange"
                                :clearable="false">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>

            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="addFormVisible = false" class="btn-style">关闭</button>
                <button @click.prevent="saveBtn" class="btn-style primary-btn">生成</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "creatReport-content",
    data() {
        return {
            addFormVisible: true,

            reportOption: [
                {
                    label: "日报",
                    value: 1
                },
                {
                    label: "周报",
                    value: 2
                },
                {
                    label: "月报",
                    value: 3
                }
            ],

            rules: {
                type: [
                    {
                        required: true,
                        message: "请选择报告类型"
                    }
                ],
                reportTime: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择报告时间"
                    }
                ]
            },
            reportForm: {
                type: 1,
                startTime: "",
                endTime: "",
                reportTime: []
            }
        };
    },
    methods: {
        //请求数据函数
        createData(data) {
            this.$http.post("home/report.json", this.qs.stringify(data), {
                timeout: null
            });
            this.suFn("已在后台进行生成");
            this.addFormVisible = false;
        },

        //功能函数
        operateChange(val) {
            if (val && val.length != 0) {
                this.reportForm.startTime = val[0];
                this.reportForm.endTime = val[1];
            }
        },
        saveBtn() {
            this.$refs["reportForm"].validate(valid => {
                if (valid) {
                    this.createData(this.reportForm);
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        resetForm() {
            this.reportForm = {
                type: 1,
                startTime: "",
                endTime: "",
                reportTime: []
            };
            this.$emit("returnMain");
        }
    }
};
</script>

<style scoped>
.creatReport-content {
    height: 100%;
    overflow-y: auto;
}
.creatReport-form {
    width: 90%;
    margin: auto;
}
.btn-style:first-child {
    margin-right: 10px;
}
.btn-style {
    padding: 0 20px;
}
</style>