<!-- 添加disk -->
<template>
    <div class="agentAdd-content" v-loading.fullscreen = 'fullscreenLoading'>
        <el-dialog 
            title="添加目录磁盘" 
            :visible.sync="addFormVisible" 
            width="600px" top="10vh" 
            class="dialog-border" 
            :close-on-click-modal="false"
            @close="resetForm">

            <el-row type="flex" class="row-bg">
                <el-form 
                    label-width="100px" 
                    :model="addForm" 
                    :rules="rules" 
                    ref="addForm" class="agentAdd-form">
                    <el-col :span="24">
                        <el-form-item label="服务器IP:" prop="ip">
                            <el-input v-model="addForm.ip"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="预处理目录:" prop="dir">
                            <el-input v-model="addForm.dir"></el-input>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>

            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="addFormVisible = false" class="btn-style">关闭</button>
                <button @click.prevent="saveBtn" v-loading.fullscreen.lock='fullscreenLoading' class="btn-style primary-btn">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { checkIPNotEnablePort, checkChinese } from "@/utils/validate.js";

export default {
    name: "agentAdd-content",
    data() {
        return {
            fullscreenLoading: false,
            addFormVisible: true,

            addForm: {
                dir: "",
                ip: ""
            },
            rules: {
                ip: [
                    {
                        trigger: "blur",
                        required: true,
                        validator: (rule, value, callback) => {
                            if (value == "") {
                                callback(new Error("请输入IP"));
                            } else {
                                if (checkIPNotEnablePort(value)) {
                                    callback();
                                } else {
                                    callback(new Error("IP格式不正确"));
                                }
                            }
                        }
                    }
                ],
                dir: [
                    {
                        trigger: "blur",
                        required: true,
                        validator: (rule, value, callback) => {
                            if (value == "") {
                                callback(new Error("请输入预处理目录"));
                            } else {
                                if (checkChinese(value)) {
                                    callback();
                                } else {
                                    callback(new Error("预处理目录格式不正确"));
                                }
                            }
                        }
                    }
                ]
            }
        };
    },
    methods: {
        //请求数据函数
        save() {
            let _this = this;

            _this.$http
                .post(
                    "preprocessDiskMonitor/addPreprocessDir",
                    _this.qs.stringify(_this.addForm)
                )
                .then(res => {
                    if (res.data.code == 0) {
                        _this.resetForm("SUCCESS");
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.erFn();
                });
        },

        //功能函数
        saveBtn() {
            let _this = this,
                data = {};

            _this.$refs["addForm"].validate(valid => {
                if (valid) {
                    _this.save(_this.addForm);
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        resetForm(val) {
            this.$refs["addForm"].resetFields();
            this.addFormVisible = false;
            this.$emit("returnMain", val);
        }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.agentAdd-content {
    height: 100%;
    overflow-y: auto;
}
.agentAdd-form {
    width: 90%;
    margin: auto;
}
.btn-style:first-child {
    margin-right: 10px;
}
.btn-style {
    padding: 0 20px;
}
</style>