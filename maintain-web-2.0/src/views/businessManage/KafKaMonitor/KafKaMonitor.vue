<!-- KafKa监控 -->
<template>
  <div class="monitor-content" v-loading.fullscreen="fullscreenLoading">
    <el-row class="content-row">
      <el-row class="monitor-row">
        <el-row class="Kafka-content">
          <el-row :span="24" class="disk-btn">
            <el-col :span="12">
              <button @click.prevent="updateBtn" class="btn-style">
                <i class="iconfont icon-update"></i>
                <span>刷新</span>
              </button>
            </el-col>
          </el-row>
          <el-row class="table-monitor">
            <el-row :span="24" class="KafKa-table">
              <el-table
                :data="dataList"
                border
                :row-class-name="bodyClass"
                :header-cell-style="tableHeader"
                :header-row-class-name="headerClass"
                height="100%"
              >
                <el-table-column
                  prop="topic"
                  label="TOPIC"
                  min-width="120"
                ></el-table-column>
                <el-table-column
                  prop="partition"
                  label="PARTITION"
                  width="100"
                ></el-table-column>
                <el-table-column
                  prop="current-offset"
                  label="CURRENT-OFFSET"
                  width="130"
                ></el-table-column>
                <el-table-column
                  prop="log-end-offset"
                  label="LOG-END-OFFSET"
                  width="130"
                ></el-table-column>
                <el-table-column
                  prop="lag"
                  label="LAG"
                  width="80"
                  sortable
                ></el-table-column>
                <el-table-column
                  prop="host"
                  width="127"
                  label="HOST"
                  sortable
                ></el-table-column>
                <el-table-column
                  prop="consumer-id"
                  label="CONSUMER-ID"
                  min-width="150"
                >
                  <template slot-scope="scope">
                    <el-popover
                      trigger="hover"
                      placement="top-start"
                      width="300"
                    >
                      <p class="popoverContent">
                        {{ scope.row["consumer-id"] }}
                      </p>
                      <div slot="reference" class="startParam-popover">
                        {{ scope.row["consumer-id"] }}
                      </div>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="client-id"
                  label="CLIENT-ID"
                  width="100"
                ></el-table-column>
              </el-table>
            </el-row>
          </el-row>
        </el-row>
      </el-row>
    </el-row>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import { parseTime } from "@/utils/index.js";
import {
  tableHeaderStyle,
  bodyRowClassName,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "monitor-content",
  data() {
    return {
      fullscreenLoading: false,
      addFormVisible: false,

      dataList: [],
    };
  },
  methods: {
    init() {
      getPermission(this.$route.name, this.btn);
      this.getDataList();
    },

    //请求数据函数
    getDataList() {
      let _this = this,
        data = {
          consumer: "logstash",
        };

      _this.fullscreenLoading = true;
      _this.$http
        .post("kafka/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            this.dataList = [...res.data.data];
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //处理数据函数

    //功能函数
    updateBtn() {
      this.getDataList();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.monitor-content {
  height: 100%;
  width: 100%;
}
.monitor-row {
  height: 100%;
  width: 100%;
  padding: 10px;
}
.Kafka-content {
  height: 100%;
  width: 100%;
  padding: 10px;
}
.table-monitor {
  height: calc(100% - 40px);
}
.KafKa-table {
  height: 100%;
}
</style>