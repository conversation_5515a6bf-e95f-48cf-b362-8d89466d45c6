<!-- 业务管理 -->
<template>
    <div class="business-content">
        <div class="tab-content" v-loading.fullscreen = 'fullscreenLoading'>
            <el-tabs 
                type="card" 
                class="colony-header" 
                v-model="activeName" >

                <el-tab-pane name="0" label="动态分析" class="buiness-tab">
                    <dynamicAnalysis v-if="activeName == '0'"></dynamicAnalysis>
                </el-tab-pane>
                <el-tab-pane name="1" label="静态分析" class="buiness-tab">
                    <staticAnalysis v-if="activeName == '1'"></staticAnalysis>
                </el-tab-pane>
                <el-tab-pane name="6" label="网页挂马监控" class="buiness-tab">
                    <webMalware v-if="activeName == '6'"></webMalware>
                </el-tab-pane>
                <el-tab-pane name="2" label="预处理目录" class="buiness-tab">
                    <pretreatmentDir v-if="activeName == '2'"></pretreatmentDir>
                </el-tab-pane>
                <el-tab-pane name="4" label="数据监控" class="buiness-tab">
                    <dataMonitor v-if="activeName == '4'"></dataMonitor>
                </el-tab-pane>
                <el-tab-pane name="7" label="mysql备份监控" class="buiness-tab">
                    <sqlBackup v-if="activeName == '7'"></sqlBackup>
                </el-tab-pane>
                <el-tab-pane name="3" label="入库信息" class="buiness-tab">
                    <storagePut v-if="activeName == '3'"></storagePut>
                </el-tab-pane>
                <el-tab-pane name="5" label="数据同步监控" class="buiness-tab">
                    <KafKaMonitor v-if="activeName == '5'"></KafKaMonitor>
                </el-tab-pane>
                <div class="colony-detail" v-if="activeName == '0'">
                    <div class="status-describe">
                        <el-form 
                        label-width="200px" 
                        ref="setForm" 
                        :rules="rules"
                        :model="form">
                            <el-col :span="18">
                                <el-form-item prop="threshold" label="标准分析量（动态分析）:" class="business-formItem">
                                    <el-input v-model.number="form.threshold"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" class="button-row form-btn">
                                <button 
                                    class="btn-style" 
                                    v-if="btn.frontDetail.value"
                                    @click.prevent="setBtn">
                                    <i class="iconfont icon-edit"></i>
                                    <span>修改</span>
                                </button>
                            </el-col>
                        </el-form>
                    </div>
                </div>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import dynamicAnalysis from "./dynamicAnalysis/dynamicAnalysis.vue";
import staticAnalysis from "./staticAnalysis/staticAnalysis.vue";
import pretreatmentDir from "./pretreatmentDir/pretreatmentDir.vue";
import storagePut from "./storagePut/storagePut.vue";
import dataMonitor from "./dataMonitor/dataMonitor.vue";
import KafKaMonitor from "./KafKaMonitor/KafKaMonitor.vue";
import webMalware from "./webMalware/webMalware.vue";
import sqlBackup from "./sqlBackup/sqlBackup.vue";
import { parseTime } from "@/utils/index.js";
import { mapMutations } from "vuex";
import getPermission from "@/utils/permissions.js";

export default {
    name: "business-content",
    components: {
        dynamicAnalysis,
        staticAnalysis,
        pretreatmentDir,
        storagePut,
        dataMonitor,
        KafKaMonitor,
        webMalware,
        sqlBackup
    },
    data() {
        return {
            activeName: "0",

            fullscreenLoading: false,

            tabOptions: [
                {
                    label: "动态分析",
                    value: "0"
                },
                {
                    label: "静态分析",
                    value: "1"
                },
                {
                    label: "网页挂马监控",
                    value: "6"
                },
                {
                    label: "预处理目录磁盘",
                    value: "2"
                },
                {
                    label: "入库信息",
                    value: "3"
                },
                {
                    label: "数据监控",
                    value: "4"
                },
                {
                    label: "数据同步监控",
                    value: "5"
                },
                {
                    label: "mysql备份监控",
                    value: "7"
                }
            ],

            rules: {
                threshold: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: (rule, value, callback) => {
                            let vin = /^\+?[1-9][0-9]*$/;
                            let vin1 = /^[\d]/g;

                            if (value === "") {
                                callback(new Error("请输入标准分析量"));
                            } else {
                                if (vin.test(value) && vin1.test(value)) {
                                    callback();
                                } else {
                                    callback(
                                        new Error("标准分析量只能为正整数")
                                    );
                                }
                            }
                        }
                    }
                ]
            },
            form: {
                threshold: null
            },
            btn: {
                frontDetail: {
                    name: "设置标准分析量",
                    value: false
                },
                addDisk: {
                    name: "添加磁盘",
                    value: false
                },
                deleteDisk: {
                    name: "删除磁盘",
                    value: true
                }
            }
        };
    },
    methods: {
        ...mapMutations(["saveOperationStatus"]),
        init() {
            getPermission(this.$route.name, this.btn);
            this.roundColor(this.tabOptions);
            this.getBusStatus();
        },

        //请求数据函数
        getBusStatus(IntervalLoading) {
            let _this = this;

            _this.$http
                .get("home/monitor.json")
                .then(res => {
                    if (res.data.code == 0) {
                        res.data.data && _this.initStatus(res.data.data);
                        _this.saveOperationStatus(res.data.data);
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.erFn();
                    console.log(error);
                });
        },
        addThreshold() {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .post(
                    "dynamicAndStatic/addThreshold",
                    _this.qs.stringify(_this.form)
                )
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        this.suFn("设置成功");
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        initStatus(data) {
            let $item = $(".el-tabs__item");

            data.forEach(item => {
                if (item.moduleName == "业务管理") {
                    $.each(item.status, (index, value) => {
                        switch (index) {
                            case "dynamic":
                                $item
                                    .eq(0)
                                    .attr("data-color", value.toLowerCase());
                                break;
                            case "static":
                                $item
                                    .eq(1)
                                    .attr("data-color", value.toLowerCase());
                                break;
                            case "webmalware":
                                $item
                                    .eq(2)
                                    .attr("data-color", value.toLowerCase());
                                break;
                            case "preprocessDiskInfo":
                                $item
                                    .eq(3)
                                    .attr("data-color", value.toLowerCase());
                                break;
                            case "indexMonitor":
                                $item
                                    .eq(4)
                                    .attr("data-color", value.toLowerCase());
                                break;
                            case "mysqlBackup":
                                $item
                                    .eq(5)
                                    .attr("data-color", value.toLowerCase());
                                break;
                        }
                    });
                }
            });
        },

        //功能函数
        setBtn() {
            let _this = this;

            this.$refs["setForm"].validate(valid => {
                if (valid) {
                    this.addThreshold();
                } else {
                    return false;
                }
            });
        },
        roundColor(data) {
            this.$nextTick(() => {
                let $item = $(".el-tabs__item");
                for (let i in data) {
                    $item.eq(i).attr("data-name", "colony");
                }
            });
        }
    },
    mounted() {
        this.init();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.business-content {
    height: calc(100% - 30px);
    width: calc(100% - 30px);
    /* border: 1px solid #ccc; */
    margin: 15px;
}
.tab-content {
    height: 100%;
    position: relative;
}
.el-tabs {
    width: 100%;
    height: 100%;
}
.el-tabs--border-card {
    border: 0;
}
.radio-content {
    width: 100%;
    text-align: center;
    margin: 7px;
}
.form-time {
    margin: 20px 10px 0;
    height: 40px;
    text-align: right;
}
.colony-detail {
    position: absolute;
    top: 5px;
    right: 0px;
    min-width: 500px;
}
.status-describe {
    display: inline-block;
    width: 100%;
}
.detail-content {
    width: calc(100% - 40px);
    margin: 0 20px 20px;
    height: calc(100% - 20px);
}
.button-row {
    line-height: 32px;
    padding-left: 20px;
}
.buiness-tab {
    height: 100%;
    width: 100%;
}
.buiness-tab /deep/ .content-row {
    width: 100%;
    /* height: calc(100% - 68px); */
    height: 100%;
    background-color: #ffffff;
    /* border: 1px solid #e2e2e2; */
    /* box-shadow: rgba(0,0,0,.2) 0px 0px 2px; */
}
.buiness-tab /deep/ .buiness-row {
    padding: 10px 10px 0 10px;
    min-height: 300px;
    height: 50%;
}
.buiness-tab /deep/ .table-row {
    height: calc(100% - 30px);
    padding: 14px 10px 10px;
}
.buiness-tab /deep/ .index-row {
    margin: 0 10px;
    min-height: 300px;
    height: 50%;
}
.buiness-tab /deep/ .buiness-tab {
    height: 100%;
}
.buiness-tab /deep/ .disk-btn {
    height: 40px;
}
.buiness-tab /deep/ .status-tag {
    color: #fff;
    border: 0;
    width: 40px;
    height: 20px;
    line-height: 20px;
}
.buiness-tab /deep/ .form-row {
    margin: 1.5rem 0;
}
.buiness-tab /deep/ .business-formItem {
    margin-bottom: 0;
}
.buiness-tab /deep/ .button-icon {
    background: url("../../assets/images/agentManage/icon-Agent.png");
    display: inline-block;
    height: 16px;
    width: 16px;
    background-size: cover !important;
    vertical-align: sub;
}
.buiness-tab /deep/ .button-add {
    background-position: 0px -102px;
}
.buiness-tab /deep/ .btn-style:hover .button-add {
    background-position: 0px -152px;
}
.buiness-tab /deep/ .host-form {
    margin: 18px 0 0 50px;
}
.buiness-tab /deep/ .form-time {
    margin: 10px 0 0 10px;
    height: 40px;
    text-align: right;
}
.buiness-tab /deep/ .chart-row {
    min-height: 250px;
    height: calc(100% - 80px);
    /* padding-top: 20px; */
}
.buiness-tab /deep/ .chart-content {
    height: 100%;
}
.buiness-tab /deep/ .chartWrap {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.buiness-tab /deep/ .hostForm-row {
    min-height: 170px;
    display: flex;
    align-items: center;
    height: calc(100% - 30px);
}
.buiness-tab /deep/ .host-row {
    padding: 10px 10px 0 10px;
    min-height: 200px;
    height: 40%;
}
.buiness-tab /deep/ .host-header {
    height: 30px;
    line-height: 36px;
    font-size: 14px;
    border-bottom: 1px solid #cdd0d3;
}
.buiness-tab /deep/ .host-text {
    display: inline-block;
    height: 30px;
    line-height: 36px;
    border-bottom: 2px solid #5174b4;
    padding: 0 3px;
    color: #203e66;
    font-weight: bold;
}
.buiness-tab /deep/ .host-content {
    padding: 0px 15px;
    height: 100%;
}
.buiness-tab /deep/ .network-row {
    margin: 0 10px;
    /* min-height: 400px; */
    height: 60%;
}
.buiness-tab /deep/ .content-table {
    height: calc(100% - 40px);
}
.buiness-tab /deep/ .progress-text {
    text-align: right;
}
.buiness-tab /deep/ .progress-text div {
    float: left;
}
</style>