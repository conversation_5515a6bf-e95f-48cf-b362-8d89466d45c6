<!-- 动态分析 -->
<template>
  <div class="dynamic-content" v-loading.fullscreen="fullscreenLoading">
    <el-row class="content-row dynamic-row">
      <el-row class="host-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">基本信息</div>
            <span class="msg-tips">*仅统计今天之前的数据</span>
          </div>
          <el-row class="hostForm-row">
            <el-col :span="24">
              <el-form class="host-form">
                <el-row>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>状态</span>
                      </div>
                      <el-popover
                        :trigger="
                          dynamicForm.content != '良好' ? 'hover' : 'manual'
                        "
                        placement="top-start"
                        v-if="dynamicForm.status"
                      >
                        <p class="popoverContent">{{ dynamicForm.desc }}</p>
                        <div slot="reference" class="name-wrapper">
                          <el-tag
                            size="small"
                            class="status-tag"
                            :color="dynamicForm.status"
                          >
                            {{ dynamicForm.content }}
                          </el-tag>
                        </div>
                      </el-popover>
                      <span v-else>-</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>监控时间</span>
                      </div>
                      <span>{{ dynamicForm.monitorTime || "-" }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>分析成功数量</span>
                      </div>
                      <span>
                        {{
                          !dynamicForm.successNum && dynamicForm.successNum != 0
                            ? "-"
                            : dynamicForm.successNum
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>分析失败数量</span>
                      </div>
                      <span>
                        {{
                          !dynamicForm.failNum && dynamicForm.failNum != 0
                            ? "-"
                            : dynamicForm.failNum
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>未分析数量</span>
                      </div>
                      <span>
                        {{
                          !dynamicForm.notAnalysisNum &&
                          dynamicForm.notAnalysisNum != 0
                            ? "-"
                            : dynamicForm.notAnalysisNum
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>历史未分析量</span>
                      </div>
                      <span>
                        {{
                          !dynamicForm.hisNotAnalysisNum &&
                          dynamicForm.hisNotAnalysisNum != 0
                            ? "-"
                            : dynamicForm.hisNotAnalysisNum
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>标准分析量</span>
                      </div>
                      <span>
                        {{
                          !dynamicForm.analysisThreshold &&
                          dynamicForm.analysisThreshold != 0
                            ? "-"
                            : dynamicForm.analysisThreshold
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>失败阈值</span>
                      </div>
                      <span>
                        {{
                          !dynamicForm.failAnalysisThreshold &&
                          dynamicForm.failAnalysisThreshold != 0
                            ? "-"
                            : dynamicForm.failAnalysisThreshold
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
          </el-row>
        </el-row>
      </el-row>

      <el-row class="network-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">历史信息</div>
          </div>
          <el-row class="form-time">
            <el-radio-group
              v-model.number="timeChart"
              size="mini"
              @change="timeChange"
            >
              <el-radio-button
                class="proce-radio"
                v-for="item in timeOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-row>
          <el-row class="chart-row">
            <div class="noData-div" v-show="chartShow">暂无数据</div>
            <el-row class="chart-content">
              <div class="chartWrap" id="historyChart"></div>
            </el-row>
          </el-row>
        </el-row>
      </el-row>

      <el-row class="analysis-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">分析机监控</div>
          </div>
          <el-row class="form-time">
            <el-radio-group
              v-model.number="timeLine"
              size="mini"
              @change="lineTimeChange"
            >
              <el-radio-button
                class="proce-radio"
                v-for="item in timeLineOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-row>
          <el-row class="chart-row">
            <div class="noData-div" v-show="lineShow">暂无数据</div>
            <el-row class="chart-content">
              <div class="chartWrap" id="analysisChart"></div>
            </el-row>
          </el-row>
        </el-row>
      </el-row>
    </el-row>
  </div>
</template>

<script>
import { parseTime } from "@/utils/index.js";

export default {
  name: "dynamic-content",
  data() {
    return {
      timeChart: 6,
      timeLine: 3,
      historyChart: "",
      analyzeChart: "",

      fullscreenLoading: false,
      chartShow: true,
      lineShow: true,

      timeOptions: [
        {
          label: "过去1天",
          value: 0,
        },
        {
          label: "过去3天",
          value: 2,
        },
        {
          label: "过去7天",
          value: 6,
        },
        {
          label: "过去1个月",
          value: 29,
        },
      ],
      timeLineOptions: [
        {
          label: "过去3小时",
          value: 3,
        },
        {
          label: "过去6小时",
          value: 6,
        },
        {
          label: "过去12小时",
          value: 12,
        },
        {
          label: "过去1天",
          value: 24,
        },
        {
          label: "过去3天",
          value: 72,
        },
      ],
      dynamicList: [],
      dynamicChart: {
        failList: [],
        successList: [],
        notList: [],
        x: [],
      },

      searchForm: {
        startTime: "",
        endTime: "",
        analysisType: 0,
      },
      LineForm: {
        startTime: "",
        endTime: "",
      },
      dynamicForm: {},
    };
  },
  methods: {
    //请求数据函数
    getDataList(data, show) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("dynamicAndStatic/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (res.data.data.length != 0) {
              _this.chartShow = false;
              _this.initData(res.data.data, show);
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getAnalysisList(data) {
      let _this = this;

      _this.$http
        .post("analysis/list.json", _this.qs.stringify(data))
        .then((res) => {
          if (res.data.code == 0) {
            res.data.data && _this.initLineData(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          console.log(error);
          _this.erFn();
        });
    },

    //处理数据函数
    initData(data, show) {
      let isShow = true;

      if (show) {
        this.dynamicForm = this.formatStatus(data[0]);
      } else {
        data.forEach((item) => {
          this.dynamicList.push(item);
          this.dynamicChart.title = "动态分析量";
          this.dynamicChart.analysisThreshold = data[0].analysisThreshold;
          this.dynamicChart.successList.push(item.successNum);
          this.dynamicChart.failList.push(item.failNum);
          this.dynamicChart.notList.push(item.notAnalysisNum);
          this.dynamicChart.x.push(item.monitorTime.split(" ")[0]);
          if (
            data[0].analysisThreshold < item.successNum ||
            data[0].analysisThreshold < item.failNum ||
            data[0].analysisThreshold < item.notAnalysisNum
          ) {
            isShow = false;
          }
        });
        this.dynamicList.length != 0 &&
          (this.dynamicForm = this.formatStatus(this.dynamicList[0]));
        this.drawDynamicChart(this.dynamicChart, isShow);
      }
    },
    initLineData(data) {
      this.lineShow = false;
      data.legend = [];
      data["series"] &&
        data["series"].forEach((item) => {
          data.legend.push(item.name);
          item.type = "line";
        });
      this.drawLine(data);
    },
    formatStatus(row) {
      switch (row.status) {
        case 0:
          row["status"] = "#25ce88";
          row["content"] = "良好";
          break;
        case 1:
          row["status"] = "#fb843b";
          row["content"] = "警告";
          break;
        case 2:
          row["status"] = "#f93846";
          row["content"] = "错误";
          break;
      }
      return row;
    },
    drawDynamicChart(data, isShow) {
      let chartDom = document.getElementById("historyChart");
      let _this = this;
      let options = {},
        lastTipIndex = null;

      options = {
        dataZoom: [
          {
            type: "slider",
            show: data.x.length > 8 ? true : false,
            bottom: 10,
            start: data.x.length > 8 ? 50 : 0,
            end: 100,
            textStyle: {
              color: "#203e66",
            },
            height: 8,
            realtime: false,
            showDetail: true,
            showDataShadow: false,
            preventDefaultMouseMove: false,
            borderColor: "transparent",
            backgroundColor: "#f2f2f2",
            handleSize: 14,
            handleStyle: { color: "#00a8ff" },
            fillerColor: "#77cdf9", // 选中范围的填充颜色
            handleIcon:
              "M512 512m-512 0a500 500 0 1 0 1024 0 500 500 0 1 0-1024 0Z",
          },
        ],
        title: {
          text: data.title,
          textStyle: {
            fontSize: 13,
            color: "#1e85e6",
          },
          left: 20,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(215,219,232,0.34)",
            },
          },
        },
        legend: {
          data: ["分析失败", "分析成功", "未分析量"],
        },
        calculable: true,
        grid: {
          top: "16%",
          bottom: "20%",
          left: "10%",
          right: "8%",
        },
        xAxis: {
          type: "category",
          boundaryGap: ["10%", "10%"],
          axisLine: {
            lineStyle: { color: "#e6e9f1" },
          },
          axisLabel: {
            color: "#203e66",
            length: 7,
          },
          data: data.x,
        },
        yAxis: [
          {
            type: "value",
            axisTick: { show: false }, //坐标轴刻度
            minInterval: 1,
            axisLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#203e66",
              },
            },
            splitLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            max: isShow ? data.analysisThreshold : null,
          },
        ],
        series: [
          {
            name: "分析失败",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "#73b9bc",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            markLine: {
              symbol: ["none", "none"],
              itemStyle: {
                normal: {
                  label: {
                    show: data.title == "动态分析量" ? true : false,
                    color: "#203e66",
                    formatter: "{b} ({c})",
                  },
                },
              },
              lineStyle: {
                color: "#409EFF",
                opacity: data.title == "动态分析量" ? 1 : 0,
              },

              data: [
                {
                  type: "max",
                  name: "标准分析量",
                  yAxis: data.analysisThreshold,
                },
              ],
            },
            data: data.failList,
          },
          {
            name: "分析成功",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "#d48265",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            data: data.successList,
          },
          {
            name: "未分析量",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "#9fdabf",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            data: data.notList,
          },
        ],
      };
      _this.historyChart = this.$echarts.init(chartDom);
      _this.historyChart.setOption(options);

      window.addEventListener("resize", _this.bindHistoryResize);
      _this.historyChart.on("showTip", function (e) {
        lastTipIndex = e.dataIndex;
      });
      _this.historyChart.on("hideTip", function (e) {
        lastTipIndex = null;
      });
      _this.historyChart.getZr().on("click", function (e) {
        if (lastTipIndex != null) {
          _this.searchForm.startTime = data.x[lastTipIndex] + " 00:00:00";
          _this.searchForm.endTime = data.x[lastTipIndex] + " 00:00:00";
          _this.getDataList(_this.searchForm, true);
        }
      });
    },
    drawLine(data) {
      let chartDom = document.getElementById("analysisChart");
      let _this = this;
      let options = {};

      options = {
        dataZoom: [
          {
            type: "slider",
            show: data.x.length > 8 ? true : false,
            bottom: 10,
            start: 0,
            end: data.x.length > 8 ? 25 : 100,
            textStyle: {
              color: "#203e66",
            },
            height: 8,
            realtime: false,
            showDetail: true,
            showDataShadow: false,
            preventDefaultMouseMove: false,
            borderColor: "transparent",
            backgroundColor: "#f2f2f2",
            handleSize: 14,
            handleStyle: { color: "#00a8ff" },
            fillerColor: "#77cdf9", // 选中范围的填充颜色
            handleIcon:
              "M512 512m-512 0a500 500 0 1 0 1024 0 500 500 0 1 0-1024 0Z",
          },
        ],
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: data.legend,
        },
        // calculable: true,
        grid: {
          top: "16%",
          bottom: "20%",
          left: "5%",
          right: "5%",
        },
        xAxis: {
          type: "category",
          boundaryGap: ["10%", "10%"],
          axisLine: {
            lineStyle: { color: "#e6e9f1" },
          },
          axisLabel: {
            color: "#203e66",
            length: 7,
          },
          data: data.x,
        },
        yAxis: [
          {
            type: "value",
            name: "进程数",
            minInterval: 1,
            nameTextStyle: {
              color: "#203e66",
            },
            axisTick: { show: false }, //坐标轴刻度
            axisLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#203e66",
              },
            },
            splitLine: {
              lineStyle: { color: "#e6e9f1" },
            },
          },
        ],
        series: data.series,
      };

      _this.analyzeChart = this.$echarts.init(chartDom);
      _this.analyzeChart.setOption(options);
      window.addEventListener("resize", _this.bindLineResize);
    },

    //功能函数
    timeChange(val) {
      let endTime = Date.parse(new Date());
      let startTime = endTime - val * 24 * 60 * 60 * 1000;

      this.historyReset();
      this.searchForm.endTime = parseTime(endTime, "{y}-{m}-{d} 00:00:00");
      this.searchForm.startTime = parseTime(startTime, "{y}-{m}-{d} 00:00:00");
      this.getDataList(this.searchForm);
    },
    lineTimeChange(val) {
      let endTime = Date.parse(new Date());
      let startTime = endTime - val * 60 * 60 * 1000;

      this.lineReset();
      this.LineForm.endTime = parseTime(endTime, "{y}-{m}-{d} {h}:00:00");
      this.LineForm.startTime = parseTime(startTime, "{y}-{m}-{d} {h}:00:00");
      this.getAnalysisList(this.LineForm);
    },
    bindHistoryResize() {
      this.historyChart && this.historyChart.resize();
    },
    bindLineResize() {
      this.analyzeChart && this.analyzeChart.resize();
    },
    historyReset() {
      this.dynamicChart = {
        failList: [],
        successList: [],
        notList: [],
        x: [],
      };
      this.historyChart && this.historyChart.dispose();
      this.chartShow = true;
      this.dynamicList = [];
      window.removeEventListener("resize", this.bindHistoryResize);
    },
    lineReset() {
      this.analyzeChart && this.analyzeChart.dispose();
      this.lineShow = true;
      window.removeEventListener("resize", this.bindLineResize);
    },
  },
  mounted() {
    this.timeChange(this.timeChart);
    this.lineTimeChange(this.timeLine);
  },
  destroyed() {
    window.removeEventListener("resize", this.bindHistoryResize);
    window.removeEventListener("resize", this.bindLineResize);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.host-header .msg-tips {
  color: #F44336;
  font-size: 12px;
  margin-left: 10px;
}

.dynamic-content {
  height: 100%;
  width: 100%;
}
.dynamic-row {
  overflow-y: auto;
}
.analysis-row {
  height: 60%;
}
</style>