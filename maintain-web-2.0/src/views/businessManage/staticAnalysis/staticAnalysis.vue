<!-- 静态分析 -->
<template>
  <div class="static-content" v-loading.fullscreen="fullscreenLoading">
    <el-row class="content-row">
      <el-row class="host-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">基本信息</div>
            <span class="msg-tips">*仅统计今天之前的数据</span>
          </div>
          <el-row class="hostForm-row">
            <el-col :span="24">
              <el-form class="host-form">
                <el-row>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>状态</span>
                      </div>
                      <el-popover
                        :trigger="
                          staticForm.content != '良好' ? 'hover' : 'manual'
                        "
                        placement="top-start"
                        v-if="staticForm.status"
                      >
                        <p class="popoverContent">{{ staticForm.desc }}</p>
                        <div slot="reference" class="name-wrapper">
                          <el-tag
                            size="small"
                            class="status-tag"
                            :color="staticForm.status"
                          >
                            {{ staticForm.content }}
                          </el-tag>
                        </div>
                      </el-popover>
                      <span v-else>-</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>监控时间</span>
                      </div>
                      <span>{{ staticForm.monitorTime || "-" }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>邮件跨站检测未完成数量</span>
                      </div>
                      <span>
                        {{
                          !staticForm.emlNotAnalysis != 0 &&
                          staticForm.emlNotAnalysis
                            ? "-"
                            : staticForm.emlNotAnalysis
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>历史邮件跨站检测未完成量</span>
                      </div>
                      <span>
                        {{
                          !staticForm.hisEmlNotAnalysis != 0 &&
                          staticForm.hisEmlNotAnalysis
                            ? "-"
                            : staticForm.hisEmlNotAnalysis
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>邮件跨站检测完成数量</span>
                      </div>
                      <span>
                        {{
                          !staticForm.emlAnalysis && staticForm.emlAnalysis != 0
                            ? "-"
                            : staticForm.emlAnalysis
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>分析成功数量</span>
                      </div>
                      <span>
                        {{
                          !staticForm.successNum && staticForm.successNum != 0
                            ? "-"
                            : staticForm.successNum
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>分析失败数量</span>
                      </div>
                      <span>
                        {{
                          !staticForm.failNum && staticForm.failNum != 0
                            ? "-"
                            : staticForm.failNum
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>未分析数量</span>
                      </div>
                      <span>
                        {{
                          !staticForm.notAnalysisNum &&
                          staticForm.notAnalysisNum != 0
                            ? "-"
                            : staticForm.notAnalysisNum
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="IP" class="label-color">
                      <div slot="label">
                        <!-- <div class="button-icon button-ip"></div> -->
                        <span>失败阈值</span>
                      </div>
                      <span>
                        {{
                          !staticForm.failAnalysisThreshold &&
                          staticForm.failAnalysisThreshold != 0
                            ? "-"
                            : staticForm.failAnalysisThreshold
                        }}
                      </span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>
          </el-row>
        </el-row>
      </el-row>

      <el-row class="network-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">历史信息</div>
          </div>
          <el-row class="form-time">
            <el-radio-group
              v-model.number="timeChart"
              size="mini"
              @change="timeChange"
            >
              <el-radio-button
                class="proce-radio"
                v-for="item in timeOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-row>
          <el-row class="chart-row">
            <div class="noData-div" v-show="chartShow">暂无数据</div>
            <el-row class="chart-content">
              <div class="chartWrap"></div>
            </el-row>
          </el-row>
        </el-row>
      </el-row>
    </el-row>
  </div>
</template>

<script>
import { parseTime } from "@/utils/index.js";

export default {
  name: "static-content",
  data() {
    return {
      timeChart: 6,
      analyzeChart: "",

      fullscreenLoading: false,
      chartShow: true,

      timeOptions: [
        {
          label: "过去1天",
          value: 0,
        },
        {
          label: "过去3天",
          value: 2,
        },
        {
          label: "过去7天",
          value: 6,
        },
        {
          label: "过去1个月",
          value: 29,
        },
      ],
      staticList: [],
      staticChart: {
        failList: [],
        successList: [],
        notList: [],
        emlSuccess: [],
        emlFail: [],
        x: [],
      },

      searchForm: {
        startTime: "",
        endTime: "",
        analysisType: 1,
      },
      staticForm: {},
    };
  },
  methods: {
    //请求数据函数
    getDataList(data, show) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("dynamicAndStatic/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (res.data.data.length != 0) {
              _this.chartShow = false;
              _this.initData(res.data.data, show);
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //处理数据函数
    initData(data, show) {
      if (show) {
        this.staticForm = this.formatStatus(data[0]);
      } else {
        data.forEach((item) => {
          this.staticList.push(item);
          this.staticChart.title = "静态分析量";
          this.staticChart.successList.push(item.successNum);
          this.staticChart.failList.push(item.failNum);
          this.staticChart.notList.push(item.notAnalysisNum);
          this.staticChart.emlSuccess.push(item.emlAnalysis);
          this.staticChart.emlFail.push(item.emlNotAnalysis);
          this.staticChart.x.push(item.monitorTime.split(" ")[0]);
        });
        this.staticList.length != 0 &&
          (this.staticForm = this.formatStatus(this.staticList[0]));
        this.drawLine(this.staticChart);
      }
    },
    formatStatus(row) {
      switch (row.status) {
        case 0:
          row["status"] = "#25ce88";
          row["content"] = "良好";
          break;
        case 1:
          row["status"] = "#fb843b";
          row["content"] = "警告";
          break;
        case 2:
          row["status"] = "#f93846";
          row["content"] = "错误";
          break;
      }
      return row;
    },

    //功能函数
    drawLine(data) {
      let chartDom = document.getElementsByClassName("chartWrap");
      let _this = this;
      let options = {},
        num = null,
        lastTipIndex = null;

      options = {
        dataZoom: [
          {
            type: "slider",
            show: data.x.length > 8 ? true : false,
            bottom: 10,
            start: data.x.length > 8 ? 50 : 0,
            end: 100,
            textStyle: {
              color: "#203e66",
            },
            height: 8,
            realtime: false,
            showDetail: true,
            showDataShadow: false,
            preventDefaultMouseMove: false,
            borderColor: "transparent",
            backgroundColor: "#f2f2f2",
            handleSize: 14,
            handleStyle: { color: "#00a8ff" },
            fillerColor: "#77cdf9", // 选中范围的填充颜色
            handleIcon:
              "M512 512m-512 0a500 500 0 1 0 1024 0 500 500 0 1 0-1024 0Z",
          },
        ],
        title: {
          text: data.title,
          textStyle: {
            fontSize: 13,
            color: "#1e85e6",
          },
          left: 20,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(215,219,232,0.34)",
            },
          },
        },
        legend: {
          data: [
            "分析失败",
            "分析成功",
            "未分析量",
            "邮件跨站检测完成数量",
            "邮件跨站检测未完成数量",
          ],
        },
        calculable: true,
        grid: {
          top: "16%",
          bottom: "20%",
          left: "10%",
          right: "8%",
        },
        xAxis: {
          type: "category",
          boundaryGap: ["10%", "10%"],
          axisLine: {
            lineStyle: { color: "#e6e9f1" },
          },
          axisLabel: {
            color: "#203e66",
            length: 7,
          },
          data: data.x,
        },
        yAxis: [
          {
            type: "value",
            axisTick: { show: false }, //坐标轴刻度
            minInterval: 1,
            axisLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#203e66",
              },
            },
            splitLine: {
              lineStyle: { color: "#e6e9f1" },
            },
          },
        ],
        series: [
          {
            name: "分析失败",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "#73b9bc",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            markLine: {
              symbol: ["none", "none"],
              itemStyle: {
                normal: {
                  label: {
                    show: data.title == "动态分析量" ? true : false,
                    color: "#203e66",
                    formatter: "{b} ({c})",
                  },
                },
              },
              lineStyle: {
                color: "#409EFF",
                opacity: data.title == "动态分析量" ? 1 : 0,
              },

              data: [
                {
                  type: "max",
                  name: "标准分析量",
                  yAxis: data.analysisThreshold,
                },
              ],
            },
            data: data.failList,
          },
          {
            name: "分析成功",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "#d48265",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            data: data.successList,
          },
          {
            name: "未分析量",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "#9fdabf",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            data: data.notList,
          },
          {
            name: "邮件跨站检测完成数量",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "rgb(99,223,246)",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            data: data.emlSuccess,
          },
          {
            name: "邮件跨站检测未完成数量",
            type: "bar",
            barWidth: 20,
            smooth: true,
            itemStyle: {
              normal: {
                color: "#8aa7f4",
                label: {
                  show: true,
                  position: "top",
                  color: "#203e66",
                },
              },
            },
            data: data.emlFail,
          },
        ],
      };

      _this.analyzeChart = this.$echarts.init(chartDom[0]);
      _this.analyzeChart.setOption(options);

      window.onresize = function () {
        _this.analyzeChart && _this.analyzeChart.resize();
      };
      _this.analyzeChart.on("showTip", function (e) {
        lastTipIndex = e.dataIndex;
      });
      _this.analyzeChart.on("hideTip", function (e) {
        lastTipIndex = null;
      });
      _this.analyzeChart.getZr().on("click", function (e) {
        if (lastTipIndex != null) {
          _this.searchForm.startTime = data.x[lastTipIndex] + " 00:00:00";
          _this.searchForm.endTime = data.x[lastTipIndex] + " 00:00:00";
          _this.getDataList(_this.searchForm, true);
        }
      });
    },
    timeChange(val) {
      let endTime = Date.parse(new Date());
      let startTime = endTime - val * 24 * 60 * 60 * 1000;

      this.reset();
      this.searchForm.endTime = parseTime(endTime, "{y}-{m}-{d} 00:00:00");
      this.searchForm.startTime = parseTime(startTime, "{y}-{m}-{d} 00:00:00");
      this.getDataList(this.searchForm);
    },
    reset() {
      this.staticChart = {
        failList: [],
        successList: [],
        notList: [],
        emlSuccess: [],
        emlFail: [],
        x: [],
      };
      this.analyzeChart && this.analyzeChart.dispose();
      this.chartShow = true;
      window.onresize = "";
      this.staticList = [];
    },
  },
  mounted() {
    this.timeChange(this.timeChart);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.static-content {
  height: 100%;
  width: 100%;
}
.host-header .msg-tips {
  color: #f44336;
  font-size: 12px;
  margin-left: 10px;
}
</style>