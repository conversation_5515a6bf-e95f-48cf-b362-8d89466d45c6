<!-- 入库信息 -->
<template>
    <div class="storage-content" v-loading.fullscreen='fullscreenLoading'>
        <el-row class="content-row">
            <el-row class="storage-row">
                <el-row class="storage-content">
                    <el-row class="table-storage">
                        <el-row :span="24" class="disk-btn">
                            <el-col :span="8">
                                <span>监控时间:</span>
                                <el-date-picker
                                    v-model="form.monitorTime"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholde="开始日期"
                                    end-placeholde="结束日期"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    @change="timeChange"
                                    :default-time="['00:00:00','23:59:59']">
                                </el-date-picker>
                            </el-col>
                            <el-col :span="8" class="node-col">
                                <span>节点名称:</span>
                                <div class="nodeBtn">
                                    <div @click="nodeBtn">
                                        <span v-if="checkdNum == 0">请选择</span>
                                        <span v-else>{{checkdNum || 0}}个已选</span>
                                        <i :class="[nodePlaneShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
                                    </div>
                                    <div class="node-plane" v-clickoutside="nodeBtn" v-if="nodePlaneShow">
                                        <div class="node-header">
                                            <el-checkbox 
                                                :indeterminate="isIndeterminate" 
                                                v-model="checkAll"
                                                @change="handelCheckAllChange">全选</el-checkbox>
                                        </div>
                                        <div class="node-content">
                                            <el-checkbox-group v-model="checkedArr" @change="handelCheckChange">
                                                <el-col 
                                                    :span="8"
                                                    v-for="(item,index) in apkeyOption" 
                                                    :key="index" 
                                                    class="nodeCheck">
                                                    <el-checkbox 
                                                        :title='item.name'
                                                        :label="item.apKey">
                                                        {{item.name}}
                                                    </el-checkbox>
                                                </el-col>
                                            </el-checkbox-group>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn-style" @click="searchBtn">
                                    <i class="iconfont icon-search"></i>
                                    <span>查询</span>
                                </button>
                            </el-col>
                        </el-row>
                        <el-row :span="24"  class="content-table">
                            <el-table 
                                :data="storageList" 
                                border
                                :row-class-name="bodyClass" 
                                :header-cell-style="tableHeader" 
                                :header-row-class-name="headerClass" 
                                height="100%">

                                <el-table-column prop="apKey" label="节点编号" class-name="apKey"></el-table-column>
                                <el-table-column prop="name" label="节点名称" class-name="name"></el-table-column>
                                <el-table-column prop="count" label="入库数量" class-name="count"></el-table-column>
                                <el-table-column prop="monitorTime" label="监控时间" class-name="monitorTime"></el-table-column>
                            </el-table>
                        </el-row>
                        <el-row class="storage-page">
                            <el-col :span="24">
                                <el-pagination 
                                    @size-change="handleSizeChange" 
                                    @current-change="handleCurrentChange" 
                                    :current-page="form.pn" 
                                    :page-sizes="[20,50,100]"
                                    :page-size="form.ps"
                                    layout="total,sizes,prev,pager,next,jumper"
                                    :total="form.total"
                                    background
                                    popper-class="pagination-popper">
                                </el-pagination>
                            </el-col>
                        </el-row>
                    </el-row>
                </el-row>
            </el-row>
        </el-row>
    </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import { parseTime } from "@/utils/index.js";
import {
    tableHeaderStyle,
    bodyRowClassName,
    headerRowClassName
} from "@/utils/tableStyle.js";

export default {
    name: "pretreatment-content",
    data() {
        return {
            checkdNum: 0,
            fullscreenLoading: false,
            nodePlaneShow: false,
            isIndeterminate: false,
            checkAll: true,

            storageList: [],
            apkeyOption: [],
            checkedOption: [],
            checkedArr: [],

            form: {
                monitorTime: [],
                apKey: "",
                startTime: "",
                endTime: "",
                pn: 1,
                ps: 20,
                total: 0
            }
        };
    },
    methods: {
        init() {
            getPermission(this.$route.name, this.btn);
            this.getApkeys();
        },

        //请求数据函数
        getDataList() {
            let _this = this;

            _this.form.apKey = _this.checkedArr.join(",");
            _this.fullscreenLoading = true;
            _this.$http
                .post("front-device/frontCount", _this.qs.stringify(_this.form))
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        res.data.data.list &&
                            (this.storageList = [...res.data.data.list]);
                        this.form.total = res.data.data.total;
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },
        getApkeys() {
            let _this = this;

            _this.fullscreenLoading = true;
            _this.$http
                .post("front-device/apKeys")
                .then(res => {
                    _this.fullscreenLoading = false;
                    if (res.data.code == 0) {
                        if (res.data.data && res.data.data.length != 0) {
                            this.apkeyOption = [...res.data.data];
                            this.form.apKey = this.apkeyOption[0].apKey;
                            this.initData(this.apkeyOption);
                            this.getDataList();
                        }
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.fullscreenLoading = false;
                    _this.erFn();
                    console.log(error);
                });
        },

        //处理数据函数
        initData(data) {
            data.forEach(item => {
                this.checkedOption.push(item.apKey);
            });
            this.checkedArr = [...this.checkedOption];
        },

        //功能函数
        handleSizeChange(val) {
            this.form.ps = val;
            this.getDataList();
        },
        handleCurrentChange(val) {
            this.form.pn = val;
            this.getDataList();
        },
        timeChange(val) {
            if (val) {
                this.form.startTime = val[0];
                this.form.endTime = val[1];
            } else {
                this.form.startTime = "";
                this.form.endTime = "";
            }
            this.getDataList();
        },
        nodeBtn(event) {
            //event.stopPropagation();
            this.nodePlaneShow = !this.nodePlaneShow;
        },
        handelCheckAllChange(val) {
            this.checkedArr = val ? this.checkedOption : [];
            this.isIndeterminate = false;
        },
        handelCheckChange(value) {
            let checkedConut = value.length;

            this.checkAll = checkedConut === this.apkeyOption.length;
            this.isIndeterminate =
                checkedConut > 0 && checkedConut < this.apkeyOption.length;
        },
        searchBtn() {
            this.getDataList();
        },
        tableHeader() {
            return tableHeaderStyle();
        },
        bodyClass() {
            return bodyRowClassName();
        },
        headerClass() {
            return headerRowClassName();
        }
    },
    created() {
        this.form.startTime = parseTime(
            Date.parse(new Date()) - 7 * 24 * 60 * 60 * 1000,
            "{y}-{m}-{d} 00:00:00"
        );
        this.form.endTime = parseTime(new Date(), "{y}-{m}-{d} 23:59:59");
        this.form.monitorTime.push(this.form.startTime);
        this.form.monitorTime.push(this.form.endTime);
    },
    mounted() {
        this.init();
    },
    watch: {
        checkedArr(val) {
            this.checkdNum = val.length;
        }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.storage-content {
    height: 100%;
    width: 100%;
}
.storage-row {
    height: 100%;
    width: 100%;
    padding: 10px;
}
.storage-content {
    padding: 0;
}
.node-col {
    display: flex;
    align-items: center;
}
.table-storage {
    height: 100%;
    padding: 10px;
}
.table-storage .nodeBtn {
    width: 130px;
    display: inline-block;
    padding-left: 10px;
    cursor: pointer;
    color: #333c49;
    height: 26px;
    line-height: 25px;
    border-radius: 4px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #dcdfe6;
    margin: 0 15px 0 5px;
    position: relative;
}
.table-storage .nodeBtn:hover {
    border-color: #c0c4cc;
}
.table-storage .nodeBtn i {
    right: 10px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}
.table-storage .node-plane {
    color: #606266;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background: #fff;
    border-radius: 4px;
    line-height: 30px;
    margin: 5px 0;
    height: 330px;
    width: 430px;
    position: absolute;
    z-index: 2;
    left: 0;
    padding: 10px;
}
.node-plane .node-header {
    height: 30px;
    padding: 0 8px;
}
.node-plane .node-content {
    height: calc(100% - 30px);
    overflow-y: auto;
}
.node-plane .node-content .nodeCheck {
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 8px;
}
.content-table {
    padding-bottom: 0;
}
.storage-page {
    margin: 10px 0 0;
    text-align: center;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.storage-content .content-table {
    height: calc(100% - 86px);
}
</style>