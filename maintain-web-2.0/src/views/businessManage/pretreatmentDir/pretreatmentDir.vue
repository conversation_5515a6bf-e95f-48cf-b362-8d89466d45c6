<!-- 预处理目录 -->
<template>
  <div class="pretreatment-content" v-loading.fullscreen="fullscreenLoading">
    <el-row class="content-row">
      <el-row class="buiness-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">磁盘信息</div>
          </div>
          <el-row class="table-row">
            <el-col :span="24" class="disk-btn">
              <button
                @click.prevent="addBtn"
                class="btn-style"
                v-if="btn.addDisk.value"
              >
                <div class="button-icon button-add"></div>
                <span>添加</span>
              </button>
            </el-col>
            <el-col :span="24" class="content-table">
              <el-table
                :data="dataList"
                border
                v-loading="tableLoading"
                :row-class-name="bodyClass"
                :header-cell-style="tableHeader"
                :header-row-class-name="headerClass"
                height="100%"
              >
                <el-table-column
                  type="index"
                  label="序号"
                  width="100"
                ></el-table-column>
                <el-table-column label="状态" width="100">
                  <template slot-scope="scope">
                    <el-popover
                      :trigger="
                        scope.row.content != '良好' ? 'hover' : 'manual'
                      "
                      placement="top-start"
                      v-if="scope.row.alarmStatus"
                    >
                      <p class="popoverContent">{{ scope.row.alarmDesc }}</p>
                      <div slot="reference" class="name-wrapper">
                        <el-tag
                          size="small"
                          class="status-tag"
                          :color="scope.row.alarmStatus"
                        >
                          {{ scope.row.content }}
                        </el-tag>
                      </div>
                    </el-popover>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="ip" label="服务器IP"></el-table-column>
                <el-table-column
                  prop="dir"
                  label="预处理目录"
                ></el-table-column>
                <el-table-column prop="dirSize" label="监控目录大小">
                  <template slot-scope="scope">
                    <span>{{ currencyCapacity(scope.row.dirSize) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="磁盘使用情况">
                  <template slot-scope="scope">
                    <div class="progress-text">
                      <div>
                        {{
                          scope.row.usedDisk && scope.row.totalDisk
                            ? currencyCapacity(scope.row.usedDisk) +
                              "/" +
                              currencyCapacity(scope.row.totalDisk)
                            : "-"
                        }}
                      </div>
                      <span v-if="scope.row.diskPercent">
                        {{ scope.row.diskPercent }}
                      </span>
                    </div>
                    <el-progress
                      v-if="scope.row.diskPercent"
                      :percentage="scope.row.diskPercent | cerrenyPercent"
                      :show-text="false"
                      color="#5db1f2"
                    >
                    </el-progress>
                  </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间">
                  <template slot-scope="scope">
                    <span>{{ scope.row.updateTime || "-" }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      class="text-button form-btn"
                      v-if="btn.deleteDisk.value"
                      @click="deleteBtn(scope.row)"
                    >
                      <i class="iconfont icon-delete"></i>
                      删除
                    </el-button>
                    <el-button
                      type="text"
                      class="text-button form-btn"
                      v-if="btn.editDisk.value"
                      @click="editBtn(scope.row)"
                    >
                      <i class="iconfont icon-edit"></i>
                      编辑阈值
                    </el-button>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-row>
      </el-row>

      <el-row class="index-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">索引信息</div>
          </div>
          <el-row class="table-row">
            <el-table
              :data="indexList"
              border
              v-loading="indexLoading"
              :row-class-name="bodyClass"
              :header-cell-style="tableHeader"
              :header-row-class-name="headerClass"
              height="100%"
            >
              <el-table-column
                prop="indexName"
                label="索引名称"
              ></el-table-column>
              <el-table-column prop="today" label="今天"></el-table-column>
              <el-table-column prop="yesterday" label="昨天"></el-table-column>
              <el-table-column
                prop="beforeYesterday"
                label="前天"
              ></el-table-column>
            </el-table>
          </el-row>
        </el-row>
      </el-row>
    </el-row>

    <el-dialog
      title="编辑告警阈值"
      :visible.sync="editFormVisible"
      width="600px"
      top="10vh"
      class="dialog-border"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-row type="flex" class="row-bg">
        <el-form
          label-width="100px"
          :model="editForm"
          :rules="rules"
          ref="editForm"
          class="editDisk-form"
        >
          <el-col :span="24">
            <el-form-item label="警告阈值:" prop="size">
              <el-input class="alarmInput" v-model="editForm.size" clearable>
                <el-select
                  slot="append"
                  class="alarmSelect"
                  v-model="editForm.unit"
                >
                  <el-option
                    v-for="(item, index) in sizeOption"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="editFormVisible = false" class="btn-style">
          关闭
        </button>
        <button
          @click.prevent="saveBtn"
          v-loading.fullscreen.lock="fullscreenLoading"
          class="btn-style primary-btn"
        >
          确定
        </button>
      </div>
    </el-dialog>
    <diskAdd v-if="addFormVisible" @returnMain="closeAdd"></diskAdd>
  </div>
</template>

<script>
import diskAdd from "../diskAdd.vue";
import getPermission from "@/utils/permissions.js";
import { parseTime } from "@/utils/index.js";
import {
  tableHeaderStyle,
  bodyRowClassName,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "pretreatment-content",
  components: { diskAdd },
  data() {
    return {
      tableLoading: false,
      indexLoading: false,
      fullscreenLoading: false,
      addFormVisible: false,
      editFormVisible: false,

      dataList: [],
      indexList: [],
      sizeOption: [
        {
          label: "MB",
          value: "M",
        },
        {
          label: "GB",
          value: "G",
        },
        {
          label: "TB",
          value: "T",
        },
      ],

      btn: {
        frontDetail: {
          name: "设置标准分析量",
          value: false,
        },
        addDisk: {
          name: "添加磁盘",
          value: false,
        },
        deleteDisk: {
          name: "删除磁盘",
          value: true,
        },
        editDisk: {
          name: "编辑阈值",
          value: true,
        },
      },
      editForm: {
        size: "",
        unit: "GB",
        ip: "",
        dir: "",
      },
      rules: {
        size: [
          {
            required: true,
            trigger: "blur",
            validator: (rule, value, callback) => {
              let vin = /^\+?[1-9][0-9]*$/;
              let vin1 = /^[\d]/g;

              if (value === "") {
                callback(new Error("请输入告警阈值"));
              } else {
                if (vin.test(value) && vin1.test(value)) {
                  callback();
                } else {
                  callback(new Error("告警阈值只能为正整数"));
                }
              }
            },
          },
        ],
      },
    };
  },
  methods: {
    init() {
      getPermission(this.$route.name, this.btn);
      this.getDiskList();
      this.getIndexList();
    },

    //请求数据函数
    getDiskList() {
      let _this = this;

      _this.tableLoading = true;
      _this.$http
        .post("preprocessDiskMonitor/list.json")
        .then((res) => {
          _this.tableLoading = false;
          if (res.data.code == 0) {
            res.data.data && this.formatDiskStatus(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.tableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getIndexList() {
      let _this = this;
      let endTime = Date.parse(new Date());
      let startTime = endTime - 2 * 24 * 60 * 60 * 1000;
      let data = {
        startTime: parseTime(startTime, "{y}-{m}-{d} 00:00:00"),
        endTime: parseTime(endTime, "{y}-{m}-{d} 00:00:00"),
      };

      _this.indexLoading = true;
      _this.$http
        .post("preprocessDiskMonitor/index.json", _this.qs.stringify(data))
        .then((res) => {
          _this.indexLoading = false;
          if (res.data.code == 0) {
            res.data.data && _this.initIndexData(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.indexLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    deleteData(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("preprocessDiskMonitor/delete", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("删除成功");
            _this.getDiskList();
          } else {
            _this.waFn("操作失败");
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
        });
    },
    getMonitorDir(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("dynamicAndStatic/getMonitorDir", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            this.editForm = { ...res.data.data };
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    updateMonitorDir(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("dynamicAndStatic/updateMonitorDir", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.editFormVisible = false;
            _this.getDiskList();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //处理数据函数
    formatDiskStatus(data) {
      data.forEach((item) => {
        switch (item.alarmStatus) {
          case 0:
            item["alarmStatus"] = "#25ce88";
            item["content"] = "良好";
            break;
          case 1:
            item["alarmStatus"] = "#f93846";
            item["content"] = "错误";
            break;
        }
      });
      this.dataList = data;
    },
    initIndexData(data) {
      $.each(data, (index, item) => {
        let obj = {};
        obj.indexName = index;
        if (item && item.length != 0) {
          obj.today = item[0];
          obj.yesterday = item[1];
          obj.beforeYesterday = item[2];
        }
        this.indexList.push(obj);
      });
    },
    currencyCapacity(value) {
      if (value || value == 0) {
        value = Number(value);
        if (value > 0 && value < 1024) {
          value = value + "B";
        } else if (value >= 1024 && value < 1048576) {
          value = (value / 1024).toFixed(2) + "KB";
        } else if (value >= 1048576 && value < 1073741824) {
          value = (value / 1048576).toFixed(2) + "MB";
        } else if (value >= 1073741824 && value < 1099511627776) {
          value = (value / 1073741824).toFixed(2) + "GB";
        } else if (value >= 1099511627776) {
          value = (value / 1099511627776).toFixed(2) + "TB";
        }
        return value;
      } else {
        return "-";
      }
    },

    //功能函数
    addBtn() {
      this.addFormVisible = true;
    },
    deleteBtn(row) {
      let _this = this,
        data = {};

      this.$confirm("您确定要删除该预处理目录吗？", "提示", {
        roundBUtton: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      }).then(() => {
        data = {
          id: row.id,
        };
        _this.deleteData(data);
      });
    },
    closeAdd(val) {
      this.addFormVisible = false;
      val === "SUCCESS" && this.getDiskList();
    },
    editBtn(row) {
      let data = {
        ip: row.ip,
        dir: row.dir,
      };
      this.getMonitorDir(data);
      this.editFormVisible = true;
    },
    saveBtn() {
      this.$refs["editForm"].validate((valid) => {
        if (valid) {
          let data = { ...this.editForm };
          data.size = data.size + data.unit;
          this.updateMonitorDir(data);
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.$refs["editForm"].resetFields();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
  },
  mounted() {
    this.init();
  },
  filters: {
    cerrenyPercent(value) {
      if (value) {
        value = Number(value.split("%")[0]);
        return Number(value);
      }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.pretreatment-content {
  height: 100%;
  width: 100%;
}
.editDisk-form {
  width: 90%;
  margin: auto;
}
.alarmInput {
  width: calc(100% - 100px);
}
.alarmSelect {
  width: 100px;
}
.btn-style:first-child {
  margin-right: 10px;
}
</style>