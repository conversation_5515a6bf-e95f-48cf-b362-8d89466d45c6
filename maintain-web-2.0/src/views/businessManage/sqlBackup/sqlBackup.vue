<!-- mysql备份监控 -->
<template>
  <div class="monitor-content">
    <el-row class="content-row">
      <el-row class="buiness-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">{{ baseConfig.title }}</div>
          </div>
          <el-row class="table-row">
            <el-col :span="24" class="disk-btn">
              <button @click.prevent="addBtn" class="btn-style">
                <div class="button-icon button-add"></div>
                <span>添加</span>
              </button>
            </el-col>
            <el-col
              :span="24"
              class="content-table"
              v-loading.fullscreen="baseTableLoading"
            >
              <el-table
                :data="baseConfig.data"
                border
                :row-class-name="bodyClass"
                :header-cell-style="tableHeader"
                :header-row-class-name="headerClass"
                height="100%"
              >
                <el-table-column
                  prop="localIp"
                  label="IP"
                  width="127"
                ></el-table-column>
                <el-table-column
                  prop="localPath"
                  label="备份路径"
                ></el-table-column>
                <el-table-column
                  prop="dbUser"
                  label="用户名"
                  width="150"
                ></el-table-column>
                <el-table-column
                  prop="remoteIp"
                  label="异机备份IP"
                  width="127"
                ></el-table-column>
                <el-table-column
                  prop="remotePath"
                  label="异机备份路径"
                ></el-table-column>
                <!-- <el-table-column
                  prop="dbPass"
                  label="备份数据库的密码"
                ></el-table-column> -->
                <el-table-column align="center" label="操作" width="140">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      class="text-button"
                      @click="editBtn(scope.row)"
                    >
                      <div class="agentButton button-edit"></div>
                      编辑
                    </el-button>
                    <el-button
                      type="text"
                      class="text-button"
                      @click="
                        deleteBtn(scope.row, scope.$index, baseConfig.data)
                      "
                    >
                      <div class="agentButton button-delete"></div>
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-row>
      </el-row>
      <el-row class="index-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">{{ sqlConfig.title }}</div>
          </div>
          <el-row class="table-row">
            <el-col :span="24" class="disk-btn">
              <span>监控时间:</span>
              <el-date-picker
                v-model="form.monitorTime"
                type="daterange"
                range-separator="至"
                start-placeholde="开始日期"
                end-placeholde="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="timeChange"
                :default-time="['00:00:00', '23:59:59']"
              >
              </el-date-picker>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;备份类型:</span>
              <el-select v-model="form.type" @change="getSqlData">
                <el-option
                  v-for="(item, index) in typeOption"
                  :key="index"
                  :value="item.value"
                  :label="item.name"
                >
                </el-option>
              </el-select>
              <el-button @click="setIgnoreStatus" class="el-icon-edit-outline"
                >设置为忽略</el-button
              >
              <el-button @click="setback" class="el-icon-refresh"
                >重新备份</el-button
              >
            </el-col>
            <el-col
              :span="24"
              class="content-table"
              v-loading.fullscreen="sqlTableLoading"
            >
              <el-table
                :data="sqlConfig.data"
                border
                :row-class-name="bodyClass"
                :header-cell-style="tableHeader"
                @selection-change="tableChange"
                :header-row-class-name="headerClass"
                height="100%"
              >
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column
                  prop="ip"
                  label="IP"
                  width="127"
                  sortable
                ></el-table-column>

                <el-table-column
                  prop="backupTime"
                  label="备份时间"
                  width="145"
                  sortable
                ></el-table-column>
                <el-table-column
                  prop="backupType"
                  label="备份类型"
                  width="100"
                  :formatter="formatType"
                ></el-table-column>

                <el-table-column
                  prop="ignoreStatus"
                  label="是否忽略"
                  width="90"
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.ignoreStatus == 1 ? "是" : "否" }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="success" label="备份状态" width="90">
                  <template slot-scope="scope">
                    <span
                      :style="{
                        color: scope.row.success == 0 ? '#ff4f4f' : '#3db3ff',
                      }"
                      >{{ scope.row.success | formatBackUpStatus }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  prop="remoteSuccess"
                  label="异机状态"
                  width="90"
                >
                  <template slot-scope="scope">
                    <span
                      :style="{
                        color:
                          scope.row.remoteSuccess == 0 ? '#ff4f4f' : '#3db3ff',
                      }"
                      >{{ scope.row.remoteSuccess | formatBackUpStatus }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  prop="backupDatabases"
                  label="备注"
                ></el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-row>
      </el-row>
    </el-row>
    <sqlBackDialog
      v-if="hideDialog"
      @returnMain="closeProdureDialog"
      :dialogData="dialogData"
    ></sqlBackDialog>
  </div>
</template>


<script>
import { parseTime } from "@/utils/index.js";
import sqlBackDialog from "./sqlBackDialog.vue";
import {
  tableHeaderStyle,
  bodyRowClassName,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "monitor-content",
  components: { sqlBackDialog },
  data() {
    return {
      sqlTableLoading: false, //表格加载遮罩
      baseTableLoading: false, //表格加载遮罩
      hideDialog: false, //是否打开弹出框
      acticeName: ["1", "2"], //选中第一个、第二个 折叠栏
      tableSelection: [],
      sqlConfig: {
        title: "mysql备份管理",
        data: [],
      },
      baseConfig: {
        title: "数据库备份管理",
        data: [],
      },
      typeOption: [
        { name: "按天备份", value: 0 },
        { name: "按周备份", value: 1 },
      ],
      form: {
        monitorTime: [],
        type: 0,
        startTime: "",
        endTime: "",
      },
    };
  },
  methods: {
    init() {
      this.getSqlData();
      this.getBaseData();
    },
    //是否关闭弹出框
    closeProdureDialog(val) {
      this.hideDialog = false;
      val === "SUCCESS" && this.getBaseData();
    },
    //请求数据函数
    getSqlData() {
      let _this = this;

      _this.sqlTableLoading = true;
      _this.$http
        .post("mysqlbackup/list.json", _this.qs.stringify(_this.form))
        .then((res) => {
          _this.sqlTableLoading = false;
          if (res.data.code == 0) {
            this.sqlConfig.data = [...res.data.data];
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.sqlTableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getBaseData() {
      let _this = this;

      _this.baseTableLoading = true;
      _this.$http
        .post("dbbackup/list.json", _this.qs.stringify({}))
        .then((res) => {
          _this.baseTableLoading = false;
          if (res.data.code == 0) {
            this.baseConfig.data = [...res.data.data];
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.baseTableLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    deleteBtn(row, index, tableData) {
      let _this = this;
      this.$confirm(`确定删除？`, "提示", {
        roundBUtton: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      }).then(() => {
        _this.sqlTableLoading = true;
        _this.$http
          .post("dbbackup/delete", _this.qs.stringify({ id: row.id }))
          .then((res) => {
            _this.sqlTableLoading = false;
            if (res.data.code == 0) {
              tableData.splice(index, 1);
            } else {
              res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
            }
          })
          .catch((error) => {
            _this.sqlTableLoading = false;
            _this.erFn();
            console.log(error);
          });
      });
    },
    editBtn(row) {
      this.dialogData = { title: "编辑数据库备份管理", type: "edit", row: row };
      this.hideDialog = true;
    },
    addBtn() {
      this.dialogData = { title: "添加数据库备份管理", type: "add" };
      this.hideDialog = true;
    },
    tableChange(val) {
      this.tableSelection = val;
    },
    //处理数据函数
    formatType(row, column, val) {
      this.typeOption.forEach((item, index) => {
        item.value == val && (val = item.name);
      });
      return val;
    },
    //重新备份
    setback() {
      var _this = this;
      this.$confirm(
        `您确定要按“${this.form.type == "1" ? "周" : "天"}”重新备份数据吗？`,
        "提示",
        {
          roundBUtton: true,
          cancelButtonText: "关闭",
          confirmButtonText: "确定",
          confirmButtonClass: "confirm-success",
          closeOnClickModal: false,
        }
      )
        .then(() => {
          _this.sqlTableLoading = true;
          _this.$http
            .post(
              "dbbackup/rebackup",
              _this.qs.stringify({ type: _this.form.type })
            )
            .then((res) => {
              _this.sqlTableLoading = false;
              if (res.data.code == 0) {
                _this.getSqlData();
              } else {
                res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
              }
            })
            .catch((error) => {
              _this.sqlTableLoading = false;
              _this.erFn();
              console.log(error);
            });
        });
    },

    setIgnoreStatus() {
      let _this = this;
      let ids = [];

      if (this.tableSelection.length == 0)
        return this.waFn("请选择需要操作的数据");

      this.tableSelection.forEach((item) => {
        item.ignoreStatus == 0 && ids.push(item.id);
      });

      if (ids.length == 0) return this.waFn("当前选中数据已全部被忽略！");
      this.$confirm("您确定要忽略选中的数据吗？", "提示", {
        roundBUtton: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      })
        .then(() => {
          _this.sqlTableLoading = true;
          _this.$http
            .post(
              "mysqlbackup/ignore_status",
              _this.qs.stringify({ idList: ids.join(",") })
            )
            .then((res) => {
              _this.sqlTableLoading = false;
              if (res.data.code == 0) {
                _this.getSqlData();
              } else {
                res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
              }
            })
            .catch((error) => {
              _this.sqlTableLoading = false;
              _this.erFn();
              console.log(error);
            });
        })
        .catch(() => {});
    },
    timeChange(val) {
      if (val) {
        this.form.startTime = val[0];
        this.form.endTime = val[1];
      } else {
        this.form.startTime = "";
        this.form.endTime = "";
      }
      this.getDataList();
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
  },
  mounted() {
    this.form.startTime = parseTime(
      Date.parse(new Date()) - 7 * 24 * 60 * 60 * 1000,
      "{y}-{m}-{d} 00:00:00"
    );
    this.form.endTime = parseTime(new Date(), "{y}-{m}-{d} 23:59:59");
    this.form.monitorTime.push(this.form.startTime);
    this.form.monitorTime.push(this.form.endTime);
    this.init();
  },
  filters: {
    formatBackUpStatus(value) {
      switch (value) {
        case 0:
          value = "失败";
          break;
        case 1:
          value = "成功";
          break;
      }
      return value;
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.monitor-content {
  height: 100%;
  width: 100%;
}

.el-icon-edit-outline {
  margin-left: 10px;
}
</style>

