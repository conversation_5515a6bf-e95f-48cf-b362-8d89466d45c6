<!-- 添加程序 -->
<template>
  <div class="procedure-content" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      :title="dialogData.title"
      :visible.sync="addFormVisible"
      :close-on-click-modal="false"
      width="540px"
      class="dialog-border"
      custom-class="procedureDialog"
      @close="resetForm"
      v-dialogDrag
    >
      <el-row type="flex" class="row-bg">
        <el-form
          label-width="85px"
          :model="addForm"
          :rules="rules"
          ref="addForm"
          class="procedure-form"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="IP:" prop="localIp">
                <el-select
                  collapse-tags
                  v-model="addForm.localIp"
                  @change="ipChange"
                  class="item-select"
                  placeholder="请选择IP"
                >
                  <el-option
                    v-for="item in ipOptions"
                    :key="item.ip"
                    :label="
                      item.os ? item.ip + '（Windows）' : item.ip + '（Linux）'
                    "
                    :value="item.ip"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="备份路径:" prop="localPath">
                <el-input maxlength="50" v-model="addForm.localPath"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item label="异机备份IP:" prop="remoteIp">
                <el-select
                  class="item-select"
                  collapse-tags
                  v-model="addForm.remoteIp"
                  @change="ipChange"
                  placeholder="请选择异机备份IP"
                >
                  <el-option
                    v-for="item in ipOptions"
                    :key="item.ip"
                    :label="
                      item.os ? item.ip + '（Windows）' : item.ip + '（Linux）'
                    "
                    :value="item.ip"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="用户名:" prop="dbUser">
                <el-input maxlength="50" v-model="addForm.dbUser"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="密码:" prop="dbPass">
                <el-input
                  maxlength="50"
                  show-password
                  v-model="addForm.dbPass"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="异机备份路径:" prop="remotePath">
                <el-input
                  maxlength="50"
                  v-model="addForm.remotePath"
                ></el-input>
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-form>
      </el-row>
      <div slot="footer" class="footer-button">
        <button @click.prevent="testConnect" class="btn-style">测试连接</button>
        <button
          @click.prevent="saveBtn"
          v-loading.fullscreen.lock="fullscreenLoading"
          class="btn-style primary-btn"
        >
          确定
        </button>
        <button @click.prevent="addFormVisible = false" class="btn-style">
          关闭
        </button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkIPNotEnablePort, checkChinese } from "@/utils/validate.js";

export default {
  name: "procedure-content",
  props: { dialogData: Object },
  data() {
    return {
      fullscreenLoading: false,
      addFormVisible: true,
      ipOptions: [],
      addForm: {
        id: "",
        remoteIp: "", //"异机备份IP"
        remotePath: "", // "异机备份路径"
        localIp: "", //"配置的备份数据库IP"
        // localPath: "", //"备份数据库机器上的路径"
        dbUser: "", // "备份数据库的用户名"
        dbPass: "", //"备份数据库的密码"
      },
      rules: {
        remoteIp: [
          {
            required: true,
            trigger: "change",
            message: "请选择IP",
          },
        ],
        remotePath: [
          {
            trigger: "blur",
            required: true,
            validator: (rule, value, callback) => {
              if (value == "") {
                callback(new Error("请输入路径"));
              } else {
                if (checkChinese(value)) {
                  callback();
                } else {
                  callback(new Error("路径格式不正确"));
                }
              }
            },
          },
        ],
        localIp: [
          {
            required: true,
            trigger: "change",
            message: "请选择IP",
          },
        ],
        localPath: [
          {
            trigger: "blur",
            required: true,
            validator: (rule, value, callback) => {
              if (value == "") {
                callback(new Error("请输入路径"));
              } else {
                if (checkChinese(value)) {
                  callback();
                } else {
                  callback(new Error("路径格式不正确"));
                }
              }
            },
          },
        ],
        dbUser: [
          {
            required: true,
            trigger: "change",
            message: "请输入备份数据库的用户名",
          },
        ],
        dbPass: [
          {
            required: true,
            trigger: "change",
            message: "请输入备份数据库的密码",
          },
        ],
      },
    };
  },
  methods: {
    init() {
      this.addForm = this.dialogData.row || this.addForm;
      this.getIP();
    },
    testConnect() {
      let _this = this;
      _this.$refs["addForm"].validate((valid) => {
        if (
          this.addForm.remoteIp &&
          this.addForm.remoteIp == this.addForm.localIp
        ) {
          this.waFn("IP与异机备份IP不能相同!");
          return false;
        }
        if (valid) {
          _this.fullscreenLoading = true;
          _this.$http
            .post("dbbackup/testConnect", _this.qs.stringify(_this.addForm))
            .then((res) => {
              _this.fullscreenLoading = false;
              if (res.data.code == 0) {
                _this.suFn("连接成功!");
              } else {
                res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
              }
            })
            .catch((error) => {
              _this.erFn();
              _this.fullscreenLoading = false;
            });
        } else return false;
      });
    },
    //请求函数
    getIP() {
      let _this = this;

      _this.$http
        .post("software/hosts")
        .then((res) => {
          if (res.data.code == 0) {
            _this.ipOptions = res.data.data;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.erFn();
          console.log(error);
        });
    },
    ipChange(val) {
      if (
        this.addForm.remoteIp &&
        this.addForm.remoteIp == this.addForm.localIp
      ) {
        this.waFn("IP与异机备份IP不能相同!");
        return false;
      }
    },
    //请求函数
    sendQuest(data, url, text) {
      let _this = this;
      _this.fullscreenLoading = true;
      _this.$http
        .post(url, _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn(text);
            _this.resetForm("SUCCESS");
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
        });
    },
    //功能函数
    addData(obj) {
      let url = "";
      let text = "";
      switch (this.dialogData.type) {
        case "add":
          url = "dbbackup/add";
          text = "添加成功";
          break;
        case "edit":
          url = "dbbackup/update";
          text = "编辑成功";
          break;
      }
      this.sendQuest(obj, url, text);
    },
    saveBtn() {
      let _this = this;
      _this.$refs["addForm"].validate((valid) => {
        if (
          this.addForm.remoteIp &&
          this.addForm.remoteIp == this.addForm.localIp
        ) {
          this.waFn("IP与异机备份IP不能相同!");
          return false;
        }

        if (valid) {
          _this.addData(_this.addForm);
        } else return false;
      });
    },
    resetForm(val) {
      this.$refs["addForm"].resetFields();
      this.addFormVisible = false;
      this.$emit("returnMain", val);
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.procedure-content {
  height: 100%;
  overflow-y: auto;
}
.procedure-form {
  width: 90%;
  margin: auto;
}
.btn-style:first-child {
  margin-right: 10px;
}
.btn-style {
  padding: 0 20px;
}
.multipleIP {
  width: 100%;
}
.batchAdd {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.item-select {
  width: 100%;
}

.footer-button {
  text-align: inherit;
}

.footer-button > button:first-child {
  float: left;
}
</style>
