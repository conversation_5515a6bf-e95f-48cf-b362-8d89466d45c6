<!-- 数据监控图表 -->
<template>
  <div class="dataChart" v-loading.fullscreen="fullscreenLoading">
    <el-dialog
      title="数据量统计趋势图"
      :visible.sync="chartFormVisible"
      width="800px"
      top="10vh"
      class="dialog-border"
      @close="resetForm"
      :close-on-click-modal="false"
    >
      <el-row type="flex" class="row-bg dataChart-row">
        <div id="chartWarp"></div>
        <div v-show="chartShow" class="chartShow">暂无数据</div>
      </el-row>

      <div slot="footer" class="dialog-footer footer-button">
        <button @click.prevent="chartFormVisible = false" class="btn-style">
          关闭
        </button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "dataChart",
  props: ["chartData"],
  data() {
    return {
      flowLevel: "",
      fullscreenLoading: false,
      chartFormVisible: true,
      chartShow: true,
    };
  },
  methods: {
    //请求数据函数
    getDataList() {
      let _this = this,
        data = {
          id: this.chartData,
        };

      _this.fullscreenLoading = true;
      _this.$http
        .post("index-monitor/history", _this.qs.stringify(data))
        .then((res) => {
          if (res.data.code == 0) {
            _this.fullscreenLoading = false;
            res.data.data && _this.drawLine(res.data.data);
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          console.log(error);
          _this.erFn();
        });
    },

    //功能函数
    resetForm(val) {
      this.$emit("returnMain");
    },
    drawLine(data) {
      let chartDom = document.getElementById("chartWarp");
      let _this = this;

      if (data.time.length != 0) {
        let options = {};

        _this.chartShow = false;
        options = {
          tooltip: {
            trigger: "axis",
            // formatter:'{b0}</br>{a0}: {c0}个',
            axisPointer: {
              lineStyle: {
                type: "dashed",
                width: 1,
              },
            },
          },
          dataZoom: [
            {
              type: "slider",
              show: data.time.length > 10,
              bottom: 10,
              left: 90,
              right: 80,
              start: 0,
              end: 100,
              textStyle: {
                color: "#203e66",
              },
              height: 8,
              realtime: false,
              showDetail: true,
              showDataShadow: false,
              preventDefaultMouseMove: false,
              borderColor: "transparent",
              backgroundColor: "#f2f2f2",
              handleSize: 14,
              handleStyle: { color: "#00a8ff" },
              fillerColor: "#77cdf9", // 选中范围的填充颜色
              handleIcon:
                "M512 512m-512 0a500 500 0 1 0 1024 0 500 500 0 1 0-1024 0Z",
            },
          ],
          legend: {
            data: ["捕获数量", "入库数量"],
          },
          calculable: true,
          grid: {
            top: "16%",
            bottom: "20%",
            left: "10%",
            right: "6%",
          },
          xAxis: {
            type: "category",
            boundaryGap: ["10%", "10%"],
            axisLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            axisLabel: {
              color: "#203e66",
              length: 7,
              formatter: (val) => {
                let str = val.split(" ");
                return str.join("\n");
              },
            },
            data: data.time,
          },
          yAxis: [
            {
              type: "value",
              axisTick: { show: false }, //坐标轴刻度
              axisLine: {
                lineStyle: { color: "#e6e9f1" },
              },
              minInterval: 1,
              axisLabel: {
                show: true,
                textStyle: {
                  color: "#203e66",
                },
              },
              splitLine: {
                lineStyle: { color: "#e6e9f1" },
              },
            },
          ],
          series: [
            {
              name: "捕获数量",
              type: "line",
              smooth: true,
              itemStyle: {
                normal: {
                  color: "rgb(99,223,246)",
                },
                emphasis: {
                  borderColor: "red",
                },
              },
              data: data.catchCount,
            },
            {
              name: "入库数量",
              type: "line",
              smooth: true,
              itemStyle: {
                normal: {
                  color: "#7C50D6",
                },
                emphasis: {
                  borderColor: "red",
                },
              },
              data: data.recordCount,
            },
          ],
        };
        _this.flowLevel = _this.$echarts.init(chartDom);
        _this.flowLevel.setOption(options);
      } else {
        _this.chartShow = true;
      }

      window.onresize = function () {
        _this.flowLevel && _this.flowLevel.resize();
      };
    },
  },
  mounted() {
    this.getDataList();
  },
  destroyed() {
    window.onresize = null;
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dataChart {
  height: 100%;
  overflow-y: auto;
}
.btn-style {
  padding: 0 20px;
}
.chartShow {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #909399;
  transform: translate(-50%, -50%);
}
.dataChart-row {
  height: 250px;
}
#chartWarp {
  height: 100%;
  width: 100%;
}
</style>