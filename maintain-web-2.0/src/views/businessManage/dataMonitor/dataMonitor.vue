<!-- 数据监控 -->
<template>
  <div class="monitor-content" v-loading.fullscreen="fullscreenLoading">
    <el-row class="content-row">
      <el-row class="monitor-row">
        <el-row class="monitor-content">
          <el-row class="table-monitor">
            <el-row :span="24" class="disk-btn">
              <el-col :span="8" class="status-row">
                <StatusMsg
                  v-if="statusData != null"
                  :statusData="statusData"
                  :statusChange="statusClick"
                ></StatusMsg>
              </el-col>
              <el-col :span="16" class="deploy-Btn">
                <el-select v-model="form.type" @change="typeChange" clearable>
                  <el-option
                    v-for="(item, index) in dataTypeOptions"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  >
                  </el-option>
                </el-select>
                <el-input
                  placeholder="支持页面名称搜索"
                  class="search-input"
                  v-model="form.name"
                  clearable
                  @keyup.enter.native="searchBtn"
                >
                  <i
                    slot="prefix"
                    class="iconfont icon-search"
                    @click="searchBtn"
                  ></i>
                </el-input>
                <button
                  @click.prevent="addBtn"
                  class="btn-style"
                  v-if="btn.addBtn.value"
                >
                  <i class="iconfont icon-add"></i>
                  <span>新增监控页面</span>
                </button>
                <button @click.prevent="edit" class="btn-style">
                  <i class="iconfont icon-edit"></i>
                  <span>是否忽略</span>
                </button>
                <button
                  @click.prevent="deleteBtn"
                  class="btn-style"
                  v-if="btn.deleteBtn.value"
                >
                  <i class="iconfont icon-delete"></i>
                  <span>删除</span>
                </button>
              </el-col>
            </el-row>
            <el-row :span="24" class="dataMonitor-table">
              <el-table
                :data="dataList"
                border
                :row-class-name="bodyClass"
                :header-cell-style="tableHeader"
                :header-row-class-name="headerClass"
                @cell-click="cellClick"
                @selection-change="tableChange"
                height="100%"
              >
                <el-table-column type="selection"></el-table-column>
                <el-table-column
                  prop="name"
                  label="页面名称"
                  class-name="name"
                  width="350"
                >
                  <template slot-scope="scope">
                    <div class="table-pageName">
                      <!-- {{scope.row.name}} -->
                      <el-popover
                        class="popoverSpan"
                        trigger="hover"
                        placement="top-start"
                      >
                        <p class="popoverContent">
                          {{ scope.row.name || "-" }}
                        </p>
                        <span slot="reference" class="name-wrapper">
                          {{ scope.row.name || "-" }}
                        </span>
                      </el-popover>
                    </div>
                    <i
                      class="iconfont icon-edit icon-btn"
                      @click="editBtn(scope.row)"
                    ></i>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="status"
                  label="数据状态"
                  class-name="status"
                  width="100"
                >
                  <template slot-scope="scope">
                    <el-tag
                      size="small"
                      class="status-tag"
                      :color="scope.row.status"
                    >
                      {{ scope.row.content }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="是否忽略" width="115">
                  <template slot-scope="scope">
                    <el-switch
                      disabled
                      v-model="scope.row.ignoreStatus"
                      active-color="#25ce88"
                      inactive-color="#cfcfcf"
                      :active-value="1"
                      :inactive-value="0"
                      :active-text="scope.row.ignoreStatus ? '忽略' : '未忽略'"
                      @click.native="switchClick(scope.row)"
                    >
                    </el-switch>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="type"
                  label="数据类型"
                  class-name="type"
                  width="100"
                  :formatter="formatType"
                ></el-table-column>
                <el-table-column
                  prop="today"
                  label="今日入库"
                  class-name="today"
                  width="90"
                ></el-table-column>
                <el-table-column
                  prop="yesterday"
                  label="昨日入库"
                  width="90"
                  class-name="yesterday"
                ></el-table-column>
                <el-table-column
                  prop="note"
                  label="备注"
                  class-name="overflowColumn"
                >
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row.editShow"
                      :ref="scope.row.refName"
                      v-model="scope.row.inputVal"
                      @blur="noteBlur(scope.row)"
                      :autosize="true"
                      :show-word-limit="true"
                      maxlength="255"
                      type="textarea"
                      rows="1"
                      class="noteInput"
                    >
                    </el-input>
                    <el-popover
                      class="popoverSpan"
                      trigger="hover"
                      placement="top-start"
                      v-else
                    >
                      <p class="popoverContent">{{ scope.row.note || "-" }}</p>
                      <span slot="reference" class="name-wrapper edit-span">
                        {{ scope.row.note || "-" }}
                      </span>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column label="趋势图" width="80" align="center">
                  <template slot-scope="scope">
                    <i
                      class="iconfont icon-trend icon-btn"
                      @click="chartBtn(scope.row)"
                    ></i>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
            <el-row class="dataMonitor-page">
              <el-col :span="24">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="form.pn"
                  :page-sizes="[20, 50, 100]"
                  :page-size="form.ps"
                  layout="total,sizes,prev,pager,next,jumper"
                  :total="form.total"
                  background
                  popper-class="pagination-popper"
                >
                </el-pagination>
              </el-col>
            </el-row>
          </el-row>
        </el-row>
      </el-row>
    </el-row>
    <dataDialog
      v-if="addFormVisible"
      @returnMain="closeDialog"
      :twoRow="twoRow"
    ></dataDialog>
    <dataChart
      v-if="chartFormVisible"
      @returnMain="closeChart"
      :chartData="chartData"
    ></dataChart>

    <el-dialog
      title="设置忽略状态"
      :visible.sync="isEditStatus"
      width="300px"
      top="10vh"
      class="dialog-border"
      :close-on-click-modal="false"
    >
      <el-row>
        <span>是否忽略：</span>
        <el-switch
          v-model="editStatus"
          active-color="#25ce88"
          inactive-color="#cfcfcf"
          :active-value="1"
          :inactive-value="0"
          :active-text="editStatus ? '忽略' : '未忽略'"
        >
        </el-switch>
      </el-row>
      <el-row
        ><span class="status-msg">*将覆盖已选数据的忽略状态 </span></el-row
      >
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="changeAllStatus"> 确定</el-button>
        <el-button @click="isEditStatus = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import getPermission from "@/utils/permissions.js";
import { parseTime } from "@/utils/index.js";
import dataDialog from "./dataDialog.vue";
import dataChart from "./dataChart.vue";
import StatusMsg from "@/views/common/statusMsg";

import {
  tableHeaderStyle,
  bodyRowClassName,
  headerRowClassName,
} from "@/utils/tableStyle.js";

export default {
  name: "monitor-content",
  components: { dataDialog, dataChart, StatusMsg },
  data() {
    return {
      isEditStatus: false,
      editStatus: 0,

      statusData: null,
      chartData: null,
      fullscreenLoading: false,
      addFormVisible: false,
      chartFormVisible: false,
      dataList: [],
      tableSelection: [],
      dataTypeOptions: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "基础数据",
          value: 0,
        },
        {
          label: "挖掘数据",
          value: 1,
        },
      ],

      btn: {
        addBtn: {
          name: "添加",
          value: true,
        },
        deleteBtn: {
          name: "删除",
          value: true,
        },
      },
      form: {
        type: "",
        name: "",
        pn: 1,
        ps: 20,
        total: 0,
        status: "", //YELLOW/RED/GREEN
      },
      addForm: {},
      twoRow: {},
    };
  },
  methods: {
    init() {
      getPermission(this.$route.name, this.btn);
      this.getDataList();
    },
    statusClick(item) {
      if (item.num == "0") {
        this.waFn("暂无数据");
        return;
      }
      this.form.status = item.field;
      this.getDataList();
    },
    //请求数据函数
    getDataList() {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("index-monitor/list.json", _this.qs.stringify(_this.form))
        .then((res) => {
          var result = res.data,
            data = result.data;

          _this.fullscreenLoading = false;
          if (result.code == 0) {
            result.data.list && this.formatData(data.list.list);
            //设置状态数量
            this.statusData = data.status;
            this.form.total = data.list.total;
          } else result.msg ? _this.waFn(result.msg) : _this.erFn();
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    getApkeys() {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("front-device/apKeys")
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    deleteDataList(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("index-monitor/delete", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("删除成功");
            _this.getDataList();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    saveNote(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("index-monitor/note", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.getDataList();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    changeStatus(data) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("index-monitor/update", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.getDataList();
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },
    changeAllStatus() {
      let _this = this;
      let ids = [];

      this.tableSelection.forEach((item) => {
        ids.push(item.id);
      });
      let data = {
        ids: ids.join(","),
        ignoreStatus: this.editStatus,
      };

      _this.fullscreenLoading = true;
      _this.$http
        .post("index-monitor/update_all_status", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            _this.suFn("修改成功");
            _this.getDataList();
            this.isEditStatus = false;
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //处理数据函数
    formatData(data) {
      data.forEach((item, index) => {
        switch (item.status) {
          case 0:
            item.status = "#25ce88";
            item.content = "良好";
            break;
          case 1:
            item.status = "#fb843b";
            item.content = "警告";
            break;
          case 2:
            item.status = "#f93846";
            item.content = "错误";
            break;
        }
        item.editShow = false;
        item.inputVal = item.note || "";
        item.refName = "noteInput" + index;
      });
      this.dataList = [...data];
    },
    formatType(row, column, value) {
      switch (value) {
        case 0:
          value = "基础数据";
          break;
        case 1:
          value = "挖掘数据";
          break;
      }
      return value;
    },

    //功能函数
    handleSizeChange(val) {
      this.form.ps = val;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.form.pn = val;
      this.getDataList();
    },
    noteBlur(row) {
      let data = {};

      row.inputVal == undefined && (row.inputVal = "");
      if (row.inputVal == row.note) {
        row.editShow = false;
        return;
      }
      data = {
        id: row.id,
        note: row.inputVal,
      };
      this.saveNote(data);
    },
    cellClick(row, column, cell, event) {
      if (column.label == "备注" && row.editShow == false) {
        row.inputVal = row.note || "";
        row.editShow = true;
        this.$nextTick(() => {
          this.$refs[row.refName].focus();
        });
      }
    },
    addBtn() {
      this.twoRow.title = "新增监控页面";
      this.twoRow.url = "index-monitor/add";
      this.addFormVisible = true;
    },
    tableChange(val) {
      this.tableSelection = val;
    },

    edit() {
      if (this.tableSelection.length == 0)
        return this.waFn("请选择需要修改的数据");
      this.isEditStatus = true;
    },

    deleteBtn() {
      let data = {},
        searchData = {};

      switch (this.tableSelection.length) {
        case 0:
          this.waFn("请选择需要删除的数据");
          return false;
          break;
        case 1:
          data.ids = this.tableSelection[0].id;
          break;
        default:
          data.ids = [];
          this.tableSelection.forEach((item) => {
            data.ids.push(item.id);
          });
          data.ids = data.ids.join(",");
      }
      this.$confirm("您确定要删除选中的数据吗？", "提示", {
        roundBUtton: true,
        cancelButtonText: "关闭",
        confirmButtonText: "确定",
        confirmButtonClass: "confirm-success",
        closeOnClickModal: false,
      })
        .then(() => {
          this.deleteDataList(data);
        })
        .catch(() => {});
    },
    searchBtn() {
      this.getDataList();
    },
    typeChange() {
      this.getDataList();
    },
    editBtn(row) {
      this.twoRow.title = "编辑监控页面";
      this.twoRow.url = "index-monitor/update";
      this.twoRow.data = row;
      this.addFormVisible = true;
    },
    chartBtn(row) {
      this.chartData = row.id;
      this.chartFormVisible = true;
    },
    switchClick(row) {
      let data = {
        id: row.id,
        ignoreStatus: row.ignoreStatus ? 0 : 1,
      };

      this.changeStatus(data);
    },
    closeDialog(val) {
      this.addFormVisible = false;
      val === "SUCCESS" && this.getDataList();
    },
    closeChart() {
      this.chartFormVisible = false;
    },
    tableHeader() {
      return tableHeaderStyle();
    },
    bodyClass() {
      return bodyRowClassName();
    },
    headerClass() {
      return headerRowClassName();
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.monitor-content {
  height: 100%;
  width: 100%;
}
.monitor-row {
  height: 100%;
  width: 100%;
  padding: 10px;
}
.monitor-content {
  padding: 0;
}
.table-monitor {
  height: 100%;
  padding: 10px 10px 0 10px;
}
.dataMonitor-table {
  padding-bottom: 0;
  height: calc(100% - 88px);
}
.dataMonitor-page {
  margin: 10px 0 0;
  text-align: center;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-style:not(:last-child) {
  margin-right: 5px;
  margin-left: 5px;
}
.btn-dialog {
  padding: 0 20px;
}
.button-col {
  text-align: right;
}
.search-input {
  width: 200px;
  margin-left: 10px;
}

.disk-btn .icon-edit {
  font-size: 14px;
}

.icon-btn {
  cursor: pointer;
  color: #1e85e5;
}
.icon-btn:hover {
  color: #409eff;
}
.table-pageName {
  width: calc(100% - 20px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: bottom;
}
.noteInput {
  width: 100%;
}
.status-row,
.status-describe {
  height: 100%;
}
.status-describe > span {
  display: inline-block;
  line-height: 26px;
  cursor: pointer;
  margin: 0 5px;
}

.status-describe > span > span > span {
  text-decoration: underline;
}
.deploy-Btn {
  text-align: right;
}
.status-msg {
  margin-top: 20px;
  color: #f44336;
  display: inline-block;
}
</style>