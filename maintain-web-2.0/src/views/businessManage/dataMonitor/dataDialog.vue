<!-- 数据监控弹出框 -->
<template>
    <div class="dataDialog" v-loading.fullscreen = 'fullscreenLoading'>
        <el-dialog 
            :title="twoRow.title" 
            :visible.sync="addFormVisible" 
            width="600px" top="10vh" 
            class="dialog-border" 
            :close-on-click-modal="false"
            @close="resetForm">

            <el-row type="flex" class="row-bg">
                <el-form 
                    label-width="100px" 
                    :model="addForm" 
                    :rules="rules" 
                    ref="addForm" class="monitorAdd-form">
                    <el-col :span="24">
                        <el-form-item label="页面名称:" prop="name">
                            <el-input v-model="addForm.name"></el-input>
                        </el-form-item>
                        <el-form-item label="页面索引:" prop="index">
                            <el-input v-model="addForm.index"></el-input>
                        </el-form-item>
                        <el-form-item label="程序名:" prop="softwareName">
                            <el-input v-model="addForm.softwareName"></el-input>
                        </el-form-item>
                        <el-form-item label="搜索条件:" prop="condition">
                            <el-input v-model="addForm.condition"></el-input>
                        </el-form-item>
                        <el-form-item label="入库时间字段:" prop="recordField">
                            <el-input v-model="addForm.recordField"></el-input>
                        </el-form-item>
                        <el-form-item label="数据类型:" prop="type">
                            <el-select v-model="addForm.type">
                                <el-option 
                                    v-for="item in dataTypeOptions" 
                                    :key="item.value" 
                                    :label="item.label" 
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>

            <div slot="footer" class="dialog-footer footer-button">
                <button @click.prevent="addFormVisible = false" class="btn-style btn-dialog">关闭</button>
                <button @click.prevent="saveBtn" v-loading.fullscreen.lock='fullscreenLoading' class="btn-style btn-dialog primary-btn">确定</button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "dataDialog",
    props: { twoRow: Object },
    data() {
        return {
            fullscreenLoading: false,
            addFormVisible: true,

            dataTypeOptions: [
                {
                    label: "基础数据",
                    value: 0
                },
                {
                    label: "挖掘数据",
                    value: 1
                }
            ],

            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入页面名称"
                    }
                ],
                index: [
                    {
                        required: true,
                        message: "请输入页面索引"
                    }
                ],
                softwareName: [
                    {
                        message: "请输入程序名"
                    }
                ],
                condition: [
                    {
                        message: "请输入搜索条件"
                    }
                ],
                recordField: [
                    {
                        required: true,
                        message: "请输入入库时间字段"
                    }
                ],
                type: [
                    {
                        required: true,
                        message: "请选择数据类型"
                    }
                ]
            },
            editData: {},
            addForm: {
                name: "",
                index: "",
                softwareName: "",
                condition: "",
                recordField: "@createtime",
                type: 0
            }
        };
    },
    methods: {
        //请求数据函数
        addData(data) {
            let _this = this;

            _this.$http
                .post(this.twoRow.url, _this.qs.stringify(data))
                .then(res => {
                    if (res.data.code == 0) {
                        let text =
                            this.twoRow.title == "新增监控页面"
                                ? "添加成功"
                                : "修改成功";
                        this.suFn(text);
                        this.resetForm("SUCCESS");
                    } else {
                        res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
                    }
                })
                .catch(error => {
                    _this.erFn();
                });
        },

        //功能函数
        resetForm(val) {
            this.$refs["addForm"].resetFields();
            this.addFormVisible = false;
            this.$emit("returnMain", val);
        },
        saveBtn() {
            this.$refs["addForm"].validate(valid => {
                if (valid) {
                    this.addData(this.addForm);
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    },
    created() {
        this.twoRow.title == "编辑监控页面" &&
            (this.addForm = { ...this.twoRow.data });
    },
    mounted() {
        // this.getDataList();
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dataDialog {
    height: 100%;
    overflow-y: auto;
}
.monitorAdd-form {
    width: 90%;
    margin: auto;
}
.btn-style:first-child {
    margin-right: 10px;
}
.btn-style {
    padding: 0 20px;
}
</style>