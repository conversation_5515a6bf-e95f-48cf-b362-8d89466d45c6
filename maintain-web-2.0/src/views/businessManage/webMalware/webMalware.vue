<!-- 网页挂马监控 -->
<template>
  <div class="dynamic-content" v-loading.fullscreen="fullscreenLoading">
    <el-row class="content-row dynamic-row">
      <el-row class="analysis-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">威胁趋势</div>
            <span class="msg-tips">*仅统计今天之前的数据</span>
          </div>
          <el-row class="form-time">
            <el-radio-group
              v-model.number="timeChart"
              size="mini"
              @change="timeChange($event, '威胁趋势')"
            >
              <el-radio-button
                class="proce-radio"
                v-for="item in timeOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-row>
          <el-row class="chart-row">
            <div class="noData-div" v-show="trendShow">暂无数据</div>
            <el-row class="chart-content">
              <div class="chartWrap" id="trendChart"></div>
            </el-row>
          </el-row>
        </el-row>
      </el-row>

      <el-row class="analysis-row">
        <el-row class="host-content">
          <div class="host-header">
            <div class="host-text">检测数量</div>
          </div>
          <el-row class="form-time">
            <el-radio-group
              v-model.number="timeLine"
              size="mini"
              @change="timeChange($event, '检测数量')"
            >
              <el-radio-button
                class="proce-radio"
                v-for="item in timeOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-row>
          <el-row class="chart-row">
            <div class="noData-div" v-show="detectShow">暂无数据</div>
            <el-row class="chart-content">
              <div class="chartWrap" id="detectChart"></div>
            </el-row>
          </el-row>
        </el-row>
      </el-row>
    </el-row>
  </div>
</template>

<script>
import { parseTime } from "@/utils/index.js";

export default {
  name: "dynamic-content",
  data() {
    return {
      timeChart: 6,
      timeLine: 6,
      trendChart: "",
      detectChart: "",

      fullscreenLoading: false,
      trendShow: true,
      detectShow: true,

      timeOptions: [
        {
          label: "过去1天",
          value: 0,
        },
        {
          label: "过去3天",
          value: 2,
        },
        {
          label: "过去5天",
          value: 4,
        },
        {
          label: "过去7天",
          value: 6,
        },
      ],
      dynamicList: [],
      colorArr: ["#ff4f5e", "#ff9757", "#4688ff"],
      trendLegend: {
        high: "高危",
        danger: "中危",
        suspicious: "低危",
      },
      detectLegend: {
        feature: "特征检测",
        static: "静态检测",
        dynamic: "动态检测",
      },
      dynamicChart: {
        failList: [],
        successList: [],
        notList: [],
        x: [],
      },

      searchForm: {
        startTime: "",
        endTime: "",
      },
    };
  },
  methods: {
    //请求数据函数
    getDataList(data, type) {
      let _this = this;

      _this.fullscreenLoading = true;
      _this.$http
        .post("webmalware/list.json", _this.qs.stringify(data))
        .then((res) => {
          _this.fullscreenLoading = false;
          if (res.data.code == 0) {
            if (res.data.data.length != 0) {
              // switch (type) {
              //     case '威胁趋势':
              //         this.initTrendData(res.data.data);
              //         break;
              //     case '检测数量':
              //         this.initDetectData(res.data.data);
              //         break;
              //     default:
              //         this.initTrendData(res.data.data);
              //         this.initDetectData(res.data.data);
              //         break;
              // }
              this.initData(res.data.data, type);
            }
          } else {
            res.data.msg ? _this.waFn(res.data.msg) : _this.erFn();
          }
        })
        .catch((error) => {
          _this.fullscreenLoading = false;
          _this.erFn();
          console.log(error);
        });
    },

    //处理数据函数
    initData(data, type) {
      let trendData = {
          x: [],
          y: [],
        },
        detectData = {
          x: [],
          y: [],
        },
        trendArr = {
          suspicious: [],
          danger: [],
          high: [],
        },
        detectArr = {
          feature: [],
          static: [],
          dynamic: [],
        };

      if (data && data.length != 0) {
        data.forEach((item) => {
          switch (true) {
            case type != "检测数量":
              if (item.suspiciousNum || item.dangerNum || item.highRiskNum) {
                trendData.x.push(item.monitorTime);
                trendArr.suspicious.push(item.suspiciousNum || 0);
                trendArr.danger.push(item.dangerNum || 0);
                trendArr.high.push(item.highRiskNum || 0);
              }
            case type != "威胁趋势":
              if (item.featureNum || item.staticNum || item.dynamicNum) {
                detectData.x.push(item.monitorTime);
                detectArr.feature.push(item.featureNum || 0);
                detectArr.static.push(item.staticNum || 0);
                detectArr.dynamic.push(item.dynamicNum || 0);
              }
          }
        });
        switch (type) {
          case "威胁趋势":
            this.trendShow = false;
            $.each(this.trendLegend, (index, name) => {
              trendData.y.push({
                name: name,
                data: trendArr[index],
              });
            });
            this.drawTrendChart(trendData);
            break;
          case "检测数量":
            this.detectShow = false;
            $.each(this.detectLegend, (index, name) => {
              detectData.y.push({
                name: name,
                data: detectArr[index],
              });
            });
            this.drawDetectChart(detectData);
            break;
          default:
            this.trendShow = false;
            $.each(this.trendLegend, (index, name) => {
              trendData.y.push({
                name: name,
                data: trendArr[index],
              });
            });
            this.drawTrendChart(trendData);
            this.detectShow = false;
            $.each(this.detectLegend, (index, name) => {
              detectData.y.push({
                name: name,
                data: detectArr[index],
              });
            });
            this.drawDetectChart(detectData);
            break;
        }
      }
    },
    initDetectData(data) {
      let chartData = {
          x: [],
          y: [],
        },
        numArr = {
          feature: [],
          static: [],
          dynamic: [],
        };

      if (data && data.length != 0) {
        this.detectShow = false;
        data.forEach((item) => {
          if (item.featureNum || item.staticNum || item.dynamicNum) {
            chartData.x.push(item.monitorTime);
            numArr.feature.push(item.featureNum || 0);
            numArr.static.push(item.staticNum || 0);
            numArr.dynamic.push(item.dynamicNum || 0);
          }
        });
        $.each(this.detectLegend, (index, name) => {
          chartData.y.push({
            name: name,
            data: numArr[index],
          });
        });
        this.drawDetectChart(chartData);
      }
    },
    initTrendData(data) {
      let chartData = {
          x: [],
          y: [],
        },
        numArr = {
          suspicious: [],
          danger: [],
          high: [],
        };

      if (data && data.length != 0) {
        this.trendShow = false;
        data.forEach((item) => {
          if (item.suspiciousNum || item.dangerNum || item.highRiskNum) {
            chartData.x.push(item.monitorTime);
            numArr.suspicious.push(item.suspiciousNum || 0);
            numArr.danger.push(item.dangerNum || 0);
            numArr.high.push(item.highRiskNum || 0);
          }
        });
        $.each(this.trendLegend, (index, name) => {
          chartData.y.push({
            name: name,
            data: numArr[index],
          });
        });
        this.drawTrendChart(chartData);
      }
    },
    drawTrendChart(data) {
      let chartDom = document.getElementById("trendChart"),
        _this = this,
        options = {};

      data.y.forEach((item, index) => {
        item = Object.assign(item, {
          type: "line",
          symbolSize: 3, //设置图中点大小
          showSymbol: false, //设置图中点不显示
          smooth: true,
          itemStyle: { normal: { color: this.colorArr[index] } },
        });
      });
      options = {
        title: {
          textStyle: {
            fontSize: 13,
            color: "#1e85e6",
          },
          left: 20,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            lineStyle: { type: "line" },
          },
          lineStyle: {
            color: "#fff",
          },
          formatter: function (params) {
            var tempHtml = "",
              template = "";
            $.each(params, function (inx, el) {
              var tempList =
                  el.marker +
                  '<span class="itemName">' +
                  el.seriesName +
                  "：" +
                  el.data +
                  "</span>",
                tempDom = `<div class="charts-tooltip-body">${tempList}</div>`;
              tempHtml += tempDom;
            });

            template = `<div class="charts-tooltip"><div class="charts-tooltip-header">${params[0].name}</div>${tempHtml}</div>`;
            return template;
          },
        },
        legend: {
          data: ["高危", "中危", "低危"],
        },
        calculable: true,
        grid: {
          top: "16%",
          bottom: "20%",
          left: "8%",
          right: "8%",
        },
        xAxis: {
          type: "category",
          boundaryGap: ["10%", "10%"],
          axisLine: {
            lineStyle: { color: "#e6e9f1" },
          },
          axisLabel: {
            color: "#203e66",
            length: 7,
            formatter: (val) => {
              let str = val.split(" ");
              return str.join("\n");
            },
          },
          data: data.x,
        },
        yAxis: [
          {
            type: "value",
            axisTick: { show: false }, //坐标轴刻度
            minInterval: 1,
            axisLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#203e66",
              },
            },
            splitLine: {
              lineStyle: { color: "#e6e9f1" },
            },
          },
        ],
        series: data.y,
      };
      _this.trendChart = this.$echarts.init(chartDom);
      _this.trendChart.setOption(options);

      window.addEventListener("resize", _this.bindTrendResize);
    },
    drawDetectChart(data) {
      let chartDom = document.getElementById("detectChart"),
        _this = this,
        options = {};

      data.y.forEach((item, index) => {
        item = Object.assign(item, {
          type: "line",
          symbolSize: 3, //设置图中点大小
          showSymbol: false, //设置图中点不显示
          smooth: true,
          itemStyle: { normal: { color: this.colorArr[index] } },
        });
      });
      options = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            lineStyle: { type: "line" },
          },
          lineStyle: {
            color: "#fff",
          },
          formatter: function (params) {
            var tempHtml = "",
              template = "";
            $.each(params, function (inx, el) {
              var tempList =
                  el.marker +
                  '<span class="itemName">' +
                  el.seriesName +
                  "：" +
                  el.data +
                  "</span>",
                tempDom = `<div class="charts-tooltip-body">${tempList}</div>`;
              tempHtml += tempDom;
            });

            template = `<div class="charts-tooltip"><div class="charts-tooltip-header">${params[0].name}</div>${tempHtml}</div>`;
            return template;
          },
        },
        legend: {
          data: ["特征检测", "静态检测", "动态检测"],
        },
        calculable: true,
        grid: {
          top: "16%",
          bottom: "20%",
          left: "8%",
          right: "8%",
        },
        xAxis: {
          type: "category",
          boundaryGap: ["10%", "10%"],
          axisLine: {
            lineStyle: { color: "#e6e9f1" },
          },
          axisLabel: {
            color: "#203e66",
            length: 7,
            formatter: (val) => {
              let str = val.split(" ");
              return str.join("\n");
            },
          },
          data: data.x,
        },
        yAxis: [
          {
            type: "value",
            minInterval: 1,
            nameTextStyle: {
              color: "#203e66",
            },
            axisTick: { show: false }, //坐标轴刻度
            axisLine: {
              lineStyle: { color: "#e6e9f1" },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#203e66",
              },
            },
            splitLine: {
              lineStyle: { color: "#e6e9f1" },
            },
          },
        ],
        series: data.y,
      };

      _this.detectChart = this.$echarts.init(chartDom);
      _this.detectChart.setOption(options);
      window.addEventListener("resize", _this.bindDetectResize);
    },

    //功能函数
    timeChange(val, type) {
      let endTime = Date.parse(new Date()),
        startTime = endTime - val * 24 * 60 * 60 * 1000;

      switch (type) {
        case "威胁趋势":
          this.trendReset();
          break;
        case "检测数量":
          this.detectReset();
          break;
      }
      this.searchForm.endTime = parseTime(endTime, "{y}-{m}-{d} 23:59:59");
      this.searchForm.startTime = parseTime(startTime, "{y}-{m}-{d} 00:00:00");
      this.getDataList(this.searchForm, type);
    },
    bindTrendResize() {
      this.trendChart && this.trendChart.resize();
    },
    bindDetectResize() {
      this.detectChart && this.detectChart.resize();
    },
    trendReset() {
      this.trendChart && this.trendChart.dispose();
      this.trendShow = true;
      this.dynamicList = [];
      window.removeEventListener("resize", this.bindTrendResize);
    },
    detectReset() {
      this.detectChart && this.detectChart.dispose();
      this.detectShow = true;
      window.removeEventListener("resize", this.bindDetectResize);
    },
  },
  mounted() {
    this.timeChange(this.timeChart);
  },
  destroyed() {
    window.removeEventListener("resize", this.bindTrendResize);
    window.removeEventListener("resize", this.bindDetectResize);
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dynamic-content {
  height: 100%;
  width: 100%;
}
.dynamic-row {
  overflow-y: auto;
}
.analysis-row {
  height: 50%;
}
.buiness-tab .chart-row {
  min-height: auto;
}
.host-header .msg-tips {
  color: #f44336;
  font-size: 12px;
  margin-left: 10px;
}
</style>