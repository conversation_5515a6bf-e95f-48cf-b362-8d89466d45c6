/* element样式更改 */
.el-menu--horizontal {
  border-bottom: 0;
}
.el-loading-mask {
  background: rgba(0, 0, 0, 0.2) !important;
}
.el-input-group__append,
.el-input-group__prepend {
  background-color: #fff;
}
.table-border {
  border: 1px solid #e2e2e2 !important;
  /* box-shadow: rgba(0,0,0,.2) 0px 0px 2px; */
}
.el-table thead,
tbody {
  color: #203e66 !important;
}
.el-table thead td,
.el-table thead th {
  padding: 5px 0 !important;
}
.el-table tbody td,
.el-table tbody th {
  padding: 6px 0 !important;
}
.el-tabs--card > .el-tabs__content {
  height: calc(100% - 39px);
  padding: 0 !important;
  background-color: white;
  box-shadow: 0px 4px 10px #ccc;
  border-radius: 0 10px 10px;
  position: static;
}
.es-tabs .el-tabs__content {
  height: calc(100% - 54px);
}
.el-dropdown-menu--small .el-dropdown-menu__item {
  font-size: 12px;
}
.el-button {
  height: 26px;
  padding: 0 10px;
}
.text-button,
.edit-span {
  color: #1e85e5 !important;
}
.text-button:hover {
  color: #409eff !important;
}
.el-pagination__editor.el-input {
  width: 40px !important;
}
.el-pagination__editor.el-input .el-input__inner {
  height: 25px !important;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #00a9fd !important;
}
.el-input--mini .el-input__inner {
  height: 25px !important;
  line-height: 25px !important;
  background-color: #ffffff;
}
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background-color: #ffffff !important;
}
.el-pagination__sizes .el-input .el-input__inner {
  font-size: 12px !important;
}
.el-input {
  font-size: 12px !important;
}
.host-form .el-form-item__label {
  text-align: left;
  color: #203e66;
  font-weight: bold;
}
.host-form .el-form-item__content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.proce-radio:hover {
  color: #1e85e6;
}
.proce-radio .el-radio-button__inner {
  border: 0 !important;
  color: #7a808a;
}
.proce-radio:not(:first-child) .el-radio-button__inner:after {
  content: "";
  display: inline-block;
  width: 1px;
  height: 8px;
  position: absolute;
  left: 0;
  top: 10px;
  background-color: #c1c5c5;
}
.proce-radio.is-active .el-radio-button__inner {
  background-color: transparent;
  color: #1e85e6;
  box-shadow: 0 0 0 0;
  border: 0;
}
.el-dialog__header {
  padding: 10px 20px !important;
  /* background-color: #F8F8F8; */
  border-bottom: 1px solid #eee;
  font-weight: bold;
}
.el-dialog__title {
  font-size: 14px !important;
  color: #203e66 !important;
}
.el-dialog__headerbtn .el-dialog__close {
  color: black !important;
  font-size: 16px;
}
.el-dialog__headerbtn {
  top: 10px !important;
}
.el-dialog__footer {
  padding: 15px !important;
  border-top: 1px solid #f5f5f5;
}
.el-message-box__header {
  background-color: #f8f8f8;
  border-bottom: 1px solid #eee;
  padding: 10px 15px !important;
}
.el-message-box__headerbtn .el-message-box__close {
  font-size: 14px;
}
.el-message-box__headerbtn {
  top: 10px !important;
}
.confirm-success {
  background-color: #1e85e6 !important;
  border: 1px solid #e2e2e2 !important;
  padding: 0 10px;
}
.confirm-success:hover {
  background-color: #2d91ef !important;
}
.el-input__inner {
  height: 26px !important;
  line-height: 26px !important;
  color: #203e66 !important;
}
.el-input__suffix,
.el-input__prefix,
.el-icon-date.el-input__icon {
  line-height: 26px;
}
.el-form-item__label {
  line-height: 32px !important;
  font-size: 12px !important;
  color: #203e66 !important;
}
.el-select .el-input__icon,
.el-form-item__content .el-input__icon {
  line-height: 26px !important;
}
.el-checkbox__label,
.el-radio__label {
  font-size: 12px !important;
  color: #203e66;
  display: inline;
}
.el-form-item__content {
  line-height: 32px !important;
  font-size: 12px !important;
}
.colony-header .el-tabs__item:before {
  content: "";
  position: absolute;
  right: -45px;
  top: 0px;
  width: 45px;
  height: 40px;
  background: transparent;
  pointer-events: none;
  border-radius: 12px;
}
.colony-header .el-tabs__item.is-active:before {
  box-shadow: -10px 10px 0 0 white, 2px -1px 2px -1px #e0eaf8 inset;
}
.business-tabs .el-tabs__item.is-top {
  height: 30px;
  line-height: 36px;
  font-size: 12px;
}
.business-tabs .el-tabs__active-bar.is-top {
  background-color: #5174b4;
}
.business-tabs .el-tabs__nav-wrap.is-top::after {
  background-color: #cdd0d3;
  height: 1px;
}
.business-tabs .el-tabs__item.is-top p {
  padding: 0 3px;
}
.business-tabs .el-tabs__item.is-top.is-active {
  color: #203e66;
  font-weight: bold;
}
.colony-header .el-tabs__item:after {
  content: "";
  width: 5px;
  height: 5px;
  position: absolute;
  border-radius: 100%;
  top: 5px;
  right: 5px;
}
.el-tabs--left .el-tabs__header.is-left {
  margin-left: 20px;
}

.el-table .caret-wrapper {
  height: 23px !important;
}
.el-table .sort-caret.ascending {
  top: 0px !important;
}
.el-table .sort-caret.descending {
  bottom: 0px !important;
}
.el-progress__text,
.el-textarea__inner {
  font-size: 12px !important;
  color: #203e66;
}
.el-textarea.is-disabled .el-textarea__inner {
  color: #606266 !important;
}
.el-collapse-item__content {
  padding-bottom: 0 !important;
}
.el-switch__core {
  height: 17px !important;
  width: 34px !important;
}
.el-switch__core:after {
  width: 13px !important;
  height: 13px !important;
}
.el-switch.is-checked .el-switch__core::after {
  margin-left: -14px !important;
}
.el-switch__label * {
  font-size: 12px !important;
  color: #203e66 !important;
}
.el-switch.is-disabled .el-switch__core,
.el-switch.is-disabled .el-switch__label {
  cursor: pointer;
}
.el-switch.is-disabled.inactiveFirewall .el-switch__core,
.el-switch.is-disabled .el-switch__label {
  cursor: not-allowed;
}
.font-switch.is-disabled .el-switch__core {
  cursor: not-allowed;
}
.el-switch.is-disabled {
  opacity: 1 !important;
}
.el-popover--plain {
  word-break: break-all;
}
.el-collapse-item__header {
  background-color: #e8f5fc !important;
  height: 40px !important;
  line-height: 40px !important;
  color: #203e66 !important;
  font-weight: bold !important;
}
.el-collapse-item__arrow {
  line-height: 40px !important;
}
.input-select .el-input__suffix-inner {
  position: absolute;
  top: -6px;
  left: -22px;
}
.input-select i.el-input__icon {
  line-height: 40px !important;
}
.el-date-editor .el-range-input,
.el-date-editor .el-range-separator {
  font-size: 12px;
}
.el-date-editor .el-range-separator,
.el-date-editor .el-range__icon {
  line-height: 19px !important;
}
.el-range-separator {
  min-width: 20px;
}
.el-range-editor--small .el-range__close-icon,
.el-range-editor--small .el-range__icon {
  line-height: 21px;
}
.el-input .el-input__clear {
  line-height: 28px;
}

.el-table__row .overflowColumn {
  font-weight: 600;
}
