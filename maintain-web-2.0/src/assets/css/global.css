* {
  margin: 0;
  padding: 0;
  font-size: 12px;
  font-family: "Microsoft YaHei";
  box-sizing: border-box;
}
html {
  height: 100%;
}
body {
  width: 100%;
  height: 100%;
  background-color: #f4f4f4;
  min-height: 770px;
  min-width: 1366px;
}
h1 {
  font-size: 28px;
}
h2 {
  font-size: 24px;
}
h3 {
  font-size: 18px;
}
ul,
ol {
  padding-left: 30px;
}
#app {
  height: 100%;
}
.row-content {
  height: 100%;
}
.content-form {
  height: 5rem;
}
.content-table {
  height: calc(100% - 5rem);
  padding-bottom: 1%;
}
.table-page {
  margin: 1rem 0;
  text-align: center;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.form-btn {
  color: #203e66;
  text-align: left;
}
.form-btn .iconfont {
  font-size: 14px;
}
.small-round {
  width: 5px;
  height: 5px;
  border-radius: 100%;
}
.btn-style {
  height: 26px;
  padding: 0 10px;
  border: 1px solid #e2e2e2;
  background-color: #fff;
  color: #203e66;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  border-radius: 3px;
}
.btn-style:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}
.primary-btn {
  background-color: #1e85e6;
  color: #fff;
}
.primary-btn:hover {
  background-color: #2d91ef;
  color: #fff;
}
.footer-button {
  text-align: center;
}
.dialog-table {
  padding: 20px;
}
.dialog-border .el-dialog {
  border: 1px solid #1e85e6;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 5px;
}
.procedure-collapse i:before {
  content: "";
}
.logSelect input.el-input__inner {
  background-color: transparent;
  border: 1px solid #c0c4cc;
}
.logRadio span.el-radio-button__inner {
  background-color: transparent;
}
.pagination-popper {
  background-color: transparent;
}

.el-select-dropdown.pagination-popper {
  background-color: #fff;
}
.hovered-row {
  background-color: #f5f7fa;
}
.el-table .bodyRow td {
  border-right: 1px solid #e2e2e2;
  border-bottom: 1px solid #e2e2e2;
}
.popoverSpan {
  display: inline-block;
  max-width: 100%;
}
.headerRow th {
  background-color: #e8f5fc !important;
}
.overflowColumn div.cell {
  white-space: nowrap;
}
.overflowColumn div.cell div {
  overflow: hidden;
  text-overflow: ellipsis;
}
.sortColumn span.caret-wrapper {
  height: 23px;
}
.sortColumn i.ascending {
  top: 0;
}
.sortColumn i.descending {
  bottom: 0;
}
.log-tree span.el-tree-node__label {
  font-size: 12px;
  text-overflow: ellipsis;
  overflow: hidden;
}
.status-describe {
  color: #203e66;
}
.status-row {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.num-red {
  color: #f53749;
}
.num-yellow {
  color: #fc833d;
}
.num-green {
  color: #21db8d;
}
.num-gray {
  color: rgb(205, 208, 211);
}
.colony-header > .el-tabs__header.is-top {
  width: max-content;
  background-color: white;
  margin-bottom: 0;
  box-shadow: 0px -3px 10px #e4e7ed;
  border-radius: 2px 10px 0 0;
}
.colony-header > .el-tabs__header .el-tabs__nav {
  border: 0;
}
[data-color="red"]:after {
  background-color: #f53749;
}
[data-color="yellow"]:after {
  background-color: #fc833d;
}
[data-color="green"]:after {
  background-color: #21db8d;
}
[data-name="colony"] {
  /* width: 122px; */
  text-align: center;
  color: #203e66;
  box-shadow: 2px 0 4px #e4e7ed;
  border-radius: 0px 10px 0px 0px;
  border-left: 0 !important;
  font-weight: bold;
}
[data-name="colony"]:hover {
  color: #1e85e6;
}
[data-name="colony"].is-active {
  color: #1e85e6;
}
[data-name="scoll"] {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}
[data-name="assign"] {
  height: auto !important;
  text-align: center !important;
  padding: 10px 20px 5px !important;
  color: #203e66;
}
[data-name="tab-item"] {
  margin-bottom: 10px;
  background-repeat: no-repeat;
  background-size: 100%;
  font-size: 13px !important;
  height: 48px !important;
  line-height: 48px !important;
  text-align: left !important;
  width: 230px;
}
.procedurePrompt .prompt-remark {
  margin-top: 10px;
}
.procedurePrompt .el-message-box__input {
  padding-top: 0;
}
.noteInput,
.note-popover {
  vertical-align: bottom;
}
.noteInput.el-textarea {
  line-height: normal;
}
.note-popover div {
  overflow: hidden;
  text-overflow: ellipsis;
}
.el-table .warning-row {
  background: #f3505b;
}
.el-table .alarm-row {
  background: #f88640;
}
.expand .el-table__expand-column .cell {
  display: none;
}
.es-detail .el-input--small .el-input__icon {
  line-height: 28px;
}
.noData-div {
  color: #909399;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.popoverContent {
  color: #203e66;
  word-wrap: break-word;
  max-width: 500px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-line;
  padding: 0 5px;
}
div[_echarts_instance_] > div:nth-child(2) {
  background-color: rgb(255, 255, 255) !important;
  border-color: rgb(214, 215, 216) !important;
  box-shadow: 2px 4px 12px rgba(0, 0, 0, 0.2);
  font-size: 12px !important;
  color: #808492 !important;
}

.scrollDialog div.el-dialog__body {
  max-height: 550px;
  overflow: auto;
}
.collapse-transition {
  transition: none;
}
.promptText {
  color: red;
  margin-top: 5px;
}
/* cdh详情操作指令下拉框图标修改 */
.instruct-form .el-input__icon {
  line-height: 26px;
}
/* 定义滚动条宽度及背景 高度分别对应横竖滚动条的尺寸 */
::-webkit-scrollbar {
  width: 4px;
  height: 6px;
}
/* 定义滚动条轨道 内阴影+圆角 */
::-webkit-scrollbar-track {
  /* box-shadow:inset 0 0 5px rgba(0,0,0,0.2); */
  /* border-radius: 5px; */
  background-color: #f1f1f1;
}
/* 定义滑块 内阴影+圆角 */
::-webkit-scrollbar-thumb {
  /* border-radius: 5px; */
  /* box-shadow:inset 0 0 5px rgba(0,0,0,.2); */
  /* background-color:#b5bbd2 */
  /* background-color:rgba(144,147,153,.3) */
  background-color: #cad9ec;
}

/* 设置时间插件 */
.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
  width: 263px;
}

/* textarea 不允许编辑 */
.el-progress__text, .el-textarea__inner{
  resize: none;
}